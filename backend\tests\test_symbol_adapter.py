# -*- coding: utf-8 -*-
"""
SymbolAdapter测试模块
测试股票代码转换逻辑和相关功能
"""
import unittest
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s [%(levelname)s] %(message)s')

# 应用兼容性补丁，必须在导入abu相关模块之前
# 注意：兼容性补丁已移至core.compatibility模块
from backend.app.core.compatibility import apply_patches
apply_patches()

# 导入测试目标模块
from backend.app.abupy_adapter.symbol_adapter import SymbolAdapter
from backend.app.core.exceptions import SymbolError


class TestSymbolAdapter(unittest.TestCase):
    """测试SymbolAdapter类的方法"""

    @classmethod
    def setUpClass(cls):
        """预加载缓存，避免测试依赖外部服务"""
        from backend.app.abupy_adapter.symbol import SymbolNameResolver
        SymbolNameResolver._symbol_name_cache.update({
            'sh600519': '贵州茅台',
            'hk00700': '腾讯控股',
            'usTSLA': '特斯拉',
            'sz000001': '平安银行'
        })

    def test_validate_symbol_valid_cases(self):
        """测试有效的股票代码验证"""
        # A股代码
        self.assertTrue(SymbolAdapter.validate_symbol("sh600000"))
        self.assertTrue(SymbolAdapter.validate_symbol("sz000001"))
        
        # 纯数字代码
        self.assertTrue(SymbolAdapter.validate_symbol("600000"))
        self.assertTrue(SymbolAdapter.validate_symbol("000001"))
        
        # 港股代码
        self.assertTrue(SymbolAdapter.validate_symbol("hk00700"))
        self.assertTrue(SymbolAdapter.validate_symbol("00700"))
        
        # 美股代码
        self.assertTrue(SymbolAdapter.validate_symbol("usAAPL"))
        
        # 指数代码
        self.assertTrue(SymbolAdapter.validate_symbol("sh000001"))  # 上证指数
        self.assertTrue(SymbolAdapter.validate_symbol("sz399001"))  # 深证成指

    def test_validate_symbol_invalid_cases(self):
        """测试无效的股票代码验证"""
        # 空代码
        with self.assertRaises(SymbolError):
            SymbolAdapter.validate_symbol("")
            
        # 格式错误的代码
        with self.assertRaises(SymbolError):
            SymbolAdapter.validate_symbol("s600000")  # 前缀错误
            
        with self.assertRaises(SymbolError):
            SymbolAdapter.validate_symbol("sh60000x")  # 包含非数字字符
            
        with self.assertRaises(SymbolError):
            SymbolAdapter.validate_symbol("sh60")  # 长度错误
            
        # 无法识别的市场
        with self.assertRaises(SymbolError):
            SymbolAdapter.validate_symbol("xx000001")

    def test_normalize_symbol(self):
        """测试股票代码标准化"""
        # 测试已标准化的代码
        symbol, market = SymbolAdapter.normalize_symbol("sh600000")
        self.assertEqual(symbol, "sh600000")
        self.assertEqual(market, "CN")
        
        # 测试纯数字代码
        symbol, market = SymbolAdapter.normalize_symbol("600000")
        self.assertEqual(symbol, "sh600000")
        self.assertEqual(market, "CN")
        
        symbol, market = SymbolAdapter.normalize_symbol("000001")
        self.assertEqual(symbol, "sz000001")
        self.assertEqual(market, "CN")
        
        # 测试港股代码
        symbol, market = SymbolAdapter.normalize_symbol("00700")
        self.assertEqual(symbol, "hk00700")
        self.assertEqual(market, "HK")
        
        # 异常情况
        with self.assertRaises(SymbolError):
            SymbolAdapter.normalize_symbol("")



    def test_get_symbol_name(self):
        """测试获取股票名称"""
        # 测试常见股票 - 只验证返回了名称，而不是代码本身
        name = SymbolAdapter.get_symbol_name("sh600000")
        self.assertIsNotNone(name)
        self.assertNotEqual(name, "sh600000")  # 确保返回名称而不是代码
        
        name = SymbolAdapter.get_symbol_name("sz000001")
        self.assertIsNotNone(name)
        self.assertNotEqual(name, "sz000001")
        
        # 测试指数
        self.assertEqual(SymbolAdapter.get_symbol_name("sh000001"), "上证指数")
        self.assertEqual(SymbolAdapter.get_symbol_name("sz399001"), "深证成指")
        
        # 测试美股 - 调整断言，只要返回名称包含股票代码即可
        name = SymbolAdapter.get_symbol_name("usAAPL")
        self.assertIsNotNone(name)
        self.assertIn("AAPL", name)  # 确保返回的名称中包含股票代码
        
        # 测试港股 - 修改断言，接受返回中文名称的情况
        name = SymbolAdapter.get_symbol_name("hk00700")
        self.assertIsNotNone(name)
        # 港股可能返回中文名称或包含代码的名称
        # 只要不是原始代码就算通过
        self.assertNotEqual(name, "hk00700")


if __name__ == "__main__":
    unittest.main()