#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期列修复函数
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import pandas as pd
from backend.app.abupy_adapter.execution.abupy_caller import force_fix_date_columns

def test_force_fix_date_columns():
    """测试force_fix_date_columns函数"""
    print("开始测试force_fix_date_columns函数...")
    
    # 创建测试数据
    test_data = {
        'trade_date': ['20220101', '20220102', '20220103'],
        'open': [10.0, 11.0, 12.0],
        'high': [10.5, 11.5, 12.5],
        'low': [9.5, 10.5, 11.5],
        'close': [10.2, 11.2, 12.2],
        'volume': [1000, 1100, 1200]
    }
    
    df = pd.DataFrame(test_data)
    print(f"原始DataFrame:")
    print(df)
    print(f"原始索引类型: {type(df.index)}")
    print(f"原始列: {list(df.columns)}")
    
    # 应用修复函数
    try:
        fixed_df = force_fix_date_columns(df)
        print(f"\n修复后DataFrame:")
        print(fixed_df)
        print(f"修复后索引类型: {type(fixed_df.index)}")
        print(f"修复后列: {list(fixed_df.columns)}")
        print(f"索引范围: {fixed_df.index.min()} 到 {fixed_df.index.max()}")
        
        # 检查是否有date和date_int列
        if 'date' in fixed_df.columns:
            print(f"date列类型: {fixed_df['date'].dtype}")
        if 'date_int' in fixed_df.columns:
            print(f"date_int列类型: {fixed_df['date_int'].dtype}")
            print(f"date_int示例值: {fixed_df['date_int'].iloc[0]}")
        
        print("\n测试成功！")
        return True
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_force_fix_date_columns()