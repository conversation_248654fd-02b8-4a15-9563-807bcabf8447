// 仪表板API Mock处理器 - 严格按照API契约

import { http, HttpResponse } from 'msw';

export const dashboardHandlers = [
  // 获取仪表盘摘要数据
  http.get('/api/v1/dashboard/summary', () => {
    // 模拟仪表板摘要数据
    const mockSummary = {
      today_gain: 0.0125, // 今日涨跌幅 1.25%
      active_strategies: 5, // 活跃策略数
      total_turnover_wan: 12580.5, // 总成交额（万元）
      signals_count: 23, // 信号数量
      market_performance: {
        date: [
          '2023-12-01', '2023-12-02', '2023-12-03', '2023-12-04', '2023-12-05',
          '2023-12-06', '2023-12-07', '2023-12-08', '2023-12-09', '2023-12-10',
          '2023-12-11', '2023-12-12', '2023-12-13', '2023-12-14', '2023-12-15',
          '2023-12-16', '2023-12-17', '2023-12-18', '2023-12-19', '2023-12-20',
          '2023-12-21', '2023-12-22', '2023-12-23', '2023-12-24', '2023-12-25',
          '2023-12-26', '2023-12-27', '2023-12-28', '2023-12-29', '2023-12-30'
        ],
        value: [
          3200.5, 3215.8, 3198.2, 3225.6, 3240.1,
          3235.9, 3248.3, 3260.7, 3255.4, 3270.2,
          3285.6, 3278.9, 3295.3, 3310.8, 3305.2,
          3320.7, 3315.4, 3330.9, 3345.2, 3340.8,
          3355.6, 3350.3, 3365.7, 3370.4, 3385.9,
          3380.2, 3395.8, 3400.5, 3415.3, 3420.7
        ]
      }
    };

    return HttpResponse.json({
      success: true,
      message: "获取仪表盘摘要成功",
      data: mockSummary
    });
  })
];