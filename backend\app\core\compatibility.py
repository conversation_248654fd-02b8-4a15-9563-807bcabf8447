# -*- coding: utf-8 -*-
"""
Python版本兼容性补丁
用于解决Python 3.12与abu项目的兼容性问题
"""
import sys
import logging
import os

def apply_patches():
    """应用所有兼容性补丁"""
    # 为numpy.NAN添加补丁（兼容旧版本abupy）
    patch_numpy_nan()
    
    # 为collections.Iterable应用补丁
    patch_collections_iterable()

    # 为pandas.DataFrame.append应用补丁
    patch_pandas_append()
    
    # 模拟IPython模块
    patch_ipython_module()
    
    # 为scipy.interp应用补丁
    patch_scipy_interp()

    # 对scipy模块应用补丁
    patch_scipy_module()
    
    # 使用独立的模拟模块文件
    try:
        from app.abupy_adapter.mock_modules import install_mock_modules
        install_mock_modules()
        logging.info("已应用 mock_modules (如 ipywidgets) 模拟补丁。")
    except ImportError:
        logging.warning("无法导入mock_modules，跳过此补丁。")
    
    # 其他潜在的补丁可以在这里添加

def patch_pandas_append():
    """
    恢复被废弃的 pandas.DataFrame.append 方法，以支持旧版abupy。
    """
    try:
        import pandas as pd
        if not hasattr(pd.DataFrame, 'append'):
            logging.info("Pandas补丁: DataFrame缺少.append方法，正在恢复...")
            
            def _append(self, other, ignore_index=False, verify_integrity=False, sort=False):
                if isinstance(other, (list, tuple)):
                    to_concat = [self] + other
                else:
                    to_concat = [self, other]
                return pd.concat(to_concat, ignore_index=ignore_index, verify_integrity=verify_integrity, sort=sort)

            pd.DataFrame.append = _append
            logging.info("Pandas补丁: DataFrame.append方法已成功恢复。")
    except ImportError:
        logging.warning("未安装pandas库，跳过DataFrame.append补丁。")
    except Exception as e:
        logging.error(f"应用pandas.DataFrame.append补丁失败: {str(e)}")

def patch_collections_iterable():
    """
    在collections模块中添加已移动到abc子模块的类型引用
    解决Python 3.12中各种类型被移动到collections.abc的问题
    """
    try:
        import collections
        from collections import abc
        
        # 需要补丁的类型列表
        abc_types = [
            'Iterable',
            'Mapping',
            'MutableMapping',
            'Sequence',
            'MutableSequence',
            'Set',
            'MutableSet',
            'Container',
            'Sized',
            'Callable'
        ]
        
        patched_count = 0
        
        # 将abc中的类型添加到collections模块
        for type_name in abc_types:
            if not hasattr(collections, type_name) and hasattr(abc, type_name):
                setattr(collections, type_name, getattr(abc, type_name))
                logging.debug(f"为 collections.{type_name} 创建别名指向 collections.abc.{type_name}")
                patched_count += 1
                
        if patched_count > 0:
            logging.info(f"已应用 collections 模块兼容性补丁，为 {patched_count} 个 abc 类型（如 Iterable, Mapping 等）在 collections 命名空间下创建了别名。")
    except Exception as e:
        logging.error(f"应用collections兼容性补丁失败: {str(e)}")

def patch_scipy_interp():
    """
    为 scipy.interp 提供兼容性补丁。
    新版本的scipy中，interp被移除，但abupy的部分模块（如MLBu）依赖它。
    此补丁使用功能等效的 numpy.interp 来替代。
    """
    try:
        import scipy
        import numpy
        if not hasattr(scipy, 'interp'):
            logging.info("Scipy补丁: scipy模块缺少interp，正在使用numpy.interp进行修补...")
            scipy.interp = numpy.interp
            logging.info("Scipy补丁: scipy.interp已成功修补。")
    except ImportError:
        logging.warning("未安装scipy或numpy，跳过scipy.interp补丁。")
    except Exception as e:
        logging.error(f"应用scipy.interp补丁失败: {str(e)}")

def patch_ipython_module():
    """
    模拟IPython模块，为测试提供必要的空实现
    """
    try:
        # 检查IPython模块是否已经存在
        try:
            import IPython
            logging.info("IPython模块已存在，无需补丁")
            return
        except ImportError:
            pass
        
        # 创建一个模拟IPython模块
        class MockDisplayModule:
            @staticmethod
            def clear_output(wait=False):
                pass
                
            @staticmethod
            def display(*args, **kwargs):
                pass
                
            @staticmethod
            def HTML(html_content):
                return html_content
                
            @staticmethod
            def Image(data, format=None, embed=None):
                return None
        
        # 创建模拟的IPython.display模块
        mock_display = MockDisplayModule()
        
        # 创建模拟的IPython.core.getipython模块
        class MockGetIPythonModule:
            @staticmethod
            def get_ipython():
                return None
        
        mock_getipython = MockGetIPythonModule()
        
        # 创建模拟的IPython模块
        class MockIPythonModule:
            display = mock_display
            
            class core:
                getipython = mock_getipython
        
        # 将模拟模块添加到sys.modules
        sys.modules['IPython'] = MockIPythonModule()
        sys.modules['IPython.display'] = mock_display
        sys.modules['IPython.core'] = MockIPythonModule.core
        sys.modules['IPython.core.getipython'] = mock_getipython
        
        logging.info("已应用IPython模块模拟补丁")
    except Exception as e:
        logging.error(f"应用IPython模块模拟补丁失败: {str(e)}")

def patch_numpy_nan():
    """
    为numpy添加NAN属性
    解决abupy使用已弃用的np.NAN的兼容性问题
    在新版本numpy中，使用np.nan替代np.NAN
    """
    try:
        import numpy as np
        
        # 检查是否已经有NAN属性
        if not hasattr(np, 'NAN') and hasattr(np, 'nan'):
            # 使用nan创建NAN别名
            np.NAN = np.nan
            logging.info("已应用numpy.NAN补丁，使用np.nan作为np.NAN的别名")
    except Exception as e:
        logging.error(f"应用numpy.NAN补丁失败: {str(e)}")

def patch_scipy_module():
    """
    对scipy模块应用补丁，处理兼容性问题
    """
    try:
        # 检查scipy模块是否已经存在
        try:
            import scipy
            # 如果存在，检查是否需要补丁
            if hasattr(scipy, 'version'):
                logging.info(f"scipy模块已存在，版本: {scipy.version.version}，无需补丁")
            return
        except ImportError:
            pass
        
        # 创建一个模拟scipy模块
        class MockScipyModule:
            # 模拟版本信息
            class version:
                version = '1.11.0'  # 模拟一个版本号
            
            # 模拟optimize子模块
            class optimize:
                @staticmethod
                def minimize(fun, x0, **kwargs):
                    # 返回一个模拟的优化结果
                    class OptimizeResult:
                        def __init__(self):
                            self.x = x0
                            self.fun = fun(x0)
                            self.success = True
                            self.message = "Mock optimization"
                    return OptimizeResult()
            
            # 模拟stats子模块
            class stats:
                @staticmethod
                def norm(*args, **kwargs):
                    return None
        
        # 将模拟模块添加到sys.modules
        sys.modules['scipy'] = MockScipyModule()
        sys.modules['scipy.optimize'] = MockScipyModule.optimize
        sys.modules['scipy.stats'] = MockScipyModule.stats
        
        logging.info("已应用scipy模块模拟补丁")
    except Exception as e:
        logging.error(f"应用scipy模块模拟补丁失败: {str(e)}")
