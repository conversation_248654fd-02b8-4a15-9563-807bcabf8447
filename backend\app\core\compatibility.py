# -*- coding: utf-8 -*-
"""
Python版本兼容性补丁
用于解决Python 3.12与abu项目的兼容性问题
"""
import sys
import logging
import os
from unittest.mock import MagicMock
import pandas as pd
import numpy as np

def apply_patches():
    """应用所有兼容性补丁"""
    # 1. 修复基础库依赖
    patch_scipy_interp()
    patch_scipy_module()
    patch_numpy_nan()
    patch_collections_iterable()
    patch_pandas_append()
    
    # 2. 修复IPython/Jupyter依赖
    patch_ipython_module()
    patch_ipywidgets_module()
    
    # 3. 修复abupy内部逻辑
    patch_abupy_atr_calculation()
    patch_pandas_timedelta_comparison()  # 现在它可以安全地导入ABuPickTimeWorker了

    patch_capture_buy_orders()
    patch_do_symbols_fallback_from_forced_orders()
    
    # 新增：为abu日期操作应用补丁（abupy特定）
    patch_abu_date_operations()
    
    # 新增：修复ABuOrder中的日期转换问题
    patch_abu_order_date_conversion()
    
    # 新增：修复ABuPickTimeWorker._task_loop中的today['date']日期处理问题
    patch_abupy_today_date_fix()
    
    # 使用独立的模拟模块文件
    try:
        from backend.app.abupy_adapter.mock_modules import install_mock_modules
        install_mock_modules()
        logging.info("已应用 mock_modules (如 ipywidgets) 模拟补丁。")
    except ImportError:
        logging.warning("无法导入mock_modules，跳过此补丁。")
    
    # 其他潜在的补丁可以在这里添加
    # --- 补丁 1: 模拟 ipywidgets 模块 ---
    # abupy 依赖了 ipywidgets，但在我们的环境中并不需要。
    # 我们在这里模拟它，以避免不必要的依赖安装和运行时错误。
    if 'ipywidgets' not in sys.modules:
        ipywidgets_mock = MagicMock()
        sys.modules['ipywidgets'] = ipywidgets_mock
        print("INFO:     [成功] 'ipywidgets' 模块已被模拟。")
    else:
        print("INFO:     [跳过] 'ipywidgets' 模块已存在。")

def patch_pandas_append():
    """
    恢复被废弃的 pandas.DataFrame.append 方法，以支持旧版abupy。
    """
    try:
        import pandas as pd
        if not hasattr(pd.DataFrame, 'append'):
            logging.info("Pandas补丁: DataFrame缺少.append方法，正在恢复...")
            
            def _append(self, other, ignore_index=False, verify_integrity=False, sort=False):
                if isinstance(other, (list, tuple)):
                    to_concat = [self] + other
                else:
                    to_concat = [self, other]
                return pd.concat(to_concat, ignore_index=ignore_index, verify_integrity=verify_integrity, sort=sort)

            pd.DataFrame.append = _append
            logging.info("Pandas补丁: DataFrame.append方法已成功恢复。")
    except ImportError:
        logging.warning("未安装pandas库，跳过DataFrame.append补丁。")
    except Exception as e:
        logging.error(f"应用pandas.DataFrame.append补丁失败: {str(e)}")

def patch_collections_iterable():
    """
    在collections模块中添加已移动到abc子模块的类型引用
    解决Python 3.12中各种类型被移动到collections.abc的问题
    """
    try:
        import collections
        from collections import abc
        
        # 需要补丁的类型列表
        abc_types = [
            'Iterable',
            'Mapping',
            'MutableMapping',
            'Sequence',
            'MutableSequence',
            'Set',
            'MutableSet',
            'Container',
            'Sized',
            'Callable'
        ]
        
        patched_count = 0
        
        # 将abc中的类型添加到collections模块
        for type_name in abc_types:
            if not hasattr(collections, type_name) and hasattr(abc, type_name):
                setattr(collections, type_name, getattr(abc, type_name))
                logging.debug(f"为 collections.{type_name} 创建别名指向 collections.abc.{type_name}")
                patched_count += 1
                
        if patched_count > 0:
            logging.info(f"已应用 collections 模块兼容性补丁，为 {patched_count} 个 abc 类型（如 Iterable, Mapping 等）在 collections 命名空间下创建了别名。")
    except Exception as e:
        logging.error(f"应用collections兼容性补丁失败: {str(e)}")

def patch_scipy_interp():
    """
    为 scipy.interp 提供兼容性补丁。
    新版本的scipy中，interp被移除，但abupy的部分模块（如MLBu）依赖它。
    此补丁使用功能等效的 numpy.interp 来替代。
    """
    try:
        import scipy
        import numpy
        if not hasattr(scipy, 'interp'):
            logging.info("Scipy补丁: scipy模块缺少interp，正在使用numpy.interp进行修补...")
            scipy.interp = numpy.interp
            logging.info("Scipy补丁: scipy.interp已成功修补。")
    except ImportError:
        logging.warning("未安装scipy或numpy，跳过scipy.interp补丁。")
    except Exception as e:
        logging.error(f"应用scipy.interp补丁失败: {str(e)}")

def patch_ipython_module():
    """
    模拟IPython模块，为测试提供必要的空实现
    """
    try:
        # 检查IPython模块是否已经存在
        try:
            import IPython
            logging.info("IPython模块已存在，无需补丁")
            return
        except ImportError:
            pass
        
        # 创建一个模拟IPython模块
        class MockDisplayModule:
            @staticmethod
            def clear_output(wait=False):
                pass
                
            @staticmethod
            def display(*args, **kwargs):
                pass
                
            @staticmethod
            def HTML(html_content):
                return html_content
                
            @staticmethod
            def Image(data, format=None, embed=None):
                return None
        
        # 创建模拟的IPython.display模块
        mock_display = MockDisplayModule()
        
        # 创建模拟的IPython.core.getipython模块
        class MockGetIPythonModule:
            @staticmethod
            def get_ipython():
                return None
        
        mock_getipython = MockGetIPythonModule()
        
        # 创建模拟的IPython模块
        class MockIPythonModule:
            display = mock_display
            
            class core:
                getipython = mock_getipython
        
        # 将模拟模块添加到sys.modules
        sys.modules['IPython'] = MockIPythonModule()
        sys.modules['IPython.display'] = mock_display
        sys.modules['IPython.core'] = MockIPythonModule.core
        sys.modules['IPython.core.getipython'] = mock_getipython
        
        logging.info("已应用IPython模块模拟补丁")
    except Exception as e:
        logging.error(f"应用IPython模块模拟补丁失败: {str(e)}")

def patch_numpy_nan():
    """
    为numpy添加NAN属性
    解决abupy使用已弃用的np.NAN的兼容性问题
    在新版本numpy中，使用np.nan替代np.NAN
    """
    try:
        import numpy as np
        
        # 检查是否已经有NAN属性
        if not hasattr(np, 'NAN') and hasattr(np, 'nan'):
            # 使用nan创建NAN别名
            np.NAN = np.nan
            logging.info("已应用numpy.NAN补丁，使用np.nan作为np.NAN的别名")
    except Exception as e:
        logging.error(f"应用numpy.NAN补丁失败: {str(e)}")

def patch_pandas_timedelta_comparison():
    """
    精确修复 ABuPickTimeWorker 中 pandas timedelta 与整数比较的兼容性问题
    直接替换第272行的问题代码，避免timedelta比较
    """
    try:
        import pandas as pd
        import numpy as np
        import logging
        
        # 确保所有必要的补丁已应用
        patch_collections_iterable()
        patch_ipython_module()
        patch_ipywidgets_module()
        
        # 导入目标模块
        try:
            import abupy.AlphaBu.ABuPickTimeWorker as ABuPickTimeWorker
        except ImportError as import_error:
            logging.warning(f"⚠️ ABuPickTimeWorker模块导入失败: {import_error}，尝试修补scipy.interp后重试导入...")
            try:
                patch_scipy_interp()
                import abupy.AlphaBu.ABuPickTimeWorker as ABuPickTimeWorker
                logging.info("ABuPickTimeWorker 经过scipy.interp修补后导入成功")
            except Exception as e2:
                logging.warning(f"ABuPickTimeWorker二次导入仍失败，跳过该补丁: {e2}")
                return
        
        # 保存原始方法
        original_fit = ABuPickTimeWorker.AbuPickTimeWorker.fit
        
        def patched_fit(self):
            """完全替换的fit方法：保留原有修复，改为严格顺序逐日执行，恢复 T+1 语义"""
            import logging
            import numpy as np
            import pandas as pd

            # 初始化 today_ind（与原版对齐）
            if not hasattr(self, 'today_ind'):
                self.today_ind = 0
                logging.info("已在 patched_fit 中初始化 today_ind 属性为 0")

            try:
                if hasattr(self, 'kl_pd') and self.kl_pd is not None:
                    kl = self.kl_pd

                    # 第247行：设置 week_task
                    if 'date_week' in kl.columns:
                        kl['week_task'] = np.where(kl.date_week == 4, 1, 0)

                    # 第272行：修复 month_task 的 timedelta 比较问题
                    if 'date' in kl.columns:
                        try:
                            if not pd.api.types.is_datetime64_any_dtype(kl['date']):
                                kl['date'] = pd.to_datetime(kl['date'])

                            date_shifted = kl.shift(-1)['date']
                            date_current = kl['date']
                            date_diff_days = (date_shifted - date_current).dt.days

                            kl['month_task'] = np.where(date_diff_days.fillna(0) > 60, 1, 0)
                            logging.info(f"成功计算 month_task 列，共 {len(kl)} 行数据")
                        except Exception as calc_error:
                            logging.warning(f"month_task 计算失败，使用默认值: {calc_error}")
                            kl['month_task'] = 0
                    else:
                        kl['month_task'] = 0

                    # 关键改动：严格顺序逐日执行，不能用 apply（会破坏次日执行的时序副作用）
                    try:
                        kl = kl.sort_index()
                    except Exception:
                        pass

                    for _, row in kl.iterrows():
                        self._task_loop(row)
                    # =====================顺序循环 patched_fit 的 for 循环结束后，加“资本探针”来查看 ABu 的 capital 内有哪些可能的记录容器=====
                    cap = getattr(self, "capital", None)
                    if cap is not None:
                        try:
                            import pandas as pd
                            logging.info(f"[CAPITAL_PROBE] keys: {list(getattr(cap, '__dict__', {}).keys())}")
                            if hasattr(cap, "capital_pd") and isinstance(cap.capital_pd, pd.DataFrame):
                                logging.info(f"[CAPITAL_PROBE] capital_pd shape: {cap.capital_pd.shape}")
                                # 可选：打印最后几行查看是否有交易带来的现金或净值变化
                                try:
                                    tail = cap.capital_pd.tail(3)
                                    logging.info(f"[CAPITAL_PROBE] capital_pd tail:\n{tail}")
                                except Exception:
                                    pass
                        except Exception:
                            pass
                    # ====================资本探针结束====================

                logging.info("patched_fit(Sequential) 成功完成")
                return None 

            except Exception as e:
                logging.exception(f"patched_fit(Sequential) 执行失败: {e}")
                raise
        
        def _apply_timedelta_fix(self):
            """修复timedelta比较问题 - 预先计算month_task列并修复日期格式"""
            try:
                # 初始化today_ind属性（原始abupy代码中缺少此初始化）
                if not hasattr(self, 'today_ind'):
                    self.today_ind = 0
                    logging.info("已初始化today_ind属性为0")
                
                # 预先修复可能导致timedelta比较错误的数据问题
                if hasattr(self, 'kl_pd') and self.kl_pd is not None:
                    # 修复第240行：安全地计算month_task
                    if 'date' in self.kl_pd.columns:
                        try:
                            # 确保date列是datetime类型
                            if not pd.api.types.is_datetime64_any_dtype(self.kl_pd['date']):
                                self.kl_pd['date'] = pd.to_datetime(self.kl_pd['date'])
                            
                            # 计算日期差异，转换为天数后再比较
                            date_shifted = self.kl_pd.shift(-1)['date']
                            date_current = self.kl_pd['date']
                            
                            # 安全的日期差异计算
                            date_diff = date_shifted - date_current
                            date_diff_days = date_diff.dt.days
                            
                            # 使用安全的比较方式，预先计算month_task
                            self.kl_pd['month_task'] = np.where(
                                date_diff_days.fillna(0) > 60, 1, 0
                            )
                            logging.info(f"成功预先计算month_task列，共{len(self.kl_pd)}行数据")
                            
                            # 验证计算结果
                            task_count = self.kl_pd['month_task'].sum()
                            logging.info(f"month_task中有{task_count}个月末标记")
                            
                            # 保持date列的原始格式，不进行转换
                            # 通过预先计算month_task列来避免timedelta比较问题
                            logging.info("已预先计算month_task列，保持date列原始格式")
                            
                        except Exception as calc_error:
                            logging.warning(f"month_task计算失败，使用默认值: {calc_error}")
                            self.kl_pd['month_task'] = 0
                    else:
                        self.kl_pd['month_task'] = 0
                        logging.info("kl_pd中无date列，设置默认month_task值")
                
            except Exception as e:
                logging.error(f"timedelta修复过程失败: {e}")
                # 确保至少有基本的month_task列
                if hasattr(self, 'kl_pd') and self.kl_pd is not None:
                    self.kl_pd['month_task'] = 0
        
        # 将修复方法添加到类中
        ABuPickTimeWorker.AbuPickTimeWorker._apply_timedelta_fix = _apply_timedelta_fix
        
        # 应用补丁
        ABuPickTimeWorker.AbuPickTimeWorker.fit = patched_fit
        
        logging.info("✅ 已成功应用ABuPickTimeWorker pandas兼容性补丁（生产版本）")
        
    except ImportError:
        logging.warning("⚠️  ABuPickTimeWorker模块未找到，跳过补丁")
    except Exception as e:
        logging.error(f"❌ 应用ABuPickTimeWorker补丁失败: {e}")
        raise e  # 生产环境中，补丁失败应该抛出异常以便及时发现问题

def patch_abu_date_operations():
    """修复abupy中的日期操作兼容性问题"""
    try:
        import abupy.CoreBu.ABuDateUtil as ABuDateUtil
        
        # 保存原始函数
        original_fmt_date = ABuDateUtil.fmt_date
        
        def patched_fmt_date(convert_date, fmt='%Y-%m-%d'):
            """修复后的fmt_date函数，支持多种日期格式"""
            try:
                # 如果是字符串，先尝试解析
                if isinstance(convert_date, str):
                    # 尝试多种日期格式
                    for date_fmt in ['%Y%m%d', '%Y-%m-%d', '%Y/%m/%d']:
                        try:
                            parsed_date = pd.to_datetime(convert_date, format=date_fmt)
                            return parsed_date.strftime(fmt)
                        except:
                            continue
                    # 如果都失败，使用pandas的智能解析
                    parsed_date = pd.to_datetime(convert_date)
                    return parsed_date.strftime(fmt)
                
                # 如果是数字（可能是时间戳），转换为datetime
                elif isinstance(convert_date, (int, float)):
                    if convert_date > 1e10:  # 毫秒时间戳
                        parsed_date = pd.to_datetime(convert_date, unit='ms')
                    else:  # 秒时间戳或天数
                        parsed_date = pd.to_datetime(convert_date, unit='s')
                    return parsed_date.strftime(fmt)
                
                # 如果已经是datetime对象
                elif hasattr(convert_date, 'strftime'):
                    return convert_date.strftime(fmt)
                
                # 其他情况，尝试原始函数
                else:
                    return original_fmt_date(convert_date, fmt)
                    
            except Exception as e:
                logging.warning(f"fmt_date转换失败: {e}, 输入: {convert_date}, 类型: {type(convert_date)}")
                # 如果所有方法都失败，返回字符串形式
                return str(convert_date)
        
        # 替换原始函数
        ABuDateUtil.fmt_date = patched_fmt_date
        logging.info("已应用 abu 日期操作兼容性补丁")
        
    except ImportError:
        logging.warning("无法导入 ABuDateUtil，跳过日期操作补丁")
    except Exception as e:
        logging.error(f"应用日期操作补丁时出错: {e}")

def patch_abu_order_date_conversion():
    """修复ABuOrder中的日期转换问题"""
    try:
        import abupy.TradeBu.ABuOrder as ABuOrder
        
        # 保存原始的fit_buy_order方法
        original_fit_buy_order = ABuOrder.AbuOrder.fit_buy_order
        
        def patched_fit_buy_order(self, day_ind, factor):
            """修复后的fit_buy_order方法，正确处理日期转换"""
            try:
                # 获取K线数据
                kl_pd_buy = factor.kl_pd.iloc[day_ind]
                
                # 安全的日期转换
                if hasattr(kl_pd_buy, 'date'):
                    date_value = kl_pd_buy.date
                    logging.info(f"[DATE_DEBUG] 原始date_value: {date_value}, 类型: {type(date_value)}")
                    
                    # 如果是Timestamp，转换为YYYYMMDD格式的整数
                    if hasattr(date_value, 'strftime'):
                        # 特殊处理：如果是1970-01-01格式但纳秒部分包含实际日期
                        if str(date_value).startswith('1970-01-01 00:00:00.0'):
                            # 从纳秒部分提取日期信息
                            nanoseconds_str = str(date_value).split('.')[-1]
                            if len(nanoseconds_str) >= 8 and nanoseconds_str.startswith('0'):
                                # 提取YYYYMMDD部分（去掉开头的0）
                                date_part = nanoseconds_str[1:9]  # 取20230105这8位
                                if date_part.isdigit() and len(date_part) == 8:
                                    self.buy_date = int(date_part)
                                    logging.info(f"[DATE_DEBUG] 从纳秒提取日期: {date_value} -> {self.buy_date}")
                                else:
                                    self.buy_date = int(date_value.strftime('%Y%m%d'))
                                    logging.info(f"[DATE_DEBUG] 标准Timestamp转换: {date_value} -> {self.buy_date}")
                            else:
                                self.buy_date = int(date_value.strftime('%Y%m%d'))
                                logging.info(f"[DATE_DEBUG] 标准Timestamp转换: {date_value} -> {self.buy_date}")
                        else:
                            self.buy_date = int(date_value.strftime('%Y%m%d'))
                            logging.info(f"[DATE_DEBUG] 标准Timestamp转换: {date_value} -> {self.buy_date}")
                    elif isinstance(date_value, str):
                        # 如果是字符串，尝试解析后转换
                        try:
                            parsed_date = pd.to_datetime(date_value)
                            self.buy_date = int(parsed_date.strftime('%Y%m%d'))
                            logging.debug(f"字符串日期转换: {date_value} -> {self.buy_date}")
                        except:
                            # 如果解析失败，尝试直接转换为整数
                            clean_date = str(date_value).replace('-', '').replace('/', '').replace(' ', '')[:8]
                            self.buy_date = int(clean_date) if clean_date.isdigit() else 20250101
                            logging.debug(f"清理后日期转换: {date_value} -> {self.buy_date}")
                    elif isinstance(date_value, (int, float)):
                        # 如果是数字，可能是时间戳或已经是YYYYMMDD格式
                        if date_value > 1e10:  # 毫秒时间戳
                            parsed_date = pd.to_datetime(date_value, unit='ms')
                            self.buy_date = int(parsed_date.strftime('%Y%m%d'))
                        elif date_value > 1e9:  # 秒时间戳
                            parsed_date = pd.to_datetime(date_value, unit='s')
                            self.buy_date = int(parsed_date.strftime('%Y%m%d'))
                        elif date_value >= 20000000 or (date_value >= 19000000 and date_value <= 21000000):  # YYYYMMDD格式
                            self.buy_date = int(date_value)
                        else:
                            # 其他情况，尝试作为YYYYMMDD格式处理
                            self.buy_date = int(date_value) if date_value > 10000000 else 20250101
                        logging.debug(f"数字日期转换: {date_value} -> {self.buy_date}")
                    else:
                        # 其他情况，尝试直接转换
                        try:
                            self.buy_date = int(str(date_value).replace('-', '').replace('/', '')[:8])
                        except:
                            self.buy_date = 20250101
                        logging.debug(f"其他类型日期转换: {date_value} ({type(date_value)}) -> {self.buy_date}")
                else:
                    # 如果没有date属性，使用索引或默认值
                    self.buy_date = 20250101  # 默认日期
                    logging.debug(f"无date属性，使用默认日期: {self.buy_date}")
                
                # 调用原始方法的其余逻辑（除了日期设置部分）
                self.buy_price = kl_pd_buy.close
                self.buy_factor = factor.__class__.__name__
                self.buy_symbol = factor.kl_pd.name
                
                # 设置其他必要属性
                if hasattr(factor, 'buy_pos'):
                    self.buy_pos = factor.buy_pos
                
                logging.info(f"成功设置订单日期: {self.buy_date}, 价格: {self.buy_price}")
                
            except Exception as e:
                logging.error(f"patched_fit_buy_order执行失败: {e}")
                # 如果补丁失败，尝试调用原始方法
                try:
                    original_fit_buy_order(self, day_ind, factor)
                except:
                    # 如果原始方法也失败，设置默认值
                    self.buy_date = 20250101
                    self.buy_price = 1.0
                    self.buy_factor = "Unknown"
                    self.buy_symbol = "Unknown"
        
        # 替换原始方法
        ABuOrder.AbuOrder.fit_buy_order = patched_fit_buy_order
        logging.info("已应用 ABuOrder 日期转换兼容性补丁")
        
    except ImportError:
        logging.warning("无法导入 ABuOrder，跳过订单日期转换补丁")
    except Exception as e:
        logging.error(f"应用订单日期转换补丁时出错: {e}")

def verify_patches():
    """
    验证所有补丁是否正确应用
    """
    try:
        # 验证 pandas 相关补丁
        import pandas as pd
        logging.info(f"Pandas 版本: {pd.__version__}")
        
        # 验证 abupy 导入
        import abupy
        logging.info("abupy 模块导入成功")
        
        # 验证 ABuPickTimeWorker 补丁
        from abupy.AlphaBu.ABuPickTimeWorker import AbuPickTimeWorker
        if hasattr(AbuPickTimeWorker, 'fit'):
            logging.info("ABuPickTimeWorker.fit 方法存在，补丁可能已应用")
        
        logging.info("所有补丁验证完成")
        return True
        
    except Exception as e:
        logging.error(f"补丁验证失败: {e}")
        return False

def patch_ipywidgets_module():
    """
    对ipywidgets模块应用补丁，处理兼容性问题
    """
    try:
        # 检查ipywidgets模块是否已经存在
        try:
            import ipywidgets
            logging.info(f"[跳过] 'ipywidgets' 模块已存在。")
            return
        except ImportError:
            pass
        
        # 创建一个模拟ipywidgets模块
        class MockIpywidgetsModule:
            # 模拟基本的widget类
            class Widget:
                def __init__(self, **kwargs):
                    pass
            
            class Button(Widget):
                def __init__(self, description='', **kwargs):
                    super().__init__(**kwargs)
                    self.description = description
            
            class Output(Widget):
                def __init__(self, **kwargs):
                    super().__init__(**kwargs)
            
            class FloatProgress(Widget):
                def __init__(self, value=0.0, min=0.0, max=100.0, **kwargs):
                    super().__init__(**kwargs)
                    self.value = value
                    self.min = min
                    self.max = max
            
            class Text(Widget):
                def __init__(self, value='', **kwargs):
                    super().__init__(**kwargs)
                    self.value = value
            
            class Box(Widget):
                def __init__(self, children=None, **kwargs):
                    super().__init__(**kwargs)
                    self.children = children or []
        
        # 将模拟模块添加到sys.modules
        sys.modules['ipywidgets'] = MockIpywidgetsModule()
        
        logging.info("已应用ipywidgets模块模拟补丁")
    except Exception as e:
        logging.error(f"应用ipywidgets模块模拟补丁失败: {str(e)}")

def patch_scipy_module():
    """
    对scipy模块应用补丁，处理兼容性问题
    """
    try:
        # 检查scipy模块是否已经存在
        try:
            import scipy
            # 如果存在，检查是否需要补丁
            if hasattr(scipy, 'version'):
                logging.info(f"scipy模块已存在，版本: {scipy.version.version}，无需补丁")
            return
        except ImportError:
            pass
        
        # 创建一个模拟scipy模块
        class MockScipyModule:
            # 模拟版本信息
            class version:
                version = '1.11.0'  # 模拟一个版本号
            
            # 模拟optimize子模块
            class optimize:
                @staticmethod
                def minimize(fun, x0, **kwargs):
                    # 返回一个模拟的优化结果
                    class OptimizeResult:
                        def __init__(self):
                            self.x = x0
                            self.fun = fun(x0)
                            self.success = True
                            self.message = "Mock optimization"
                    return OptimizeResult()
            
            # 模拟stats子模块
            class stats:
                @staticmethod
                def norm(*args, **kwargs):
                    return None
        
        # 将模拟模块添加到sys.modules
        sys.modules['scipy'] = MockScipyModule()
        sys.modules['scipy.optimize'] = MockScipyModule.optimize
        sys.modules['scipy.stats'] = MockScipyModule.stats
        
        logging.info("已应用scipy模块模拟补丁")
    except Exception as e:
        logging.error(f"应用scipy模块模拟补丁失败: {str(e)}")

def _fixed_calc_atr_from_pd(tr, time_period):
    """
    一个修复版的ATR计算函数，直接使用现代pandas的API。
    这是对 abupy.IndicatorBu.ABuNDAtr._calc_atr_from_pd 的替代，
    解决了旧版ewma API在新版pandas中不存在的问题。
    """
    if not isinstance(tr, pd.Series):
        tr = pd.Series(tr)
    
    # 直接使用现代pandas的 .ewm().mean()
    try:
        # adjust=False与abupy旧版ewma的行为更接近
        atr = tr.ewm(span=time_period, min_periods=time_period, adjust=False).mean()
        return atr.to_numpy()
    except Exception as e:
        logging.error(f"在修复版的ATR计算中发生错误: {e}")
        return np.full(tr.shape, np.nan)

def patch_abupy_atr_calculation():
    """
    使用猴子补丁，在运行时将abupy的有问题的ATR计算函数，
    替换为我们自己编写的、兼容现代pandas的修复版函数。
    """
    try:
        from abupy.IndicatorBu import ABuNDAtr
        from abupy.CoreBu import ABuPdHelper
        
        # 执行"心脏搭桥手术" - 替换ATR计算函数
        ABuNDAtr._calc_atr_from_pd = _fixed_calc_atr_from_pd
        
        # 修复版的pd_ewm_mean函数，使用现代pandas API
        def _fixed_pd_ewm_mean(pd_object, **kwargs):
            """修复版的pd_ewm_mean函数，使用现代pandas API"""
            if not isinstance(pd_object, pd.Series):
                pd_object = pd.Series(pd_object)
            return pd_object.ewm(**kwargs).mean()
        
        # 在多个模块中替换pd_ewm_mean函数，因为它们在导入时就获得了函数引用
        ABuPdHelper.pd_ewm_mean = _fixed_pd_ewm_mean
        ABuNDAtr.pd_ewm_mean = _fixed_pd_ewm_mean
        
        # 同时修复其他可能使用pd_ewm_mean的模块
        try:
            from abupy.IndicatorBu import ABuNDMacd
            ABuNDMacd.pd_ewm_mean = _fixed_pd_ewm_mean
        except ImportError:
            pass
            
        try:
            from abupy.IndicatorBu import ABuNDMa
            ABuNDMa.pd_ewm_mean = _fixed_pd_ewm_mean
        except ImportError:
            pass
        
        logging.info("【兼容性补丁】: abupy ATR计算函数和pd_ewm_mean函数已成功应用补丁！")
    except ImportError:
        logging.warning("【兼容性补丁】: 未找到abupy.IndicatorBu.ABuNDAtr，跳过ATR计算补丁。")
    except Exception as e:
        logging.error(f"【兼容性补丁】: 应用abupy ATR计算补丁失败: {e}")

def patch_abupy_today_date_fix():
    """修复ABuPickTimeWorker._task_loop中的today['date']日期处理问题"""
    import pandas as pd
    import logging
    try:
        from abupy.AlphaBu import ABuPickTimeWorker as PTW
    except Exception as e:
        logging.warning(f"patch_abupy_today_date_fix import failed: {e}")
        return

    if getattr(PTW, '_today_date_fix_patched', False):
        return

    _orig_task_loop = PTW.AbuPickTimeWorker._task_loop

    def _patched_task_loop(self, today):
        # 无条件把 today['date'] 设为索引对应的真实 Timestamp
        try:
            if hasattr(today, 'name'):
                today['date'] = pd.Timestamp(today.name)
        except Exception:
            pass
        return _orig_task_loop(self, today)

    PTW.AbuPickTimeWorker._task_loop = _patched_task_loop
    PTW._today_date_fix_patched = True
    
    logging.info("【兼容性补丁】: ABuPickTimeWorker._task_loop today['date']修复补丁已成功应用！")

# --------------------------------------------------------------------

def patch_capture_buy_orders():
    """
    1) 包裹买因子 read_fit_day：日志 + 捕获 ret 到 capital._forced_orders
    2) 尝试调用 worker/capital 的下单方法进行记账（多名称轮询）
    3) 若已成功记账，则返回 None 避免原流程重复；若失败则原样返回 ret
    4) 同时保证 buy_tomorrow 被重定向到 buy_today（当日成交）
    """
    import logging, types
    try:
        from abupy.AlphaBu import ABuPickTimeWorker as PTW
        from abupy.FactorBuyBu.ABuFactorBuyBase import AbuFactorBuyBase
    except Exception as e:
        logging.warning(f"patch_capture_buy_orders import failed: {e}")
        return

    if getattr(PTW, "_read_with_capture_patched", False):
        return

    _orig_day_task = PTW.AbuPickTimeWorker._day_task

    def _patched_day_task(self, today):
        cap = getattr(self, "capital", None)
        if cap is not None and not hasattr(cap, "_forced_orders"):
            setattr(cap, "_forced_orders", [])

        # 包裹每个买因子
        try:
            for f in getattr(self, "buy_factors", []):
                if not isinstance(f, AbuFactorBuyBase):
                    continue
                # 注入 worker 引用
                try:
                    setattr(f, "_ptw", self)
                except Exception:
                    pass

                # 强制 buy_tomorrow 重定向为 buy_today（方法重定向，非布尔）
                bt = getattr(f, "buy_tomorrow", None)
                if (not callable(bt)) or (not getattr(f, "_buy_tomorrow_wrapped_to_today", False)):
                    def _redirect(self2, *a, **k):
                        return self2.buy_today()
                    f.buy_tomorrow = types.MethodType(_redirect, f)
                    f._buy_tomorrow_wrapped_to_today = True

                # 包裹 read_fit_day
                if getattr(f, "_read_wrapped_capture", False):
                    continue

                _orig_read = f.read_fit_day

                def _read_with_capture(self_f, tdy):
                    ret = _orig_read(tdy)
                    # 打日志
                    try:
                        dt = getattr(tdy, "name", None)
                        if hasattr(tdy, "get"):
                            dt = tdy.get("date", dt)
                        info = f"type={type(ret)}"
                        if ret is not None:
                            attrs = []
                            for k in ("price", "buy_cnt", "buy_price", "symbol"):
                                if hasattr(ret, k):
                                    attrs.append(f"{k}={getattr(ret, k)}")
                            info += " " + " ".join(attrs)
                        logging.info(f"[BUY_RET] {self_f.__class__.__name__} on {dt}: {info}")
                    except Exception:
                        pass

                    # 捕获并尽力记账
                    try:
                        if ret is not None:
                            worker = getattr(self_f, "_ptw", None) or self
                            cap0 = getattr(worker, "capital", None)

                            # 记录精简订单到 capital._forced_orders
                            symbol = getattr(worker, "symbol", None) or getattr(self_f, "symbol", None)
                            price = getattr(ret, "buy_price", None) or getattr(ret, "price", None)
                            qty = getattr(ret, "buy_cnt", None) or getattr(ret, "cnt", None) or getattr(ret, "quantity", None)
                            rec = {"dt": dt, "symbol": symbol, "price": price, "qty": qty, "side": "BUY"}
                            if cap0 is not None and hasattr(cap0, "_forced_orders"):
                                cap0._forced_orders.append(rec)
                                logging.info(f"[FORCED_CAPTURE] {rec}")

                            # 多方法名轮询尝试记账
                            committed = False
                            for meth in ("make_buy_order", "_make_buy_order", "buy_from_factor", "order", "market_order"):
                                fn = getattr(worker, meth, None)
                                if callable(fn):
                                    try:
                                        fn(ret)
                                        logging.info(f"[FORCED_COMMIT] via worker.{meth} ok")
                                        committed = True
                                        break
                                    except Exception as e:
                                        logging.warning(f"[FORCED_COMMIT] worker.{meth} failed: {e}")

                            if not committed and cap0 is not None:
                                for meth in ("order", "market_order", "buy_from_factor"):
                                    fn = getattr(cap0, meth, None)
                                    if callable(fn):
                                        try:
                                            # 尝试常见签名
                                            if meth == "order":
                                                fn(symbol, qty or 0, price or 0.0, "buy", dt=dt)
                                            else:
                                                fn(symbol=symbol, qty=qty or 0, price=price or 0.0, side="BUY", dt=dt)
                                            logging.info(f"[FORCED_COMMIT] via capital.{meth} ok")
                                            committed = True
                                            break
                                        except Exception as e:
                                            logging.warning(f"[FORCED_COMMIT] capital.{meth} failed: {e}")

                            # 避免重复：如果已经成功记账，返回 None，让上游不再处理
                            if committed:
                                return None
                    except Exception as e2:
                        logging.warning(f"[FORCED_CAPTURE] failed: {e2}")

                    return ret

                f.read_fit_day = types.MethodType(_read_with_capture, f)
                f._read_wrapped_capture = True

        except Exception as e:
            logging.warning(f"wrap read_fit_day failed: {e}")

        # 一次性打印 worker/capital 上可疑方法名，便于我们精确对齐记账 API
        try:
            w_methods = [n for n in dir(self) if callable(getattr(self, n)) and ("order" in n or "buy" in n)]
            c_methods = []
            if cap is not None:
                c_methods = [n for n in dir(cap) if callable(getattr(cap, n)) and ("order" in n or "buy" in n)]
            logging.info(f"[WORKER_PROBE] worker methods: {w_methods}")
            logging.info(f"[WORKER_PROBE] capital methods: {c_methods}")
        except Exception:
            pass

        return _orig_day_task(self, today)

    PTW.AbuPickTimeWorker._day_task = _patched_day_task
    PTW._read_with_capture_patched = True
    logging.info("已开启 read_fit_day 捕获 + 尝试记账")

def patch_do_symbols_fallback_from_forced_orders():
    """
    对 abupy.AlphaBu.ABuPickTimeExecute.do_symbols_with_same_factors 做最外层兜底：
    如返回的 orders_pd/action_pd 为 None，则从 ABuEnv.g_capital._forced_orders 构造最小 DataFrame 返回。
    """
    import logging
    try:
        try:
            import abupy.AlphaBu.ABuPickTimeExecute as Exe
        except Exception:
            from abupy.AlphaBu import ABuPickTimeExecute as Exe
        from abupy.CoreBu import ABuEnv
    except Exception as e:
        logging.warning(f"patch_do_symbols_fallback_from_forced_orders import failed: {e}")
        return

    if getattr(Exe, "_fallback_from_forced_orders_patched", False):
        return

    _orig = Exe.do_symbols_with_same_factors

    def _wrapped(*args, **kwargs):
        res = _orig(*args, **kwargs)
        try:
            orders_pd, action_pd, all_fit_symbols_cnt = res
        except Exception:
            return res

        cap = getattr(ABuEnv, "g_capital", None)
        need_fallback = (orders_pd is None) or (getattr(orders_pd, "empty", False) is True)
        if need_fallback and cap is not None and hasattr(cap, "_forced_orders") and cap._forced_orders:
            try:
                import pandas as pd
                rows = []
                for r in cap._forced_orders:
                    dt = r.get("dt")
                    try:
                        dt = pd.to_datetime(dt)
                    except Exception:
                        pass
                    rows.append({
                        "symbol": r.get("symbol"),
                        "buy_date": dt,
                        "buy_price": r.get("price"),
                        "buy_cnt": r.get("qty"),
                        "side": r.get("side", "BUY")
                    })
                orders_df = pd.DataFrame(rows)
                actions_df = pd.DataFrame([{
                    "dt": x["buy_date"], "symbol": x["symbol"], "price": x["buy_price"], "action": "buy"
                } for x in rows])

                # 赋回 capital，兼容可能的后续读取
                try:
                    cap.orders_pd = orders_df
                    cap.action_pd = actions_df
                except Exception:
                    pass

                logging.info(f"[FALLBACK] 构造 orders_pd/action_pd from cap._forced_orders, n={len(rows)}")
                return orders_df, actions_df, all_fit_symbols_cnt
            except Exception as e:
                logging.warning(f"[FALLBACK] 构造失败: {e}")

        return res

    Exe.do_symbols_with_same_factors = _wrapped
    Exe._fallback_from_forced_orders_patched = True
    logging.info("已开启 do_symbols_with_same_factors 返回兜底（from _forced_orders）")
