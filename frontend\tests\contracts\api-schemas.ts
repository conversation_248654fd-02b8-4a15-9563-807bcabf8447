import { z } from 'zod';

/**
 * API契约定义文件
 * 基于后端API探索报告定义的真实响应格式
 * 确保前端测试与后端API的一致性
 */

// ===== 基础数据类型 =====

/**
 * 股票代码格式验证
 * 支持多种格式：sh000001, sz000001, 000001.SZ等
 */
export const StockSymbolSchema = z.string().regex(
  /^(sh|sz)?\d{6}(\.S[HZ])?$/i,
  '股票代码格式无效'
);

/**
 * 日期格式验证
 * 支持YYYYMMDD和YYYY-MM-DD格式
 */
export const DateSchema = z.string().regex(
  /^\d{4}(\d{2}\d{2}|-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))$/,
  '日期格式无效，支持YYYYMMDD或YYYY-MM-DD'
);

/**
 * 资金金额验证（正数）
 */
export const CapitalSchema = z.number().positive('资金金额必须为正数');

// ===== 因子相关Schema =====

/**
 * 买入因子Schema
 */
export const BuyFactorSchema = z.object({
  name: z.string().min(1, '因子名称不能为空'),
  class_name: z.string().min(1, '因子类名不能为空'),
  parameters: z.record(z.string(), z.any()),
  factor_type: z.literal('buy').default('buy')
});

/**
 * 卖出因子Schema
 */
export const SellFactorSchema = z.object({
  name: z.string().min(1, '因子名称不能为空'),
  class_name: z.string().min(1, '因子类名不能为空'),
  parameters: z.record(z.string(), z.any()),
  factor_type: z.literal('sell').default('sell')
});

/**
 * 仓位管理策略Schema
 */
export const PositionStrategySchema = z.object({
  name: z.string(),
  class_name: z.string(),
  parameters: z.record(z.string(), z.any())
}).optional();

// ===== 策略相关Schema =====

/**
 * 策略创建请求Schema
 */
export const StrategyCreateRequestSchema = z.object({
  name: z.string().min(1, '策略名称不能为空'),
  description: z.string().optional(),
  buy_factors: z.array(BuyFactorSchema).min(1, '至少需要一个买入因子'),
  sell_factors: z.array(SellFactorSchema).min(1, '至少需要一个卖出因子'),
  position_strategy: PositionStrategySchema,
  parameters: z.record(z.string(), z.any()).default({}),
  umpire_rules: z.array(z.record(z.string(), z.any())).optional()
});

/**
 * 策略响应Schema
 */
export const StrategySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  buy_factors: z.array(BuyFactorSchema),
  sell_factors: z.array(SellFactorSchema),
  position_strategy: PositionStrategySchema,
  parameters: z.record(z.string(), z.any()),
  umpire_rules: z.array(z.record(z.string(), z.any())).nullable(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  owner: z.string().optional(),
  is_public: z.boolean().optional()
});

/**
 * 策略列表响应Schema
 */
export const StrategyListResponseSchema = z.object({
  items: z.array(StrategySchema),
  total: z.number(),
  page: z.number(),
  size: z.number(),
  pages: z.number()
});

// ===== 回测执行相关Schema =====

/**
 * 回测执行请求Schema
 */
export const StrategyExecuteRequestSchema = z.object({
  choice_symbols: z.array(StockSymbolSchema).min(1, '至少需要选择一个股票'),
  start_date: DateSchema,
  end_date: DateSchema,
  capital: CapitalSchema.optional(),
  benchmark_symbol: StockSymbolSchema.optional(),
  data_source: z.enum(['tushare', 'local']).default('local'),
  n_folds: z.number().int().positive().default(1)
}).refine(
  (data) => {
    // 验证结束日期晚于开始日期
    const start = data.start_date.replace(/-/g, '');
    const end = data.end_date.replace(/-/g, '');
    return parseInt(end) > parseInt(start);
  },
  {
    message: '结束日期必须晚于开始日期',
    path: ['end_date']
  }
);

/**
 * 交易订单Schema
 */
export const OrderSchema = z.object({
  buy_date: z.number().int(),
  sell_date: z.number().int(),
  buy_price: z.number().positive(),
  sell_price: z.number().positive(),
  profit: z.number(),
  symbol: StockSymbolSchema
});

/**
 * 单个股票回测结果Schema
 */
export const SymbolResultSchema = z.object({
  symbol: StockSymbolSchema,
  orders_count: z.number().int().nonnegative(),
  message: z.string(),
  orders: z.array(OrderSchema)
});

/**
 * 回测执行摘要Schema
 */
export const ExecutionSummarySchema = z.object({
  total_symbols: z.number().int().nonnegative(),
  symbols_with_trades: z.number().int().nonnegative(),
  total_trades: z.number().int().nonnegative(),
  initial_capital: z.number().positive(),
  final_capital: z.number().positive(),
  cumulative_return: z.number(),
  annualized_return: z.number(),
  max_drawdown: z.number(),
  sharpe_ratio: z.number(),
  win_rate: z.number().min(0).max(1),
  profit_loss_ratio: z.number().nonnegative()
});

/**
 * 回测执行响应Schema
 */
export const StrategyExecuteResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    status: z.string(),
    message: z.string(),
    results: z.array(SymbolResultSchema),
    execution_summary: ExecutionSummarySchema,
    parameters_used: z.object({
      initial_capital: z.number().positive(),
      symbols: z.array(StockSymbolSchema),
      start_date: z.string(),
      end_date: z.string()
    })
  })
});

// ===== 性能指标Schema =====

/**
 * 性能指标Schema
 */
export const PerformanceMetricsSchema = z.object({
  cumulative_return: z.number(),
  annualized_return: z.number(),
  max_drawdown: z.number(),
  sharpe_ratio: z.number(),
  win_rate: z.number().min(0).max(1),
  profit_loss_ratio: z.number().nonnegative(),
  alpha: z.number(),
  beta: z.number(),
  total_trades: z.number().int().nonnegative(),
  annualized_volatility: z.number().nonnegative(),
  benchmark_return: z.number(),
  information_ratio: z.number()
});

/**
 * 性能指标计算请求Schema
 */
export const MetricsCalculateRequestSchema = z.object({
  returns: z.array(z.number()),
  benchmark_returns: z.array(z.number()).optional(),
  risk_free_rate: z.number().default(0.03)
});

// ===== 网格搜索相关Schema =====

/**
 * 网格搜索请求Schema
 */
export const GridSearchRequestSchema = z.object({
  choice_symbols: z.array(StockSymbolSchema).min(1),
  buy_factors: z.array(BuyFactorSchema).min(1),
  sell_factors: z.array(SellFactorSchema).min(1),
  read_cash: CapitalSchema
});

/**
 * 网格搜索响应Schema
 */
export const GridSearchResponseSchema = z.object({
  task_id: z.string().uuid('任务ID格式无效')
});

/**
 * 网格搜索状态响应Schema
 */
export const GridSearchStatusResponseSchema = z.object({
  task_id: z.string().uuid(),
  status: z.enum(['PENDING', 'STARTED', 'SUCCESS', 'FAILURE', 'RETRY', 'REVOKED']),
  result: z.any().optional(),
  error: z.string().optional(),
  progress: z.number().min(0).max(100).optional()
});

// ===== 市场数据相关Schema =====

/**
 * K线数据请求Schema
 */
export const KlineRequestSchema = z.object({
  symbol: StockSymbolSchema,
  market: z.enum(['sh', 'sz', 'bj']),
  start_date: DateSchema.optional(),
  end_date: DateSchema.optional(),
  limit: z.number().int().positive().max(10000).optional(),
  ktype: z.enum(['D', 'W', 'M', '5', '15', '30', '60']).default('D')
});

/**
 * K线数据点Schema
 */
export const KlineDataPointSchema = z.object({
  date: z.string(),
  open: z.number().positive(),
  high: z.number().positive(),
  low: z.number().positive(),
  close: z.number().positive(),
  volume: z.number().nonnegative(),
  amount: z.number().nonnegative().optional()
});

/**
 * K线数据响应Schema
 */
export const KlineResponseSchema = z.object({
  symbol: StockSymbolSchema,
  data: z.array(KlineDataPointSchema),
  total: z.number().int().nonnegative()
});

// ===== 错误响应Schema =====

/**
 * API错误响应Schema
 */
export const ErrorResponseSchema = z.object({
  success: z.literal(false),
  message: z.string().min(1, '错误消息不能为空'),
  error_code: z.string().optional(),
  details: z.any().optional(),
  timestamp: z.string().optional()
});

/**
 * 验证错误详情Schema
 */
export const ValidationErrorSchema = z.object({
  field: z.string(),
  message: z.string(),
  value: z.any().optional()
});

/**
 * 验证错误响应Schema
 */
export const ValidationErrorResponseSchema = z.object({
  success: z.literal(false),
  message: z.string(),
  error_code: z.literal('VALIDATION_ERROR'),
  details: z.array(ValidationErrorSchema)
});

// ===== 通用响应Schema =====

/**
 * 成功响应Schema
 */
export const SuccessResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.literal(true),
    message: z.string(),
    data: dataSchema
  });

/**
 * 分页响应Schema
 */
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    items: z.array(itemSchema),
    total: z.number().int().nonnegative(),
    page: z.number().int().positive(),
    size: z.number().int().positive(),
    pages: z.number().int().nonnegative()
  });

// ===== 契约验证工具类 =====

/**
 * 契约不匹配记录
 */
interface ContractMismatch {
  api: string;
  timestamp: string;
  expected: any;
  actual: any;
  error: string;
}

/**
 * 契约验证工具类
 */
export class ContractValidator {
  private static mismatches: ContractMismatch[] = [];
  private static validationErrors: string[] = [];
  private static contractMismatches: Array<{
    api: string;
    expected: any;
    actual: any;
    timestamp: string;
  }> = [];

  /**
   * 验证请求数据
   */
  static validateRequest(data: unknown, schema: z.ZodSchema, apiName: string): boolean {
    try {
      schema.parse(data);
      // 验证成功时清空错误
      this.validationErrors = [];
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        this.validationErrors = error.issues?.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ) || ['Unknown validation error'];
        const errorMessage = error.issues?.map(err => err.message).join('; ') || 'Unknown validation error';
        this.recordMismatch(apiName, 'request', schema._def, data, errorMessage);
      } else {
        this.validationErrors = ['Unknown validation error'];
        this.recordMismatch(apiName, 'request', schema._def, data, 'Unknown validation error');
      }
      return false;
    }
  }

  /**
   * 验证响应数据
   */
  static validateResponse(data: unknown, schema: z.ZodSchema, apiName: string): boolean {
    try {
      schema.parse(data);
      // 验证成功时清空错误
      this.validationErrors = [];
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        this.validationErrors = error.issues?.map(err => {
          const path = err.path.length > 0 ? `${err.path.join('.')}: ` : '';
          return `${path}${err.message}`;
        }) || ['Unknown validation error'];
        const errorMessage = error.issues?.map(err => err.message).join('; ') || 'Unknown validation error';
        this.recordMismatch(apiName, 'response', schema._def, data, errorMessage);
      } else {
        this.validationErrors = ['Unknown validation error'];
        this.recordMismatch(apiName, 'response', schema._def, data, 'Unknown validation error');
      }
      return false;
    }
  }

  /**
   * 记录契约不匹配
   */
  static recordMismatch(api: string, type: 'request' | 'response', expected: any, actual: any, error: string): void {
    const mismatch: ContractMismatch = {
      api: `${api} (${type})`,
      timestamp: new Date().toISOString(),
      expected,
      actual,
      error
    };
    
    this.mismatches.push(mismatch);
    
    // 同时记录到新的格式中
    this.contractMismatches.push({
      api: `${api} (${type})`,
      expected,
      actual,
      timestamp: new Date().toISOString()
    });
    
    // 在开发环境下输出警告
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
      console.warn('🚨 API契约不匹配:', mismatch);
    }
  }

  /**
   * 获取所有契约不匹配记录
   */
  static getMismatches(): ContractMismatch[] {
    return [...this.mismatches];
  }

  /**
   * 获取验证错误
   */
  static getValidationErrors(): string[] {
    return [...this.validationErrors];
  }

  /**
   * 获取契约不匹配记录
   */
  static getContractMismatches() {
    return [...this.contractMismatches];
  }

  /**
   * 清空契约不匹配记录
   */
  static clearMismatches(): void {
    this.mismatches = [];
  }

  /**
   * 清除验证记录
   */
  static clearValidationErrors(): void {
    this.validationErrors = [];
    this.contractMismatches = [];
  }

  /**
   * 生成契约不匹配报告
   */
  static generateMismatchReport(): string {
    if (this.mismatches.length === 0) {
      return '✅ 没有发现API契约不匹配问题';
    }

    let report = `🚨 发现 ${this.mismatches.length} 个API契约不匹配问题:\n\n`;
    
    this.mismatches.forEach((mismatch, index) => {
      report += `${index + 1}. ${mismatch.api}\n`;
      report += `   时间: ${mismatch.timestamp}\n`;
      report += `   错误: ${mismatch.error}\n`;
      report += `   实际数据: ${JSON.stringify(mismatch.actual, null, 2)}\n\n`;
    });

    return report;
  }

  /**
   * 生成验证报告
   */
  static generateValidationReport(): {
    totalErrors: number;
    totalMismatches: number;
    errors: string[];
    mismatches: Array<{
      api: string;
      expected: any;
      actual: any;
      timestamp: string;
    }>;
  } {
    return {
      totalErrors: this.validationErrors.length,
      totalMismatches: this.contractMismatches.length,
      errors: this.getValidationErrors(),
      mismatches: this.getContractMismatches()
    };
  }

  /**
   * 验证数据类型安全
   */
  static validateTypeSafety<T>(data: unknown, schema: z.ZodSchema<T>): data is T {
    try {
      schema.parse(data);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 安全解析数据（不抛出异常）
   */
  static safeParse<T>(data: unknown, schema: z.ZodSchema<T>): {
    success: boolean;
    data?: T;
    error?: z.ZodError;
    errors?: string[];
  } {
    const result = schema.safeParse(data);
    if (result.success) {
      return {
        success: result.success,
        data: result.data
      };
    } else {
      // 确保能正确获取错误信息
      let errors: string[] = [];
      if (result.error && result.error.issues && Array.isArray(result.error.issues)) {
        errors = result.error.issues.map(err => {
          const path = err.path && err.path.length > 0 ? `${err.path.join('.')}: ` : '';
          return `${path}${err.message || 'Validation error'}`;
        });
      }
      
      if (errors.length === 0) {
        errors = ['Unknown validation error'];
      }
      
      return {
        success: result.success,
        error: result.error,
        errors
      };
    }
  }

  /**
   * 批量验证
   */
  static batchValidate(items: Array<{
    data: unknown;
    schema: z.ZodSchema;
    name: string;
  }>): {
    totalItems: number;
    validItems: number;
    invalidItems: number;
    results: Array<{
      name: string;
      isValid: boolean;
      errors?: string[];
    }>;
  } {
    const results = items.map(item => {
      const parseResult = this.safeParse(item.data, item.schema);
      return {
        name: item.name,
        isValid: parseResult.success,
        errors: parseResult.errors
      };
    });

    const validItems = results.filter(r => r.isValid).length;
    
    return {
      totalItems: items.length,
      validItems,
      invalidItems: items.length - validItems,
      results
    };
  }

  /**
   * 验证并转换数据
   */
  static parseAndValidate<T>(data: unknown, schema: z.ZodSchema<T>, apiName: string): T {
    const result = this.safeParse(data, schema);
    
    if (!result.success) {
      this.recordMismatch(apiName, 'response', schema._def, data, result.error!.message);
      throw new Error(`API契约验证失败: ${apiName} - ${result.error!.message}`);
    }
    
    return result.data!;
  }
}

// ===== 类型导出 =====

// 推断的TypeScript类型
export type StockSymbol = z.infer<typeof StockSymbolSchema>;
export type DateString = z.infer<typeof DateSchema>;
export type BuyFactor = z.infer<typeof BuyFactorSchema>;
export type SellFactor = z.infer<typeof SellFactorSchema>;
export type Strategy = z.infer<typeof StrategySchema>;
export type StrategyCreateRequest = z.infer<typeof StrategyCreateRequestSchema>;
export type StrategyExecuteRequest = z.infer<typeof StrategyExecuteRequestSchema>;
export type StrategyExecuteResponse = z.infer<typeof StrategyExecuteResponseSchema>;
export type Order = z.infer<typeof OrderSchema>;
export type SymbolResult = z.infer<typeof SymbolResultSchema>;
export type ExecutionSummary = z.infer<typeof ExecutionSummarySchema>;
export type PerformanceMetrics = z.infer<typeof PerformanceMetricsSchema>;
export type GridSearchRequest = z.infer<typeof GridSearchRequestSchema>;
export type GridSearchResponse = z.infer<typeof GridSearchResponseSchema>;
export type GridSearchStatusResponse = z.infer<typeof GridSearchStatusResponseSchema>;
export type KlineRequest = z.infer<typeof KlineRequestSchema>;
export type KlineDataPoint = z.infer<typeof KlineDataPointSchema>;
export type KlineResponse = z.infer<typeof KlineResponseSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type ValidationErrorResponse = z.infer<typeof ValidationErrorResponseSchema>;

// 导出所有Schema供测试使用
export const ApiSchemas = {
  // 基础类型
  StockSymbol: StockSymbolSchema,
  Date: DateSchema,
  Capital: CapitalSchema,
  
  // 因子相关
  BuyFactor: BuyFactorSchema,
  SellFactor: SellFactorSchema,
  PositionStrategy: PositionStrategySchema,
  
  // 策略相关
  Strategy: StrategySchema,
  StrategyCreateRequest: StrategyCreateRequestSchema,
  StrategyListResponse: StrategyListResponseSchema,
  
  // 回测执行
  StrategyExecuteRequest: StrategyExecuteRequestSchema,
  StrategyExecuteResponse: StrategyExecuteResponseSchema,
  Order: OrderSchema,
  SymbolResult: SymbolResultSchema,
  ExecutionSummary: ExecutionSummarySchema,
  
  // 性能指标
  PerformanceMetrics: PerformanceMetricsSchema,
  MetricsCalculateRequest: MetricsCalculateRequestSchema,
  
  // 网格搜索
  GridSearchRequest: GridSearchRequestSchema,
  GridSearchResponse: GridSearchResponseSchema,
  GridSearchStatusResponse: GridSearchStatusResponseSchema,
  
  // 市场数据
  KlineRequest: KlineRequestSchema,
  KlineDataPoint: KlineDataPointSchema,
  KlineResponse: KlineResponseSchema,
  
  // 错误响应
  ErrorResponse: ErrorResponseSchema,
  ValidationErrorResponse: ValidationErrorResponseSchema,
  
  // 通用响应
  SuccessResponse: SuccessResponseSchema,
  PaginatedResponse: PaginatedResponseSchema
};