import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useStrategyStore } from '../../../src/stores/useStrategyStore';
import * as strategyApi from '@/api/strategy';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '@/api/strategy';

// Advanced测试文件 - useStrategyStore
// 包含完整业务流程、异常恢复测试和复杂场景

vi.mock('@/api/strategy');

// 复杂测试数据
const mockStrategy: Strategy = {
  id: 'strategy-1',
  name: '测试策略',
  description: '这是一个测试策略',
  buy_factors: ['AbuFactorBuyBreak'],
  sell_factors: ['AbuFactorSellBreak'],
  select_factors: ['AbuFactorSelectN'],
  capital: 100000,
  commission: 0.001,
  slippage: 0.001,
  benchmark: 'SPY',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockStrategies: Strategy[] = Array.from({ length: 100 }, (_, i) => ({
  ...mockStrategy,
  id: `strategy-${i + 1}`,
  name: `策略 ${i + 1}`,
  capital: 100000 + i * 10000
}));

describe('useStrategyStore - Advanced测试套件', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllTimers();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  describe('完整业务流程测试', () => {
    it('应该处理策略的完整CRUD生命周期 - Advanced', async () => {
      const store = useStrategyStore();
      
      // 1. 初始状态
      expect(store.strategies).toEqual([]);
      expect(store.loading).toBe(false);
      
      // 2. 获取策略列表
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      await store.fetchStrategies();
      expect(store.strategies).toHaveLength(100);
      
      // 3. 创建新策略
      const newStrategy: CreateStrategyRequest = {
        name: '新策略',
        description: '新创建的策略',
        buy_factors: ['AbuFactorBuyBreak'],
        sell_factors: ['AbuFactorSellBreak'],
        select_factors: ['AbuFactorSelectN'],
        capital: 200000,
        commission: 0.002,
        slippage: 0.002,
        benchmark: 'QQQ'
      };
      
      const createdStrategy = { ...mockStrategy, id: 'strategy-101', ...newStrategy };
      vi.mocked(strategyApi.createStrategy).mockResolvedValue(createdStrategy);
      await store.createStrategy(newStrategy);
      expect(store.strategies).toHaveLength(101);
      expect(store.strategies.find(s => s.id === 'strategy-101')).toEqual(createdStrategy);
      
      // 4. 更新策略
      const updateRequest: UpdateStrategyRequest = {
        name: '更新后的策略',
        capital: 300000
      };
      
      const updatedStrategy = { ...createdStrategy, ...updateRequest };
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue(updatedStrategy);
      await store.updateStrategy('strategy-101', updateRequest);
      
      const foundStrategy = store.strategies.find(s => s.id === 'strategy-101');
      expect(foundStrategy?.name).toBe('更新后的策略');
      expect(foundStrategy?.capital).toBe(300000);
      
      // 5. 删除策略
      vi.mocked(strategyApi.deleteStrategy).mockResolvedValue(undefined);
      await store.deleteStrategy('strategy-101');
      expect(store.strategies).toHaveLength(100);
      expect(store.strategies.find(s => s.id === 'strategy-101')).toBeUndefined();
    });

    it('应该处理策略批量操作 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      await store.fetchStrategies();
      
      // 批量删除
      const deleteIds = ['strategy-1', 'strategy-2', 'strategy-3'];
      vi.mocked(strategyApi.deleteStrategy).mockResolvedValue(undefined);
      
      await Promise.all(deleteIds.map(id => store.deleteStrategy(id)));
      expect(store.strategies).toHaveLength(97);
      
      deleteIds.forEach(id => {
        expect(store.strategies.find(s => s.id === id)).toBeUndefined();
      });
    });

    it('应该处理策略复制和模板功能 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue([mockStrategy]);
      await store.fetchStrategies();
      
      // 复制策略
      const copyRequest: CreateStrategyRequest = {
        name: `${mockStrategy.name} - 副本`,
        description: mockStrategy.description,
        buy_factors: [...mockStrategy.buy_factors],
        sell_factors: [...mockStrategy.sell_factors],
        select_factors: [...mockStrategy.select_factors],
        capital: mockStrategy.capital,
        commission: mockStrategy.commission,
        slippage: mockStrategy.slippage,
        benchmark: mockStrategy.benchmark
      };
      
      const copiedStrategy = { ...mockStrategy, id: 'strategy-copy', ...copyRequest };
      vi.mocked(strategyApi.createStrategy).mockResolvedValue(copiedStrategy);
      await store.createStrategy(copyRequest);
      
      expect(store.strategies).toHaveLength(2);
      expect(store.strategies.find(s => s.id === 'strategy-copy')?.name).toBe('测试策略 - 副本');
    });
  });

  describe('异常恢复测试', () => {
    it('应该从创建策略失败中恢复 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue([mockStrategy]);
      await store.fetchStrategies();
      
      const initialCount = store.strategies.length;
      
      // 创建失败
      const newStrategy: CreateStrategyRequest = {
        name: '失败的策略',
        description: '',
        buy_factors: [],
        sell_factors: [],
        select_factors: [],
        capital: 100000,
        commission: 0.001,
        slippage: 0.001,
        benchmark: 'SPY'
      };
      
      vi.mocked(strategyApi.createStrategy).mockRejectedValueOnce(new Error('创建失败'));
      
      try {
        await store.createStrategy(newStrategy);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
      
      // 策略列表应该保持不变
      expect(store.strategies).toHaveLength(initialCount);
      expect(store.loading).toBe(false);
      
      // 重试创建应该成功
      const successStrategy = { ...mockStrategy, id: 'strategy-retry', ...newStrategy };
      vi.mocked(strategyApi.createStrategy).mockResolvedValueOnce(successStrategy);
      await store.createStrategy(newStrategy);
      
      expect(store.strategies).toHaveLength(initialCount + 1);
    });

    it('应该处理更新策略时的冲突解决 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue([mockStrategy]);
      await store.fetchStrategies();
      
      // 模拟并发更新冲突
      const updateRequest1: UpdateStrategyRequest = { name: '更新1' };
      const updateRequest2: UpdateStrategyRequest = { name: '更新2' };
      
      vi.mocked(strategyApi.updateStrategy)
        .mockRejectedValueOnce(new Error('版本冲突'))
        .mockResolvedValueOnce({ ...mockStrategy, name: '更新2' });
      
      // 第一次更新失败
      try {
        await store.updateStrategy(mockStrategy.id, updateRequest1);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
      
      // 策略名称应该保持原样
      expect(store.strategies[0].name).toBe('测试策略');
      
      // 第二次更新成功
      await store.updateStrategy(mockStrategy.id, updateRequest2);
      expect(store.strategies[0].name).toBe('更新2');
    });

    it('应该处理网络中断和重连 - Advanced', async () => {
      const store = useStrategyStore();
      let networkFailureCount = 0;
      
      vi.mocked(strategyApi.getStrategies).mockImplementation(() => {
        networkFailureCount++;
        if (networkFailureCount <= 2) {
          return Promise.reject(new Error('网络错误'));
        }
        return Promise.resolve(mockStrategies);
      });
      
      // 模拟重试机制
      let success = false;
      for (let i = 0; i < 3 && !success; i++) {
        try {
          await store.fetchStrategies();
          success = true;
        } catch (error) {
          // 继续重试
        }
      }
      
      expect(success).toBe(true);
      expect(store.strategies).toHaveLength(100);
      expect(networkFailureCount).toBe(3);
    });
  });

  describe('性能和并发测试', () => {
    it('应该处理大量策略的性能表现 - Advanced', async () => {
      const store = useStrategyStore();
      const largeStrategies = Array.from({ length: 10000 }, (_, i) => ({
        ...mockStrategy,
        id: `strategy-${i + 1}`,
        name: `策略 ${i + 1}`
      }));
      
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(largeStrategies);
      
      const startTime = performance.now();
      await store.fetchStrategies();
      const endTime = performance.now();
      
      expect(store.strategies).toHaveLength(10000);
      expect(endTime - startTime).toBeLessThan(500); // 应在500ms内完成
    });

    it('应该处理并发的CRUD操作 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies.slice(0, 10));
      await store.fetchStrategies();
      
      // 并发创建、更新、删除操作
      const operations = [
        // 创建操作
        ...Array.from({ length: 5 }, (_, i) => {
          const newStrategy = {
            name: `并发策略 ${i + 1}`,
            description: '',
            buy_factors: ['AbuFactorBuyBreak'],
            sell_factors: ['AbuFactorSellBreak'],
            select_factors: ['AbuFactorSelectN'],
            capital: 100000,
            commission: 0.001,
            slippage: 0.001,
            benchmark: 'SPY'
          };
          const created = { ...mockStrategy, id: `concurrent-${i + 1}`, ...newStrategy };
          vi.mocked(strategyApi.createStrategy).mockResolvedValueOnce(created);
          return store.createStrategy(newStrategy);
        }),
        
        // 更新操作
        ...Array.from({ length: 3 }, (_, i) => {
          const updateRequest = { name: `更新策略 ${i + 1}` };
          const updated = { ...mockStrategies[i], ...updateRequest };
          vi.mocked(strategyApi.updateStrategy).mockResolvedValueOnce(updated);
          return store.updateStrategy(mockStrategies[i].id, updateRequest);
        }),
        
        // 删除操作
        ...Array.from({ length: 2 }, (_, i) => {
          vi.mocked(strategyApi.deleteStrategy).mockResolvedValueOnce(undefined);
          return store.deleteStrategy(mockStrategies[i + 7].id);
        })
      ];
      
      await Promise.allSettled(operations);
      
      // 验证最终状态
      expect(store.strategies.length).toBeGreaterThan(10); // 创建了5个，删除了2个，更新了3个
    });

    it('应该处理内存优化和垃圾回收 - Advanced', async () => {
      const store = useStrategyStore();
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 大量数据操作
      for (let i = 0; i < 100; i++) {
        const largeStrategies = Array.from({ length: 1000 }, (_, j) => ({
          ...mockStrategy,
          id: `strategy-${i}-${j}`,
          name: `策略 ${i}-${j}`,
          description: 'A'.repeat(1000) // 大量文本数据
        }));
        
        vi.mocked(strategyApi.getStrategies).mockResolvedValueOnce(largeStrategies);
        await store.fetchStrategies();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // 内存增长应该在合理范围内（小于100MB）
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
    });
  });

  describe('边界条件和数据完整性', () => {
    it('应该处理策略数据的完整性验证 - Advanced', async () => {
      const store = useStrategyStore();
      
      // 测试不完整的策略数据
      const incompleteStrategy = {
        id: 'incomplete-1',
        name: '不完整策略',
        // 缺少必要字段
      } as any;
      
      vi.mocked(strategyApi.getStrategies).mockResolvedValue([incompleteStrategy]);
      await store.fetchStrategies();
      
      expect(store.strategies).toHaveLength(1);
      expect(store.strategies[0]).toEqual(incompleteStrategy);
    });

    it('应该处理策略参数的边界值 - Advanced', async () => {
      const store = useStrategyStore();
      
      const extremeStrategy: CreateStrategyRequest = {
        name: 'A'.repeat(1000), // 极长名称
        description: 'B'.repeat(10000), // 极长描述
        buy_factors: Array.from({ length: 100 }, (_, i) => `Factor${i}`), // 大量因子
        sell_factors: Array.from({ length: 100 }, (_, i) => `SellFactor${i}`),
        select_factors: Array.from({ length: 100 }, (_, i) => `SelectFactor${i}`),
        capital: Number.MAX_SAFE_INTEGER,
        commission: 0.99, // 极高手续费
        slippage: 0.99, // 极高滑点
        benchmark: 'EXTREME_BENCHMARK'
      };
      
      const createdStrategy = { ...mockStrategy, id: 'extreme-1', ...extremeStrategy };
      vi.mocked(strategyApi.createStrategy).mockResolvedValue(createdStrategy);
      await store.createStrategy(extremeStrategy);
      
      expect(store.strategies).toHaveLength(1);
      expect(store.strategies[0].capital).toBe(Number.MAX_SAFE_INTEGER);
      expect(store.strategies[0].buy_factors).toHaveLength(100);
    });

    it('应该处理策略状态的一致性 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue([mockStrategy]);
      await store.fetchStrategies();
      
      // 模拟状态不一致的情况
      const inconsistentUpdate: UpdateStrategyRequest = {
        name: '不一致更新',
        capital: -100000 // 负数资本
      };
      
      const updatedStrategy = { ...mockStrategy, ...inconsistentUpdate };
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue(updatedStrategy);
      await store.updateStrategy(mockStrategy.id, inconsistentUpdate);
      
      // Store应该接受服务器返回的数据，即使看起来不合理
      expect(store.strategies[0].capital).toBe(-100000);
      expect(store.strategies[0].name).toBe('不一致更新');
    });
  });

  describe('状态管理和持久化', () => {
    it('应该支持策略状态的快照和恢复 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies.slice(0, 5));
      await store.fetchStrategies();
      
      // 创建状态快照
      const snapshot = JSON.parse(JSON.stringify(store.$state));
      
      // 修改状态
      vi.mocked(strategyApi.deleteStrategy).mockResolvedValue(undefined);
      await store.deleteStrategy(mockStrategies[0].id);
      expect(store.strategies).toHaveLength(4);
      
      // 恢复状态
      store.$patch(snapshot);
      expect(store.strategies).toHaveLength(5);
      expect(store.strategies[0].id).toBe(mockStrategies[0].id);
    });

    it('应该处理Store的序列化和反序列化 - Advanced', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies.slice(0, 3));
      await store.fetchStrategies();
      
      // 序列化
      const serialized = JSON.stringify(store.$state);
      expect(serialized).toContain('strategy-1');
      expect(serialized).toContain('strategy-2');
      expect(serialized).toContain('strategy-3');
      
      // 反序列化
      const deserialized = JSON.parse(serialized);
      expect(deserialized.strategies).toHaveLength(3);
      expect(deserialized.strategies[0].id).toBe('strategy-1');
    });

    it('应该处理多标签页的状态同步 - Advanced', async () => {
      // 模拟多个Store实例（多标签页场景）
      const store1 = useStrategyStore();
      const store2 = useStrategyStore();
      
      // 验证是单例
      expect(store1).toBe(store2);
      
      vi.mocked(strategyApi.getStrategies).mockResolvedValue([mockStrategy]);
      await store1.fetchStrategies();
      
      // 两个实例应该有相同的状态
      expect(store2.strategies).toHaveLength(1);
      expect(store2.strategies[0].id).toBe(mockStrategy.id);
    });
  });
});