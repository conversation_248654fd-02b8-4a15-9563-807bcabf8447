"""代码重构工具

提供自动化重构、代码优化和重构建议功能。
"""

import ast
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from pathlib import Path
from collections import defaultdict

@dataclass
class RefactoringOpportunity:
    """重构机会"""
    file_path: str
    line_number: int
    refactoring_type: str
    severity: str  # 'high', 'medium', 'low'
    description: str
    current_code: str
    suggested_code: str
    benefits: List[str]
    effort_level: str  # 'low', 'medium', 'high'

@dataclass
class CodeDuplication:
    """代码重复"""
    files: List[str]
    line_ranges: List[Tuple[int, int]]
    similarity_score: float
    duplicate_code: str
    suggested_extraction: str

@dataclass
class RefactoringReport:
    """重构报告"""
    total_opportunities: int
    high_priority_count: int
    medium_priority_count: int
    low_priority_count: int
    duplications_found: int
    estimated_effort_hours: float
    potential_benefits: List[str]

class RefactoringAnalyzer:
    """重构分析器"""
    
    def __init__(self, source_dir: str):
        self.source_dir = Path(source_dir)
        self.opportunities: List[RefactoringOpportunity] = []
        self.duplications: List[CodeDuplication] = []
        
        # 重构规则配置
        self.max_function_length = 50
        self.max_class_length = 500
        self.max_parameter_count = 5
        self.max_complexity = 10
        self.min_duplication_lines = 5
        
    def analyze_refactoring_opportunities(self) -> Dict[str, Any]:
        """分析重构机会"""
        try:
            python_files = list(self.source_dir.rglob("*.py"))
            
            # 限制分析的文件数量，避免超时
            if len(python_files) > 100:
                print(f"⚠️ Too many files ({len(python_files)}), analyzing first 100 files only")
                python_files = python_files[:100]
            
            for i, file_path in enumerate(python_files):
                if self._should_analyze_file(file_path):
                    try:
                        self._analyze_file(file_path)
                        if i % 20 == 0:  # 每20个文件打印一次进度
                            print(f"  Analyzed {i+1}/{len(python_files)} files...")
                    except Exception as e:
                        print(f"  ⚠️ Error analyzing {file_path.name}: {e}")
                        continue
            
            # 跳过代码重复检测，因为它可能很耗时
            # self._detect_code_duplication(python_files)
            
            return self._generate_refactoring_report()
        except Exception as e:
            print(f"⚠️ Refactoring analysis failed: {e}")
            return {
                'summary': {
                    'total_opportunities': 0,
                    'high_priority': 0,
                    'medium_priority': 0,
                    'low_priority': 0,
                    'duplications_found': 0,
                    'estimated_effort_hours': 0
                },
                'opportunities': [],
                'duplications': [],
                'recommendations': [],
                'potential_benefits': []
            }
    
    def _should_analyze_file(self, file_path: Path) -> bool:
        """判断是否应该分析该文件"""
        exclude_patterns = ['__pycache__', '.git', 'venv', '.venv']
        return not any(pattern in str(file_path) for pattern in exclude_patterns)
    
    def _analyze_file(self, file_path: Path):
        """分析单个文件的重构机会"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            tree = ast.parse(content)
            
            # 各种重构检查
            self._check_long_functions(file_path, tree)
            self._check_long_classes(file_path, tree)
            self._check_complex_functions(file_path, tree)
            self._check_parameter_lists(file_path, tree)
            self._check_nested_conditions(file_path, tree)
            self._check_magic_numbers(file_path, tree, lines)
            self._check_string_literals(file_path, tree, lines)
            self._check_exception_handling(file_path, tree)
            self._check_naming_improvements(file_path, tree)
            self._check_code_smells(file_path, tree, lines)
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
    
    def _check_long_functions(self, file_path: Path, tree: ast.AST):
        """检查过长的函数"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                func_length = self._get_function_length(node)
                if func_length > self.max_function_length:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="extract_method",
                        severity="medium",
                        description=f"Function '{node.name}' is too long ({func_length} lines)",
                        current_code=f"def {node.name}(...):",
                        suggested_code="Break into smaller, focused functions",
                        benefits=["Improved readability", "Better testability", "Easier maintenance"],
                        effort_level="medium"
                    ))
    
    def _check_long_classes(self, file_path: Path, tree: ast.AST):
        """检查过长的类"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_length = self._get_class_length(node)
                if class_length > self.max_class_length:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="extract_class",
                        severity="high",
                        description=f"Class '{node.name}' is too large ({class_length} lines)",
                        current_code=f"class {node.name}:",
                        suggested_code="Split into multiple classes with single responsibilities",
                        benefits=["Single Responsibility Principle", "Better modularity", "Easier testing"],
                        effort_level="high"
                    ))
    
    def _check_complex_functions(self, file_path: Path, tree: ast.AST):
        """检查复杂的函数"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_complexity(node)
                if complexity > self.max_complexity:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="reduce_complexity",
                        severity="high",
                        description=f"Function '{node.name}' has high complexity ({complexity})",
                        current_code=f"def {node.name}(...):",
                        suggested_code="Extract methods, use early returns, simplify conditions",
                        benefits=["Improved readability", "Easier debugging", "Better maintainability"],
                        effort_level="medium"
                    ))
    
    def _check_parameter_lists(self, file_path: Path, tree: ast.AST):
        """检查参数列表过长的函数"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                param_count = len(node.args.args)
                if param_count > self.max_parameter_count:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="parameter_object",
                        severity="medium",
                        description=f"Function '{node.name}' has too many parameters ({param_count})",
                        current_code=f"def {node.name}({', '.join([arg.arg for arg in node.args.args])}):",
                        suggested_code="Use parameter object or configuration class",
                        benefits=["Cleaner interface", "Easier to extend", "Better encapsulation"],
                        effort_level="low"
                    ))
    
    def _check_nested_conditions(self, file_path: Path, tree: ast.AST):
        """检查嵌套条件"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                max_nesting = self._get_max_nesting_level(node)
                if max_nesting > 3:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="reduce_nesting",
                        severity="medium",
                        description=f"Function '{node.name}' has deep nesting (level {max_nesting})",
                        current_code="Deeply nested if/for statements",
                        suggested_code="Use early returns, extract methods, or guard clauses",
                        benefits=["Improved readability", "Reduced cognitive load", "Easier testing"],
                        effort_level="low"
                    ))
    
    def _check_magic_numbers(self, file_path: Path, tree: ast.AST, lines: List[str]):
        """检查魔法数字"""
        magic_numbers = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Constant) and isinstance(node.value, (int, float)):
                # 排除常见的非魔法数字
                if node.value not in [0, 1, -1, 2, 10, 100, 1000]:
                    magic_numbers.add((node.lineno, node.value))
        
        for line_no, value in magic_numbers:
            self.opportunities.append(RefactoringOpportunity(
                file_path=str(file_path),
                line_number=line_no,
                refactoring_type="extract_constant",
                severity="low",
                description=f"Magic number {value} should be extracted to a named constant",
                current_code=f"... {value} ...",
                suggested_code=f"MEANINGFUL_NAME = {value}\n... MEANINGFUL_NAME ...",
                benefits=["Better readability", "Easier maintenance", "Self-documenting code"],
                effort_level="low"
            ))
    
    def _check_string_literals(self, file_path: Path, tree: ast.AST, lines: List[str]):
        """检查重复的字符串字面量"""
        string_literals = defaultdict(list)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Constant) and isinstance(node.value, str):
                if len(node.value) > 5:  # 只检查较长的字符串
                    string_literals[node.value].append(node.lineno)
        
        for string_value, line_numbers in string_literals.items():
            if len(line_numbers) > 2:  # 出现3次或以上
                self.opportunities.append(RefactoringOpportunity(
                    file_path=str(file_path),
                    line_number=line_numbers[0],
                    refactoring_type="extract_constant",
                    severity="low",
                    description=f"String literal '{string_value[:30]}...' appears {len(line_numbers)} times",
                    current_code=f'... "{string_value}" ...',
                    suggested_code=f'STRING_CONSTANT = "{string_value}"\n... STRING_CONSTANT ...',
                    benefits=["DRY principle", "Easier maintenance", "Consistent values"],
                    effort_level="low"
                ))
    
    def _check_exception_handling(self, file_path: Path, tree: ast.AST):
        """检查异常处理"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ExceptHandler):
                # 检查裸露的except
                if node.type is None:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="specific_exception",
                        severity="medium",
                        description="Bare except clause should specify exception type",
                        current_code="except:",
                        suggested_code="except SpecificException:",
                        benefits=["Better error handling", "Avoid catching unexpected errors", "Clearer intent"],
                        effort_level="low"
                    ))
                
                # 检查空的except块
                if len(node.body) == 1 and isinstance(node.body[0], ast.Pass):
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=node.lineno,
                        refactoring_type="handle_exception",
                        severity="medium",
                        description="Empty except block should handle the exception properly",
                        current_code="except Exception:\n    pass",
                        suggested_code="except Exception as e:\n    logger.error(f'Error: {e}')\n    # Handle appropriately",
                        benefits=["Better error visibility", "Proper error handling", "Easier debugging"],
                        effort_level="low"
                    ))
    
    def _check_naming_improvements(self, file_path: Path, tree: ast.AST):
        """检查命名改进机会"""
        # 检查单字母变量名（除了循环变量）
        for node in ast.walk(tree):
            if isinstance(node, ast.Name) and len(node.id) == 1:
                # 排除常见的单字母变量
                if node.id not in ['i', 'j', 'k', 'x', 'y', 'z', 'e']:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=getattr(node, 'lineno', 0),
                        refactoring_type="improve_naming",
                        severity="low",
                        description=f"Single letter variable '{node.id}' should have a descriptive name",
                        current_code=f"{node.id} = ...",
                        suggested_code="descriptive_name = ...",
                        benefits=["Better readability", "Self-documenting code", "Easier maintenance"],
                        effort_level="low"
                    ))
    
    def _check_code_smells(self, file_path: Path, tree: ast.AST, lines: List[str]):
        """检查代码异味"""
        # 检查长方法链
        for node in ast.walk(tree):
            if isinstance(node, ast.Attribute):
                chain_length = self._get_method_chain_length(node)
                if chain_length > 3:
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=getattr(node, 'lineno', 0),
                        refactoring_type="break_method_chain",
                        severity="low",
                        description=f"Long method chain ({chain_length} calls) should be broken down",
                        current_code="obj.method1().method2().method3().method4()",
                        suggested_code="intermediate = obj.method1().method2()\nresult = intermediate.method3().method4()",
                        benefits=["Better debugging", "Improved readability", "Easier testing"],
                        effort_level="low"
                    ))
        
        # 检查重复的代码块
        self._check_duplicate_code_blocks(file_path, lines)
    
    def _check_duplicate_code_blocks(self, file_path: Path, lines: List[str]):
        """检查重复的代码块"""
        # 简化版本：检查连续的相似行
        for i in range(len(lines) - self.min_duplication_lines):
            block = lines[i:i + self.min_duplication_lines]
            
            # 查找相似的块
            for j in range(i + self.min_duplication_lines, len(lines) - self.min_duplication_lines):
                other_block = lines[j:j + self.min_duplication_lines]
                
                similarity = self._calculate_similarity(block, other_block)
                if similarity > 0.8:  # 80%相似度
                    self.opportunities.append(RefactoringOpportunity(
                        file_path=str(file_path),
                        line_number=i + 1,
                        refactoring_type="extract_method",
                        severity="medium",
                        description=f"Duplicate code block found (lines {i+1}-{i+self.min_duplication_lines} and {j+1}-{j+self.min_duplication_lines})",
                        current_code="\n".join(block),
                        suggested_code="Extract to a separate method",
                        benefits=["DRY principle", "Easier maintenance", "Single source of truth"],
                        effort_level="medium"
                    ))
                    break  # 避免重复报告
    
    def _detect_code_duplication(self, files: List[Path]):
        """检测跨文件的代码重复"""
        # 简化版本的重复检测
        file_contents = {}
        
        for file_path in files:
            if self._should_analyze_file(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_contents[str(file_path)] = f.read().splitlines()
                except Exception:
                    continue
        
        # 比较文件间的相似代码块
        file_list = list(file_contents.keys())
        for i in range(len(file_list)):
            for j in range(i + 1, len(file_list)):
                file1, file2 = file_list[i], file_list[j]
                duplications = self._find_duplications_between_files(
                    file1, file_contents[file1],
                    file2, file_contents[file2]
                )
                self.duplications.extend(duplications)
    
    def _find_duplications_between_files(self, file1: str, lines1: List[str], 
                                        file2: str, lines2: List[str]) -> List[CodeDuplication]:
        """查找两个文件间的重复代码"""
        duplications = []
        
        for i in range(len(lines1) - self.min_duplication_lines):
            block1 = lines1[i:i + self.min_duplication_lines]
            
            for j in range(len(lines2) - self.min_duplication_lines):
                block2 = lines2[j:j + self.min_duplication_lines]
                
                similarity = self._calculate_similarity(block1, block2)
                if similarity > 0.9:  # 90%相似度
                    duplication = CodeDuplication(
                        files=[file1, file2],
                        line_ranges=[(i + 1, i + self.min_duplication_lines), 
                                   (j + 1, j + self.min_duplication_lines)],
                        similarity_score=similarity,
                        duplicate_code="\n".join(block1),
                        suggested_extraction="Extract to a shared utility function"
                    )
                    duplications.append(duplication)
        
        return duplications
    
    def _calculate_similarity(self, block1: List[str], block2: List[str]) -> float:
        """计算两个代码块的相似度"""
        if len(block1) != len(block2):
            return 0.0
        
        similar_lines = 0
        for line1, line2 in zip(block1, block2):
            # 简化比较：去除空白字符后比较
            if line1.strip() == line2.strip():
                similar_lines += 1
            elif self._lines_similar(line1.strip(), line2.strip()):
                similar_lines += 0.5
        
        return similar_lines / len(block1)
    
    def _lines_similar(self, line1: str, line2: str) -> bool:
        """判断两行代码是否相似"""
        # 简化版本：检查结构相似性
        if not line1 or not line2:
            return False
        
        # 去除变量名，只比较结构
        pattern1 = re.sub(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', 'VAR', line1)
        pattern2 = re.sub(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', 'VAR', line2)
        
        return pattern1 == pattern2
    
    def _get_function_length(self, func_node: ast.FunctionDef) -> int:
        """获取函数长度"""
        if hasattr(func_node, 'end_lineno'):
            return func_node.end_lineno - func_node.lineno
        return 0
    
    def _get_class_length(self, class_node: ast.ClassDef) -> int:
        """获取类长度"""
        if hasattr(class_node, 'end_lineno'):
            return class_node.end_lineno - class_node.lineno
        return 0
    
    def _calculate_complexity(self, func_node: ast.FunctionDef) -> int:
        """计算函数复杂度"""
        complexity = 1
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        return complexity
    
    def _get_max_nesting_level(self, func_node: ast.FunctionDef) -> int:
        """获取最大嵌套层级"""
        def get_nesting_level(node, current_level=0):
            max_level = current_level
            for child in ast.iter_child_nodes(node):
                if isinstance(child, (ast.If, ast.While, ast.For, ast.With)):
                    child_level = get_nesting_level(child, current_level + 1)
                    max_level = max(max_level, child_level)
                else:
                    child_level = get_nesting_level(child, current_level)
                    max_level = max(max_level, child_level)
            return max_level
        
        return get_nesting_level(func_node)
    
    def _get_method_chain_length(self, attr_node: ast.Attribute) -> int:
        """获取方法链长度"""
        length = 1
        current = attr_node.value
        
        while isinstance(current, ast.Attribute):
            length += 1
            current = current.value
        
        return length
    
    def _generate_refactoring_report(self) -> Dict[str, Any]:
        """生成重构报告"""
        # 统计优先级
        priority_counts = {'high': 0, 'medium': 0, 'low': 0}
        for opportunity in self.opportunities:
            priority_counts[opportunity.severity] += 1
        
        # 估算工作量
        effort_mapping = {'low': 1, 'medium': 4, 'high': 8}
        total_effort = sum(effort_mapping.get(opp.effort_level, 2) for opp in self.opportunities)
        
        # 收集潜在收益
        all_benefits = set()
        for opportunity in self.opportunities:
            all_benefits.update(opportunity.benefits)
        
        report = RefactoringReport(
            total_opportunities=len(self.opportunities),
            high_priority_count=priority_counts['high'],
            medium_priority_count=priority_counts['medium'],
            low_priority_count=priority_counts['low'],
            duplications_found=len(self.duplications),
            estimated_effort_hours=total_effort,
            potential_benefits=list(all_benefits)
        )
        
        return {
            'summary': {
                'total_opportunities': report.total_opportunities,
                'high_priority': report.high_priority_count,
                'medium_priority': report.medium_priority_count,
                'low_priority': report.low_priority_count,
                'duplications_found': report.duplications_found,
                'estimated_effort_hours': report.estimated_effort_hours
            },
            'opportunities': [self._opportunity_to_dict(opp) for opp in self.opportunities],
            'duplications': [self._duplication_to_dict(dup) for dup in self.duplications],
            'recommendations': self._generate_recommendations(),
            'potential_benefits': report.potential_benefits
        }
    
    def _opportunity_to_dict(self, opportunity: RefactoringOpportunity) -> Dict[str, Any]:
        """将重构机会转换为字典"""
        return {
            'file_path': opportunity.file_path,
            'line_number': opportunity.line_number,
            'type': opportunity.refactoring_type,
            'severity': opportunity.severity,
            'description': opportunity.description,
            'current_code': opportunity.current_code,
            'suggested_code': opportunity.suggested_code,
            'benefits': opportunity.benefits,
            'effort_level': opportunity.effort_level
        }
    
    def _duplication_to_dict(self, duplication: CodeDuplication) -> Dict[str, Any]:
        """将代码重复转换为字典"""
        return {
            'files': duplication.files,
            'line_ranges': duplication.line_ranges,
            'similarity_score': duplication.similarity_score,
            'duplicate_code': duplication.duplicate_code,
            'suggested_extraction': duplication.suggested_extraction
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成重构建议"""
        recommendations = []
        
        high_priority = sum(1 for opp in self.opportunities if opp.severity == 'high')
        if high_priority > 0:
            recommendations.append(f"Address {high_priority} high-priority refactoring opportunities first")
        
        complexity_issues = sum(1 for opp in self.opportunities if opp.refactoring_type == 'reduce_complexity')
        if complexity_issues > 0:
            recommendations.append("Focus on reducing function complexity to improve maintainability")
        
        duplication_count = len(self.duplications)
        if duplication_count > 0:
            recommendations.append(f"Extract {duplication_count} duplicate code blocks into reusable functions")
        
        long_functions = sum(1 for opp in self.opportunities if opp.refactoring_type == 'extract_method')
        if long_functions > 5:
            recommendations.append("Consider breaking down large functions into smaller, focused methods")
        
        magic_numbers = sum(1 for opp in self.opportunities if opp.refactoring_type == 'extract_constant')
        if magic_numbers > 10:
            recommendations.append("Extract magic numbers and repeated strings to named constants")
        
        return recommendations

# 便捷函数
def analyze_refactoring_opportunities(source_dir: str) -> Dict[str, Any]:
    """分析重构机会的便捷函数"""
    analyzer = RefactoringAnalyzer(source_dir)
    return analyzer.analyze_refactoring_opportunities()

def generate_refactoring_report(analysis_result: Dict[str, Any], output_file: str):
    """生成重构报告文件"""
    content = []
    content.append("# Code Refactoring Report\n\n")
    content.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    summary = analysis_result['summary']
    content.append("## Summary\n\n")
    content.append(f"- **Total Opportunities:** {summary['total_opportunities']}\n")
    content.append(f"- **High Priority:** {summary['high_priority']}\n")
    content.append(f"- **Medium Priority:** {summary['medium_priority']}\n")
    content.append(f"- **Low Priority:** {summary['low_priority']}\n")
    content.append(f"- **Code Duplications:** {summary['duplications_found']}\n")
    content.append(f"- **Estimated Effort:** {summary['estimated_effort_hours']} hours\n\n")
    
    # 高优先级机会
    high_priority_opps = [opp for opp in analysis_result['opportunities'] if opp['severity'] == 'high']
    if high_priority_opps:
        content.append("## High Priority Opportunities\n\n")
        for opp in high_priority_opps:
            content.append(f"### {opp['type'].replace('_', ' ').title()}\n")
            content.append(f"**File:** `{Path(opp['file_path']).name}` (line {opp['line_number']})\n\n")
            content.append(f"**Description:** {opp['description']}\n\n")
            content.append(f"**Current Code:**\n```python\n{opp['current_code']}\n```\n\n")
            content.append(f"**Suggested Improvement:** {opp['suggested_code']}\n\n")
            content.append(f"**Benefits:** {', '.join(opp['benefits'])}\n\n")
            content.append("---\n\n")
    
    # 代码重复
    if analysis_result['duplications']:
        content.append("## Code Duplications\n\n")
        for i, dup in enumerate(analysis_result['duplications'], 1):
            content.append(f"### Duplication {i}\n")
            content.append(f"**Files:** {', '.join([Path(f).name for f in dup['files']])}\n\n")
            content.append(f"**Similarity:** {dup['similarity_score']:.1%}\n\n")
            content.append(f"**Suggestion:** {dup['suggested_extraction']}\n\n")
            content.append("---\n\n")
    
    # 建议
    if analysis_result['recommendations']:
        content.append("## Recommendations\n\n")
        for rec in analysis_result['recommendations']:
            content.append(f"- {rec}\n")
        content.append("\n")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(''.join(content))