# MetricsBu-GridSearch技术实现指南

**日期**: 2024年7月25日

**模块**: `MetricsBu`

**勘探者**: 勘探者AI

## 1. 概述

本指南旨在详细阐述 `abupy` 库中 `MetricsBu` 模块的网格搜索功能，核心围绕 `ABuGridSearch` 类。它将帮助开发者理解如何配置和执行参数寻优，以找到最佳的策略组合。

## 2. 核心组件与流程

网格搜索的核心流程可以概括为以下几步：

1.  **定义参数网格 (Parameter Grid)**：为买入因子、卖出因子（以及可选的选股因子）定义一个参数空间。每个因子可以有多个参数，每个参数可以有多个待选值。
2.  **启动网格搜索**：调用核心函数 `GridSearch.grid_search()` 来启动整个流程。
3.  **参数组合生成**：内部使用 `ParameterGrid` 和 `itertools.product` 生成所有可能的因子参数组合。
4.  **并行回测**：`GridSearch.fit()` 方法会启动多个进程（默认为CPU核心数），将不同的参数组合分配给不同进程，并行执行回测 (`grid_search_mul_process`)。
5.  **结果评分**：每个回测完成后，会生成一个 `AbuScoreTuple` 对象，其中包含了订单、资金等结果。所有回测结束后，使用评分器（如 `WrsmScorer`）对所有 `AbuScoreTuple` 进行打分。
6.  **返回结果**：最终返回一个包含所有组合得分的 `pandas.Series` 和一个包含所有回测详细结果的 `list`。

## 3. 如何启动一次网格搜索？

### 3.1. 核心函数

启动网格搜索的主要入口是 `GridSearch.grid_search()` 这个类方法。

### 3.2. 输入参数详解

`GridSearch.grid_search()` 函数需要以下关键输入：

-   `choice_symbols` (list): 必需。一个包含股票代码的列表，作为回测的股票池。
-   `buy_factors` (dict or list): 必需。买入策略的参数网格。这是最核心的配置部分。
-   `sell_factors` (dict or list): 必需。卖出策略的参数网格。
-   `read_cash` (int): 可选。初始资金，默认为10,000,000。

### 3.3. 如何定义参数变化范围？

定义参数范围是网格搜索的关键。你需要为每个要优化的因子构造一个字典，其中：

-   `'class'` 键的值是策略的类名。
-   其他键是该策略类的参数名，其值必须是一个**列表（list）**，包含了所有要测试的参数值。

**示例**：

假设我们有一个突破买入策略 `AbuFactorBuyBreak`，它有一个参数 `xd`（代表突破周期）。我们想测试 `xd` 在 `[20, 30, 40, 50]` 这几个值上的表现。配置如下：

```python
buy_factors = {
    'class': AbuFactorBuyBreak,
    'xd': [20, 30, 40, 50]
}
```

如果想同时优化多个策略，或者一个策略的多个参数，可以构造一个列表，列表中的每个元素都是一个如上格式的字典。

`abupy` 会自动将这些配置转换成所有可能的参数组合。

## 4. 返回的数据结构

`GridSearch.grid_search()` 函数返回一个元组 `(scores, score_tuple_array)`：

-   `scores` (`pandas.Series`): 索引是参数组合的内部编号，值是该组合的回测评分。**分数越高，代表表现越好**。你可以通过 `scores.index[-1]` 找到最佳组合的索引。
-   `score_tuple_array` (list): 一个列表，每个元素都是一个 `AbuScoreTuple` 对象。`AbuScoreTuple` 是一个 `namedtuple`，包含了某一次回测的完整结果，包括：
    -   `orders_pd`: 交易订单详情 (DataFrame)
    -   `action_pd`: 交易行为详情 (DataFrame)
    -   `capital`: 资金对象
    -   `benchmark`: 基准对象
    -   `buy_factors`: 该次回测使用的买入因子
    -   `sell_factors`: 该次回测使用的卖出因子

你可以通过 `score_tuple_array[scores.index[-1]]` 获取最佳参数组合的回测详情。

## 5. 完整可运行示例

下面的代码演示了如何对一个简单的突破策略（买入：N日新高，卖出：M日新低）进行网格搜索。

```python
# 确保在 notebook 或可以显示日志的环境中运行
import logging
logging.basicConfig(level=logging.INFO)

from abupy import ABuEnv, GridSearch
from abupy.FactorBuyBu import AbuFactorBuyBreak
from abupy.FactorSellBu import AbuFactorSellBreak

# 1. 初始化运行环境
# 使用沙盒数据模式，这样无需下载数据
# 设置回测的股票池
ABuEnv.g_market_target = ABuEnv.EMarketTargetType.E_MARKET_TARGET_US
choice_symbols = ['usTSLA', 'usNOAH', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA']

# 2. 定义买入和卖出因子的参数网格
# 买入策略：测试突破周期 xd = 20, 30, 40, 50 天
buy_factors = [{
    'class': AbuFactorBuyBreak,
    'xd': [20, 30, 40, 50]
}]

# 卖出策略：测试突破周期 xd = 10, 15, 20 天
sell_factors = [{
    'class': AbuFactorSellBreak,
    'xd': [10, 15, 20]
}]

# 3. 启动网格搜索
# 初始资金设置为 1,000,000
print("开始进行网格搜索...")
scores, score_tuple_array = GridSearch.grid_search(
    choice_symbols=choice_symbols,
    buy_factors=buy_factors,
    sell_factors=sell_factors,
    read_cash=1000000
)

# 4. 分析结果
print("\n网格搜索完成！")

# 打印所有参数组合的得分
print("\n--- 所有组合得分 ---")
print(scores)

# 找到最佳参数组合
best_score_index = scores.index[-1]
best_score_tuple = score_tuple_array[best_score_index]

print("\n--- 最佳参数组合 ---")
print(f"最佳买入策略: {best_score_tuple.buy_factors}")
print(f"最佳卖出策略: {best_score_tuple.sell_factors}")

# 5. 显示最佳组合的详细回测度量
from abupy.MetricsBu import AbuMetricsBase

print("\n--- 最佳组合回测详情 ---")
AbuMetricsBase.show_general(
    best_score_tuple.orders_pd,
    best_score_tuple.action_pd,
    best_score_tuple.capital,
    best_score_tuple.benchmark,
    returns_cmp=True,
    only_info=True
)

```

这份指南和示例代码为在 `abu_modern` 项目中实现和使用网格搜索功能提供了坚实的基础。
