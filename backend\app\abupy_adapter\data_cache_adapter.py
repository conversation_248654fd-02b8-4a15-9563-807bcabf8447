# backend/app/abupy_adapter/data_cache_adapter.py

# -*- coding: utf-8 -*-
"""
为abu框架的ABuDataCache模块提供适配器，将其函数封装成类
"""
import logging
import threading
import os
import time
from importlib import import_module
from typing import Tuple, Optional, Any

# 新增导入 1：我们需要abupy的核心KLManager来创建实例
from abupy.TradeBu.ABuKLManager import AbuKLManager

# 导入自定义异常
# 新增导入 2：我们的新方法会抛出AdapterError，所以需要导入它
from ..core.exceptions import CacheError, AdapterError

# 尝试导入原始模块
try:
    # 动态导入原始模块，避免直接引用可能不存在的模块或属性
    original_module = import_module('abupy.MarketBu.ABuDataCache')
    
    # 将原始模块中的所有函数复制到当前模块
    for name in dir(original_module):
        if not name.startswith('_'):  # 只复制公共函数和变量
            globals()[name] = getattr(original_module, name)
            
    logging.info(f"成功导入原始ABuDataCache模块的函数: {[name for name in dir(original_module) if not name.startswith('_')]}")
except (ImportError, AttributeError) as e:
    logging.error(f"导入原始ABuDataCache模块失败: {str(e)}")

# 全局文件锁字典，保证对同一文件的操作是线程安全的
_file_locks = {}
_file_locks_lock = threading.RLock()

# 获取文件锁的函数
def get_file_lock(file_path: str) -> threading.RLock:
    """获取指定文件路径的锁，如果不存在则创建
    
    Args:
        file_path: 文件路径或唯一标识符
        
    Returns:
        threading.RLock: 与该文件路径关联的锁
    """
    # 使用绝对路径作为锁的键，确保不同线程得到相同的锁
    abs_path = os.path.abspath(file_path) if os.path.exists(file_path) else file_path
    
    with _file_locks_lock:
        if abs_path not in _file_locks:
            _file_locks[abs_path] = threading.RLock()
        return _file_locks[abs_path]

# --- 以下所有辅助函数均为您原始文件中的内容，完整保留 ---

def _ensure_directory_exists(file_path: str) -> bool:
    """确保文件所在目录存在"""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logging.error(f"创建目录 {directory} 失败: {str(e)}")
            return False
    return True

def _read_file_content(file_path: str) -> Optional[str]:
    """读取文件内容"""
    if not os.path.exists(file_path):
        return None
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logging.error(f"读取文件 {file_path} 失败: {str(e)}")
        raise

def _write_file_with_backup(file_path: str, content: str):
    """带备份地写入文件，并在失败时尝试恢复"""
    backup_path = f"{file_path}.bak"
    # 1. 创建备份
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as src, open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        except Exception as e:
            logging.warning(f"创建备份文件 {backup_path} 失败: {str(e)}")
            # 备份失败不应中断主流程，但需记录

    # 2. 写入新内容
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            f.flush()
            os.fsync(f.fileno())
    except Exception as e:
        logging.error(f"写入文件 {file_path} 失败: {str(e)}")
        # 3. 尝试从备份恢复
        if os.path.exists(backup_path):
            try:
                with open(backup_path, 'r', encoding='utf-8') as src, open(file_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
                logging.info(f"从备份 {backup_path} 恢复文件成功")
            except Exception as restore_err:
                logging.error(f"从备份 {backup_path} 恢复文件失败: {str(restore_err)}")
        raise  # 重新引发异常，以便重试逻辑捕获

def safe_read_modify_write_file(file_path: str, modify_func, max_retries: int = 3, retry_delay: float = 0.05) -> Tuple[bool, Any]:
    """线程安全地读取、修改并写回文件内容，支持重试机制（重构版）

    Args:
        file_path: 文件路径
        modify_func: 接受当前文件内容并返回新内容的函数
        max_retries: 最大重试次数
        retry_delay: 重试间隔时间(秒)

    Returns:
        Tuple[bool, Any]: (操作是否成功, 修改函数的返回值)
    """
    if not _ensure_directory_exists(file_path):
        return False, None

    lock = get_file_lock(file_path)
    last_error = None

    for attempt in range(max_retries + 1):
        with lock:
            try:
                # 1. 读取
                current_content = _read_file_content(file_path)

                # 2. 修改
                result = modify_func(current_content)
                if result is None:
                    logging.warning(f"修改函数返回了None，操作终止，保留原内容")
                    return True, current_content
                
                if isinstance(result, tuple) and len(result) > 0:
                    new_content = str(result[0])
                else:
                    new_content = str(result)

                # 3. 写入
                _write_file_with_backup(file_path, new_content)

                return True, result  # 成功完成，直接返回

            except Exception as e:
                last_error = e
                logging.warning(f"操作失败 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                if attempt < max_retries:
                    time.sleep(retry_delay)
    
    logging.error(f"所有重试均失败，最后错误: {str(last_error)}")
    return False, None

# 线程安全的文件读取
def safe_read_file(file_path: str) -> Optional[str]:
    """线程安全地读取文件内容
    
    Args:
        file_path: 文件路径
        
    Returns:
        Optional[str]: 文件内容，如果文件不存在或读取失败则返回None
    """
    if not os.path.exists(file_path):
        return None
        
    lock = get_file_lock(file_path)
    with lock:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logging.error(f"读取文件 {file_path} 失败: {str(e)}")
            return None

# 线程安全的文件写入
def safe_write_file(file_path: str, content: str) -> bool:
    """线程安全地写入文件内容
    
    Args:
        file_path: 文件路径
        content: 要写入的内容
        
    Returns:
        bool: 操作是否成功
    """
    # 确保目录存在
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            logging.error(f"创建目录 {directory} 失败: {str(e)}")
            return False
    
    lock = get_file_lock(file_path)
    with lock:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            logging.error(f"写入文件 {file_path} 失败: {str(e)}")
            return False

# 创建一个适配器类，提供与原始ABuDataCache模块相同的功能，但增加线程安全性
class AbuDataCache:
    """
    ABuDataCache模块适配器类，将原始模块的函数封装为类属性和方法，并增加线程安全性
    """
    # 类属性，用于配置
    disable_cache = False
    cache_expiry_days = 30
    
    # 类级别锁，用于保护类属性访问
    _class_lock = threading.RLock()
    
    # ==================== 新增方法：开始 ====================
    @classmethod
    def get_kl_pd_manager_for_training(cls, start: str, end: str, benchmark: str, choice_symbols: list) -> AbuKLManager:
        """
        为模型训练创建一个配置好的ABuKLManager实例。
        这是为了修复测试中 'get_kl_pd_manager_for_training' 的 AttributeError。

        Args:
            start (str): 回测开始日期，格式如 '2020-01-01'
            end (str): 回测结束日期，格式如 '2021-01-01'
            benchmark (str): 基准市场代码，如 'sh' 或 'us'
            choice_symbols (list): 选股列表，如 ['usAAPL', 'usGOOG']

        Returns:
            ABuKLManager: 一个配置好日期范围的K线数据管理器实例
        """
        try:
            logging.info(f"为训练创建KLManager: benchmark={benchmark}, symbols={choice_symbols}, start={start}, end={end}")
            # 1. 使用基准和选股列表初始化KLManager
            kl_manager = ABuKLManager(benchmark, choice_symbols)
            # 2. 设置回测的时间范围
            kl_manager.set_date_range(start, end)
            return kl_manager
        except Exception as e:
            logging.error(f"创建KLManager失败: {e}", exc_info=True)
            # 将底层错误包装成我们自己的AdapterError
            raise AdapterError(f"创建或配置K线数据管理器时出错: {e}")
    # ==================== 新增方法：结束 ====================

    @classmethod
    def load_kline_df(cls, symbol_key):
        """线程安全地调用原始模块的load_kline_df函数"""
        if 'load_kline_df' in globals():
            try:
                # 使用文件锁保护对同一symbol_key的并发访问
                lock = get_file_lock(f"cache_{symbol_key}")
                with lock:
                    return globals()['load_kline_df'](symbol_key)
            except Exception as e:
                logging.error(f"加载K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"加载K线数据缓存失败: {str(e)}",
                    data={"symbol_key": symbol_key}
                )
        logging.error("找不到load_kline_df函数")
        return None, None, None
    
    @classmethod
    def dump_kline_df(cls, dump_df, symbol_key, date_key):
        """线程安全地调用原始模块的dump_kline_df函数"""
        if 'dump_kline_df' in globals():
            try:
                # 使用文件锁保护对同一symbol_key的并发访问
                lock = get_file_lock(f"cache_{symbol_key}")
                with lock:
                    return globals()['dump_kline_df'](dump_df, symbol_key, date_key)
            except Exception as e:
                logging.error(f"保存K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"保存K线数据缓存失败: {str(e)}",
                    data={"symbol_key": symbol_key, "date_key": date_key}
                )
        logging.error("找不到dump_kline_df函数")
        return None
    
    @classmethod
    def save_kline_df(cls, df, temp_symbol, start_int, end_int):
        """线程安全地调用原始模块的save_kline_df函数"""
        if 'save_kline_df' in globals():
            try:
                # 使用文件锁保护对同一temp_symbol的并发访问
                lock = get_file_lock(f"cache_{temp_symbol}_{start_int}_{end_int}")
                with lock:
                    return globals()['save_kline_df'](df, temp_symbol, start_int, end_int)
            except Exception as e:
                logging.error(f"保存K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"保存K线数据失败: {str(e)}",
                    data={"symbol": temp_symbol, "start": start_int, "end": end_int}
                )
        logging.error("找不到save_kline_df函数")
        return None
    
    @classmethod
    def load_kline_df_net(cls, source, temp_symbol, n_folds, start, end, start_int, end_int, save):
        """线程安全地调用原始模块的load_kline_df_net函数"""
        if 'load_kline_df_net' in globals():
            try:
                # 使用文件锁保护对同一temp_symbol的并发访问
                lock = get_file_lock(f"cache_{temp_symbol}_{start_int}_{end_int}")
                with lock:
                    return globals()['load_kline_df_net'](source, temp_symbol, n_folds, start, end, start_int, end_int, save)
            except Exception as e:
                logging.error(f"从网络加载K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"从网络加载K线数据失败: {str(e)}",
                    data={"symbol": temp_symbol, "start": start, "end": end}
                )
        logging.error("找不到load_kline_df_net函数")
        return None
        
    @classmethod
    def clear_cache(cls, symbol_key=None):
        """清除缓存数据"""
        try:
            # 如果指定了symbol_key，只清除特定股票的缓存
            if symbol_key:
                lock = get_file_lock(f"cache_{symbol_key}")
                with lock:
                    # 这里应该实现清除特定缓存的逻辑
                    pass
            else:
                # 清除所有缓存的逻辑
                pass
        except Exception as e:
            logging.error(f"清除缓存失败: {str(e)}")
            raise CacheError(
                message=f"清除缓存失败: {str(e)}",
                data={"symbol_key": symbol_key}
            )