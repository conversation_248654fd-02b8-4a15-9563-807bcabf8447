# -*- coding: utf-8 -*-
"""
为abu框架的ABuDataCache模块提供适配器，将其函数封装成类
"""
import logging
import threading
import os
from importlib import import_module
from typing import Tuple, Optional, Any

# 导入自定义异常
from ..core.exceptions import CacheError

# 尝试导入原始模块
try:
    # 动态导入原始模块，避免直接引用可能不存在的模块或属性
    original_module = import_module('abupy.MarketBu.ABuDataCache')
    
    # 将原始模块中的所有函数复制到当前模块
    for name in dir(original_module):
        if not name.startswith('_'):  # 只复制公共函数和变量
            globals()[name] = getattr(original_module, name)
            
    logging.info(f"成功导入原始ABuDataCache模块的函数: {[name for name in dir(original_module) if not name.startswith('_')]}")
except (ImportError, AttributeError) as e:
    logging.error(f"导入原始ABuDataCache模块失败: {str(e)}")

# 全局文件锁字典，保证对同一文件的操作是线程安全的
_file_locks = {}
_file_locks_lock = threading.RLock()

# 获取文件锁的函数
def get_file_lock(file_path: str) -> threading.RLock:
    """获取指定文件路径的锁，如果不存在则创建
    
    Args:
        file_path: 文件路径或唯一标识符
        
    Returns:
        threading.RLock: 与该文件路径关联的锁
    """
    # 使用绝对路径作为锁的键，确保不同线程得到相同的锁
    abs_path = os.path.abspath(file_path) if os.path.exists(file_path) else file_path
    
    with _file_locks_lock:
        if abs_path not in _file_locks:
            _file_locks[abs_path] = threading.RLock()
        return _file_locks[abs_path]

# 读取并修改文件的原子操作
def safe_read_modify_write_file(file_path: str, modify_func, max_retries: int = 3, retry_delay: float = 0.05) -> Tuple[bool, Any]:
    """线程安全地读取、修改并写回文件内容，支持重试机制
    
    Args:
        file_path: 文件路径
        modify_func: 接受当前文件内容并返回新内容的函数
        max_retries: 最大重试次数
        retry_delay: 重试间隔时间(秒)
        
    Returns:
        Tuple[bool, Any]: (操作是否成功, 修改函数的返回值)
    """
    # 确保目录存在
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            logging.error(f"创建目录 {directory} 失败: {str(e)}")
            return False, None
    
    # 获取文件锁
    lock = get_file_lock(file_path)
    
    # 试行重试机制
    retry_count = 0
    last_error = None
    
    while retry_count <= max_retries:
        with lock:
            try:
                # 读取当前内容
                current_content = None
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            current_content = f.read()
                    except Exception as e:
                        logging.error(f"读取文件 {file_path} 失败: {str(e)}")
                        last_error = e
                        retry_count += 1
                        if retry_count <= max_retries:
                            logging.warning(f"重试读取 ({retry_count}/{max_retries})")
                            time.sleep(retry_delay)
                            continue
                        return False, None
                
                # 调用修改函数
                try:
                    result = modify_func(current_content)
                    # 处理各种返回类型
                    if isinstance(result, tuple) and len(result) > 0:
                        new_content = str(result[0])
                    elif result is None:
                        logging.warning(f"修改函数返回了None，保留原内容")
                        return True, current_content  # 如果修改函数返回None，保留原内容
                    else:
                        new_content = str(result)
                except Exception as e:
                    logging.error(f"调用修改函数失败: {str(e)}")
                    last_error = e
                    retry_count += 1
                    if retry_count <= max_retries:
                        logging.warning(f"重试修改函数 ({retry_count}/{max_retries})")
                        time.sleep(retry_delay)
                        continue
                    return False, None
                
                # 写入新内容
                try:
                    # 写入新内容前先备份原文件(如果存在)
                    if os.path.exists(file_path):
                        backup_path = f"{file_path}.bak"
                        try:
                            with open(file_path, 'r', encoding='utf-8') as src, open(backup_path, 'w', encoding='utf-8') as dst:
                                dst.write(src.read())
                        except Exception as e:
                            logging.warning(f"创建备份文件失败: {str(e)}")
                            # 继续执行，不中断操作
                    
                    # 写入新内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                        # 强制写入磁盘
                        f.flush()
                        os.fsync(f.fileno())
                        
                    return True, result
                except Exception as e:
                    logging.error(f"写入文件 {file_path} 失败: {str(e)}")
                    last_error = e
                    
                    # 尝试恢复备份
                    backup_path = f"{file_path}.bak"
                    if os.path.exists(backup_path):
                        try:
                            with open(backup_path, 'r', encoding='utf-8') as src, open(file_path, 'w', encoding='utf-8') as dst:
                                dst.write(src.read())
                            logging.info(f"从备份恢复文件成功")
                        except Exception as restore_err:
                            logging.error(f"从备份恢复文件失败: {str(restore_err)}")
                    
                    retry_count += 1
                    if retry_count <= max_retries:
                        logging.warning(f"重试写入操作 ({retry_count}/{max_retries})")
                        time.sleep(retry_delay)
                        continue
                    return False, None
                    
            except Exception as e:
                logging.error(f"对文件 {file_path} 进行原子操作失败: {str(e)}")
                last_error = e
                retry_count += 1
                if retry_count <= max_retries:
                    logging.warning(f"重试整个原子操作 ({retry_count}/{max_retries})")
                    time.sleep(retry_delay)
                    continue
                return False, None
    
    # 如果所有重试都失败
    if last_error:
        logging.error(f"所有重试均失败，最后错误: {str(last_error)}")
    return False, None

# 线程安全的文件读取
def safe_read_file(file_path: str) -> Optional[str]:
    """线程安全地读取文件内容
    
    Args:
        file_path: 文件路径
        
    Returns:
        Optional[str]: 文件内容，如果文件不存在或读取失败则返回None
    """
    if not os.path.exists(file_path):
        return None
        
    lock = get_file_lock(file_path)
    with lock:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logging.error(f"读取文件 {file_path} 失败: {str(e)}")
            return None

# 线程安全的文件写入
def safe_write_file(file_path: str, content: str) -> bool:
    """线程安全地写入文件内容
    
    Args:
        file_path: 文件路径
        content: 要写入的内容
        
    Returns:
        bool: 操作是否成功
    """
    # 确保目录存在
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            logging.error(f"创建目录 {directory} 失败: {str(e)}")
            return False
    
    lock = get_file_lock(file_path)
    with lock:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            logging.error(f"写入文件 {file_path} 失败: {str(e)}")
            return False

# 创建一个适配器类，提供与原始ABuDataCache模块相同的功能，但增加线程安全性
class AbuDataCache:
    """
    ABuDataCache模块适配器类，将原始模块的函数封装为类属性和方法，并增加线程安全性
    """
    # 类属性，用于配置
    disable_cache = False
    cache_expiry_days = 30
    
    # 类级别锁，用于保护类属性访问
    _class_lock = threading.RLock()
    
    @classmethod
    def load_kline_df(cls, symbol_key):
        """线程安全地调用原始模块的load_kline_df函数"""
        if 'load_kline_df' in globals():
            try:
                # 使用文件锁保护对同一symbol_key的并发访问
                lock = get_file_lock(f"cache_{symbol_key}")
                with lock:
                    return globals()['load_kline_df'](symbol_key)
            except Exception as e:
                logging.error(f"加载K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"加载K线数据缓存失败: {str(e)}",
                    data={"symbol_key": symbol_key}
                )
        logging.error("找不到load_kline_df函数")
        return None, None, None
    
    @classmethod
    def dump_kline_df(cls, dump_df, symbol_key, date_key):
        """线程安全地调用原始模块的dump_kline_df函数"""
        if 'dump_kline_df' in globals():
            try:
                # 使用文件锁保护对同一symbol_key的并发访问
                lock = get_file_lock(f"cache_{symbol_key}")
                with lock:
                    return globals()['dump_kline_df'](dump_df, symbol_key, date_key)
            except Exception as e:
                logging.error(f"保存K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"保存K线数据缓存失败: {str(e)}",
                    data={"symbol_key": symbol_key, "date_key": date_key}
                )
        logging.error("找不到dump_kline_df函数")
        return None
    
    @classmethod
    def save_kline_df(cls, df, temp_symbol, start_int, end_int):
        """线程安全地调用原始模块的save_kline_df函数"""
        if 'save_kline_df' in globals():
            try:
                # 使用文件锁保护对同一temp_symbol的并发访问
                lock = get_file_lock(f"cache_{temp_symbol}_{start_int}_{end_int}")
                with lock:
                    return globals()['save_kline_df'](df, temp_symbol, start_int, end_int)
            except Exception as e:
                logging.error(f"保存K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"保存K线数据失败: {str(e)}",
                    data={"symbol": temp_symbol, "start": start_int, "end": end_int}
                )
        logging.error("找不到save_kline_df函数")
        return None
    
    @classmethod
    def load_kline_df_net(cls, source, temp_symbol, n_folds, start, end, start_int, end_int, save):
        """线程安全地调用原始模块的load_kline_df_net函数"""
        if 'load_kline_df_net' in globals():
            try:
                # 使用文件锁保护对同一temp_symbol的并发访问
                lock = get_file_lock(f"cache_{temp_symbol}_{start_int}_{end_int}")
                with lock:
                    return globals()['load_kline_df_net'](source, temp_symbol, n_folds, start, end, start_int, end_int, save)
            except Exception as e:
                logging.error(f"从网络加载K线数据失败: {str(e)}")
                raise CacheError(
                    message=f"从网络加载K线数据失败: {str(e)}",
                    data={"symbol": temp_symbol, "start": start, "end": end}
                )
        logging.error("找不到load_kline_df_net函数")
        return None
        
    @classmethod
    def clear_cache(cls, symbol_key=None):
        """清除缓存数据"""
        try:
            # 如果指定了symbol_key，只清除特定股票的缓存
            if symbol_key:
                lock = get_file_lock(f"cache_{symbol_key}")
                with lock:
                    # 这里应该实现清除特定缓存的逻辑
                    pass
            else:
                # 清除所有缓存的逻辑
                pass
        except Exception as e:
            logging.error(f"清除缓存失败: {str(e)}")
            raise CacheError(
                message=f"清除缓存失败: {str(e)}",
                data={"symbol_key": symbol_key}
            )
