# 前端测试指南

本文档旨在为 `abu_modern` 前端项目提供一套完整的测试规范和流程，以确保代码质量、提升开发效率和项目可维护性。

## 1. 技术栈

- **测试框架**: [Vitest](https://vitest.dev/)
- **Vue 测试工具**: [Vue Test Utils](https://test-utils.vuejs.org/)
- **代码规范**: [ESLint](https://eslint.org/)
- **Git Hooks**: [<PERSON>sky](https://typicode.github.io/husky/) + [lint-staged](https://github.com/okonet/lint-staged)

## 2. 测试类型

我们主要关注以下两种测试类型：

### 2.1. 单元测试 (Unit Tests)

- **目标**: 测试单个组件、函数或模块的逻辑是否正确。
- **位置**: `tests/` 目录下，与被测试文件结构保持一致。
- **命名**: `*.test.ts` 或 `*.spec.ts`。
- **示例**: `tests/components/Dashboard.test.ts`

### 2.2. 端到端测试 (E2E Tests)

- **目标**: 模拟真实用户操作，测试完整的业务流程。
- **工具**: (待定，可考虑 Cypress 或 Playwright)
- **位置**: `e2e/` 目录。

## 3. 测试驱动开发 (TDD) 流程

我们鼓励在开发新功能或修复 Bug 时遵循 TDD 流程：

1.  **编写失败的测试**: 针对要实现的功能，首先编写一个测试用例，此时运行测试应该是失败的。
2.  **编写最少的代码**: 编写刚好能让测试通过的代码，不要过度设计。
3.  **重构**: 在测试通过的前提下，优化代码结构，提高可读性和性能。
4.  **重复**: 循环以上步骤，直到功能完成。

## 4. 如何运行测试

- **运行所有测试**:

  ```bash
  npm run test
  ```

- **运行单个测试文件**:

  ```bash
  npm run test -- tests/components/Dashboard.test.ts
  ```

- **开启 Watch 模式**:

  ```bash
  npm run test -- --watch
  ```

## 5. 代码规范检查

- **手动检查**:

  ```bash
  npm run lint
  ```

- **自动检查 (Commit 阶段)**: 在每次 `git commit` 时，`husky` 会自动运行 `lint-staged`，对暂存区的文件进行代码规范检查和自动修复。这保证了所有提交的代码都符合基本的代码风格。

- **质量门禁 (Push/CI 阶段)**: 为保证代码仓库的稳定性，所有代码在合并到主分支前，必须通过完整的单元测试。我们强烈建议在推送（`git push`）代码前，在本地手动运行 `npm run test`。同时，CI/CD 流水线也会强制执行所有测试，只有测试通过的代码才能被合并。

## 6. 测试用例编写规范

- **清晰的描述**: 使用 `describe` 和 `it` 来组织测试用例，描述应清晰明了。
- **隔离性**: 每个测试用例都应该是独立的，不依赖于其他测试用例的执行结果。
- **断言**: 使用 `expect` 进行断言，确保断言的准确性和完整性。
- **模拟 (Mock)**: 对于外部依赖（如 API 请求），应使用 `vi.mock` 进行模拟，以保证测试的稳定性和速度。