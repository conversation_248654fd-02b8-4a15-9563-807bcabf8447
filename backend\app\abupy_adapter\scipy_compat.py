# -*- coding: utf-8 -*-
"""
SciPy兼容性模块，处理abu原有代码与现代SciPy版本的兼容性问题
"""
import logging

# 导入新版本SciPy中的interp函数
try:
    from scipy.interpolate import interp1d
    
    # 创建与旧版本兼容的interp函数
    def interp(x, xp, fp, *args, **kwargs):
        """
        兼容旧版本scipy.interp的实现
        
        参数:
            x: 需要插值的点
            xp: 已知点的x坐标
            fp: 已知点的y坐标
        """
        try:
            # 创建一个一维插值函数
            f = interp1d(xp, fp, bounds_error=False, fill_value="extrapolate")
            # 返回插值结果
            return f(x)
        except Exception as e:
            logging.error(f"插值函数调用失败: {str(e)}")
            # 简单回退方案: 返回fp的平均值或第一个值
            if hasattr(fp, '__len__') and len(fp) > 0:
                import numpy as np
                return np.ones_like(x) * np.mean(fp)
            return 0
            
    logging.info("已创建兼容版本的scipy.interp函数")
    
except ImportError as e:
    logging.error(f"无法导入scipy.interpolate.interp1d: {str(e)}")
    
    # 定义一个占位函数，以防导入失败
    def interp(x, xp, fp, *args, **kwargs):
        logging.error("SciPy插值函数不可用")
        return 0
