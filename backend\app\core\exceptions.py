# -*- coding: utf-8 -*-
"""
自定义异常类
"""
from typing import Any, Dict, Optional


class BaseAbuModernException(Exception):
    """基础异常类，所有自定义异常的基类"""
    
    def __init__(
        self, 
        message: str = "发生了未知错误", 
        status_code: int = 500,
        error_code: str = "UNKNOWN_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.data = data or {}
        super().__init__(self.message)


class DataNotFoundError(BaseAbuModernException):
    """数据未找到异常"""
    
    def __init__(
        self, 
        message: str = "请求的数据未找到", 
        error_code: str = "DATA_NOT_FOUND",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=404, error_code=error_code, data=data)


class ExternalAPIError(BaseAbuModernException):
    """外部API调用失败异常"""
    
    def __init__(
        self, 
        message: str = "外部API调用失败", 
        error_code: str = "EXTERNAL_API_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=503, error_code=error_code, data=data)


class ValidationError(BaseAbuModernException):
    """数据验证失败异常"""
    
    def __init__(
        self, 
        message: str = "输入数据验证失败", 
        error_code: str = "VALIDATION_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=400, error_code=error_code, data=data)


class SymbolError(BaseAbuModernException):
    """股票代码错误异常"""
    
    def __init__(
        self, 
        message: str = "股票代码格式或内容无效", 
        error_code: str = "SYMBOL_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=400, error_code=error_code, data=data)


class CacheError(BaseAbuModernException):
    """缓存操作异常"""
    
    def __init__(
        self, 
        message: str = "缓存操作失败", 
        error_code: str = "CACHE_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=500, error_code=error_code, data=data)


class AdapterError(BaseAbuModernException):
    """适配器操作异常"""
    
    def __init__(
        self, 
        message: str = "适配器操作失败", 
        error_code: str = "ADAPTER_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=500, error_code=error_code, data=data)


class FactorError(BaseAbuModernException):
    """因子操作异常"""
    
    def __init__(
        self, 
        message: str = "因子操作失败", 
        error_code: str = "FACTOR_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=400, error_code=error_code, data=data)


class ParameterError(BaseAbuModernException):
    """参数错误异常"""
    
    def __init__(
        self, 
        message: str = "参数无效或格式错误", 
        error_code: str = "PARAMETER_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=400, error_code=error_code, data=data)


class ExecutionError(BaseAbuModernException):
    """策略执行异常"""
    
    def __init__(
        self, 
        message: str = "执行策略时出错", 
        error_code: str = "EXECUTION_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=500, error_code=error_code, data=data)


class BenchmarkError(BaseAbuModernException):
    """基准指数处理异常"""
    
    def __init__(
        self, 
        message: str = "基准指数处理出错", 
        error_code: str = "BENCHMARK_ERROR",
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, status_code=500, error_code=error_code, data=data)
