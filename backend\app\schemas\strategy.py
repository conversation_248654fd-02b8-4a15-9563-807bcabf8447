"""
策略管理模块的数据模型定义
包含策略、买入因子、卖出因子等Pydantic模型
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from typing import Literal
from pydantic import BaseModel, Field, ConfigDict


class BaseFactor(BaseModel):
    """因子基础模型"""
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    factor_type: str  # 如"buy"或"sell"
    factor_class: str  # 原abu中的类名
    parameters: Dict[str, Any] = Field(default_factory=dict)


class BuyFactor(BaseFactor):
    """买入因子模型，继承自BaseFactor"""
    factor_type: str = "buy"
    # 买入因子特有字段可在此添加


class SellFactor(BaseFactor):
    """卖出因子模型，继承自BaseFactor"""
    factor_type: str = "sell"
    # 卖出因子特有字段可在此添加


class PositionStrategy(BaseModel):
    """仓位管理策略模型"""
    class_name: str = Field(..., description="仓位管理策略的类名")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="仓位管理策略的参数")


class Strategy(BaseModel):
    """策略模型"""
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    owner: Optional[str] = None
    is_public: bool = False
    buy_factors: List[BuyFactor] = Field(default_factory=list)
    sell_factors: List[SellFactor] = Field(default_factory=list)
    position_strategy: Optional[PositionStrategy] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)
    tags: Optional[List[str]] = None


class StrategyCreate(BaseModel):
    """创建策略的请求模型"""
    name: str = Field(..., min_length=1)
    description: Optional[str] = None
    is_public: bool = False
    buy_factors: List[BuyFactor] = Field(..., min_length=1)
    sell_factors: List[SellFactor] = Field(default_factory=list)
    position_strategy: Optional[PositionStrategy] = None
    umpire_rules: Optional[List[Dict[str, Any]]] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)
    tags: Optional[List[str]] = None


class StrategyUpdate(BaseModel):
    """更新策略的请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[bool] = None
    buy_factors: Optional[List[BuyFactor]] = None
    sell_factors: Optional[List[SellFactor]] = None
    position_strategy: Optional[PositionStrategy] = None
    umpire_rules: Optional[List[Dict[str, Any]]] = None
    parameters: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None


class StrategyInDB(Strategy):
    """数据库中的策略模型，包含更多数据库相关字段"""
    pass


class FactorListResponse(BaseModel):
    """因子列表响应模型"""
    buy_factors: List[BuyFactor] = Field(default_factory=list)
    sell_factors: List[SellFactor] = Field(default_factory=list)


class StrategyResponse(BaseModel):
    """策略API响应模型，用于标准化API响应"""
    data: Optional[Strategy] = None
    message: Optional[str] = None
    success: bool = True


class StrategiesListResponse(BaseModel):
    """策略列表API响应模型"""
    data: List[Strategy] = Field(default_factory=list)
    total: int = 0
    message: Optional[str] = None
    success: bool = True


class PerformanceMetrics(BaseModel):
    """回测性能指标模型
    
    包含策略回测的各项性能指标，用于评估策略表现
    """
    cumulative_return: float = Field(0.0, description="累计收益率")
    annualized_return: float = Field(0.0, description="年化收益率")
    max_drawdown: float = Field(0.0, description="最大回撤")
    sharpe_ratio: float = Field(0.0, description="夏普比率")
    win_rate: float = Field(0.0, description="胜率")
    profit_loss_ratio: float = Field(0.0, description="盈亏比")
    alpha: float = Field(0.0, description="阿尔法")
    beta: float = Field(0.0, description="贝塔")
    total_trades: int = Field(0, description="总交易次数")
    annualized_volatility: float = Field(0.0, description="年化波动率")
    benchmark_return: float = Field(0.0, description="基准收益率")
    benchmark_annualized_return: float = Field(0.0, description="基准年化收益率")
    information_ratio: float = Field(0.0, description="信息比率")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "cumulative_return": 0.156,
                "annualized_return": 0.085,
                "max_drawdown": 0.125,
                "sharpe_ratio": 1.25,
                "win_rate": 0.65,
                "profit_loss_ratio": 1.8,
                "alpha": 0.03,
                "beta": 0.85,
                "total_trades": 42,
                "annualized_volatility": 0.18,
                "benchmark_return": 0.08,
                "benchmark_annualized_return": 0.045,
                "information_ratio": 0.75
            }
        }
    )


class StrategyExecuteRequest(BaseModel):
    """策略执行请求模型
    
    用于执行策略回测的请求数据模型，确保参数命名规范和默认值设置
    """
    choice_symbols: List[str] = Field(..., description="股票代码列表，必须提供")
    start_date: str = Field(..., description="回测开始日期 (格式: YYYYMMDD 或 YYYY-MM-DD)")
    end_date: str = Field(..., description="回测结束日期 (格式: YYYYMMDD 或 YYYY-MM-DD)")
    capital: Optional[float] = Field(None, description="初始资金，若不提供则使用策略默认参数")
    benchmark_symbol: Optional[str] = Field(None, description="基准股票/指数代码")
    data_source: Literal['tushare', 'local'] = 'local'
    n_folds: Optional[int] = Field(1, description="交叉验证折数")
    
    model_config = ConfigDict(
        extra="allow",  # 允许额外字段，以支持未来扩展
        json_schema_extra={
            "example": {
                "choice_symbols": ["sh000001", "sz000001"],
                "start_date": "20210101",
                "end_date": "20211231",
                "capital": 1000000,
                "benchmark_symbol": "sh000300",
                "data_source": "tushare"
            }
        }
    )
