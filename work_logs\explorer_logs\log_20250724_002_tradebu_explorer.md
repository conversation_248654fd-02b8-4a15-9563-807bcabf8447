# 关于 TradeBu/ABuBenchmark.py 的勘探报告

**日期**: 2024年7月26日

**模块**: `TradeBu`

**勘探者**: 勘探者AI

## 目标

调查 `abupy` 库中的 `AbuBenchmark` 类，明确其如何导入和使用 `ABuSymbolPd` 和 `make_kl_df`。

## 发现

`AbuBenchmark` 类定义于 `abupy/TradeBu/ABuBenchmark.py` 文件中。

### 依赖导入

在文件顶部，通过以下相对导入语句引入了 `ABuSymbolPd` 模块：

```python
from ..MarketBu import ABuSymbolPd
```

这表明 `ABuSymbolPd` 是从 `MarketBu` 包中导入的。

### `make_kl_df` 的使用

`make_kl_df` 函数并不是直接导入的，而是作为 `ABuSymbolPd` 模块的一个属性（函数）来使用的。在 `AbuBenchmark` 类的 `__init__` 方法中，可以看到如下调用：

```python
self.kl_pd = ABuSymbolPd.make_kl_df(benchmark, data_mode=EMarketDataSplitMode.E_DATA_SPLIT_SE,
                                    n_folds=n_folds,
                                    start=start, end=end)
```

## 结论

在 `abu_modern` 项目中重构 `AbuBenchmark` 类时，需要确保：

1.  从 `MarketBu` 模块中正确导入 `ABuSymbolPd`。
2.  通过 `ABuSymbolPd.make_kl_df()` 的方式调用 `make_kl_df` 函数。

这份报告将为实现者AI提供清晰的指引，以确保在 `abu_modern` 中正确实现 `AbuBenchmark` 的功能。
