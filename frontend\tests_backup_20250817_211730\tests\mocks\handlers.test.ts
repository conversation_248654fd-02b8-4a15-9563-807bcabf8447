// MSW Handlers 测试 - 验证API契约兼容性

import { describe, it, expect, beforeAll, afterEach, afterAll } from 'vitest';
import { setupServer } from 'msw/node';
import { simpleHandlers } from './simple';

// 设置MSW服务器
const server = setupServer(...simpleHandlers);

beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
});

afterEach(() => {
  server.resetHandlers();
});

afterAll(() => {
  server.close();
});

describe('MSW Handlers API契约测试', () => {
  describe('Market API', () => {
    it('应该正确响应K线数据请求', async () => {
      const response = await fetch('/api/market/kline?symbol=000001.SZ&period=daily&start_date=2023-01-01&end_date=2023-12-31');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(Array.isArray(data.data.data)).toBe(true);
    });

    it('应该正确响应股票列表请求', async () => {
      const response = await fetch('/api/market/stocks');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('Strategy API', () => {
    it('应该正确响应策略列表请求', async () => {
      const response = await fetch('/api/strategy');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(Array.isArray(data.data)).toBe(true);
    });

    it('应该正确响应因子列表请求', async () => {
      const response = await fetch('/api/strategy/factors');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(Array.isArray(data.data.buy_factors)).toBe(true);
      expect(Array.isArray(data.data.sell_factors)).toBe(true);
    });
  });

  describe('Dashboard API', () => {
    it('应该正确响应仪表板摘要请求', async () => {
      const response = await fetch('/api/dashboard/summary');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.today_gain).toBeDefined();
      expect(data.data.active_strategies).toBeDefined();
      expect(data.data.market_performance).toBeDefined();
    });
  });

  describe('Options API', () => {
    it('应该正确响应仓位管理选项请求', async () => {
      const response = await fetch('/api/options/positions');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.options).toBeDefined();
      expect(Array.isArray(data.data.options)).toBe(true);
    });

    it('应该正确响应裁判规则选项请求', async () => {
      const response = await fetch('/api/options/judges');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.options).toBeDefined();
      expect(Array.isArray(data.data.options)).toBe(true);
    });
  });

  describe('Metrics API', () => {
    it('应该正确响应指标计算请求', async () => {
      const mockBacktestResult = {
        orders: [
          {
            symbol: '000001.SZ',
            action: 'buy',
            quantity: 100,
            price: 10.0,
            timestamp: '2023-01-01T09:30:00'
          }
        ],
        operations: [
          {
            type: 'position_change',
            symbol: '000001.SZ',
            quantity: 100,
            timestamp: '2023-01-01T09:30:00'
          }
        ],
        start_cash: 100000
      };

      const response = await fetch('/api/metrics/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(mockBacktestResult)
      });
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.total_return).toBeDefined();
      expect(data.data.volatility).toBeDefined();
      expect(data.data.total_trades).toBeDefined();
    });
  });

  describe('Grid Search API', () => {
    it('应该正确响应网格搜索启动请求', async () => {
      const mockGridSearchParams = {
        strategy_template: 'ma_cross_strategy',
        param_grid: {
          ma_short: [5, 10, 15],
          ma_long: [20, 30, 40]
        },
        symbols: ['000001.SZ'],
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        initial_capital: 100000
      };

      const response = await fetch('/api/grid-search/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(mockGridSearchParams)
      });
      const data = await response.json();
      
      expect(response.status).toBe(202);
      expect(data.success).toBe(true);
      expect(data.data.task_id).toBeDefined();
      expect(data.data.status).toBe('running');
    });
  });
});