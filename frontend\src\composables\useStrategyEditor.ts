import { ref, computed } from 'vue'
import type { Strategy } from '@/api/types'
import { 
  STRATEGY_STATES, 
  ERROR_MESSAGES,
  isNewStrategy as checkIsNewStrategy,
  validateStrategyName,
  validateStrategyDescription
} from '@/constants/strategy'

export type StrategyState = typeof STRATEGY_STATES[keyof typeof STRATEGY_STATES]

// 验证错误类型
export interface ValidationError {
  field: string
  message: string
}

/**
 * 策略编辑器组合函数
 * 统一管理策略编辑状态、验证和保存逻辑
 */
export function useStrategyEditor() {
  // 状态管理
  const editingStrategy = ref<Strategy | null>(null)
  const originalStrategy = ref<Strategy | null>(null)
  const isDirty = ref(false)
  const isSaving = ref(false)
  const errors = ref<ValidationError[]>([])
  
  // 计算属性
  const canSave = computed(() => {
    if (!editingStrategy.value) return false
    
    const strategy = editingStrategy.value
    const hasName = strategy.name?.trim() !== ''
    const hasBuyFactors = (strategy.buy_factors?.length || 0) > 0
    
    return hasName && hasBuyFactors && errors.value.length === 0
  })
  
  const isNewStrategy = computed(() => {
    return checkIsNewStrategy(editingStrategy.value)
  })
  
  // 验证策略
  const validateStrategy = (strategy: Strategy): ValidationError[] => {
    const validationErrors: ValidationError[] = []
    
    // 检查策略是否为空
    if (!strategy) {
      validationErrors.push({
        field: 'strategy',
        message: '策略不能为空'
      })
      return validationErrors
    }
    
    // 验证策略名称
    const nameValidation = validateStrategyName(strategy.name || '')
    if (!nameValidation.isValid) {
      validationErrors.push({
        field: 'name',
        message: nameValidation.message || ERROR_MESSAGES.STRATEGY_NAME_REQUIRED
      })
    }
    
    // 验证策略描述
    const descValidation = validateStrategyDescription(strategy.description || '')
    if (!descValidation.isValid) {
      validationErrors.push({
        field: 'description',
        message: descValidation.message || ERROR_MESSAGES.DESCRIPTION_TOO_LONG
      })
    }
    
    // 验证买入因子
    if (!strategy.buy_factors || strategy.buy_factors.length === 0) {
      validationErrors.push({
        field: 'buy_factors',
        message: ERROR_MESSAGES.BUY_FACTORS_REQUIRED
      })
    }
    
    return validationErrors
  }
  
  // 检查是否有变更
  const checkDirty = () => {
    if (!editingStrategy.value || !originalStrategy.value) {
      isDirty.value = false
      return
    }
    
    const current = JSON.stringify(editingStrategy.value)
    const original = JSON.stringify(originalStrategy.value)
    isDirty.value = current !== original
  }
  
  // 更新策略
  const updateStrategy = (strategy: Strategy, resetOriginal: boolean = false) => {
    editingStrategy.value = { ...strategy }
    
    // 如果是首次设置或明确要求重置，记录原始状态
    if (!originalStrategy.value || resetOriginal) {
      originalStrategy.value = { ...strategy }
    }
    
    // 验证
    errors.value = validateStrategy(editingStrategy.value)
    
    // 检查是否有变更
    checkDirty()
  }
  
  // 保存策略
  const saveStrategy = async (autoResetStates: boolean = true): Promise<void> => {
    if (!editingStrategy.value) {
      throw new Error(ERROR_MESSAGES.VALIDATION_FAILED)
    }
    
    isSaving.value = true
    
    try {
      // 验证策略
      const validationErrors = validateStrategy(editingStrategy.value)
      if (validationErrors.length > 0) {
        errors.value = validationErrors
        throw new Error('无法保存策略：验证失败')
      }
      
      // 检查canSave状态
      if (!canSave.value) {
        throw new Error(ERROR_MESSAGES.VALIDATION_FAILED)
      }
      
      // 如果需要自动重置状态（测试模式或简单使用）
      if (autoResetStates) {
        originalStrategy.value = { ...editingStrategy.value }
        isDirty.value = false
        errors.value = []
      }
      
      // 无论是否自动重置状态，都要重置isSaving
      isSaving.value = false
    } catch (error) {
      isSaving.value = false
      throw error
    }
  }
  
  // 保存成功后调用此方法重置状态
  const onSaveSuccess = () => {
    if (editingStrategy.value) {
      originalStrategy.value = { ...editingStrategy.value }
      isDirty.value = false
      errors.value = []
    }
    isSaving.value = false
  }
  
  // 保存失败后调用此方法重置状态
  const onSaveError = () => {
    isSaving.value = false
  }
  
  // 重置更改
  const resetChanges = () => {
    if (originalStrategy.value) {
      editingStrategy.value = { ...originalStrategy.value }
      isDirty.value = false
      errors.value = []
    }
  }
  
  // 清空编辑器
  const clearEditor = () => {
    editingStrategy.value = null
    originalStrategy.value = null
    isDirty.value = false
    errors.value = []
    isSaving.value = false
  }
  
  return {
    // 状态
    editingStrategy: computed(() => editingStrategy.value),
    isDirty: computed(() => isDirty.value),
    isSaving: computed(() => isSaving.value),
    errors: computed(() => errors.value),
    
    // 计算属性
    canSave,
    isNewStrategy,
    
    // 方法
    updateStrategy,
    saveStrategy,
    onSaveSuccess,
    onSaveError,
    resetChanges,
    clearEditor,
    validateStrategy
  }
}

/**
 * 策略工具函数
 */
export const strategyUtils = {
  /**
   * 检查是否为新策略
   */
  isNewStrategy: checkIsNewStrategy,
  
  /**
   * 生成策略描述
   */
  generateDescription: (strategy: Strategy): string => {
    const buyFactors = strategy.buy_factors || []
    const sellFactors = strategy.sell_factors || []
    
    let description = ''
    
    if (buyFactors.length > 0) {
      const buyDescriptions = buyFactors
        .map(factor => {
          const desc = factor.description && factor.description.trim() ? factor.description : factor.class_name
          return desc
        })
        .filter(desc => desc && desc.trim())
      if (buyDescriptions.length > 0) {
        description += `买入规则：${buyDescriptions.join('；')}`
      }
    }
    
    if (sellFactors.length > 0) {
      const sellDescriptions = sellFactors
        .map(factor => {
          const desc = factor.description && factor.description.trim() ? factor.description : factor.class_name
          return desc
        })
        .filter(desc => desc && desc.trim())
      if (sellDescriptions.length > 0) {
        if (description) description += '。'
        description += `卖出规则：${sellDescriptions.join('；')}`
      }
    }
    
    if (description) {
      description += '。'
    } else {
      description = '请添加买入和卖出因子来完善此策略。'
    }
    
    return description
  },
  
  /**
   * 统一错误处理
   */
  handleError: (error: Error, context: string) => {
    console.error(`[${context}]`, error)
    
    // 这里可以集成到全局错误处理系统
    return {
      message: error.message || '操作失败',
      context
    }
  }
}
