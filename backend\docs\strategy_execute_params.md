# 策略执行接口参数详解

本文档详细说明了策略执行(`/strategy/{id}/execute`)接口的参数要求、格式以及注意事项。

## 1. 基本信息

- **接口URL**: `/strategy/{id}/execute`
- **请求方法**: POST
- **接口描述**: 根据指定ID的策略进行回测，返回回测结果
- **路径参数**: `strategy_id` - 策略ID

## 2. 请求参数详解

请求体需要以JSON格式提交，包含以下字段：

### 2.1 必需参数

| 参数名 | 类型 | 描述 | 格式 | 示例 |
|-------|-----|------|-----|-----|
| **choice_symbols** | 数组 | 股票代码列表 | 字符串数组 | `["sh000001", "sz399001"]` |
| **start_date** | 字符串 | 回测开始日期 | YYYYMMDD 或 YYYY-MM-DD | `"20220101"` 或 `"2022-01-01"` |
| **end_date** | 字符串 | 回测结束日期 | YYYYMMDD 或 YYYY-MM-DD | `"20221231"` 或 `"2022-12-31"` |
| **benchmark_symbol** | 字符串 | 基准指数代码 | 字符串 | `"sh000300"` |

### 2.2 可选参数

| 参数名 | 类型 | 默认值 | 描述 | 示例 |
|-------|-----|-------|------|-----|
| **data_source** | 字符串 | "tushare" | 数据源类型，支持的值为："tushare"或"local" | `"tushare"` |
| **capital** | 数值 | 策略参数中的initial_capital | 初始资金 | 1000000 |
| **n_folds** | 整数 | 1 | 交叉验证折数 | 2 |

### 2.3 参数优先级说明

1. **初始资金(capital)**:
   - 首先使用请求中提供的`capital`参数
   - 如果未提供，则使用策略对象的`parameters.initial_capital`参数
   - 两者均未提供时，接口会返回错误

2. **其他参数**:
   - 请求体中的参数将覆盖策略对象中的同名参数
   - 建议在请求中明确提供所有必需参数，避免依赖策略对象中的默认值

## 3. data_source参数说明

### 3.1 基本定义

`data_source`参数指定获取历史行情数据的来源，是一个关键参数：

- **默认值**: "tushare"
- **支持的值**: 
  - `"tushare"`: 使用tushare数据源获取在线数据
  - `"local"`: 使用本地数据源获取数据

### 3.2 重要性

此参数非常重要，**强烈建议在每次请求中明确指定**，原因如下：

1. 不同数据源可能导致回测结果差异
2. 若未传递此参数且底层服务无法确定默认数据源，会导致回测失败，报错"不支持的数据源: None"
3. 当系统配置变更时，默认数据源可能会改变，显式指定可增强代码可维护性

### 3.3 使用建议

- 生产环境: 建议始终显式传递`data_source`参数，不要依赖默认值
- 开发测试: 可以根据测试需要切换不同数据源
- 批量处理: 在批量执行多个回测任务时，确保data_source参数统一

## 4. 格式要求与注意事项

### 4.1 参数命名特别注意

- 股票代码列表**必须**使用`choice_symbols`作为参数名，而**非**`symbols`
- 使用错误的参数名(如`symbols`)会导致回测失败，报错缺少必要参数

### 4.2 日期格式

- 支持`YYYYMMDD`格式: 如"20220101"
- 支持`YYYY-MM-DD`格式: 如"2022-01-01"
- 建议使用`YYYYMMDD`格式以保持与底层库兼容性

### 4.3 股票代码格式

- 应包含市场前缀: 如`sh`开头表示上海市场, `sz`开头表示深圳市场
- 示例: `sh000001`(上证指数)，`sz399001`(深证成指)

## 5. 示例请求

### 5.1 基本请求

```json
{
  "choice_symbols": ["sh000001", "sz399001"],
  "start_date": "20220101",
  "end_date": "20221231", 
  "benchmark_symbol": "sh000300",
  "data_source": "tushare",
  "capital": 1000000,
  "n_folds": 1
}
```

### 5.2 最小请求(使用默认值)

```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "end_date": "20220131",
  "benchmark_symbol": "sh000300",
  "data_source": "tushare"
}
```

## 6. 常见错误及处理

| 错误描述 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 400 Bad Request: "缺少必要的参数" | 未提供必需参数 | 检查是否提供了所有必需参数 |
| 400 Bad Request: "不支持的数据源: None" | 未提供data_source且系统无法确定默认值 | 明确提供data_source参数 |
| 400 Bad Request: "不支持的数据源: xxx" | 提供了不支持的数据源值 | 使用支持的值: "tushare"或"local" |
| 400 Bad Request: JSON格式错误 | 请求体JSON格式有误 | 检查JSON格式是否正确 |
| 404 Not Found | 策略ID不存在 | 确认策略ID是否正确 |
