import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useStrategyEditorStore } from '../../../src/stores/useStrategyEditorStore';

// TDD专用测试文件 - useStrategyEditorStore
// 专注于策略编辑相关操作的快速验证

// 契约 1: 定义标准的模拟因子配置数据
const mockBuyFactorConfig = {
  id: 'factor-001',
  name: 'AbuDoubleMaBuy',
  description: '双均线交叉买入策略',
  factor_type: 'buy',
  class_name: 'AbuDoubleMaBuy',
  parameters: {
    fast_ma: 5,
    slow_ma: 20
  }
};

const mockSellFactorConfig = {
  id: 'factor-002',
  name: 'AbuRSISell',
  description: 'RSI超买卖出策略',
  factor_type: 'sell',
  class_name: 'AbuRSISell',
  parameters: {
    rsi_period: 14,
    overbought_threshold: 70
  }
};

// 模拟策略数据
const mockStrategy = {
  id: 'strategy-001',
  name: '测试策略',
  description: '测试描述',
  content: '策略内容',
  is_public: true,
  buy_factors: [],
  sell_factors: [],
  author: 'test_user',
  create_time: '2025-01-01T00:00:00Z',
  update_time: '2025-01-01T00:00:00Z'
};

describe('useStrategyEditorStore - TDD专用测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态 - TDD', () => {
      const store = useStrategyEditorStore();
      expect(store.currentSelectedStrategy).toBeNull();
      expect(store.isEditing).toBe(false);
    });
  });

  describe('契约 B: addBuyFactor action', () => {
    it('调用 addBuyFactor 时，应将新因子添加到 currentSelectedStrategy.buy_factors 数组中 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 设置当前选中的策略
      store.currentSelectedStrategy = { ...mockStrategy };
      
      // 验证初始状态：buy_factors 为空数组
      expect(store.currentSelectedStrategy.buy_factors).toEqual([]);
      
      // 调用 addBuyFactor
      store.addBuyFactor(mockBuyFactorConfig);
      
      // 验证因子已添加
      expect(store.currentSelectedStrategy.buy_factors).toHaveLength(1);
      expect(store.currentSelectedStrategy.buy_factors[0]).toEqual(mockBuyFactorConfig);
    });

    it('多次调用 addBuyFactor 应能正确累加买入因子 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 设置当前选中的策略
      store.currentSelectedStrategy = { ...mockStrategy };
      
      const secondBuyFactor = {
        ...mockBuyFactorConfig,
        id: 'factor-003',
        name: 'AbuRSIBuy',
        class_name: 'AbuRSIBuy'
      };
      
      // 添加第一个买入因子
      store.addBuyFactor(mockBuyFactorConfig);
      expect(store.currentSelectedStrategy.buy_factors).toHaveLength(1);
      
      // 添加第二个买入因子
      store.addBuyFactor(secondBuyFactor);
      expect(store.currentSelectedStrategy.buy_factors).toHaveLength(2);
      
      // 验证两个因子都存在
      expect(store.currentSelectedStrategy.buy_factors[0]).toEqual(mockBuyFactorConfig);
      expect(store.currentSelectedStrategy.buy_factors[1]).toEqual(secondBuyFactor);
    });

    it('addBuyFactor 不应影响 sell_factors 数组 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 设置当前选中的策略，预先添加一个卖出因子
      store.currentSelectedStrategy = {
        ...mockStrategy,
        sell_factors: [mockSellFactorConfig]
      };
      
      // 验证初始状态
      expect(store.currentSelectedStrategy.sell_factors).toHaveLength(1);
      expect(store.currentSelectedStrategy.buy_factors).toHaveLength(0);
      
      // 添加买入因子
      store.addBuyFactor(mockBuyFactorConfig);
      
      // 验证买入因子已添加，但卖出因子不受影响
      expect(store.currentSelectedStrategy.buy_factors).toHaveLength(1);
      expect(store.currentSelectedStrategy.sell_factors).toHaveLength(1);
      expect(store.currentSelectedStrategy.sell_factors[0]).toEqual(mockSellFactorConfig);
    });

    it('当 currentSelectedStrategy 为 null 时，addBuyFactor 应能安全处理 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 确保 currentSelectedStrategy 为 null
      store.currentSelectedStrategy = null;
      
      // 调用 addBuyFactor 不应报错
      expect(() => {
        store.addBuyFactor(mockBuyFactorConfig);
      }).not.toThrow();
      
      // currentSelectedStrategy 仍应为 null
      expect(store.currentSelectedStrategy).toBeNull();
    });

    it('当策略没有 buy_factors 属性时，addBuyFactor 应能初始化数组 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 设置一个没有 buy_factors 属性的策略
      store.currentSelectedStrategy = {
        id: 'strategy-002',
        name: '不完整的策略'
      } as any;
      
      // 调用 addBuyFactor
      store.addBuyFactor(mockBuyFactorConfig);
      
      // 验证 buy_factors 数组被正确初始化并添加了因子
      expect(store.currentSelectedStrategy.buy_factors).toBeDefined();
      expect(store.currentSelectedStrategy.buy_factors).toHaveLength(1);
      expect(store.currentSelectedStrategy.buy_factors[0]).toEqual(mockBuyFactorConfig);
    });
  });

  describe('契约 C: 策略选择状态管理', () => {
    it('setCurrentSelectedStrategy 应正确设置当前选中的策略 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 验证初始状态
      expect(store.currentSelectedStrategy).toBeNull();
      
      // 设置策略
      store.setCurrentSelectedStrategy(mockStrategy);
      
      // 验证策略已设置
      expect(store.currentSelectedStrategy).toEqual(mockStrategy);
    });

    it('clearCurrentSelectedStrategy 应清除当前选中的策略 - TDD', () => {
      const store = useStrategyEditorStore();
      
      // 先设置一个策略
      store.setCurrentSelectedStrategy(mockStrategy);
      expect(store.currentSelectedStrategy).toEqual(mockStrategy);
      
      // 清除策略
      store.clearCurrentSelectedStrategy();
      
      // 验证策略已清除
      expect(store.currentSelectedStrategy).toBeNull();
    });
  });
});
