from typing import Generic, TypeVar, Optional, List, Any
from pydantic import BaseModel, Field

DataType = TypeVar('DataType')

class SuccessResponse(BaseModel, Generic[DataType]):
    success: bool = Field(True, description="操作是否成功")
    message: Optional[str] = Field(None, description="可选的成功消息")
    data: Optional[DataType] = Field(None, description="响应数据")

class ErrorResponse(BaseModel):
    success: bool = Field(False, description="操作是否成功")
    error_code: Optional[str] = Field(None, description="错误代码")
    message: str = Field(..., description="错误消息")
    details: Optional[Any] = Field(None, description="错误的详细信息")

class PaginatedResponse(SuccessResponse[List[DataType]]):
    total: int = Field(..., description="总记录数")
    page: Optional[int] = Field(None, description="当前页码")
    size: Optional[int] = Field(None, description="每页记录数")
    # data: List[DataType] # Inherited and specialized from SuccessResponse
