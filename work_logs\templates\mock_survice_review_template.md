Mock服务器定义文件 (handlers.ts) 审查清单
核心目标：确保我们为测试脚本提供的“假服务器”是可靠、全面且与真实世界一致的。这个文件是所有API相关测试的基石。

第一层：宏观审查 —— “它模拟了正确的后端世界吗？”
✅ API 端点覆盖度 (API Endpoint Coverage)

检查：文件中定义的 http 处理器，是否覆盖了前端应用在当前测试范围内的所有核心API端点？
目的：防止因缺少某个API的模拟，导致整个测试流程中断。
✅ 场景覆盖度 (Scenario Coverage)

检查：对于每一个API端点，是否都提供了足够丰富的模拟场景？
成功场景（Happy Path）：返回 200 或 201 的成功响应。
客户端错误：如 404 Not Found（资源不存在）、400 Bad Request（请求参数错误）、403 Forbidden（权限不足）、409 Conflict（资源冲突）。
服务端错误：如 500 Internal Server Error、503 Service Unavailable。
网络错误：HttpResponse.error()。
目的：为测试脚本提供充足的“弹药”，使其能够验证前端在各种成功和失败条件下的行为是否健壮。
✅ 文件结构与可维护性 (File Structure & Maintainability)

检查：
数据与逻辑分离：模拟数据（如 mockStrategies）是否作为独立的常量定义在文件顶部，与请求处理逻辑分离？
逻辑分组：相关的API处理器是否通过注释（如 // Strategy API handlers）进行了清晰的分组？
命名清晰：模拟数据的变量名是否清晰地反映了其内容？
目的：保证该文件的长期可读性、可维护性和可扩展性。
第二层：微观审查 —— “它模拟的方式正确吗？”
🚨 [P0级] 响应数据结构一致性 (Response Data Structure Consistency)

检查：这是最重要的检查点。 对于所有成功的场景，返回的 HttpResponse.json() 内容，其最外层结构是否与我们项目定义的标准API响应范式 ({ success: boolean, data: any, total?: number, message?: string | null }) 完全一致？
警惕：绝对不能直接返回一个原始数组 [...] 或原始对象 {...}。必须用标准范式进行包装。
目的：从根源上杜绝“测试环境通过，生产环境失败”的严重问题。确保前端代码处理的是真实的数据结构。
✅ Mock 数据质量 (Mock Data Quality)

检查：
模拟数据的内容是否具有一定的真实性？（例如，日期是合法的ISO格式字符串，id 具有区分度）。
数据是否能覆盖不同的业务状态？（例如，mockStrategies 中是否既包含了 is_public: true 的策略，也包含了 is_public: false 的策略）。
目的：让测试不仅仅是“跑通”，而是能发现由特定数据引发的边界问题。
✅ 异步处理正确性 (Async Handling Correctness)

检查：对于需要读取请求体的处理器（通常是 http.post, http.put, http.patch），函数定义是否为 async，并且是否正确地使用了 await request.json() 来解析请求体？
目的：确保模拟服务器能正确处理前端在测试中发送过来的数据。
✅ 类型安全 (Type Safety)

检查：
文件顶部的 import type 是否从真实的应用代码中导入了核心的数据类型（如 Strategy, Symbol）？
模拟数据（如 mockStrategies: Strategy[]）和从请求中解析出的数据（如 as CreateStrategyRequest）是否标注了正确的类型？
目的：利用TypeScript的静态检查能力，提前发现模拟数据与应用类型之间的不匹配，防止运行时错误。
您的审查实战流程 (建议)
全局扫描：快速通读 handlers.ts，了解它模拟了哪些API，以及文件的大致结构。
聚焦P0检查：首先，也是最优先地，对每一个成功响应的处理器进行“响应数据结构一致性”的审查。 这是最容易出问题、后果也最严重的地方。
清单逐项核对：带着上述清单，逐个检查每个处理器，确保宏观和微观的各个要点都达标。
做出判断：
高质量：所有检查项，特别是P0级检查项，都完美通过。
需要修改：发现任何问题，特别是结构性问题。您需要向执行者（AI或人类）提出具体、可操作的修改指令。例如：
“P0级错误：/api/strategies 的所有成功响应都缺少标准API包装器，请为 GET, POST, PUT 请求的成功返回添加 { success: true, data: ... } 包装。”
“场景覆盖不足：请为 DELETE /api/strategies/:id 补充一个模拟‘资源正在使用，无法删除’的 400 错误场景。”