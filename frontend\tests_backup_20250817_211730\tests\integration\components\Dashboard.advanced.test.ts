import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import Dashboard from '@/views/Dashboard.vue';
import * as dashboardApi from '@/api/dashboard';

// Mock echarts
vi.mock('echarts', () => ({
  init: vi.fn(() => ({
    setOption: vi.fn(),
    resize: vi.fn(),
  })),
}));

// Mock API module
vi.mock('@/api/dashboard');

describe('Dashboard.vue', () => {
  let pinia;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    vi.resetAllMocks();
  });

  it('renders loading state while fetching data', async () => {
    vi.mocked(dashboardApi.getDashboardSummary).mockReturnValue(new Promise(() => {}));
    const wrapper = mount(Dashboard, { global: { plugins: [pinia] } });
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.el-loading-mask').exists()).toBe(true);
  });

  it('renders statistics after data is fetched', async () => {
    const summaryData = {
      today_gain: 100,
      active_strategies: 5,
      total_turnover_wan: 1000,
      signals_count: 50,
      market_performance: { date: ['2023-01-01'], value: [100] },
    };
    vi.mocked(dashboardApi.getDashboardSummary).mockResolvedValue(summaryData);

    const wrapper = mount(Dashboard, {
      global: {
        plugins: [pinia],
      },
    });

    await new Promise(resolve => setImmediate(resolve));
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.el-loading-mask').isVisible()).toBe(false);
    const statistics = wrapper.findAll('.el-statistic');
    expect(statistics.length).toBe(4);
  });
});