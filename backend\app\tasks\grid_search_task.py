import logging
from backend.app.core.celery_app import celery_app
from backend.app.abupy_adapter.abupy_factor_adapter import AbuPyFactorAdapter

# ------------------ 福尔摩斯最终版 (核心任务) ------------------
# 论证：根据探测情报，ABuEnv 必须通过导入 'env' 模块来间接访问。
#       GridSearch 的位置已确认在 MetricsBu。
# 修正：采用全新的、经过情报证实的导入方式。
try:
    # 关键修正：从 CoreBu 导入 env 模块
    from abupy.CoreBu import env
    # GridSearch 的路径已确认
    from abupy.MetricsBu import GridSearch
except ImportError as e:
    logging.basicConfig(level=logging.ERROR)
    logging.error(f"无法从abupy的子模块导入核心类，任务将失败。错误: {e}", exc_info=True)
    env = None
    GridSearch = None
# ----------------------------------------------------------------

logger = logging.getLogger(__name__)

@celery_app.task(name="tasks.run_grid_search")
def run_grid_search(choice_symbols, buy_factors, sell_factors, read_cash):
    if env is None or GridSearch is None:
        msg = "核心依赖 env 模块或 GridSearch 类未能成功导入，任务无法执行。"
        logger.error(msg)
        return {"status": "FAILURE", "message": msg}

    try:
        logging.basicConfig(level=logging.INFO)
        logger.info(f"开始网格搜索任务: cash={read_cash}, symbols={choice_symbols}")

        # 关键修正：所有对 ABuEnv 的调用现在都通过 env 对象
        env.g_market_target = env.EMarketTargetType.E_MARKET_TARGET_US
        logger.info(f"设置市场目标为: {env.g_market_target}")

        logger.info("正在适配因子...")
        adapted_buy_factors = AbuPyFactorAdapter.adapt_factors(buy_factors)
        adapted_sell_factors = AbuPyFactorAdapter.adapt_factors(sell_factors)
        logger.info("因子适配完成。")

        logger.info("正在执行 GridSearch.grid_search 静态方法...")
        scores, score_tuple_array = GridSearch.grid_search(
            choice_symbols=choice_symbols,
            buy_factors=adapted_buy_factors,
            sell_factors=adapted_sell_factors,
            read_cash=read_cash
        )
        logger.info("网格搜索执行完毕。")

        if scores.empty:
            logger.warning("网格搜索完成，但未找到有效结果。")
            return {"status": "SUCCESS", "message": "Grid search completed, but no valid results found."}

        best_score_index = scores.index[-1]
        best_score_tuple = score_tuple_array[best_score_index]
        logger.info(f"找到最优结果，得分: {scores.iloc[-1]}")

        result = {
            "best_buy_factors": str(best_score_tuple.buy_factors),
            "best_sell_factors": str(best_score_tuple.sell_factors),
            "best_score": scores.iloc[-1],
            "all_scores": scores.to_dict()
        }
        return {"status": "SUCCESS", "result": result}

    except Exception as e:
        logger.error(f"网格搜索任务执行失败: {e}", exc_info=True)
        return {"status": "FAILURE", "message": str(e)}