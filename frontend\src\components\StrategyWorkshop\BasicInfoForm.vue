<template>
  <div class="basic-info-section">
    <h4>{{ UI_TEXT.BASIC_INFO }}</h4>
    <el-form :model="form" label-width="100px" class="basic-form compact">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="策略名称" required>
            <el-input 
              v-model="form.name" 
              placeholder="请输入策略名称"
              maxlength="50"
              show-word-limit
              data-testid="strategy-name-input"
              @input="handleUpdate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="UI_TEXT.PUBLIC_STRATEGY">
            <el-switch 
              v-model="form.is_public"
              @change="handleUpdate"
            />
            <span class="form-help">开启后其他用户也可以查看和使用此策略</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="策略描述">
            <el-input 
              v-model="form.description"
              type="textarea"
              placeholder="请描述您的策略逻辑、适用场景等（会根据因子自动生成）"
              :rows="3"
              maxlength="500"
              show-word-limit
              data-testid="strategy-description-input"
              @input="handleDescriptionInput"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import type { Strategy } from '@/api/types'
import { UI_TEXT } from '@/constants/strategy'
import { strategyUtils } from '@/composables/useStrategyEditor'

interface Props {
  strategy: Strategy
}

interface Emits {
  (e: 'update', updates: Partial<Strategy>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const form = ref({
  name: '',
  description: '',
  is_public: false
})

// 跟踪描述是否由用户手动编辑过
const isDescriptionManuallyEdited = ref(false)

// 监听策略变化，同步表单
watch(() => props.strategy, (strategy) => {
  if (strategy) {
    const newForm = {
      name: strategy.name || '',
      description: strategy.description || '',
      is_public: strategy.is_public || false
    }
    
    let shouldEmitUpdate = false
    
    // 如果描述为空或未手动编辑，自动生成描述
    if (!newForm.description || !isDescriptionManuallyEdited.value) {
      const autoDescription = strategyUtils.generateDescription(strategy)
      if (autoDescription !== newForm.description) {
        newForm.description = autoDescription
        shouldEmitUpdate = true
      }
      // 重置手动编辑标记
      isDescriptionManuallyEdited.value = false
    }
    
    form.value = newForm
    
    // 只有在描述真正发生变化时才发出更新事件
    if (shouldEmitUpdate && (!strategy.description || strategy.description !== newForm.description)) {
      // 使用nextTick确保form.value已经更新
      nextTick(() => {
        handleUpdate()
      })
    }
  }
}, { immediate: true, deep: true })

// 监听因子变化，自动更新描述
watch(() => [props.strategy?.buy_factors, props.strategy?.sell_factors], () => {
  if (props.strategy && !isDescriptionManuallyEdited.value) {
    const autoDescription = strategyUtils.generateDescription(props.strategy)
    if (autoDescription !== form.value.description) {
      form.value.description = autoDescription
      // 自动发出更新事件
      handleUpdate()
    }
  }
}, { deep: true })

// 处理更新
const handleUpdate = () => {
  emit('update', {
    name: form.value.name,
    description: form.value.description,
    is_public: form.value.is_public
  })
}

// 处理描述手动编辑
const handleDescriptionInput = () => {
  // 标记为手动编辑
  isDescriptionManuallyEdited.value = true
  // 发出更新事件
  handleUpdate()
}
</script>

<style scoped>
.basic-info-section {
  padding: var(--space-lg);
  background: var(--bg-color-secondary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color-light);
  margin-bottom: var(--space-config-cards-gap);
}

.basic-info-section h4 {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.basic-form {
  max-width: 100%;
}

.form-help {
  margin-left: var(--space-xs);
  color: var(--text-color-secondary);
  font-size: var(--font-size-xs);
}
</style>
