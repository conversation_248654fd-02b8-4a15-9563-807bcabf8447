#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载沪深300指数数据脚本
使用tushare数据源下载2021年全年的沪深300指数数据
"""

import os
import sys
import pandas as pd
import tushare as ts
from datetime import datetime

# 添加backend目录到路径，以便导入配置
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def load_tushare_token():
    """从环境变量或.env文件加载tushare token"""
    # 首先尝试从环境变量获取
    token = os.getenv('TUSHARE_TOKEN')
    
    if not token:
        # 尝试从backend/.env文件读取
        env_file = os.path.join(os.path.dirname(__file__), '..', 'backend', '.env')
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip().startswith('TUSHARE_TOKEN'):
                        token = line.split('=')[1].strip().strip('"')
                        break
    
    if not token:
        raise ValueError("未找到TUSHARE_TOKEN，请检查环境变量或backend/.env文件")
    
    return token

def download_hs300_data(start_date='20210101', end_date='20211231'):
    """下载沪深300指数数据"""
    print(f"正在下载沪深300指数数据: {start_date} 至 {end_date}")
    
    try:
        # 加载token并初始化tushare
        token = load_tushare_token()
        ts.set_token(token)
        pro = ts.pro_api()
        
        print(f"使用token: {token[:10]}...")
        
        # 下载沪深300指数数据
        # ts_code: 000300.SH 是沪深300指数的代码
        print("正在调用tushare API...")
        df = pro.index_daily(
            ts_code='000300.SH',
            start_date=start_date,
            end_date=end_date
        )
        
        if df.empty:
            print("警告: 未获取到任何数据")
            return None
        
        print(f"成功获取 {len(df)} 条数据")
        
        # 数据预处理
        # 按日期排序（从早到晚）
        df = df.sort_values('trade_date')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        # 显示数据信息
        print("\n数据概览:")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print(f"日期范围: {df['trade_date'].min()} 至 {df['trade_date'].max()}")
        
        print("\n前5行数据:")
        print(df.head())
        
        print("\n后5行数据:")
        print(df.tail())
        
        # 保存为CSV文件
        output_file = os.path.join(os.path.dirname(__file__), 'hs300_2021.csv')
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n数据已保存到: {output_file}")
        
        # 显示保存的文件信息
        file_size = os.path.getsize(output_file)
        print(f"文件大小: {file_size:,} 字节")
        
        return df
        
    except Exception as e:
        print(f"下载数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("沪深300指数数据下载脚本")
    print("=" * 50)
    
    # 检查tushare是否已安装
    try:
        import tushare as ts
        print(f"tushare版本: {ts.__version__}")
    except ImportError:
        print("错误: 未安装tushare库")
        print("请运行: pip install tushare")
        return
    
    # 下载数据
    df = download_hs300_data()
    
    if df is not None:
        print("\n=" * 50)
        print("数据下载完成！")
        print("=" * 50)
        
        # 显示一些统计信息
        print("\n数据统计:")
        print(f"交易日数量: {len(df)}")
        print(f"开盘价范围: {df['open'].min():.2f} - {df['open'].max():.2f}")
        print(f"收盘价范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"最高价: {df['high'].max():.2f}")
        print(f"最低价: {df['low'].min():.2f}")
        print(f"平均成交量: {df['vol'].mean():.0f}")
        
    else:
        print("\n数据下载失败！")

if __name__ == '__main__':
    main()