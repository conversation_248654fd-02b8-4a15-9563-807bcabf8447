# abu_modern 前端UI布局技术规范 (UI Layout Specification V1.1)

## 1. 核心设计原则

- 一致性 (Consistency): 相似的元素应有相似的外观和行为。
- 对齐 (Alignment): 所有元素都应在不可见的网格上对齐，创造秩序感。
- 留白 (Whitespace): 慷慨地使用留白来组织内容，降低认知负荷。
- 层次感 (Hierarchy): 通过大小、粗细、颜色，清晰地展示信息的主次关系。

## 2. 基础栅格与间距系统 (Grid & Spacing)

- 基础单位 (Base Unit): 8px。所有的间距、边距、填充都应为 8px 的整数倍。
- 栅格系统 (ElRow / ElCol): 遵循 Element Plus 的 24 栅格系统。
- 列间距 (gutter): 标准列间距应设置为 var(--space-md) (`:gutter="16"`) 或 var(--space-lg) (`:gutter="24"`).

- 标准间距令牌 (Spacing Tokens):
  - `--space-xs: 4px` (0.5x) - 用于元素内部极小的间距。
  - `--space-sm: 8px` (1x) - 用于小组件之间，或组件内部。
  - `--space-md: 16px` (2x) - 最常用，用于卡片内边距、表单项之间。
  - `--space-lg: 24px` (3x) - 用于大模块之间，或页面的主要分区。
  - `--space-xl: 32px` (4x) - 用于页面的顶部和底部外边距。

**【V1.1新增】CSS变量冲突解决方案：**

### 2.1 注意事项

**JavaScript优先原则：**
- 当需要动态改变布局尺寸（如侧边栏宽度）时，应**优先考虑通过切换CSS类的方式**，而不是在JavaScript中直接`setProperty`来覆盖CSS变量
- **目的**：避免优先级冲突，确保CSS变量系统的一致性
- **推荐做法**：
```javascript
// ❌ 不推荐：直接覆盖CSS变量
appLayout.style.setProperty('--sidebar-width', isCollapsed.value ? 'var(--sidebar-width-collapsed)' : 'var(--sidebar-width)')

// ✅ 推荐：使用CSS类切换
appLayout.classList.toggle('sidebar-collapsed', isCollapsed.value)
```

**!important使用规范：**
- 应**谨慎使用**`!important`声明
- **特别注意**：在`_utilities.scss`中，如果工具类使用了`!important`，可能会覆盖组件内的变量定义
- **冲突识别**：当CSS变量无法生效时，检查是否存在`!important`工具类覆盖
- **解决方案**：
  - 提高CSS优先级：为关键属性添加`!important`
  - 移除冲突工具类：检查并移除可能使用冲突工具类的元素
  - 创建专用工具类：为特定组件创建专用的样式类

### 2.2 CSS变量优先级管理
```scss
// 示例：处理gap属性的优先级冲突
.workshop-layout {
  gap: var(--space-workshop-gap, var(--space-lg)) !important; // 必要时使用!important
}

// 或者通过更具体的选择器提高优先级
.strategy-workshop .workshop-layout {
  gap: var(--space-workshop-gap, var(--space-lg));
}
```

## 3. 整体页面布局

### 主布局 (DefaultLayout.vue)

- 顶栏 (ElHeader) 高度: var(--header-height)。
- **【V1.1更新】侧边栏 (ElAside) 宽度**: **var(--sidebar-width)** (展开时)，var(--sidebar-width-collapsed) (折叠时)。
- 主内容区 (ElMain):
  - 内边距 (padding): var(--space-lg)。

**【V1.1新增】Header优化规范：**

### 3.1 Header自动隐藏交互规范
基于实践验证，增加以下Header交互规范：

- **自动隐藏机制**：页面header支持自动隐藏功能，提升内容展示空间
- **悬停显示**：鼠标悬停时智能显示，提供平滑动画效果
- **延迟隐藏**：实现延迟隐藏机制，避免快速移动时的闪烁
- **视觉反馈**：隐藏/显示过程应有平滑的过渡动画

```css
/* 【V1.1新增】Header自动隐藏实现示例 */
.app-header {
  transition: transform 0.3s ease-in-out;
  transform: translateY(0);
}

.app-header.auto-hide {
  transform: translateY(-100%);
}

.app-header:hover,
.app-header.show-on-hover {
  transform: translateY(0);
}
```

### 页面标题 (ElPageHeader 或自定义标题)

- 应作为每个视图 (`.vue` 文件位于 `/views`) 的第一个元素。
- 主标题 (font-size): 20px，font-weight: 500。
- 副标题/描述 (font-size): 14px，颜色: `var(--el-text-color-secondary)`。
- 与下方内容的间距 (margin-bottom): var(--space-lg)。

## 4. 卡片 (ElCard) 布局规范

- 标准用法: 用于包裹一个独立的功能或信息模块。
- 外边距 (margin): 卡片之间不应互相设置外边距，应由其父级的栅格 gutter 或 flex/grid 的 gap 来控制间距。
- 内边距 (padding):
  - 卡片头部 (`.el-card__header`): `padding: var(--space-md) 20px;`
  - 卡片主体 (`.el-card__body`): `padding: 20px;`
- 标题 (header slot):
  - 标题文字 (font-size): 16px，font-weight: 500。
  - 标题应与卡片左侧对齐。
  - 如果头部有操作按钮（如"+ 添加"），按钮应右对齐。

示例：

```html
<div class="card-header">
  <span>模块标题</span>
  <el-button type="primary" size="small">+ 添加</el-button>
</div>
```

```css
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
```

## 5. 表单 (ElForm) 布局规范

- 标签位置 (label-position): 统一使用 top，提供清晰的垂直阅读流，适配不同宽度的屏幕。
- 表单项间距 (margin-bottom): 每个 ElFormItem 的下外边距应为 18px（Element Plus 默认）或统一为 var(--space-md)。
- 控件宽度: 表单内的输入控件（ElInput、ElSelect 等）宽度默认为 100%，占据其所在的栅格列。
- 操作按钮组:
  - 表单底部的"保存""取消"等按钮，应右对齐。
  - 主操作按钮（如"保存"）应为 `type="primary"`。
  - 按钮之间应有 var(--space-sm) 的间距。

## 6. 两栏式布局规范（如"策略工场"）

- 左侧列表栏:
  - 宽度: 300px 到 360px 之间，具体取决于内容。
  - 与右侧编辑器之间应有清晰的分隔线（`border-right`）或 var(--space-lg) 的间距。
- 右侧内容/编辑器栏:
  - 占据剩余的全部宽度（`flex: 1`）。
- 滚动行为:
  - 必须采用"左右分栏各自独立滚动"的设计。
  - 外层容器应设置固定高度（如 `height: calc(100vh - ...)`）并设置 `overflow: hidden`。
  - 左右两栏应设置 `overflow-y: auto`，各自管理自己的滚动条。

**【V1.1新增】两栏布局边距管理规范：**

### 6.1 间距变量统一管理
- **所有间距值**应使用CSS变量定义，避免硬编码
- **变量命名规范**：使用语义化的变量名，如`--space-workshop-gap`、`--space-config-cards-gap`
- **优先级处理**：当遇到样式冲突时，优先检查是否有工具类覆盖

### 6.2 布局约束处理
```css
/* 【V1.1新增】处理固定宽度约束的示例 */
.workshop-left-panel {
  width: 320px;
  flex-shrink: 0; /* 防止收缩，保持固定宽度 */
}

.workshop-right-panel {
  flex: 1;
  margin-left: var(--space-workshop-gap, var(--space-lg)); /* 使用变量而非固定值 */
}
```

## 7. 对话框 (ElDialog) 布局规范

- 标准宽度:
  - 小 (small): 400px（用于简单的确认、提示）。
  - 中 (medium): 600px（最常用，用于简单的表单）。
  - 大 (large): 800px（用于复杂的表单或内容展示）。
- 页脚 (footer slot):
  - 按钮组必须右对齐。
  - 标准顺序: [ 取消 ] [ 确认 ]（主操作在右）。

**【V1.1新增】8. CSS变量系统最佳实践**

### 8.1 变量定义层级
```scss
// 全局变量定义（_spacing.scss）
:root {
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  
  // 【V1.1新增】语义化布局变量
  --sidebar-width: 200px;
  --sidebar-width-collapsed: 64px;
  --header-height: 60px;
  --space-workshop-gap: var(--space-lg);
  --space-config-cards-gap: 30px;
}
```

### 8.2 变量使用规范
```css
/* ✅ 推荐：使用CSS变量 */
.component {
  margin-bottom: var(--space-lg);
  gap: var(--space-workshop-gap);
}

/* ❌ 不推荐：硬编码数值 */
.component {
  margin-bottom: var(--space-lg);
  gap: var(--space-config-cards-gap);
}
```

### 8.3 动态样式管理
```javascript
// ✅ 推荐：通过CSS类控制状态
element.classList.toggle('collapsed', isCollapsed)

// ❌ 不推荐：直接修改CSS变量
element.style.setProperty('--width', newWidth)
```

**【V1.1新增】9. 样式冲突诊断与解决**

### 9.1 常见冲突场景
1. **工具类覆盖**：`_utilities.scss`中的`!important`声明覆盖组件样式
2. **作用域限制**：scoped样式无法访问全局CSS变量
3. **优先级冲突**：JavaScript直接设置的样式覆盖CSS变量

### 9.2 诊断步骤
1. **检查样式作用域**：确认样式是否在正确的作用域中定义
2. **验证变量访问**：确保CSS变量在当前作用域中可访问
3. **排查优先级冲突**：检查是否有`!important`或内联样式覆盖
4. **审查工具类使用**：确认是否有冲突的工具类应用

### 9.3 解决方案模板
```css
/* 解决方案1：提高选择器优先级 */
.specific-container .target-element {
  property: var(--css-variable) !important;
}

/* 解决方案2：创建专用工具类 */
.workshop-gap-override {
  gap: var(--space-workshop-gap) !important;
}

/* 解决方案3：使用更具体的变量 */
.component {
  margin: var(--component-specific-margin, var(--space-lg));
}
```