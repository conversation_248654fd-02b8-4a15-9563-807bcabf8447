import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import BacktestAnalysis from '@/components/BacktestAnalysis.vue'
import type { BacktestResult } from '@/api/types/backtest'

describe('BacktestAnalysis.vue', () => {
  let wrapper: VueWrapper<any>

  const mockBacktestResult: BacktestResult = {
    task_id: 'test-task-001',
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 125000,
    total_return: 0.25,
    sharpe_ratio: 1.5,
    max_drawdown: 0.08,
    trades: [
      { id: '1', symbol: '000001.SZ', side: 'buy', quantity: 100, price: 10.5, timestamp: '2023-01-01T10:00:00Z' },
      { id: '2', symbol: '000001.SZ', side: 'sell', quantity: 100, price: 12.0, timestamp: '2023-06-01T10:00:00Z' },
      { id: '3', symbol: '000001.SZ', side: 'buy', quantity: 200, price: 11.0, timestamp: '2023-07-01T10:00:00Z' }
    ],
    daily_returns: [0.01, -0.005, 0.02, -0.01, 0.015, 0.008, -0.003]
  }

  beforeEach(() => {
    wrapper = mount(BacktestAnalysis, {
      props: {
        result: mockBacktestResult
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染组件标题', () => {
      expect(wrapper.find('h2').text()).toBe('深度分析')
    })

    it('应该渲染所有标签页按钮', () => {
      expect(wrapper.find('[data-testid="tab-performance"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="tab-trades"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="tab-risk"]').exists()).toBe(true)
    })

    it('默认应该激活性能分析标签页', () => {
      const performanceTab = wrapper.find('[data-testid="tab-performance"]')
      expect(performanceTab.classes()).toContain('active')
    })

    it('应该显示性能分析内容', () => {
      expect(wrapper.text()).toContain('风险收益指标')
      expect(wrapper.text()).toContain('收益分布')
    })
  })

  describe('标签页切换测试', () => {
    it('点击交易分析标签应该切换到交易分析', async () => {
      await wrapper.find('[data-testid="tab-trades"]').trigger('click')
      await nextTick()

      expect(wrapper.find('[data-testid="tab-trades"]').classes()).toContain('active')
      expect(wrapper.text()).toContain('交易统计')
      expect(wrapper.text()).toContain('交易记录')
    })

    it('点击风险分析标签应该切换到风险分析', async () => {
      await wrapper.find('[data-testid="tab-risk"]').trigger('click')
      await nextTick()

      expect(wrapper.find('[data-testid="tab-risk"]').classes()).toContain('active')
      expect(wrapper.text()).toContain('风险指标')
      expect(wrapper.text()).toContain('回撤曲线')
    })

    it('切换标签页时应该更新active状态', async () => {
      // 初始状态
      expect(wrapper.find('[data-testid="tab-performance"]').classes()).toContain('active')
      
      // 切换到交易分析
      await wrapper.find('[data-testid="tab-trades"]').trigger('click')
      await nextTick()
      
      expect(wrapper.find('[data-testid="tab-performance"]').classes()).not.toContain('active')
      expect(wrapper.find('[data-testid="tab-trades"]').classes()).toContain('active')
    })
  })

  describe('性能分析测试', () => {
    it('应该显示所有风险收益指标', () => {
      expect(wrapper.find('[data-testid="annual-return"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="annual-volatility"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="sharpe-ratio"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="max-drawdown"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="calmar-ratio"]').exists()).toBe(true)
    })

    it('应该正确显示夏普比率', () => {
      expect(wrapper.find('[data-testid="sharpe-ratio"]').text()).toBe('1.500')
    })

    it('应该正确显示最大回撤', () => {
      expect(wrapper.find('[data-testid="max-drawdown"]').text()).toBe('8.00%')
    })

    it('应该显示收益分布图表', () => {
      expect(wrapper.find('[data-testid="returns-chart"]').exists()).toBe(true)
    })
  })

  describe('交易分析测试', () => {
    beforeEach(async () => {
      await wrapper.find('[data-testid="tab-trades"]').trigger('click')
      await nextTick()
    })

    it('应该显示交易统计信息', () => {
      expect(wrapper.find('[data-testid="total-trades"]').text()).toBe('3')
      expect(wrapper.find('[data-testid="winning-trades"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="losing-trades"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="win-rate"]').exists()).toBe(true)
    })

    it('应该显示交易记录表格', () => {
      expect(wrapper.find('[data-testid="trades-table"]').exists()).toBe(true)
      
      const rows = wrapper.findAll('tbody tr')
      expect(rows.length).toBe(3) // 3条交易记录
    })

    it('应该正确显示交易方向', () => {
      const tradeSides = wrapper.findAll('.trade-side')
      expect(tradeSides[0].text()).toBe('买入')
      expect(tradeSides[0].classes()).toContain('buy')
      expect(tradeSides[1].text()).toBe('卖出')
      expect(tradeSides[1].classes()).toContain('sell')
    })

    it('胜率应该正确计算和显示', () => {
      const winRate = wrapper.find('[data-testid="win-rate"]').text()
      expect(winRate).toMatch(/\d+\.\d+%/) // 应该是百分比格式
    })
  })

  describe('风险分析测试', () => {
    beforeEach(async () => {
      await wrapper.find('[data-testid="tab-risk"]').trigger('click')
      await nextTick()
    })

    it('应该显示风险指标', () => {
      expect(wrapper.find('[data-testid="var-95"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="max-consecutive-loss"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="drawdown-duration"]').exists()).toBe(true)
    })

    it('应该显示回撤曲线图表', () => {
      expect(wrapper.find('[data-testid="drawdown-chart"]').exists()).toBe(true)
    })

    it('VaR应该显示为百分比格式', () => {
      const var95 = wrapper.find('[data-testid="var-95"]').text()
      expect(var95).toMatch(/-?\d+\.\d+%/)
    })

    it('最大连续亏损应该显示数字', () => {
      const maxLoss = wrapper.find('[data-testid="max-consecutive-loss"]').text()
      expect(maxLoss).toMatch(/\d+/)
    })

    it('回撤持续时间应该显示天数', () => {
      const duration = wrapper.find('[data-testid="drawdown-duration"]').text()
      expect(duration).toContain('天')
    })
  })

  describe('计算指标测试', () => {
    it('应该正确计算年化收益率', () => {
      // 年化收益率 = 总收益率 * (365 / 交易天数)
      const annualReturn = wrapper.find('[data-testid="annual-return"]').text()
      expect(annualReturn).toMatch(/\d+\.\d+%/)
    })

    it('应该正确计算年化波动率', () => {
      const volatility = wrapper.find('[data-testid="annual-volatility"]').text()
      expect(volatility).toMatch(/\d+\.\d+%/)
    })

    it('应该正确计算卡尔玛比率', () => {
      const calmar = wrapper.find('[data-testid="calmar-ratio"]').text()
      expect(calmar).toMatch(/\d+\.\d+/)
    })
  })

  describe('无数据处理测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: null })
    })

    it('无结果时应该显示默认值', () => {
      expect(wrapper.find('[data-testid="sharpe-ratio"]').text()).toBe('0.000')
      expect(wrapper.find('[data-testid="max-drawdown"]').text()).toBe('0.00%')
    })

    it('无交易记录时应该显示0', async () => {
      await wrapper.find('[data-testid="tab-trades"]').trigger('click')
      await nextTick()
      
      expect(wrapper.find('[data-testid="total-trades"]').text()).toBe('0')
    })
  })

  describe('格式化函数测试', () => {
    it('应该正确格式化百分比', () => {
      const percentage = wrapper.find('[data-testid="max-drawdown"]').text()
      expect(percentage).toBe('8.00%')
    })

    it('应该正确格式化数字', () => {
      const number = wrapper.find('[data-testid="sharpe-ratio"]').text()
      expect(number).toBe('1.500')
    })
  })

  describe('边界情况测试', () => {
    it('交易数组为空时应该正确处理', async () => {
      const resultWithoutTrades = { ...mockBacktestResult, trades: [] }
      await wrapper.setProps({ result: resultWithoutTrades })
      await wrapper.find('[data-testid="tab-trades"]').trigger('click')
      await nextTick()
      
      expect(wrapper.find('[data-testid="total-trades"]').text()).toBe('0')
      expect(wrapper.findAll('tbody tr').length).toBe(0)
    })

    it('日收益率数组为空时应该正确处理', async () => {
      const resultWithoutReturns = { ...mockBacktestResult, daily_returns: [] }
      await wrapper.setProps({ result: resultWithoutReturns })
      
      expect(wrapper.find('[data-testid="annual-volatility"]').text()).toBe('0.00%')
    })

    it('最大回撤为0时卡尔玛比率应该为0', async () => {
      const resultWithoutDrawdown = { ...mockBacktestResult, max_drawdown: 0 }
      await wrapper.setProps({ result: resultWithoutDrawdown })
      
      expect(wrapper.find('[data-testid="calmar-ratio"]').text()).toBe('0.000')
    })
  })

  describe('样式和布局测试', () => {
    it('应该具有正确的CSS类', () => {
      expect(wrapper.find('.backtest-analysis').exists()).toBe(true)
      expect(wrapper.find('.analysis-header').exists()).toBe(true)
      expect(wrapper.find('.analysis-tabs').exists()).toBe(true)
    })

    it('统计卡片应该使用网格布局', async () => {
      await wrapper.find('[data-testid="tab-trades"]').trigger('click')
      await nextTick()
      
      expect(wrapper.find('.stats-grid').exists()).toBe(true)
    })

    it('风险卡片应该使用网格布局', async () => {
      await wrapper.find('[data-testid="tab-risk"]').trigger('click')
      await nextTick()
      
      expect(wrapper.find('.risk-grid').exists()).toBe(true)
    })
  })

  describe('可访问性测试', () => {
    it('标签页按钮应该有正确的data-testid', () => {
      expect(wrapper.find('[data-testid="tab-performance"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="tab-trades"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="tab-risk"]').exists()).toBe(true)
    })

    it('重要指标应该有data-testid', () => {
      expect(wrapper.find('[data-testid="annual-return"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="sharpe-ratio"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="max-drawdown"]').exists()).toBe(true)
    })

    it('图表容器应该有data-testid', () => {
      expect(wrapper.find('[data-testid="returns-chart"]').exists()).toBe(true)
    })
  })
})