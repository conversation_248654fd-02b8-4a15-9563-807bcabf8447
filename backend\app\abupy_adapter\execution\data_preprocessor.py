import logging
import pandas as pd
import numpy as np
import talib
from app.schemas.market import KlineData

def kline_data_to_dataframe(kline_data: KlineData) -> pd.DataFrame:
    """
    将 KlineData Pydantic 模型转换为 abupy 期望的 DataFrame 格式。
    这个函数现在被高度加固，以防止静默失败。
    """
    if not kline_data or not kline_data.data:
        logging.warning(f"输入到 kline_data_to_dataframe 的 KlineData 为空或没有kline列表。")
        return pd.DataFrame()

    symbol_for_log = kline_data.symbol

    try:
        # 1. 初始转换
        df = pd.DataFrame([item.model_dump() for item in kline_data.data])
        if df.empty:
            logging.warning(f"从 KlineData (symbol: {symbol_for_log}) 创建的初始DataFrame为空。")
            return pd.DataFrame()

        logging.info(f"[{symbol_for_log}] 初始DataFrame创建成功，行数: {len(df)}。列: {df.columns.tolist()}")

        # 2. 核心数据类型和索引转换 (最容易出错的地方)
        # 确保列名是abupy期望的
        # 注意：vol到volume的重命名应该在上游的 kline_provider 中完成，这里只做断言检查
        assert 'volume' in df.columns, f"DataFrame for {symbol_for_log} 缺少'volume'列！"
        assert 'date' in df.columns, f"DataFrame for {symbol_for_log} 缺少'date'列！"

        # 关键：转换日期并设置为索引，这是最常见的失败点
        df['date'] = pd.to_datetime(df['date']) # 假设上游已确保是标准格式
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)

        # 3. 添加abupy期望的衍生列
        df['pre_close'] = df['close'].shift(1)
        df['p_change'] = (df['close'] / df['pre_close'] - 1) * 100
        df['date_week'] = df.index.dayofweek

        # 计算ATR等指标
        tr1 = df['high'] - df['low']
        tr2 = np.abs(df['high'] - df['pre_close'])
        tr3 = np.abs(df['low'] - df['pre_close'])
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1, skipna=True)
        df['atr21'] = talib.ATR(df.high.values, df.low.values, df.close.values, timeperiod=21)
        df['atr14'] = talib.ATR(df.high.values, df.low.values, df.close.values, timeperiod=14)
        df['key'] = range(len(df))

        # --- 决胜修改 ---
        # abupy的某些模块(如AbuCapital)需要一个名为'date'的列，即使日期已经是索引
        # 我们将索引复制为新的一列来满足这个要求
        df['date'] = df.index.strftime('%Y%m%d').astype(int)

        # 最终填充NaN
        df.fillna(0, inplace=True)

        # 4. 最终检查
        if df.empty:
            logging.error(f"[{symbol_for_log}] 在处理后，DataFrame意外变为空！")
        else:
            logging.info(f"[{symbol_for_log}] DataFrame处理完成，返回非空DataFrame。")

        # --- ★★★ 总攻核心 ★★★ ---
        # abupy期望DataFrame带有一个name属性，内容是其符号
        # 我们从输入的KlineData对象中获取符号并赋值
        df.name = kline_data.symbol
        # --- ★★★★★★★★★★★★★ ---

        return df

    except Exception as e:
        # 关键：捕获任何异常，记录详细信息，然后重新抛出
        logging.error(
            f"在 kline_data_to_dataframe 处理符号 {symbol_for_log} 时发生致命错误: {e}",
            exc_info=True  # 这会打印完整的堆栈跟踪
        )
        # 在这种关键路径上，抛出异常比返回空DF更明确
        raise ValueError(f"处理 {symbol_for_log} 数据失败") from e