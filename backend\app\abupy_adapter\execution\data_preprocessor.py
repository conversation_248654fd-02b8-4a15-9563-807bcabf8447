import pandas as pd
from app.schemas.market import KlineData

def kline_data_to_dataframe(kline_data: KlineData | None) -> pd.DataFrame:
    """
    将KlineData Pydantic模型转换为适用于abupy的DataFrame。
    - 包含入口处的先决条件检查。
    - 设置DatetimeIndex。
    - 计算必要的技术指标（p_change, atr21等）。
    - 将symbol赋给DataFrame的name属性。
    - 包含健壮的异常处理。
    """
    if kline_data is None or not kline_data.data:
        return pd.DataFrame()

    symbol = kline_data.symbol
    try:
        df = pd.DataFrame([item.model_dump() for item in kline_data.data])
        if df.empty:
            df.name = symbol
            return df

        # 关键修正: 主动进行先决条件检查
        required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
        # 使用集合操作找到缺失的列，更高效
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            raise ValueError(f"输入数据缺少必需的列: {sorted(list(missing_columns))}")

        df['date'] = pd.to_datetime(df['date'])
        df = df.set_index('date')
        # 关键修复：确保数据按日期升序排列，避免Abu择时逻辑异常
        df = df.sort_index()

        # 计算abupy需要的基础列
        df['p_change'] = df['close'].pct_change() * 100
        
        # 计算ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        df['atr21'] = tr.rolling(window=21).mean()
        
        df['date_week'] = df.index.dayofweek
        df['key'] = df.index.strftime('%Y%m%d')
        df['date'] = df.index

        # 修复：避免对价格列填充0，使用前向填充保持价格数据完整性
        price_cols = ['open', 'high', 'low', 'close', 'pre_close']
        for col in price_cols:
            if col in df.columns:
                df[col] = df[col].ffill()  # 前向填充价格列
        
        # 对重要的交易指标使用前向填充，避免填充0影响交易逻辑
        important_cols = ['volume', 'amount', 'turnover_rate']
        for col in important_cols:
            if col in df.columns:
                df[col] = df[col].ffill()  # 前向填充重要指标
        
        # 其他非关键列可以填充0
        other_cols = [col for col in df.columns if col not in price_cols and col not in important_cols]
        for col in other_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0)
        
        df = df.infer_objects(copy=False)
        df.name = symbol

        return df

    except Exception as e:
        raise ValueError(f"处理 {symbol} 数据失败: {e}") from e
