"""
因子服务单元测试模块

测试因子服务(FactorService)的功能，主要是获取可用因子列表。
"""

import unittest
from unittest.mock import patch, MagicMock
from backend.app.services.factor_service import FactorService
from backend.app.core.exceptions import AdapterError

class TestFactorService(unittest.TestCase):
    """测试因子服务类"""

    @patch('backend.app.services.factor_service.StrategyAdapter.get_available_abu_factors')
    def test_get_available_factors_success(self, mock_get_factors):
        """测试成功获取可用因子列表"""
        # 模拟 StrategyAdapter 返回的数据
        mock_factors_data = [
            {'id': 'buy_1', 'name': 'Buy Factor 1', 'class_name': 'BF1', 'factor_type': 'buy', 'parameters': {}},
            {'id': 'sell_1', 'name': 'Sell Factor 1', 'class_name': 'SF1', 'factor_type': 'sell', 'parameters': {}},
        ]
        mock_get_factors.return_value = mock_factors_data

        # 调用服务
        factor_list = FactorService.get_available_factors()

        # 断言
        self.assertEqual(len(factor_list.buy_factors), 1)
        self.assertEqual(len(factor_list.sell_factors), 1)
        self.assertEqual(factor_list.buy_factors[0].name, 'Buy Factor 1')
        self.assertEqual(factor_list.sell_factors[0].name, 'Sell Factor 1')
        mock_get_factors.assert_called_once_with(factor_type=None)

    @patch('backend.app.services.factor_service.StrategyAdapter.get_available_abu_factors')
    def test_get_available_factors_adapter_error(self, mock_get_factors):
        """测试当Adapter层抛出异常时，服务层是否正确传递异常"""
        # 模拟 StrategyAdapter 抛出异常
        mock_get_factors.side_effect = AdapterError("Failed to load factors")

        # 断言服务层会重新抛出 AdapterError
        with self.assertRaises(AdapterError):
            FactorService.get_available_factors()


if __name__ == "__main__":
    unittest.main()
