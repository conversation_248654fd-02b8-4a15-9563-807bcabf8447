"""
因子服务单元测试模块

测试因子服务(FactorService)的功能，主要是获取可用因子列表。
"""

import unittest
from backend.app.services.factor_service import FactorService


class TestFactorService(unittest.TestCase):
    """测试因子服务类"""
    
    def test_get_available_factors(self):
        """测试获取可用因子列表功能"""
        # 获取可用因子列表
        factor_list = FactorService.get_available_factors()
        
        # 验证返回的对象类型正确
        self.assertIsNotNone(factor_list)
        
        # 验证买入因子列表不为空
        self.assertIsNotNone(factor_list.buy_factors)
        self.assertTrue(len(factor_list.buy_factors) > 0)
        
        # 验证卖出因子列表不为空
        self.assertIsNotNone(factor_list.sell_factors)
        self.assertTrue(len(factor_list.sell_factors) > 0)
        
        # 验证买入因子的属性
        for buy_factor in factor_list.buy_factors:
            self.assertIsNotNone(buy_factor.id)
            self.assertIsNotNone(buy_factor.name)
            self.assertEqual(buy_factor.factor_type, "buy")
            self.assertIsNotNone(buy_factor.factor_class)
            self.assertIsNotNone(buy_factor.parameters)
        
        # 验证卖出因子的属性
        for sell_factor in factor_list.sell_factors:
            self.assertIsNotNone(sell_factor.id)
            self.assertIsNotNone(sell_factor.name)
            self.assertEqual(sell_factor.factor_type, "sell")
            self.assertIsNotNone(sell_factor.factor_class)
            self.assertIsNotNone(sell_factor.parameters)
        
        # 特别验证几个预期的因子
        buy_factor_names = [f.name for f in factor_list.buy_factors]
        self.assertIn("示例买入因子1", buy_factor_names)
        self.assertIn("突破买入", buy_factor_names)
        
        sell_factor_names = [f.name for f in factor_list.sell_factors]
        self.assertIn("示例卖出因子1", sell_factor_names)
        self.assertIn("超时卖出", sell_factor_names)


if __name__ == "__main__":
    unittest.main()
