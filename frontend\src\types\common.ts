/**
 * 通用类型定义
 * 跨模块共享的基础类型和工具类型
 */

// API 响应包装类型
export interface ApiResponse<T> {
  data: T
  success: boolean
  message: string | null
  total?: number
  page?: number
  page_size?: number
}

// 分页参数接口
export interface PaginationParams {
  page?: number
  page_size?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 分页响应接口
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  page_size: number
  has_next: boolean
  has_prev: boolean
}

// 通用查询参数接口
export interface QueryParams extends PaginationParams {
  search?: string
  filters?: Record<string, any>
}

// 错误响应接口
export interface ErrorResponse {
  success: false
  message: string
  code?: string
  details?: any
}

// 成功响应接口
export interface SuccessResponse<T = any> {
  success: true
  data: T
  message?: string
}

// 加载状态接口
export interface LoadingState {
  isLoading: boolean
  error: string | null
}

// 表单验证规则接口
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  message?: string
  validator?: (value: any) => boolean | string
}

// 表单字段配置接口
export interface FormFieldConfig {
  name: string
  label: string
  type: 'text' | 'number' | 'select' | 'checkbox' | 'radio' | 'textarea' | 'date' | 'datetime'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: ValidationRule[]
  default?: any
  readonly?: boolean
  visible?: boolean
  dependencies?: string[]
}

// 表单配置接口
export interface FormConfig {
  fields: FormFieldConfig[]
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelWidth?: string
  submitText?: string
  resetText?: string
}

// 选择器选项接口
export interface SelectOption<T = any> {
  label: string
  value: T
  disabled?: boolean
  children?: SelectOption<T>[]
}

// 树形节点接口
export interface TreeNode<T = any> {
  id: string | number
  label: string
  data?: T
  children?: TreeNode<T>[]
  parent?: TreeNode<T>
  level?: number
  expanded?: boolean
  selected?: boolean
  disabled?: boolean
  icon?: string
}

// 菜单项接口
export interface MenuItem {
  id: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  meta?: Record<string, any>
  permissions?: string[]
  visible?: boolean
}

// 权限接口
export interface Permission {
  id: string
  name: string
  code: string
  description?: string
  module: string
  actions: string[]
}

// 用户角色接口
export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: Permission[]
  is_system?: boolean
}

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  nickname?: string
  avatar?: string
  roles: Role[]
  last_login?: string
  created_at: string
  updated_at?: string
  is_active: boolean
}

// 系统配置接口
export interface SystemConfig {
  app_name: string
  app_version: string
  api_base_url: string
  upload_max_size: number
  supported_file_types: string[]
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  features: Record<string, boolean>
}

// 通知消息接口
export interface NotificationMessage {
  id: string
  type: 'success' | 'info' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
  timestamp: string
  read?: boolean
}

// 日志记录接口
export interface LogRecord {
  id: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  timestamp: string
  module?: string
  user_id?: string
  ip_address?: string
  user_agent?: string
  extra_data?: Record<string, any>
}

// 文件上传接口
export interface FileUpload {
  id: string
  filename: string
  original_name: string
  mime_type: string
  size: number
  url: string
  thumbnail_url?: string
  upload_time: string
  uploader_id?: string
}

// 导出配置接口
export interface ExportConfig {
  format: 'xlsx' | 'csv' | 'pdf' | 'json'
  filename?: string
  fields?: string[]
  filters?: Record<string, any>
  template?: string
}

// 导入配置接口
export interface ImportConfig {
  file_type: 'xlsx' | 'csv' | 'json'
  has_header: boolean
  field_mapping: Record<string, string>
  validation_rules?: Record<string, ValidationRule[]>
}

// 缓存配置接口
export interface CacheConfig {
  key: string
  ttl: number // 生存时间（秒）
  tags?: string[]
  dependency?: string[]
}

// 任务状态接口
export interface TaskStatus {
  id: string
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number // 0-100
  message?: string
  started_at?: string
  completed_at?: string
  error_message?: string
  result?: any
}

// 通用工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type Required<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>
export type Nullable<T> = T | null
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}
export type KeyOf<T> = keyof T
export type ValueOf<T> = T[keyof T]
export type RecordKey = string | number | symbol

// 事件类型
export type EventHandler<T = any> = (event: T) => void
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>

// 组件Props类型辅助
export type ComponentProps<T> = T extends new (...args: any) => infer R ? R['$props'] : never
export type ComponentEmits<T> = T extends new (...args: any) => infer R ? R['$emit'] : never
export type ComponentSlots<T> = T extends new (...args: any) => infer R ? R['$slots'] : never

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'

// 请求配置接口
export interface RequestConfig {
  method?: HttpMethod
  url: string
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer'
}

// 响应接口
export interface ResponseData<T = any> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
  config: RequestConfig
}

// 国际化类型
export interface I18nMessage {
  [key: string]: string | I18nMessage
}

export interface I18nConfig {
  locale: string
  fallbackLocale: string
  messages: Record<string, I18nMessage>
}

// 主题配置接口
export interface ThemeConfig {
  name: string
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  textColor: string
  borderColor: string
  shadowColor: string
  borderRadius: string
  fontSize: Record<string, string>
  spacing: Record<string, string>
}

// 布局配置接口
export interface LayoutConfig {
  header: {
    height: string
    fixed: boolean
    visible: boolean
  }
  sidebar: {
    width: string
    collapsedWidth: string
    collapsed: boolean
    visible: boolean
  }
  footer: {
    height: string
    fixed: boolean
    visible: boolean
  }
  content: {
    padding: string
  }
}

// 重新导出API类型，保持向后兼容性
export type {
  Factor,
  FactorParameter,
  FactorInstance,
  BuyFactorInstance,
  SellFactorInstance,
  Strategy,
  CreateStrategyRequest,
  UpdateStrategyRequest
} from '../api/types'