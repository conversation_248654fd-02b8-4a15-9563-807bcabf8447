import { defineStore } from 'pinia';
import * as strategyApi from '../../api/modules/strategy';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../types';

export const useStrategyStore = defineStore('strategy', {
  state: () => ({
    strategies: [] as Strategy[],
    currentStrategy: null as Strategy | null,
    currentSelectedStrategy: null as Strategy | null,
    isLoading: false,
    error: null as string | null,
  }),
  actions: {
    async fetchStrategies() {
      this.isLoading = true;
      this.error = null;
      try {
        const data = await strategyApi.getStrategies();
        this.strategies = data;
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.isLoading = false;
      }
    },

    async fetchStrategyById(id: string) {
        this.isLoading = true;
        this.error = null;
        try {
            const response = await strategyApi.getStrategy(id);
            this.currentStrategy = response;
        } catch (error: any) {
            this.error = error.message;
        } finally {
            this.isLoading = false;
        }
    },

    async createNewStrategy(strategyData: CreateStrategyRequest) {
        this.isLoading = true;
        this.error = null;
        try {
            await strategyApi.createStrategy(strategyData);
            // 创建成功后，重新获取列表以保证数据同步
            await this.fetchStrategies(); 
        } catch (error: any) {
            this.error = error.message;
        } finally {
            this.isLoading = false;
        }
    },

    async updateExistingStrategy(id: string, strategyData: UpdateStrategyRequest) {
        this.isLoading = true;
        this.error = null;
        try {
            await strategyApi.updateStrategy(id, strategyData);
            await this.fetchStrategies(); // 同步列表
        } catch (error: any) {
            this.error = error.message;
        } finally {
            this.isLoading = false;
        }
    },

    async deleteExistingStrategy(id: string) {
        this.isLoading = true;
        this.error = null;
        try {
            await strategyApi.deleteStrategy(id);
            await this.fetchStrategies(); // 同步列表
        } catch (error: any) {
            this.error = error.message;
        } finally {
            this.isLoading = false;
        }
    },

    // 直接调用后端创建策略，不做列表刷新，返回创建结果
    async createStrategy(strategyData: CreateStrategyRequest) {
        return await strategyApi.createStrategy(strategyData);
    },

    // 直接调用后端更新策略，不做列表刷新，返回更新结果
    async updateStrategy(id: string, strategyData: UpdateStrategyRequest) {
        return await strategyApi.updateStrategy(id, strategyData);
    },

    // 设置当前选中的策略
    setCurrentSelectedStrategy(strategy: Strategy | null) {
        this.currentSelectedStrategy = strategy;
    },

    // 清除当前选中的策略
    clearCurrentSelectedStrategy() {
        this.currentSelectedStrategy = null;
    },

    // 获取策略列表后自动选中第一个策略
    async fetchStrategiesAndSelectFirst() {
        await this.fetchStrategies();
        
        // 检查列表是否为空，如果不为空则自动选中第一个策略
        if (this.strategies.length > 0) {
            this.setCurrentSelectedStrategy(this.strategies[0]);
        } else {
            this.clearCurrentSelectedStrategy();
        }
    },

    // 开始新建策略：清空左侧选中状态，右侧进入一个默认空策略的编辑态
    startNewStrategyCreation() {
        const now = new Date().toISOString();
        const defaultNewStrategy = {
            id: 'new',
            name: '',
            description: '',
            buy_factors: [],
            sell_factors: [],
            created_at: now,
            updated_at: now,
            is_active: true,
            performance_metrics: {
                total_return: 0,
                annual_return: 0,
                sharpe_ratio: 0,
                max_drawdown: 0,
                win_rate: 0
            }
        } as Strategy;
        this.setCurrentSelectedStrategy(defaultNewStrategy);
    },

    // 为指定买入因子添加专属卖出规则
    addExclusiveSellFactor(buyFactorId: string, sellFactor: any) {
        if (!this.currentSelectedStrategy) {
            throw new Error('没有当前选中的策略');
        }

        const buyFactor = this.currentSelectedStrategy.buy_factors?.find(factor => factor.id === buyFactorId);
        if (!buyFactor) {
            throw new Error(`找不到ID为 ${buyFactorId} 的买入因子`);
        }

        // 确保 sell_factors 数组存在
        if (!Array.isArray(buyFactor.sell_factors)) {
            buyFactor.sell_factors = [];
        }

        // 检查是否已存在相同ID的卖出因子
        const existingIndex = buyFactor.sell_factors.findIndex(factor => factor.id === sellFactor.id);
        if (existingIndex === -1) {
            buyFactor.sell_factors.push(sellFactor);
        }
        // 如果已存在，则不进行任何操作（防止重复添加）
    },

    // 移除指定买入因子的专属卖出规则
    removeExclusiveSellFactor(buyFactorId: string, sellFactorId: string) {
        if (!this.currentSelectedStrategy) {
            throw new Error('没有当前选中的策略');
        }

        const buyFactor = this.currentSelectedStrategy.buy_factors?.find(factor => factor.id === buyFactorId);
        if (!buyFactor || !Array.isArray(buyFactor.sell_factors)) {
            return;
        }

        const index = buyFactor.sell_factors.findIndex(factor => factor.id === sellFactorId);
        if (index !== -1) {
            buyFactor.sell_factors.splice(index, 1);
        }
    },

    // 更新买入因子的卖出规则列表
    updateBuyFactorSellRules(buyFactorId: string, sellFactors: any[]) {
        if (!this.currentSelectedStrategy) {
            throw new Error('没有当前选中的策略');
        }

        const buyFactor = this.currentSelectedStrategy.buy_factors?.find(factor => factor.id === buyFactorId);
        if (!buyFactor) {
            throw new Error(`找不到ID为 ${buyFactorId} 的买入因子`);
        }

        // 为所有卖出因子添加专属标记
        const exclusiveSellFactors = sellFactors.map(factor => ({
            ...factor,
            exclusive_to_buy_factor: buyFactorId
        }));

        buyFactor.sell_factors = exclusiveSellFactors;
    },

    // 获取指定买入因子的专属卖出规则
    getExclusiveSellFactors(buyFactorId: string) {
        if (!this.currentSelectedStrategy) {
            return [];
        }

        const buyFactor = this.currentSelectedStrategy.buy_factors?.find(factor => factor.id === buyFactorId);
        return Array.isArray(buyFactor?.sell_factors) ? buyFactor.sell_factors : [];
    },

    // 执行策略回测
    async executeStrategy(strategyId: string, config: any) {
        this.isLoading = true;
        this.error = null;
        try {
            const response = await strategyApi.executeStrategy(strategyId, config);
            return response;
        } catch (error: any) {
            this.error = error.message;
            throw error;
        } finally {
            this.isLoading = false;
        }
    }
  },
});