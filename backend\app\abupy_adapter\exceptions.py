"""异常定义模块

该模块定义了abupy适配器中使用的自定义异常类型。每个异常类型都有明确的使用场景和详细的错误信息。
"""

from typing import Optional, Dict, Any
from backend.app.core.exceptions import AdapterError, FactorError, ParameterError

# 重新导出核心异常以简化导入
__all__ = [
    'AdapterError',
    'FactorError',
    'ParameterError',
    'DataNotFoundError',
    'BenchmarkError',
    'ExecutionError',
    'SymbolError',
    'DataTransformError',
    'CacheError',
    'ValidationError'
]

class DataNotFoundError(AdapterError):
    """当所需数据（如K线、基准等）无法找到时引发的异常。
    
    使用场景：
    - 从本地或远程数据源获取K线数据失败
    - 基准数据不存在或无法访问
    - 股票数据在指定时间范围内不可用
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="DATA_NOT_FOUND", data=data)

class BenchmarkError(AdapterError):
    """基准对象相关错误。
    
    使用场景：
    - 基准对象创建失败
    - 基准数据格式不正确
    - 基准计算过程中出现错误
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="BENCHMARK_ERROR", data=data)

class ExecutionError(AdapterError):
    """策略执行过程中发生的错误。
    
    使用场景：
    - 策略执行过程中的abupy内部错误
    - 因子应用失败
    - 订单生成失败
    - 资金计算错误
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="EXECUTION_ERROR", data=data)

class SymbolError(AdapterError):
    """当处理的股票代码有问题时引发的异常。
    
    使用场景：
    - 股票代码格式无效
    - 股票代码不存在
    - 股票代码转换失败
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="SYMBOL_ERROR", data=data)

class DataTransformError(AdapterError):
    """数据转换过程中发生的错误。
    
    使用场景：
    - DataFrame转换失败
    - 日期格式转换错误
    - 数据类型转换失败
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="DATA_TRANSFORM_ERROR", data=data)

class CacheError(AdapterError):
    """缓存操作相关错误。
    
    使用场景：
    - 缓存写入失败
    - 缓存读取错误
    - 缓存清理异常
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="CACHE_ERROR", data=data)

class ValidationError(AdapterError):
    """数据验证失败时引发的异常。
    
    使用场景：
    - 输入参数验证失败
    - 数据格式验证失败
    - 业务规则验证失败
    """
    def __init__(self, message: str, data: Optional[Dict[str, Any]] = None):
        super().__init__(message=message, error_code="VALIDATION_ERROR", data=data)