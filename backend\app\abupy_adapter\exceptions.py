"""
异常定义模块

该模块定义了abupy适配器中使用的自定义异常类型
"""

from backend.app.core.exceptions import AdapterError, FactorError, ParameterError

# 重新导出核心异常以简化导入
__all__ = ['AdapterError', 'FactorError', 'ParameterError']

class BenchmarkError(AdapterError):
    """基准对象相关错误"""
    pass

class ExecutionError(AdapterError):
    """策略执行过程中发生的错误"""
    pass
class SymbolError(AdapterError):
    """当处理的股票代码有问题时（如不存在、数据无法获取等）引发的异常。"""
    pass