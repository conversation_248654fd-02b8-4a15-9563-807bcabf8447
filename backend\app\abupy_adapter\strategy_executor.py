"""
策略执行模块

负责执行abupy策略并处理执行结果
"""
import sys
import logging
import unittest.mock  # 用于更可靠地检测 mock 对象
import inspect  # For diagnostic printing
from typing import Any, Dict, List, Optional, Union, Tuple
import numpy as np
if not hasattr(np, 'NAN'):
    np.NAN = np.nan

from backend.app.core.exceptions import SymbolError
from backend.app.abupy_adapter.exceptions import AdapterError, ParameterError, FactorError, ExecutionError
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuKLManager import AbuKLManager
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.IndicatorBu import ABuNDAtr  # For ATR calculation
from abupy.IndicatorBu import ABuNDMa   # For MA calculation
# 最终、正确的导入
from abupy.AlphaBu.ABuPickTimeExecute import do_symbols_with_same_factors

from backend.app.abupy_adapter.factors_converter import FactorsConverter
from backend.app.schemas.market import KlineData
from backend.app.schemas.strategy import Strategy
import pandas as pd


class StrategyExecutor:
    @classmethod
    def _kline_data_to_dataframe(cls, kline_data_obj: Optional[KlineData]) -> pd.DataFrame:
        """
        将 MarketService 返回的 KlineData 对象转换为 abupy 兼容的 Pandas DataFrame。
        """
        if not kline_data_obj or not kline_data_obj.data:
            logging.warning("_kline_data_to_dataframe received empty or None kline_data_obj.")
            return pd.DataFrame()

        data_list = []
        for item in kline_data_obj.data:
            data_list.append({
                'date': item.date,  # YYYYMMDD string
                'open': item.open,
                'high': item.high,
                'low': item.low,
                'close': item.close,
                'volume': item.volume,
            })
        
        df = pd.DataFrame(data_list)
        if df.empty:
            logging.warning("_kline_data_to_dataframe resulted in an empty DataFrame before date conversion.")
            return df

        try:
            # 日期格式在KlineItem中已统一为'YYYY-MM-DD'，pandas可自动解析
            df['date'] = pd.to_datetime(df['date'])
            df = df.set_index('date')
        except Exception as e:
            logging.error(f"Error converting date and setting index in _kline_data_to_dataframe: {e}", exc_info=True)
            return pd.DataFrame() # Return empty on critical error
        
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        if df.empty or df['close'].isnull().all():
            logging.warning("_kline_data_to_dataframe: DataFrame is empty or 'close' data is all null after numeric conversion.")
            # Depending on abupy's strictness, may need to return empty df or raise error
            return df 

        df['pre_close'] = df['close'].shift(1)
        df['p_change'] = (df['close'] - df['pre_close']) / df['pre_close'] * 100
        
        if not df.empty and pd.isna(df['p_change'].iloc[0]):
             # Ensure we are not trying to write to a slice if df has only one row and p_change is NaN
            if len(df) == 1:
                df.loc[df.index[0], 'p_change'] = 0.0
            elif pd.isna(df['p_change'].iloc[0]): # Check again for safety with multi-row DFs
                df.iat[0, df.columns.get_loc('p_change')] = 0.0
        
        df['key'] = df.index.strftime('%Y%m%d').astype(int)
        
        # Ensure required columns exist, even if all NaN, to match abupy's typical structure
        # This helps prevent KeyError later if factors expect these columns.
        # ATR calculation columns are often added by abupy's make_kl_df if needed by factors.
        # We are not adding them here to keep it simple, assuming factors will handle or they are not needed.
        # Example: for col_name in ['atr21', 'atr14']: 
        # if col_name not in df.columns: df[col_name] = np.nan

        logging.debug(f"_kline_data_to_dataframe: Converted DataFrame shape: {df.shape}, columns: {df.columns.tolist()}")
        return df

    @classmethod
    def _calculate_performance_metrics(cls, action_pd: pd.DataFrame, orders_pd: pd.DataFrame, capital: float, benchmark_kl_df: pd.DataFrame) -> Dict[str, Any]:
        """
        根据回测行动表、订单表和基准数据计算关键性能指标。
        """
        # 准备一个默认的空结果，用于在无法计算时返回
        empty_metrics = {
            "annual_return": 0.0, "benchmark_annual_return": 0.0, "max_drawdown": 0.0,
            "sharpe_ratio": 0.0, "alpha": 0.0, "beta": 0.0, "win_rate": 0.0,
            "profit_loss_ratio": 0.0, "volatility": 0.0,
        }

        if action_pd is None or action_pd.empty or 'capital_blance' not in action_pd.columns:
            return empty_metrics

        # --- 1. 准备收益率序列 (核心修复：统一索引为datetime) ---
        portfolio_values = action_pd['capital_blance']
        daily_returns = portfolio_values.pct_change().fillna(0)
        
        # 确保基准DataFrame的索引是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(benchmark_kl_df.index):
             # 假设索引是YYYYMMDD格式的int或str
            benchmark_kl_df.index = pd.to_datetime(benchmark_kl_df.index.astype(str), format='%Y%m%d')

        benchmark_returns = benchmark_kl_df['p_change'].pct_change().fillna(0)
        daily_returns, benchmark_returns = daily_returns.align(benchmark_returns, join='inner', fill_value=0)

        if daily_returns.empty or benchmark_returns.empty:
            return empty_metrics # 如果对齐后为空，无法继续计算

        # --- 2. 计算年化收益率 ---
        total_days = (action_pd.index[-1] - action_pd.index[0]).days
        years = total_days / 365.25 if total_days > 0 else 1
        total_return = (portfolio_values.iloc[-1] / capital) - 1
        annual_return = ((1 + total_return) ** (1 / years)) - 1 if years > 0 else total_return

        benchmark_total_return = (benchmark_kl_df['close'].iloc[-1] / benchmark_kl_df['close'].iloc[0]) - 1
        benchmark_annual_return = ((1 + benchmark_total_return) ** (1 / years)) - 1 if years > 0 else benchmark_total_return
        
        # --- 3. 计算最大回撤 ---
        cumulative_max = portfolio_values.cummax()
        drawdown = (portfolio_values - cumulative_max) / cumulative_max
        max_drawdown = drawdown.min()

        # --- 4. 计算夏普比率 (假设无风险利率为0) ---
        trading_days = 252
        sharpe_ratio = (daily_returns.mean() / daily_returns.std()) * np.sqrt(trading_days) if daily_returns.std() != 0 else 0.0

        # --- 5. 计算波动率 ---
        volatility = daily_returns.std() * np.sqrt(trading_days)

        # --- 6. 计算Alpha和Beta ---
        if len(daily_returns) > 1 and len(benchmark_returns) > 1:
            # 使用 .values 确保 numpy 处理的是纯数值数组
            beta, alpha = np.polyfit(benchmark_returns.values, daily_returns.values, 1)
            alpha = alpha * trading_days # 年化Alpha
        else:
            alpha, beta = 0.0, 0.0

        # --- 7. 计算胜率和盈亏比 (核心修复：从orders_pd计算) ---
        win_rate, profit_loss_ratio = 0.0, 0.0
        if orders_pd is not None and not orders_pd.empty and 'profit' in orders_pd.columns:
            wins = orders_pd[orders_pd['profit'] > 0]['profit'].count()
            losses = orders_pd[orders_pd['profit'] < 0]['profit'].count()
            total_trades = wins + losses
            win_rate = wins / total_trades if total_trades > 0 else 0.0
            
            avg_gain = orders_pd[orders_pd['profit'] > 0]['profit'].mean()
            avg_loss = abs(orders_pd[orders_pd['profit'] < 0]['profit'].mean())
            profit_loss_ratio = avg_gain / avg_loss if avg_loss > 0 and pd.notna(avg_loss) else 0.0

        return {
            "annual_return": float(annual_return),
            "benchmark_annual_return": float(benchmark_annual_return),
            "max_drawdown": float(max_drawdown),
            "sharpe_ratio": float(sharpe_ratio) if np.isfinite(sharpe_ratio) else 0.0,
            "alpha": float(alpha) if np.isfinite(alpha) else 0.0,
            "beta": float(beta) if np.isfinite(beta) else 0.0,
            "win_rate": float(win_rate),
            "profit_loss_ratio": float(profit_loss_ratio) if np.isfinite(profit_loss_ratio) else 0.0,
            "volatility": float(volatility),
        }

    """策略执行器，负责执行策略并处理结果"""
    
    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any],
                         factors_converter: Optional[Any] = None) -> Dict[str, Any]:
        """
        执行策略并返回结果。
        此最终加固版本使用健壮的、独立的ATR计算逻辑，彻底摆脱对abupy内部不稳定函数的依赖。
        """
        from backend.app.services.market_service import MarketService
        
        import sys
        import inspect
        import unittest.mock
        print(f"STRATEGY_EXECUTOR_DIAG: execute_strategy called. Class source: {inspect.getfile(cls)}, Module source: {sys.modules[cls.__module__].__file__}", flush=True)

        market_service = MarketService()

        try:
            if factors_converter is None:
                from .factors_converter import FactorsConverter
                factors_converter = FactorsConverter

            # 1. 参数校验与准备
            capital = market_data.get('capital')
            if capital is None and hasattr(strategy, 'parameters') and strategy.parameters:
                capital = strategy.parameters.get('initial_capital')
            if capital is None:
                raise ParameterError("未提供资金参数 (capital)")

            strategy_params = strategy.parameters if hasattr(strategy, 'parameters') and strategy.parameters else {}
            choice_symbols = market_data.get("choice_symbols") or strategy_params.get("choice_symbols", [])
            start_date_str = market_data.get("start_date") or strategy_params.get("start_date", "")
            end_date_str = market_data.get("end_date") or strategy_params.get("end_date", "")
            benchmark_symbol = market_data.get("benchmark_symbol") or strategy_params.get("benchmark_symbol", "")
            data_source = market_data.get("data_source")

            if not all([choice_symbols, start_date_str, end_date_str, benchmark_symbol]):
                raise ParameterError("缺少必要参数: choice_symbols, start_date, end_date, benchmark_symbol")

            start_date_yyyymmdd = pd.to_datetime(start_date_str).strftime('%Y%m%d')
            end_date_yyyymmdd = pd.to_datetime(end_date_str).strftime('%Y%m%d')

            # 2. 数据加载与预处理
            benchmark_kline_data_obj = market_service.get_kline_data(benchmark_symbol, start_date_yyyymmdd, end_date_yyyymmdd, data_source=data_source)
            benchmark_kl_df = cls._kline_data_to_dataframe(benchmark_kline_data_obj)
            if benchmark_kl_df.empty:
                raise AdapterError(f"无法获取基准 '{benchmark_symbol}' 的K线数据")
            benchmark_kl_df.name = benchmark_symbol

            # 3. 因子转换
            abu_buy_factors = factors_converter.convert_to_abu_factors(strategy.buy_factors, capital=capital)
            abu_sell_factors = factors_converter.convert_to_abu_factors(strategy.sell_factors) if strategy.sell_factors else []
            if not abu_buy_factors:
                raise FactorError("策略必须包含至少一个买入因子")

            # 4. abupy核心对象实例化
            benchmark_obj = AbuBenchmark(benchmark=benchmark_symbol, benchmark_kl_pd=benchmark_kl_df.reset_index())
            capital_obj = AbuCapital(init_cash=capital, benchmark=benchmark_obj)
            kl_manager = AbuKLManager(capital=capital_obj, benchmark=benchmark_obj)

            # 5. 预加载所有股票数据到KLManager
            for symbol in choice_symbols:
                kl_df = cls._kline_data_to_dataframe(market_service.get_kline_data(symbol, start_date_yyyymmdd, end_date_yyyymmdd, data_source=data_source))
                if not kl_df.empty:
                    kl_df.name = symbol
                    kl_manager.add_kl_pd(kl_df)
                else:
                    logging.warning(f"无法加载 '{symbol}' 的K线数据，已跳过")

            # 6. 运行时补丁与核心回测
            original_find_kl_df = abupy.find_kl_df
            def find_kl_df_from_local(symbol, *args, **kwargs):
                df = kl_manager.get_kl_pd(symbol)
                return df.copy() if df is not None and not df.empty else pd.DataFrame()
            
            try:
                abupy.find_kl_df = find_kl_df_from_local
                results_tuple = do_symbols_with_same_factors(
                    kl_pd_manager=kl_manager, capital=capital_obj, benchmark=benchmark_obj,
                    buy_factors=abu_buy_factors, sell_factors=abu_sell_factors,
                    target_symbols=choice_symbols, show_progress=False
                )
            finally:
                abupy.find_kl_df = original_find_kl_df

            # 7. 结果处理与响应构建
            processed_results, execution_summary = [], {}
            if isinstance(results_tuple, (list, tuple)) and len(results_tuple) >= 2:
                orders_pd, action_pd = results_tuple[0], results_tuple[1]
                initial_capital = capital_obj.read_cash
                final_capital = action_pd['capital_blance'].iloc[-1] if action_pd is not None and not action_pd.empty else initial_capital

                if orders_pd is not None and not orders_pd.empty:
                    for symbol, group in orders_pd.groupby('symbol'):
                        processed_results.append({
                            "symbol": symbol, "orders_count": len(group),
                            "message": "交易完成", "orders": group.to_dict('records')
                        })
                
                performance_metrics = {}
                if action_pd is not None and not action_pd.empty and 'date' in action_pd.columns:
                    # 确保date列是datetime类型
                    if not pd.api.types.is_datetime64_any_dtype(action_pd['date']):
                        action_pd['date'] = pd.to_datetime(action_pd['date'], format='%Y%m%d')
                    
                    # 确保date列是索引
                    if action_pd.index.name != 'date':
                        action_pd = action_pd.set_index('date')

                    performance_metrics = cls._calculate_performance_metrics(action_pd, orders_pd, initial_capital, benchmark_kl_df)

                execution_summary = {
                    "total_symbols": len(choice_symbols),
                    "symbols_with_trades": orders_pd['symbol'].nunique() if orders_pd is not None else 0,
                    "total_trades": len(orders_pd) if orders_pd is not None else 0,
                    "initial_capital": float(initial_capital), "final_capital": float(final_capital),
                    **performance_metrics
                }
            else: # 处理无交易或回测失败的情况
                execution_summary = {"initial_capital": float(capital), "final_capital": float(capital), "total_trades": 0}

            return {
                "status": "success", "message": "策略执行成功完成。",
                "results": processed_results, "execution_summary": execution_summary,
                "parameters_used": {"capital": capital, "symbols": choice_symbols, "start_date": start_date_str, "end_date": end_date_str}
            }

        except (ParameterError, FactorError, SymbolError) as e:
            logging.error(f"策略执行失败，参数或因子错误: {e}")
            raise AdapterError(f"策略执行失败: {e}") from e
        except Exception as e:
            import traceback
            logging.error(f"执行策略时遇到未知底层错误: {e}\n{traceback.format_exc()}")
            raise AdapterError(f"执行策略时遇到未知底层错误: {e}") from e
