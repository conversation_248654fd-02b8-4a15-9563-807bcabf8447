import logging
import pandas as pd
import numpy as np
import re
from typing import Dict, Any, Optional, List, Union

# 导入abupy核心模块
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.AlphaBu.ABuPickTimeExecute import do_symbols_with_same_factors
from abupy.MarketBu import ABuSymbolPd
from abupy.UmpBu import ABuUmpManager
from abupy.CoreBu import ABuEnv

# 导入项目内部模块
from .umpire_adapter import create_umpire_managers

# 导入项目内部模块
from backend.app.schemas.market import KlineData
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor, PerformanceMetrics
from backend.app.utils.symbol_util import to_abupy_symbol_format, from_abupy_symbol_format, to_tushare_symbol_format
from .exceptions import AdapterError, ParameterError, FactorError, SymbolError, DataNotFoundError


# 全局变量，用于存储预加载的K线数据，以便在补丁函数中访问
all_raw_kline_data = {}

class StrategyExecutor:
    """
    负责执行基于abupy的量化策略。
    此类将应用内部的策略定义转换为abupy可执行的因子和参数，
    并管理数据的获取、回测的执行以及结果的格式化。
    """

    @staticmethod
    def _kline_data_to_dataframe(kline_data: KlineData) -> pd.DataFrame:
        """
        将 KlineData Pydantic 模型转换为 abupy 期望的 DataFrame 格式。
        这个函数现在被高度加固，以防止静默失败。
        """
        if not kline_data or not kline_data.data:
            logging.warning(f"输入到 _kline_data_to_dataframe 的 KlineData 为空或没有kline列表。")
            return pd.DataFrame()

        symbol_for_log = kline_data.symbol

        try:
            # 1. 初始转换
            df = pd.DataFrame([item.model_dump() for item in kline_data.data])
            if df.empty:
                logging.warning(f"从 KlineData (symbol: {symbol_for_log}) 创建的初始DataFrame为空。")
                return pd.DataFrame()

            logging.info(f"[{symbol_for_log}] 初始DataFrame创建成功，行数: {len(df)}。列: {df.columns.tolist()}")

            # 2. 核心数据类型和索引转换 (最容易出错的地方)
            # 确保列名是abupy期望的
            # 注意：vol到volume的重命名应该在上游的 kline_provider 中完成，这里只做断言检查
            assert 'volume' in df.columns, f"DataFrame for {symbol_for_log} 缺少'volume'列！"
            assert 'date' in df.columns, f"DataFrame for {symbol_for_log} 缺少'date'列！"

            # 关键：转换日期并设置为索引，这是最常见的失败点
            df['date'] = pd.to_datetime(df['date']) # 假设上游已确保是标准格式
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)

            # 3. 添加abupy期望的衍生列
            df['pre_close'] = df['close'].shift(1)
            df['p_change'] = (df['close'] / df['pre_close'] - 1) * 100
            df['date_week'] = df.index.dayofweek

            # 计算ATR等指标
            tr1 = df['high'] - df['low']
            tr2 = np.abs(df['high'] - df['pre_close'])
            tr3 = np.abs(df['low'] - df['pre_close'])
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1, skipna=True)
            import talib
            df['atr21'] = talib.ATR(df.high.values, df.low.values, df.close.values, timeperiod=21)
            df['atr14'] = talib.ATR(df.high.values, df.low.values, df.close.values, timeperiod=14)
            df['key'] = range(len(df))

            # 最终填充NaN
            # --- 决胜修改 ---
            # abupy的某些模块(如AbuCapital)需要一个名为'date'的列，即使日期已经是索引
            # 我们将索引复制为新的一列来满足这个要求
            df['date'] = df.index.strftime('%Y%m%d').astype(int)

            # 最终填充NaN
            df.fillna(0, inplace=True)

            # 4. 最终检查
            if df.empty:
                logging.error(f"[{symbol_for_log}] 在处理后，DataFrame意外变为空！")
            else:
                logging.info(f"[{symbol_for_log}] DataFrame处理完成，返回非空DataFrame。")

            return df

        except Exception as e:
            # 关键：捕获任何异常，记录详细信息，然后重新抛出
            logging.error(
                f"在 _kline_data_to_dataframe 处理符号 {symbol_for_log} 时发生致命错误: {e}",
                exc_info=True  # 这会打印完整的堆栈跟踪
            )
            # 在这种关键路径上，抛出异常比返回空DF更明确
            raise ValueError(f"处理 {symbol_for_log} 数据失败") from e
    @staticmethod
    def _calculate_performance_metrics(orders_pd: pd.DataFrame, capital_obj: AbuCapital, benchmark_obj: AbuBenchmark) -> PerformanceMetrics:
        """
        根据订单数据和资金对象计算详细的性能指标。
        
        参数:
            orders_pd: 交易订单DataFrame
            capital_obj: 资金对象
            benchmark_obj: 基准对象
            
        返回:
            PerformanceMetrics: 包含所有计算指标的Pydantic模型实例
        """
        import math
        from datetime import datetime, timedelta
        import numpy as np
        
        # 如果没有交易订单，返回默认的性能指标对象
        if orders_pd is None or orders_pd.empty:
            return PerformanceMetrics()
            
        try:
            # 获取初始资金和基准数据
            initial_capital = float(capital_obj.read_cash)
            benchmark_df = benchmark_obj.kl_pd
            
            # 确保基准数据有日期索引
            if not pd.api.types.is_datetime64_any_dtype(benchmark_df.index):
                if 'date' in benchmark_df.columns:
                    benchmark_df['date'] = pd.to_datetime(benchmark_df['date'])
                    benchmark_df = benchmark_df.set_index('date')
            
            # 1. 构建每日市值曲线
            # 获取回测的开始和结束日期
            start_date = benchmark_df.index.min()
            end_date = benchmark_df.index.max()
            
            # 创建日期范围的DataFrame作为基础
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            portfolio_df = pd.DataFrame(index=date_range)
            portfolio_df.index.name = 'date'
            
            # 初始化资金曲线
            portfolio_df['cash'] = initial_capital  # 现金
            portfolio_df['position_value'] = 0.0    # 持仓市值
            portfolio_df['total_value'] = initial_capital  # 总市值
            
            # 按交易日期对订单排序
            orders_pd = orders_pd.sort_values('buy_date' if 'buy_date' in orders_pd.columns else 'date')
            
            # 处理每个交易订单，更新资金曲线
            positions = {}  # 当前持仓 {symbol: {price: 价格, volume: 数量}}
            
            for _, order in orders_pd.iterrows():
                # 获取交易信息
                symbol = order['symbol']
                if 'buy_date' in order and not pd.isna(order['buy_date']):
                    buy_date = pd.to_datetime(order['buy_date'])
                    buy_price = float(order['buy_price'])
                    buy_volume = float(order['buy_cnt']) if 'buy_cnt' in order else float(order['buy_volume'] if 'buy_volume' in order else 0)
                else:
                    # 如果没有买入日期，可能是导入的订单或其他格式
                    continue
                    
                # 卖出信息可能为空（未平仓的持仓）
                sell_date = None
                if 'sell_date' in order and not pd.isna(order['sell_date']):
                    sell_date = pd.to_datetime(order['sell_date'])
                    sell_price = float(order['sell_price'])
                
                # 买入操作 - 更新现金和持仓
                buy_value = buy_price * buy_volume
                
                # 确保买入日期在日期范围内
                if buy_date in portfolio_df.index:
                    # 更新买入日的现金和持仓
                    portfolio_df.loc[buy_date:, 'cash'] -= buy_value
                    
                    # 更新持仓
                    if symbol not in positions:
                        positions[symbol] = {'volume': 0, 'cost': 0}
                    positions[symbol]['volume'] += buy_volume
                    positions[symbol]['cost'] = buy_price  # 简化处理，使用最后买入价作为成本价
                    
                    # 更新买入日及之后的持仓市值
                    portfolio_df.loc[buy_date:, 'position_value'] += buy_value
                
                # 卖出操作 - 更新现金和持仓
                if sell_date is not None and sell_date in portfolio_df.index:
                    sell_value = sell_price * buy_volume  # 假设全部卖出
                    
                    # 更新卖出日的现金和持仓
                    portfolio_df.loc[sell_date:, 'cash'] += sell_value
                    portfolio_df.loc[sell_date:, 'position_value'] -= buy_value  # 简化处理，使用买入价值
                    
                    # 更新持仓
                    if symbol in positions:
                        positions[symbol]['volume'] -= buy_volume
                        if positions[symbol]['volume'] <= 0:
                            del positions[symbol]
            
            # 计算每日总市值
            portfolio_df['total_value'] = portfolio_df['cash'] + portfolio_df['position_value']
            
            # 只保留交易日数据
            portfolio_df = portfolio_df[portfolio_df.index.isin(benchmark_df.index)]
            
            # 确保基准数据和投资组合数据有相同的日期索引
            common_dates = portfolio_df.index.intersection(benchmark_df.index)
            portfolio_df = portfolio_df.loc[common_dates]
            benchmark_series = benchmark_df.loc[common_dates, 'close']
            
            # 2. 计算各项指标
            # 计算日收益率序列
            portfolio_df['daily_return'] = portfolio_df['total_value'].pct_change().fillna(0)
            benchmark_daily_return = benchmark_series.pct_change().fillna(0)
            
            # 累计收益率
            final_value = portfolio_df['total_value'].iloc[-1]
            cumulative_return = (final_value / initial_capital) - 1
            
            # 基准累计收益率
            benchmark_return = (benchmark_series.iloc[-1] / benchmark_series.iloc[0]) - 1
            
            # 计算年化收益率
            days = (portfolio_df.index[-1] - portfolio_df.index[0]).days
            years = days / 365.0
            annualized_return = (1 + cumulative_return) ** (1 / years) - 1 if years > 0 else 0
            benchmark_annualized_return = (1 + benchmark_return) ** (1 / years) - 1 if years > 0 else 0
            
            # 计算最大回撤
            portfolio_df['cumulative_max'] = portfolio_df['total_value'].cummax()
            portfolio_df['drawdown'] = (portfolio_df['total_value'] / portfolio_df['cumulative_max']) - 1
            max_drawdown = abs(portfolio_df['drawdown'].min())
            
            # 计算年化波动率
            daily_std = portfolio_df['daily_return'].std()
            annualized_volatility = daily_std * math.sqrt(252) if not pd.isna(daily_std) else 0
            
            # 计算夏普比率 (假设无风险利率为0)
            risk_free_rate = 0.0
            sharpe_ratio = (annualized_return - risk_free_rate) / annualized_volatility if annualized_volatility > 0 else 0
            
            # 计算贝塔和阿尔法
            # 贝塔 = 投资组合收益率与市场收益率的协方差 / 市场收益率的方差
            covariance = np.cov(portfolio_df['daily_return'].values, benchmark_daily_return.values)[0, 1]
            variance = np.var(benchmark_daily_return.values)
            beta = covariance / variance if variance > 0 else 1.0
            
            # 阿尔法 = 投资组合年化收益率 - 无风险利率 - 贝塔 * (市场年化收益率 - 无风险利率)
            alpha = annualized_return - risk_free_rate - beta * (benchmark_annualized_return - risk_free_rate)
            
            # 计算胜率和盈亏比
            # 筛选已平仓的交易
            closed_trades = orders_pd[~pd.isna(orders_pd['sell_date']) if 'sell_date' in orders_pd.columns else []]
            
            if not closed_trades.empty:
                # 计算每笔交易的盈亏
                if 'profit' in closed_trades.columns:
                    profits = closed_trades['profit']
                else:
                    # 如果没有直接的利润列，计算买卖价差
                    buy_prices = closed_trades['buy_price'].values
                    sell_prices = closed_trades['sell_price'].values
                    volumes = closed_trades['buy_cnt'].values if 'buy_cnt' in closed_trades.columns else np.ones_like(buy_prices)
                    profits = (sell_prices - buy_prices) * volumes
                
                # 计算胜率
                win_trades = sum(profits > 0)
                total_trades = len(profits)
                win_rate = win_trades / total_trades if total_trades > 0 else 0
                
                # 计算盈亏比
                avg_profit = profits[profits > 0].mean() if any(profits > 0) else 0
                avg_loss = abs(profits[profits < 0].mean()) if any(profits < 0) else 1  # 避免除以零
                profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
            else:
                win_rate = 0
                profit_loss_ratio = 0
                total_trades = 0
            
            # 计算信息比率 (超额收益/跟踪误差)
            excess_returns = portfolio_df['daily_return'] - benchmark_daily_return
            tracking_error = excess_returns.std() * math.sqrt(252)
            information_ratio = (annualized_return - benchmark_annualized_return) / tracking_error if tracking_error > 0 else 0
            
            # 3. 返回结构化数据
            return PerformanceMetrics(
                cumulative_return=round(cumulative_return, 4),
                annualized_return=round(annualized_return, 4),
                max_drawdown=round(max_drawdown, 4),
                sharpe_ratio=round(sharpe_ratio, 4),
                win_rate=round(win_rate, 4),
                profit_loss_ratio=round(profit_loss_ratio, 4),
                alpha=round(alpha, 4),
                beta=round(beta, 4),
                total_trades=total_trades,
                annualized_volatility=round(annualized_volatility, 4),
                benchmark_return=round(benchmark_return, 4),
                benchmark_annualized_return=round(benchmark_annualized_return, 4),
                information_ratio=round(information_ratio, 4)
            )
            
        except Exception as e:
            logging.error(f"计算性能指标时发生错误: {e}", exc_info=True)
            return PerformanceMetrics()

    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any],
                         factors_converter: Optional[Any] = None) -> Dict[str, Any]:
        """
        [最终稳定版] 通过完全的数据注入和对abupy的精确补丁来执行策略。
        """
        from backend.app.services.market_service import MarketService

        logging.debug(f"STRATEGY_EXECUTOR: execute_strategy called for strategy '{strategy.name}'.")
        market_service = MarketService()

        original_make_kl_df = ABuSymbolPd.make_kl_df
        original_combine_pre_kl_pd = ABuSymbolPd.combine_pre_kl_pd
        
        try:
            if factors_converter is None:
                from .factors_converter import FactorsConverter
                factors_converter = FactorsConverter

            # 1. 因子和参数准备
            # 步骤 1.1: 因子校验 (最优先)
            abu_buy_factors = factors_converter.convert_to_abu_factors(strategy.buy_factors)
            if not abu_buy_factors: raise FactorError("策略必须包含至少一个买入因子")
            abu_sell_factors = factors_converter.convert_to_abu_factors(strategy.sell_factors) if strategy.sell_factors else []

            # 步骤 1.2: 其他参数校验
            capital_val = market_data.get('capital') or strategy.parameters.get('initial_capital')
            if capital_val is None: raise ParameterError("未提供资金参数")

            choice_symbols_api = market_data.get("choice_symbols", [])
            start_date_str = market_data.get("start_date")
            end_date_str = market_data.get("end_date")
            benchmark_symbol_api = market_data.get("benchmark_symbol")
            
            if not all([choice_symbols_api, start_date_str, end_date_str, benchmark_symbol_api]):
                raise ParameterError("缺少必要参数: 股票池, 起始日期, 结束日期, 或基准")

            # 将API传入的abupy格式符号 ('sh600519') 转换为MarketService所需的Tushare格式 ('600519.SH')
            choice_symbols_tushare = [to_tushare_symbol_format(s) for s in choice_symbols_api]
            benchmark_symbol_tushare = to_tushare_symbol_format(benchmark_symbol_api)

            start_date = pd.to_datetime(start_date_str).strftime('%Y%m%d')
            end_date = pd.to_datetime(end_date_str).strftime('%Y%m%d')

            # 2. 数据加载 (在所有校验通过后执行)
            # 清空并填充全局K线数据缓存，以便后续的补丁函数可以访问
            global all_raw_kline_data
            all_raw_kline_data.clear()
            loaded_data = {s: cls._kline_data_to_dataframe(
                market_service.get_kline_data(
                    symbol=s, 
                    start_date=start_date, 
                    end_date=end_date, 
                    data_source=market_data.get("data_source")
                )
            ) for s in set([benchmark_symbol_tushare] + choice_symbols_tushare)}
            # 更新缓存时，确保使用Tushare格式的键
            for symbol_tushare, df in loaded_data.items():
                if df is not None and not df.empty:
                    all_raw_kline_data[symbol_tushare] = df

            # --- 诊断日志 ---
            logging.info(f"诊断：已加载数据字典的键: {list(loaded_data.keys())}")
            logging.info(f"诊断：已加载的非空数据帧的键: {[s for s, df in loaded_data.items() if df is not None and not df.empty]}")
            logging.info(f"诊断：当前缓存中的所有键: {list(all_raw_kline_data.keys())}")
            logging.info(f"诊断：正在查找的基准键: {benchmark_symbol_tushare}")
            # --- 诊断日志结束 ---

            # 验证基准数据是否已成功加载
            if benchmark_symbol_tushare not in all_raw_kline_data:
                raise DataNotFoundError(f"关键错误：基准 '{benchmark_symbol_api}' (tushare: {benchmark_symbol_tushare}) 的K线数据未能加载到缓存中，无法继续执行。")

            
            # 3. 定义补丁函数
            def make_kl_df_from_cache(symbol_or_obj, *args, **kwargs):
                """
                [最终正确版补丁]
                这个补丁的核心是，从K线缓存中获取一个【已经完全处理好】的DataFrame，
                并简单地为其附加abupy期望的.name属性。最关键的是保留DatetimeIndex。
                """
                symbol_str = getattr(symbol_or_obj, 'value', symbol_or_obj)
                
                # 尝试多种可能的键格式，优先使用Tushare格式
                possible_keys = [
                    to_tushare_symbol_format(symbol_str),  # 优先使用tushare格式
                    from_abupy_symbol_format(symbol_str, list(all_raw_kline_data.keys())),  # abupy格式转换
                    symbol_str.split('.')[-1] if '.' in symbol_str else symbol_str[2:]  # 最后尝试纯数字代码
                ]
                
                df = None
                used_key = None
                
                for key in possible_keys:
                    if key in all_raw_kline_data:
                        df = all_raw_kline_data[key]
                        used_key = key
                        logging.info(f"MAKE_KL_DF_PATCH: 使用键 '{key}' 找到了 '{symbol_str}' 的数据。")
                        break
                
                if df is None:
                    logging.warning(f"MAKE_KL_DF_PATCH: Symbol '{symbol_str}' 在缓存中未找到。已尝试的键: {possible_keys}。返回空DataFrame。")
                    df = pd.DataFrame()

                df.name = symbol_str
                return df

            def patched_combine_pre_kl_pd(kl_pd, n_folds=1): return kl_pd
            
            # 4. 应用补丁并执行回测
            ABuSymbolPd.make_kl_df = make_kl_df_from_cache
            ABuSymbolPd.combine_pre_kl_pd = patched_combine_pre_kl_pd

            # 使用原始的abupy格式符号创建基准对象
            benchmark_obj = AbuBenchmark(benchmark=benchmark_symbol_api, start=start_date_str, end=end_date_str)
            if benchmark_obj.kl_pd is None or benchmark_obj.kl_pd.empty:
                raise DataNotFoundError(f"通过补丁未能成功初始化基准 '{benchmark_symbol_api}' (tushare格式: {benchmark_symbol_tushare})")
            
            capital_obj = AbuCapital(init_cash=capital_val, benchmark=benchmark_obj)

            # --- 集成仓位管理 (使用全局配置) ---
            from abupy.BetaBu import AbuPositionBase, AbuAtrPosition, AbuKellyPosition, AbuPtPosition

            # 在回测开始前，清理旧的全局配置，恢复成默认
            AbuPositionBase.g_default_pos_class = None

            if hasattr(strategy, 'position_strategy') and strategy.position_strategy:
                # 将Pydantic PositionStrategy对象转换为字典
                position_config = strategy.position_strategy.dict()
                logging.info(f"成功将仓位策略对象转换为字典: {position_config}")
                
                class_name = position_config.get('class_name')
                parameters = position_config.get('parameters', {})

                if class_name:
                    # 映射类名到实际的类
                    position_class_map = {
                        "AbuAtrPosition": AbuAtrPosition,
                        "AbuKellyPosition": AbuKellyPosition,
                        "AbuPtPosition": AbuPtPosition
                    }
                    target_class = position_class_map.get(class_name)

                    if target_class:
                        # 设置全局默认仓位管理类
                        AbuPositionBase.g_default_pos_class = target_class
                        # 设置该类的参数
                        if parameters:
                            for key, value in parameters.items():
                                # abupy的全局参数通常是模块级或类级的变量
                                # 这里假设参数是设置在目标类上的
                                if hasattr(target_class, key):
                                    setattr(target_class, key, value)
                                else:
                                    # 对于像g_atr_pos_base这样的全局变量，需要特殊处理
                                    # 这里简化处理，只设置类属性
                                    logging.warning(f"仓位管理类 {class_name} 没有参数 {key}")
                        logging.info(f"已应用全局仓位管理策略: {class_name} with params {parameters}")
                    else:
                        logging.warning(f"未找到名为 {class_name} 的仓位管理类")
            # --- 仓位管理结束 ---
            
            abupy_choice_symbols = [to_abupy_symbol_format(s) for s in choice_symbols_tushare]
            
            # --- Umpire (裁判系统) 配置开始 ---
            umpire_instances = []
            if hasattr(strategy, 'umpire_rules') and strategy.umpire_rules:
                logging.info(f"策略 '{strategy.name}' 包含 {len(strategy.umpire_rules)} 条裁判规则，开始配置...")
                try:
                    # 1. 创建裁判实例
                    market_name = market_data.get('market', 'cn')  # 默认使用 'cn'
                    logging.info(f"传递给 create_umpire_managers 的 umpire_rules: {strategy.umpire_rules} 和 market_name: {market_name}")
                    umpire_instances = create_umpire_managers(strategy.umpire_rules, market_name=market_name)
                    
                    # 2. 开启abupy的全局开关
                    ABuUmpManager.g_enable_user_ump = True
                    ABuEnv.g_enable_ml_feature = True # 裁判系统依赖此项
                    
                    # 3. 清理旧配置并添加新裁判
                    ABuUmpManager.clear_user_ump()
                    for ump in umpire_instances:
                        ABuUmpManager.append_user_ump(ump)
                    
                    logging.info(f"成功配置 {len(umpire_instances)} 个裁判: {[str(u) for u in umpire_instances]}")
                    
                except ValueError as e:
                    logging.error(f"配置裁判系统时出错: {e}", exc_info=True)
                    # 根据业务决定是抛出异常还是继续无裁判回测
                    # 此处选择抛出异常，因为用户期望风控被执行
                    raise AdapterError(f"裁判系统配置失败: {e}")
            # --- Umpire (裁判系统) 配置结束 ---

            try:
                # AbuKLManager不是必须的，回测引擎会通过make_kl_df（已被我们patch）获取数据
                orders_pd, action_pd, _ = do_symbols_with_same_factors(
                    target_symbols=abupy_choice_symbols, benchmark=benchmark_obj,
                    buy_factors=abu_buy_factors, sell_factors=abu_sell_factors, capital=capital_obj
                )
            finally:
                # --- Umpire (裁判系统) 清理 --- 
                if umpire_instances: # 只有在本次执行配置了裁判时才清理
                    ABuUmpManager.g_enable_user_ump = False
                    ABuEnv.g_enable_ml_feature = False
                    ABuUmpManager.clear_user_ump()
                    logging.info("回测执行完毕，已清理并关闭裁判系统。")
        except DataNotFoundError as e:
            logging.error(f"数据加载失败: {e.message}", exc_info=True)
            raise
        except FactorError as e:
            logging.error(f"因子验证失败: {e.message}", exc_info=True)
            raise
        except ParameterError as e:
            logging.error(f"参数错误: {e.message}", exc_info=True)
            raise
        except SymbolError as e:
            logging.error(f"符号错误: {e.message}", exc_info=True)
            raise
        except AdapterError as e:
            logging.error(f"适配器内部错误: {e.message}", exc_info=True)
            raise
        except Exception as e:
            logging.error(f"策略执行过程中发生未知错误: {e}", exc_info=True)
            raise AdapterError(
                message=f"策略执行失败: {str(e)}",
                error_code="STRATEGY_EXECUTION_FAILED",
                details=traceback.format_exc()
            )
        finally:
            # 恢复原始函数
            ABuSymbolPd.make_kl_df = original_make_kl_df
            ABuSymbolPd.combine_pre_kl_pd = original_combine_pre_kl_pd
            
        # 5. 结果处理
        processed_results, execution_summary = [], {}
        initial_capital = capital_obj.read_cash

        # 路径A: 有交易订单产生
        if orders_pd is not None and not orders_pd.empty:
            # 路径A.1: 资金曲线也成功生成 (最理想情况)
            if action_pd is not None and not action_pd.empty and 'capital_blance' in action_pd.columns:
                final_capital = action_pd['capital_blance'].iloc[-1]
                # 使用新的性能指标计算方法，只需要orders_pd, capital_obj和benchmark_obj
                performance_metrics_obj = cls._calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj)
                # 将Pydantic模型转换为字典，以便与原有响应格式兼容
                performance_metrics = performance_metrics_obj.model_dump()
            # 路径A.2: 资金曲线生成失败 (abupy内部错误)
            else:
                logging.warning("abupy成功生成订单，但资金曲线(action_pd)不完整或缺失，尝试使用独立性能指标计算模块。")
                final_capital = initial_capital  # 默认资金视为无变化
                # 即使没有action_pd，也尝试使用独立性能指标计算模块
                performance_metrics_obj = cls._calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj)
                performance_metrics = performance_metrics_obj.model_dump()

            execution_summary = {
                "total_symbols": len(choice_symbols_tushare),
                "symbols_with_trades": orders_pd['symbol'].nunique(),
                "total_trades": len(orders_pd),
                "initial_capital": float(initial_capital),
                "final_capital": float(final_capital),
                **performance_metrics
            }

            # 格式化订单详情
            orders_pd_dict = orders_pd.to_dict('records')
            for order in orders_pd_dict:
                order['symbol'] = from_abupy_symbol_format(order['symbol'], choice_symbols_tushare)
            
            for symbol in choice_symbols_tushare:
                symbol_orders = [o for o in orders_pd_dict if o['symbol'] == symbol]
                if symbol_orders:
                    processed_results.append({
                        "symbol": symbol,
                        "orders_count": len(symbol_orders),
                        "message": "交易完成",
                        "orders": symbol_orders
                    })

        # 路径B: 没有任何交易订单产生
        # (这包含了abupy彻底失败，连orders_pd都无法生成的场景)
        else:
            logging.info("策略执行完成，但未产生任何交易订单。")
            execution_summary = {
                "total_symbols": len(choice_symbols_tushare),
                "symbols_with_trades": 0,
                "total_trades": 0,
                "initial_capital": float(initial_capital),
                "final_capital": float(initial_capital)
            }

        # 汇总所有未交易的股票
        symbols_with_trades = {res['symbol'] for res in processed_results}
        for symbol in choice_symbols_tushare:
            if symbol not in symbols_with_trades:
                processed_results.append({
                    "symbol": symbol,
                    "orders_count": 0,
                    "message": "无交易",
                    "orders": []
                })

        return {
            "status": "success",
            "message": "策略执行成功完成。",
            "results": sorted(processed_results, key=lambda x: x['symbol']),
            "execution_summary": execution_summary,
            "parameters_used": {
                "capital": float(capital_val),
                "symbols": choice_symbols_tushare,
                "start_date": start_date_str,
                "end_date": end_date_str
            }
        }