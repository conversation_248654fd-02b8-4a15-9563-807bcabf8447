import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { http, HttpResponse } from 'msw';
import { setupServer } from 'msw/node';
import { setActivePinia, createPinia } from 'pinia';
import { getStrategies, getStrategy, createStrategy, updateStrategy, deleteStrategy } from '../../src/api/strategy';
import { useAppStore } from '../../src/stores/app';
import { handlers } from '../mocks/handlers';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../src/api/types';

// 设置MSW服务器
const server = setupServer(...handlers);

const mockStrategies: Strategy[] = [
  { 
    id: '1', 
    name: 'Test Strategy 1', 
    description: 'Description 1', 
    author: 'Author 1', 
    content: 'content 1',
    create_time: '2024-01-01T10:00:00Z',
    update_time: '2024-01-01T10:00:00Z',
    owner: 'Author 1',
    is_public: false,
    buy_factors: [{ class_name: 'AbuFactorBuyBreak', parameters: {} }],
    sell_factors: [{ class_name: 'AbuFactorSellNDay', parameters: { sell_n: 5 } }],
    position_strategy: { class_name: 'AbuPositionBase', parameters: {} },
    parameters: { capital: 100000, commission: 0.001 },
    tags: ['test', 'strategy'],
    umpire_rules: []
  },
  { 
    id: '2', 
    name: 'Test Strategy 2', 
    description: 'Description 2', 
    author: 'Author 2', 
    content: 'content 2',
    create_time: '2024-01-02T10:00:00Z',
    update_time: '2024-01-02T10:00:00Z',
    owner: 'Author 2',
    is_public: true,
    buy_factors: [{ class_name: 'AbuFactorBuyWD', parameters: {} }],
    sell_factors: [{ class_name: 'AbuFactorSellXD', parameters: { sell_x: 10 } }],
    position_strategy: { class_name: 'AbuPositionBase', parameters: {} },
    parameters: { capital: 200000, commission: 0.002 },
    tags: ['test', 'public'],
    umpire_rules: []
  },
];

describe('Strategy API', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen();
  });
  
  afterEach(() => {
    // 清理所有mock
    vi.clearAllMocks();
    // 重置MSW处理器
    server.resetHandlers();
    // 重置Pinia store状态
    const store = useAppStore();
    store.$reset();
  });

  afterAll(() => {
    server.close();
  });

  describe('getStrategies', () => {
    it('should return a list of strategies successfully', async () => {
      const strategies = await getStrategies();
      expect(strategies).toEqual(mockStrategies);
      expect(strategies.length).toBe(2);
    });

    it('should throw error when server returns 500', async () => {
        server.use(http.get('/api/strategies', () => new HttpResponse('Internal server error', { status: 500 })));
        await expect(getStrategies()).rejects.toThrow('Internal server error');
    });

    it('should set loading to true while fetching strategies', async () => {
      const store = useAppStore();
      const promise = getStrategies();
      // 等待下一个tick以确保loading状态已更新
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(true);
      await promise;
      // 等待响应处理完成后再检查loading状态
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(false);
    });

    it('should throw error when access is forbidden', async () => {
      server.use(http.get('/api/strategies', () => new HttpResponse('Access denied: insufficient permissions', { status: 403 })));
      await expect(getStrategies()).rejects.toThrow('Access denied: insufficient permissions');
    });
  });

  describe('getStrategy', () => {
    it('should return a single strategy successfully', async () => {
      const strategy = await getStrategy('1');
      expect(strategy).toEqual(mockStrategies[0]);
    });

    it('should throw error when strategy is not found', async () => {
      await expect(getStrategy('999')).rejects.toThrow('Strategy not found');
    });

    it('should throw error when strategy ID format is invalid', async () => {
        await expect(getStrategy('invalid-id')).rejects.toThrow('Invalid strategy ID format');
    });

    it('should throw error when access to strategy is forbidden', async () => {
      await expect(getStrategy('forbidden')).rejects.toThrow('Access denied: insufficient permissions');
    });
  });

  describe('createStrategy', () => {
    it('should create a new strategy successfully', async () => {
      const newStrategy: CreateStrategyRequest = { name: 'New Strategy', description: 'New Desc', content: 'New Content', is_public: false, buy_factors: [], sell_factors: [], parameters: {} };
      const created = await createStrategy(newStrategy);
      expect(created).toHaveProperty('id', '3');
      expect(created.name).toBe('New Strategy');
    });

    it('should set loading to true while creating strategy', async () => {
      const store = useAppStore();
      const promise = createStrategy({ name: 'New Strategy', description: 'A new one', content: 'New Content', is_public: false, buy_factors: [], sell_factors: [], parameters: {} });
      // 等待下一个tick以确保loading状态已更新
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(true);
      await promise;
      // 等待响应处理完成后再检查loading状态
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(false);
    });

    it('should throw error when validation fails', async () => {
      const newStrategy: CreateStrategyRequest = { name: '', description: '', content: '', is_public: false, buy_factors: [], sell_factors: [], parameters: {} };
      await expect(createStrategy(newStrategy)).rejects.toThrow('Name is required');
    });

    it('should throw error when strategy name already exists', async () => {
        const newStrategy: CreateStrategyRequest = { name: 'Existing Strategy', description: '', content: 'Some Content', is_public: false, buy_factors: [], sell_factors: [], parameters: {} };
        await expect(createStrategy(newStrategy)).rejects.toThrow('Strategy with this name already exists');
    });

    it('should throw error when create access is forbidden', async () => {
      const newStrategy: CreateStrategyRequest = { name: 'Forbidden Strategy', description: 'Test', content: 'Test Content', is_public: false, buy_factors: [], sell_factors: [], parameters: {} };
      await expect(createStrategy(newStrategy)).rejects.toThrow('Access denied: insufficient permissions');
    });
  });

  describe('updateStrategy', () => {
    it('should update a strategy successfully', async () => {
      const updates: UpdateStrategyRequest = { name: 'Updated Name' };
      const updated = await updateStrategy('1', updates);
      expect(updated.name).toBe('Updated Name');
    });

    it('should set loading to true while updating strategy', async () => {
      const store = useAppStore();
      const promise = updateStrategy('1', { name: 'Updated Strategy' });
      // 等待下一个tick以确保loading状态已更新
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(true);
      await promise;
      // 等待响应处理完成后再检查loading状态
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(false);
    });

    it('should throw error when strategy to update is not found', async () => {
      const updates: UpdateStrategyRequest = { name: 'Updated Name' };
      await expect(updateStrategy('not-found', updates)).rejects.toThrow('Strategy not found');
    });

    it('should throw error when update conflict occurs', async () => {
        const updates: UpdateStrategyRequest = { name: 'Updated Name' };
        await expect(updateStrategy('conflict', updates)).rejects.toThrow('Update conflict: strategy was modified by another user');
    });

    it('should throw error when update access is forbidden', async () => {
      const updates: UpdateStrategyRequest = { name: 'Updated Name' };
      await expect(updateStrategy('forbidden', updates)).rejects.toThrow('Access denied: insufficient permissions');
    });
  });

  describe('deleteStrategy', () => {
    it('should delete a strategy successfully', async () => {
      await expect(deleteStrategy('1')).resolves.toBeUndefined();
    });

    it('should set loading to true while deleting strategy', async () => {
      const store = useAppStore();
      const promise = deleteStrategy('1');
      // 等待下一个tick以确保loading状态已更新
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(true);
      await promise;
      // 等待响应处理完成后再检查loading状态
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(false);
    });

    it('should throw error when strategy to delete is not found', async () => {
      await expect(deleteStrategy('not-found')).rejects.toThrow('Strategy not found');
    });

    it('should throw error when strategy is in use', async () => {
        await expect(deleteStrategy('in-use')).rejects.toThrow('Cannot delete strategy: strategy is currently in use');
    });

    it('should throw error when delete access is forbidden', async () => {
      await expect(deleteStrategy('forbidden')).rejects.toThrow('Access denied: insufficient permissions');
    });
  });
});