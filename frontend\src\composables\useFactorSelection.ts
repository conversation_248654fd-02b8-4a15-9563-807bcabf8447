import { ref } from 'vue'
import type { Strategy } from '@/api/types'
import type { Factor } from '@/types'
import { useFactorsStore } from '@/stores'

/**
 * 因子选择组合函数
 * 管理因子选择对话框的状态和逻辑
 */
export function useFactorSelection() {
  // 对话框状态
  const isDialogVisible = ref(false)
  const currentFactorType = ref<'buy' | 'sell' | null>(null)
  const editingType = ref<'buy' | 'sell' | null>(null)
  const editingIndex = ref<number | null>(null)
  
  // 因子选择状态
  const selectedFactor = ref<Factor | null>(null)
  const searchKeyword = ref('')
  
  // 因子store
  const factorsStore = useFactorsStore()
  
  // 买入因子列表（用于对话框）
  const buyFactorsForDialog = ref([])
  
  // 卖出因子列表（用于对话框）
  const sellFactorsForDialog = ref([])
  
  // 打开因子选择对话框
  const openDialog = (factorType: 'buy' | 'sell') => {
    currentFactorType.value = factorType
    editingType.value = null // 新增模式
    editingIndex.value = null
    isDialogVisible.value = true
  }
  
  // 编辑现有因子
  const editFactor = (type: 'buy' | 'sell', index: number, factor: any) => {
    currentFactorType.value = type
    editingType.value = type
    editingIndex.value = index
    isDialogVisible.value = true
  }
  
  // 删除因子
  const deleteFactor = (strategy: Strategy, type: 'buy' | 'sell', index: number): Partial<Strategy> => {
    const key = type === 'buy' ? 'buy_factors' : 'sell_factors'
    const factors = [...(strategy[key] || [])]
    
    if (index >= 0 && index < factors.length) {
      factors.splice(index, 1)
    }
    
    return {
      [key]: factors
    }
  }
  
  // 添加或更新因子
  const saveFactor = (
    strategy: Strategy, 
    type: 'buy' | 'sell', 
    factor: any, 
    editIndex?: number
  ): Partial<Strategy> => {
    const key = type === 'buy' ? 'buy_factors' : 'sell_factors'
    const factors = [...(strategy[key] || [])]
    
    if (editIndex !== undefined && editIndex >= 0) {
      // 编辑模式：替换现有因子
      factors[editIndex] = factor
    } else {
      // 新增模式：添加到末尾
      factors.push(factor)
    }
    
    return {
      [key]: factors
    }
  }
  
  // 选择因子
  const selectFactor = (factor: Factor) => {
    selectedFactor.value = factor
  }
  
  // 清除选择
  const clearSelection = () => {
    selectedFactor.value = null
  }
  
  // 重置状态
  const reset = () => {
    selectedFactor.value = null
    searchKeyword.value = ''
    isDialogVisible.value = false
    currentFactorType.value = null
    editingType.value = null
    editingIndex.value = null
  }
  
  // 初始化因子选择
  const initializeFactorSelection = async () => {
    try {
      await factorsStore.fetchFactors()
    } catch (error) {
      console.error('Failed to initialize factor selection:', error)
    }
  }
  
  // 关闭对话框
  const closeDialog = () => {
    isDialogVisible.value = false
    currentFactorType.value = null
    editingType.value = null
    editingIndex.value = null
  }
  
  return {
    // 状态
    isDialogVisible,
    currentFactorType,
    editingType,
    editingIndex,
    selectedFactor,
    searchKeyword,
    buyFactorsForDialog,
    sellFactorsForDialog,
    factorsStore,
    
    // 方法
    openDialog,
    editFactor,
    deleteFactor,
    saveFactor,
    selectFactor,
    clearSelection,
    reset,
    initialize: initializeFactorSelection,
    closeDialog
  }
}

/**
 * 因子操作工具函数
 */
export const factorUtils = {
  /**
   * 验证因子配置
   */
  validateFactor: (factor: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (!factor) {
      errors.push('因子不能为空')
      return {
        isValid: false,
        errors
      }
    }
    
    if (!factor.class_name) {
      errors.push('因子类名不能为空')
    }
    
    if (!factor.factor_type) {
      errors.push('因子类型不能为空')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  },
  
  /**
   * 格式化因子用于显示
   */
  formatFactorForDisplay: (factor: any) => {
    if (!factor) {
      return {
        name: '未命名因子',
        description: '无描述',
        parameters: {},
        type: 'unknown'
      }
    }
    return {
      name: factor.name || factor.class_name || '未命名因子',
      description: factor.description || '无描述',
      parameters: factor.parameters || {},
      type: factor.factor_type || 'unknown'
    }
  },
  
  /**
   * 创建因子副本
   */
  cloneFactor: (factor: any) => {
    if (!factor) {
      return factor
    }
    return JSON.parse(JSON.stringify(factor))
  }
}