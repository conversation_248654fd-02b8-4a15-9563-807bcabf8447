import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StrategyManager from '@/views/StrategyManager.vue'
import { useStrategyStore } from '@/stores/useStrategyStore'

// 模拟策略Store
vi.mock('@/stores/useStrategyStore')

describe('StrategyManager.vue', () => {
  let wrapper: VueWrapper<any>
  let mockStrategyStore: any

  beforeEach(() => {
    // 设置Pinia
    const pinia = createPinia()
    setActivePinia(pinia)

    // 模拟策略Store
    mockStrategyStore = {
      strategies: [],
      loading: false,
      error: null,
      fetchStrategies: vi.fn(),
      createStrategy: vi.fn(),
      updateStrategy: vi.fn(),
      deleteStrategy: vi.fn()
    }
    
    vi.mocked(useStrategyStore).mockReturnValue(mockStrategyStore)

    wrapper = mount(StrategyManager, {
      global: {
        plugins: [pinia]
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染策略管理标题', () => {
      expect(wrapper.find('h1').text()).toBe('策略管理')
    })

    it('应该显示开发中提示信息', () => {
      expect(wrapper.find('p').text()).toContain('功能正在开发中')
    })

    it('应该正确挂载组件', () => {
      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('Store集成测试', () => {
    it('应该正确调用策略Store', () => {
      expect(useStrategyStore).toHaveBeenCalled()
    })
  })

  describe('可访问性测试', () => {
    it('应该具有正确的语义结构', () => {
      const heading = wrapper.find('h1')
      expect(heading.exists()).toBe(true)
      expect(heading.element.tagName).toBe('H1')
    })
  })
})