import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useBacktestStore } from '../../src/stores/modules/useBacktestStore'
import * as backtestApi from '../../src/api/modules/backtest'
import type { BacktestConfig, BacktestResult, BacktestTask } from '../../src/api/types/backtest'
import { BacktestStatus } from '../../src/api/types/backtest'
import { BacktestDataFactory } from '../factories/BacktestDataFactory'
import { SimpleBacktestDataFactory } from '../factories/SimpleBacktestDataFactory'

// Mock API modules
vi.mock('../../src/api/modules/backtest', () => ({
  startBacktest: vi.fn(),
  getBacktestResults: vi.fn(),
  getBacktestHistory: vi.fn(),
  stopBacktest: vi.fn(),
  getBacktestProgress: vi.fn(),
  deleteBacktest: vi.fn()
}))

const mockBacktestApi = vi.mocked(backtestApi)

describe('useBacktestStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const store = useBacktestStore()
      
      expect(store.isBacktesting).toBe(false)
      expect(store.backtestProgress).toBe(0)
      expect(store.backtestResult).toBeNull()
      expect(store.backtestHistory).toEqual([])
      expect(store.backtestError).toBe('')
      expect(store.isLoadingResults).toBe(false)
      expect(store.currentBacktestTask).toBeNull()
    })
  })

  describe('getters', () => {
    it('hasResult应该正确判断是否有结果', () => {
      const store = useBacktestStore()
      
      expect(store.hasResult).toBe(false)
      
      store.backtestResult = BacktestDataFactory.createBacktestResult()
      expect(store.hasResult).toBe(true)
    })

    it('isCompleted应该正确判断任务是否完成', () => {
      const store = useBacktestStore()
      
      expect(store.isCompleted).toBe(false)
      
      const config = BacktestDataFactory.createValidConfig()
      const task = SimpleBacktestDataFactory.createSimpleTask(config)
      task.status = BacktestStatus.COMPLETED
      store.currentBacktestTask = task
      expect(store.isCompleted).toBe(true)
    })

    it('hasActiveBacktest应该正确判断是否有活跃回测', () => {
      const store = useBacktestStore()
      
      expect(store.hasActiveBacktest).toBe(false)
      
      store.isBacktesting = true
      expect(store.hasActiveBacktest).toBe(true)
    })
  })

  describe('startBacktest', () => {
    it('应该能够成功启动回测', async () => {
      const store = useBacktestStore()
      const config: BacktestConfig = BacktestDataFactory.createValidConfig()
      const task: BacktestTask = SimpleBacktestDataFactory.createSimpleTask(config)
      
      mockBacktestApi.startBacktest.mockResolvedValue({ data: task })
      
      await store.startBacktest(config)
      
      expect(mockBacktestApi.startBacktest).toHaveBeenCalledWith(config)
      expect(store.isBacktesting).toBe(true)
      expect(store.currentBacktestTask).toEqual(task)
      expect(store.backtestError).toBe('')
    })

    it('应该处理启动回测失败的情况', async () => {
      const store = useBacktestStore()
      const config: BacktestConfig = BacktestDataFactory.createValidConfig()
      const error = new Error('启动回测失败')
      
      mockBacktestApi.startBacktest.mockRejectedValue(error)
      
      await store.startBacktest(config)
      
      expect(store.isBacktesting).toBe(false)
      expect(store.backtestError).toBe('启动回测失败')
      expect(store.currentBacktestTask).toBeNull()
    })

    it('应该防止重复启动回测', async () => {
      const store = useBacktestStore()
      const config: BacktestConfig = BacktestDataFactory.createValidConfig()
      
      store.isBacktesting = true
      
      await store.startBacktest(config)
      
      expect(mockBacktestApi.startBacktest).not.toHaveBeenCalled()
    })
  })

  describe('loadBacktestResults', () => {
    it('应该能够加载回测结果', async () => {
      const store = useBacktestStore()
      const taskId = 'test-task-id'
      const result: BacktestResult = BacktestDataFactory.createBacktestResult()
      
      mockBacktestApi.getBacktestResults.mockResolvedValue({ data: result })
      
      await store.loadBacktestResults(taskId)
      
      expect(mockBacktestApi.getBacktestResults).toHaveBeenCalledWith(taskId)
      expect(store.backtestResult).toEqual(result)
      expect(store.isLoadingResults).toBe(false)
    })

    it('应该处理taskId为空的情况', async () => {
      const store = useBacktestStore()
      
      await store.loadBacktestResults('')
      
      expect(store.backtestError).toBe('Task ID is required')
      expect(mockBacktestApi.getBacktestResults).not.toHaveBeenCalled()
    })

    it('应该处理加载结果失败的情况', async () => {
      const store = useBacktestStore()
      const taskId = 'test-task-id'
      const error = new Error('加载结果失败')
      
      mockBacktestApi.getBacktestResults.mockRejectedValue(error)
      
      await store.loadBacktestResults(taskId)
      
      expect(store.backtestError).toBe('加载结果失败')
      expect(store.isLoadingResults).toBe(false)
    })
  })

  describe('stopCurrentBacktest', () => {
    it('应该能够停止当前回测', async () => {
      const store = useBacktestStore()
      const taskId = 'test-task-id'
      
      const config = BacktestDataFactory.createValidConfig()
      const task = SimpleBacktestDataFactory.createSimpleTask({ ...config, strategy_id: taskId })
      task.id = taskId // 确保任务ID正确
      store.currentBacktestTask = task
      mockBacktestApi.stopBacktest.mockResolvedValue({ data: { success: true } })
      
      await store.stopCurrentBacktest()
      
      expect(mockBacktestApi.stopBacktest).toHaveBeenCalledWith(taskId)
      expect(store.isBacktesting).toBe(false)
    })

    it('应该处理没有当前任务的情况', async () => {
      const store = useBacktestStore()
      
      await store.stopCurrentBacktest()
      
      expect(mockBacktestApi.stopBacktest).not.toHaveBeenCalled()
    })
  })

  describe('fetchBacktestHistory', () => {
    it('应该能够获取回测历史', async () => {
      const store = useBacktestStore()
      const strategyId = 'test-strategy-id'
      
      await store.fetchBacktestHistory(strategyId)
      
      // 验证返回了模拟的历史数据
      expect(store.backtestHistory).toHaveLength(2)
      expect(store.backtestHistory[0].strategy_id).toBe(strategyId)
      expect(store.backtestHistory[1].strategy_id).toBe(strategyId)
      expect(store.isLoadingResults).toBe(false)
    })

    it('应该处理获取历史失败的情况', async () => {
      const store = useBacktestStore()
      const strategyId = 'test-strategy-id'
      
      // 模拟fetchBacktestHistory方法抛出错误
      vi.spyOn(store, 'fetchBacktestHistory').mockImplementation(async () => {
        store.isLoadingResults = true
        store.backtestError = ''
        try {
          throw new Error('获取历史失败')
        } catch (error) {
          store.backtestError = error instanceof Error ? error.message : 'Unknown error'
        } finally {
          store.isLoadingResults = false
        }
      })
      
      await store.fetchBacktestHistory(strategyId)
      
      expect(store.backtestError).toBe('获取历史失败')
    })
  })

  describe('deleteBacktest', () => {
    it('应该能够删除回测任务', async () => {
      const store = useBacktestStore()
      const taskId = 'test-task-id'
      
      // 先添加一些历史数据
      store.backtestHistory = [
        { id: 'test-task-id', strategy_id: 'strategy-1', strategy_name: '策略1', symbol: 'hs300', start_date: '2023-01-01', end_date: '2023-12-31', status: 'completed', created_at: '2023-01-01T00:00:00Z', capital: 1000000 },
        { id: 'other-task-id', strategy_id: 'strategy-2', strategy_name: '策略2', symbol: 'sz50', start_date: '2023-06-01', end_date: '2023-12-31', status: 'completed', created_at: '2023-06-01T00:00:00Z', capital: 2000000 }
      ]
      
      await store.deleteBacktest(taskId)
      
      // 验证任务已从历史记录中移除
      expect(store.backtestHistory).toHaveLength(1)
      expect(store.backtestHistory[0].id).toBe('other-task-id')
    })

    it('应该处理删除失败的情况', async () => {
      const store = useBacktestStore()
      const taskId = 'test-task-id'
      
      // 模拟删除过程中的错误
      vi.spyOn(store, 'deleteBacktest').mockImplementation(async () => {
        store.backtestError = '删除失败'
        throw new Error('删除失败')
      })
      
      try {
        await store.deleteBacktest(taskId)
      } catch (error) {
        // 预期的错误
      }
      
      expect(store.backtestError).toBe('删除失败')
    })
  })

  describe('utility methods', () => {
    it('clearError应该清除错误信息', () => {
      const store = useBacktestStore()
      
      store.backtestError = '测试错误'
      store.clearError()
      
      expect(store.backtestError).toBe('')
    })

    it('resetBacktestState应该重置回测状态', () => {
      const store = useBacktestStore()
      
      // 设置一些状态
      store.isBacktesting = true
      store.backtestProgress = 50
      store.backtestResult = BacktestDataFactory.createBacktestResult()
      store.backtestError = '测试错误'
      
      store.resetBacktestState()
      
      expect(store.isBacktesting).toBe(false)
      expect(store.backtestProgress).toBe(0)
      expect(store.backtestResult).toBeNull()
      expect(store.backtestError).toBe('')
    })
  })
})