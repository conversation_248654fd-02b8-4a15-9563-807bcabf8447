abu_modern 量化投研平台前端总设计图 (Grand Blueprint for Frontend)
版本： V3.0 (最终版)
核心设计哲学：
工作流驱动 (Workflow-Centric): 引导用户无缝完成 想法 -> 策略构建 -> 回测验证 -> 参数优化 -> 模拟/实盘 的完整闭环。
专业性与易用性兼备 (Professional yet Intuitive): 将复杂的量化概念，通过直观的UI和智能引导，变得易于普通用户理解和使用。
渐进式增强 (Progressive Enhancement): MVP阶段提供核心价值，未来版本平滑地增加高级功能，而不破坏核心体验。
第一部分：应用整体结构与导航
    1. 整体布局 (The Shell):
        采用经典的顶栏(Header)、**可伸缩侧边栏(Aside)和主内容区(Main)**的三栏式后台管理布局。
    2. 用户系统 (User System):
        页面： Login.vue, Register.vue (远期), Profile.vue。
        MVP实现： 采用单一用户的“硬编码”认证，只实现一个极简的Login.vue页面，绕过注册流程。
        长期规划： 实现完整的多用户注册、登录、个人信息管理（如API Key设置）功能。
    3. 侧边栏导航 (Sidebar Navigation - The Site Map):
        [✓] 仪表盘 (Dashboard)
        [✓] 策略工场 (Strategy Workshop)
        [✓] 市场中心 (Market Center)
        [✓] 选股器 (Screener) (名称优化)
        [✓] 交易驾驶舱 (Cockpit) (名称优化)
        [✓] 系统设置 (Settings)
第二部分：核心页面模块详细设计 (最终版)
    1. 仪表盘 (Dashboard)
        定位： 信息中枢，提供“黄金三秒”全局概览。
        布局： “2+2”四象限布局。
        模块：
        市场脉搏: 主要指数K线图。
        绩效概览: 模拟/实盘总权益曲线 vs 基准。
        信号与机会: [实时信号中心] 的UI体现，一个实时滚动的信号列表。
        策略健康度: 策略统计与Top 5榜单，提供到**[策略工场]**的快捷入口。
    2. 策略工场 (Strategy Workshop) - 平台的“中央车站”
        定位： 策略的创建、管理、配置、优化和所有后续行动的发起点。
        布局： 左侧“我的策略”列表 + 右侧“策略详情/编辑器” (ElTabs) 的两栏式布局。
        核心交互流程 - 可视化因子编辑器 (已由高保真设计图最终确认):
        空状态： 界面清晰地分为“买入规则”和“卖出规则”两个区域，并有“+ 添加因子”按钮和友好的空状态提示。
        添加因子 - 步骤1 (选择)：
            点击“+ 添加因子”按钮，弹出一个模态对话框。
            对话框内，以列表形式清晰展示所有从GET /api/v1/factors获取的可用因子（包含友好名称和描述），并提供搜索功能。
        添加因子 - 步骤2 (配置)：
            用户选择一个因子并点击“下一步”后，对话框内容切换为参数配置表单。
            表单根据所选因子的元数据动态渲染出对应的参数输入控件。
        添加因子 - 步骤3 (展示)：
            用户“确认添加”后，对话框关闭。一个新的因子卡片出现在主界面的相应区域。
            卡片上清晰展示因子的名称、配置的参数，并提供**“编辑”和“删除”**按钮。
            其他标签页规划 (保持不变):
        高级设置: [融入式] 仓位管理(BetaBu)与策略级风控(UmpBu)配置。
        参数优化: [独立流程] 基于MetricsBu-GridSearch的异步参数寻优工作台。
        回测与方案: 发起回测的入口，展示回测历史列表，并提供**“生成投资交易方案”**的核心价值功能。
    3. 市场中心 (Market Center)
        定位： 专业的数据探索工具。
        功能： K线图（可叠加IndicatorBu指标，可进行TLineBu分析）、基本面、财务数据等。
    4. 选股器 (Stock Screener) - [新增独立页面]
        定位： “思想”的另一个源头，帮助用户发现投资机会。
        布局： 条件选择区 + 结果展示区。
        功能：
            用户通过UI点选AlphaBu/PickStockBu提供的各种选股条件（如“N日新高”、“小市值”、“形态相似”等）。
            执行选股任务，并在表格中展示选出的股票列表。
            提供将选股结果“一键创建为股票池”或“一键应用策略进行批量回测”的功能。
    5. 交易驾驶舱 (Trading Cockpit) - [新增独立页面]
        定位： “所练即所用”，从模拟到实盘的统一操作界面。
        布局： 经典的交易软件布局（账户概览、持仓列表、委托/成交、操作区）。
        核心功能:
            模式切换器: 页面顶部一个极其醒目的开关 [ 模拟盘 ] / [ 实盘: 券商名称 ]。
            策略部署与管理: 正在运行的策略列表，提供“启动/暂停/停止”操作。
        账户模块: 实时资金概览、持仓列表（含成本、浮动盈亏）。
        交易模块: 今日委托、今日成交、历史成交查询。
        手动下单模块 (可选): 提供一个简单的手动下单面板，作为程序化交易的补充。
    6. 系统设置 (Settings)
        定位： 全局配置中心。
        功能：
        基础配置（API Keys等）。
        [融入式] 全局风险控制 (UmpBu) 配置。
        [独立流程] 风控模型管理： 提供“训练风控模型”的按钮，调用POST /api/umpires/train。
    7. 回测分析报告 (Backtest Analysis Report) - [独立的路由页面，但无导航入口]
        定位: 这是一个**“结果页”**，而不是一个功能入口。它没有独立的侧边栏导航。
        访问方式: 只能从**[策略工场]**的“回测历史”列表点击跳转过来。
        功能: 全方位、多维度、深度展示单次回测的结果。

第三部分：开发策略与启动指令
    开发模式 (最终确认):
    严格遵循“自顶向下”模式： 先搭骨架 (Layout)，再布路由 (Routing)，最后逐一填充页面模块 (Views & Components)。
    启动指令 (最终确认):
        第一步 (骨架搭建):
            创建所有核心页面的“空壳”文件。
            配置完整的路由和侧边栏导航，确保应用的“交通网络”完全通畅。
        第二步 (核心攻坚):
            在骨架搭建完成后，立即集中全部火力，首先攻坚“策略工场”的“核心配置”标签页。
            严格按照我们已有的、包含完整交互流程的三张高保真设计图，以像素级精度，实现整个“可视化因子编辑器”的功能。