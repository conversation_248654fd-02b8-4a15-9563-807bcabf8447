{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test": "vitest tests/ --run", "test:tdd": "vitest tests/unit/ --watch", "test:unit": "vitest tests/unit/ --run", "test:integration": "vitest tests/integration/ --run", "test:e2e": "vitest tests/e2e/ --run", "test:all": "vitest tests/ --run", "test:coverage": "vitest tests/ --coverage", "test:api": "vitest tests/api/ --run", "test:stores": "vitest tests/stores/ --run", "test:utils": "vitest tests/utils/ --run", "test:components": "vitest tests/components/ --run", "test:watch": "vitest tests/ --watch", "test:ci": "vitest run --coverage --reporter=json --reporter=html", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix"}, "lint-staged": {"*.{vue,js,ts,jsx,tsx}": ["eslint --fix"]}, "dependencies": {"axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.4.27", "vue-router": "^4.4.0"}, "devDependencies": {"@iconify-json/ep": "^1.2.2", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/compiler-sfc": "^3.4.27", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "msw": "^2.10.4", "sass": "^1.77.4", "typescript": "^5.4.5", "unplugin-auto-import": "^0.17.6", "unplugin-element-plus": "^0.8.0", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.0", "vite": "^5.3.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vue-tsc": "^2.0.21", "zod": "^4.0.14"}}