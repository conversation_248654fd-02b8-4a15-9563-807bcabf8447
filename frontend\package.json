{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.4.27", "vue-router": "^4.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "@vue/compiler-sfc": "^3.4.27", "@vue/tsconfig": "^0.7.0", "sass": "^1.77.4", "typescript": "^5.4.5", "unplugin-auto-import": "^0.17.6", "unplugin-element-plus": "^0.8.0", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.0", "vite": "^5.3.1", "vue-tsc": "^2.0.21"}}