# 策略API手动集成测试文档

本文档提供了策略API的手动测试案例，包括创建、查询、更新、删除策略，以及执行策略回测和获取可用因子列表的测试用例。

## 环境准备

1. 确保后端服务已启动
2. API 基础地址: `http://localhost:8000` (根据实际部署情况调整)
3. 测试工具: curl、Postman、浏览器插件（如Advanced REST Client）或其他API测试工具

## 1. 策略CRUD测试

### 1.1 创建策略 (POST /strategy/)

**请求URL:**
```
http://localhost:8000/strategy/
```

**请求体:**
```json
{
  "name": "测试双均线策略",
  "description": "使用5日均线和30日均线的金叉死叉策略",
  "is_public": true,
  "buy_factors": [
    {
      "name": "5日均线上穿30日均线买入",
      "description": "当5日均线上穿30日均线时买入",
      "factor_class": "FactorBuyTrend",
      "parameters": {
        "xd": 5,
        "past_factor": 1.0,
        "up_deg_threshold": 3
      }
    }
  ],
  "sell_factors": [
    {
      "name": "N日后卖出",
      "description": "持仓N日后卖出",
      "factor_class": "FactorSellNDay",
      "parameters": {
        "sell_n_day": 10
      }
    }
  ],
  "parameters": {
    "initial_capital": 100000,
    "n_folds": 2
  },
  "tags": ["均线", "趋势", "测试"]
}
```

**预期响应:**
- 状态码: 201 Created
- 响应包含成功消息和创建的策略信息，包括自动生成的ID

**异常测试:**
1. 空名称测试:
```json
{
  "name": "",
  "description": "测试空名称",
  "buy_factors": [...]
}
```
预期: 400 Bad Request，提示名称不能为空

2. 无买入因子测试:
```json
{
  "name": "无买入因子策略",
  "description": "测试没有买入因子的情况",
  "buy_factors": [],
  "sell_factors": [...]
}
```
预期: 400 Bad Request，提示至少需要一个买入因子

3. 未知因子类型测试:
```json
{
  "name": "未知因子类型策略",
  "description": "测试未知因子类型",
  "buy_factors": [
    {
      "name": "未知因子",
      "factor_class": "NonExistentFactor",
      "parameters": {}
    }
  ]
}
```
预期: 400 Bad Request，提示因子类型不存在

### 1.2 获取策略列表 (GET /strategy/)

**请求URL:**
```
http://localhost:8000/strategy/?skip=0&limit=10
```

**预期响应:**
- 状态码: 200 OK
- 包含策略列表和分页信息

**分页测试:**
1. 基础分页: `?skip=0&limit=5`
2. 第二页: `?skip=5&limit=5`
3. 大页码: `?skip=100&limit=10` (若数据不足，应返回空列表)

### 1.3 获取单个策略 (GET /strategy/{strategy_id})

**请求URL:**
```
http://localhost:8000/strategy/{STRATEGY_ID}
```
注: 使用从创建策略响应中获取的ID替换{STRATEGY_ID}

**预期响应:**
- 状态码: 200 OK
- 包含指定ID的策略详情

**异常测试:**
1. 无效ID测试: `/strategy/non-existent-id`
预期: 404 Not Found，提示策略不存在

### 1.4 更新策略 (PUT /strategy/{strategy_id})

**请求URL:**
```
http://localhost:8000/strategy/{STRATEGY_ID}
```

**请求体:**
```json
{
  "name": "更新后的均线策略",
  "description": "更新策略描述字段和参数",
  "parameters": {
    "initial_capital": 200000
  }
}
```

**预期响应:**
- 状态码: 200 OK
- 包含更新后的策略信息

**部分更新测试:**
1. 仅更新名称: `{"name": "新名称"}`
2. 仅更新买入因子: `{"buy_factors": [...]}`
3. 仅更新参数: `{"parameters": {"initial_capital": 500000}}`

### 1.5 删除策略 (DELETE /strategy/{strategy_id})

**请求URL:**
```
http://localhost:8000/strategy/{STRATEGY_ID}
```

**预期响应:**
- 状态码: 200 OK
- 包含删除成功的消息和删除的策略ID

**验证删除:**
1. 删除后尝试重新获取该策略，应返回404
2. 删除不存在的策略，应返回404

## 2. 策略执行测试 (POST /strategy/{strategy_id}/execute)

### 2.1 基本回测测试

**请求URL:**
```
http://localhost:8000/strategy/{STRATEGY_ID}/execute
```

**请求体:**
```json
{
  "choice_symbols": ["sz000001", "sh600000"],
  "start_date": "20220101",
  "end_date": "20221231",
  "capital": 1000000,
  "benchmark_symbol": "sh000300",
  "data_source": "local",
  "n_folds": 1
}
```

**预期响应:**
- 状态码: 200 OK
- 包含回测结果，包括交易记录、最终资金等

### 2.2 测试data_source参数

**不同数据源测试:**
1. 使用默认数据源(tushare):
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "end_date": "20220201",
  "benchmark_symbol": "sh000300"
}
```

2. 明确指定tushare数据源:
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "end_date": "20220201",
  "benchmark_symbol": "sh000300",
  "data_source": "tushare"
}
```

3. 指定local数据源:
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "end_date": "20220201",
  "benchmark_symbol": "sh000300",
  "data_source": "local"
}
```

4. 指定不支持的数据源:
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "end_date": "20220201",
  "benchmark_symbol": "sh000300",
  "data_source": "unknown_source"
}
```
预期: 400 Bad Request，提示不支持的数据源

### 2.3 参数缺失测试

1. 缺少choice_symbols:
```json
{
  "start_date": "20220101",
  "end_date": "20221231",
  "benchmark_symbol": "sh000300"
}
```
预期: 400 Bad Request，提示缺少必要参数

2. 缺少start_date:
```json
{
  "choice_symbols": ["sh000001"],
  "end_date": "20221231",
  "benchmark_symbol": "sh000300"
}
```
预期: 400 Bad Request，提示缺少必要参数

3. 缺少end_date:
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "benchmark_symbol": "sh000300"
}
```
预期: 400 Bad Request，提示缺少必要参数

4. 缺少benchmark_symbol:
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20220101",
  "end_date": "20221231"
}
```
预期: 400 Bad Request，提示缺少必要参数

### 2.4 格式错误测试

1. 日期格式错误:
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "2022/01/01",
  "end_date": "2022-12-31",
  "benchmark_symbol": "sh000300"
}
```
预期: 400 Bad Request，提示日期格式错误

2. 股票代码错误:
```json
{
  "choice_symbols": ["invalid_symbol"],
  "start_date": "20220101",
  "end_date": "20221231",
  "benchmark_symbol": "sh000300"
}
```
预期: 400 Bad Request，提示股票代码无效

### 2.5 性能指标验证测试

**目的:** 验证执行回测后，响应中是否包含完整且格式正确的性能指标。

**请求URL:**
```
http://localhost:8000/strategy/{STRATEGY_ID}/execute
```

**请求体:**
```json
{
  "choice_symbols": ["sh000001"],
  "start_date": "20210101",
  "end_date": "20221231",
  "capital": 1000000,
  "benchmark_symbol": "sh000300",
  "data_source": "tushare"
}
```

**预期响应:**
- 状态码: 200 OK
- 响应体中的 `execution_summary` 字段应包含以下所有性能指标，且值为数值类型（浮点数或整数）。

```json
{
  "status": "success",
  "message": "策略执行完成",
  "results": [...],
  "parameters_used": {...},
  "execution_summary": {
    "total_symbols": 1,
    "symbols_with_trades": 1,
    "total_trades": 10, // 示例值
    "initial_capital": 1000000.0,
    "final_capital": 1250000.0, // 示例值
    "annual_return": 0.118, // 示例值
    "benchmark_annual_return": 0.05, // 示例值
    "max_drawdown": -0.15, // 示例值
    "sharpe_ratio": 1.2, // 示例值
    "alpha": 0.06, // 示例值
    "beta": 0.9, // 示例值
    "win_rate": 0.6, // 示例值
    "profit_loss_ratio": 2.5, // 示例值
    "volatility": 0.18 // 示例值
  }
}
```

## 3. 获取可用因子列表测试 (GET /strategy/factors/)

### 3.1 获取所有因子

**请求URL:**
```
http://localhost:8000/strategy/factors/
```

**预期响应:**
- 状态码: 200 OK
- 包含买入因子和卖出因子列表

### 3.2 按类型筛选因子

1. 只获取买入因子:
```
http://localhost:8000/strategy/factors/?factor_type=buy
```

2. 只获取卖出因子:
```
http://localhost:8000/strategy/factors/?factor_type=sell
```

3. 无效的因子类型:
```
http://localhost:8000/strategy/factors/?factor_type=invalid
```
预期: 400 Bad Request，提示因子类型无效

## 4. 系统健壮性测试

### 4.1 持久化验证

1. 重启服务后验证策略是否保存:
   - 创建策略
   - 重启服务
   - 获取刚创建的策略，验证数据是否完整

### 4.2 并发请求测试

1. 同时发送多个创建策略请求
2. 同时发送多个查询不同策略的请求
3. 同时发送多个执行策略请求

### 4.3 错误恢复测试

1. 发送包含错误的请求后，继续发送正确的请求，确认API能正常响应

## 5. 文档符合性测试

验证以下几点:
1. 所有API响应是否符合文档中定义的格式
2. 错误响应是否包含清晰的错误消息
3. 参数拼写是否与文档一致(特别是choice_symbols而非symbols)
