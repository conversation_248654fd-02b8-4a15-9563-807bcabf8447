"""  
策略管理服务模块

提供策略的CRUD操作，包括创建、查询、更新和删除策略的功能。
使用SQLModel和SQLite实现数据库持久化存储。
"""

import uuid
import time
from datetime import datetime
from typing import List, Optional, Tuple, Dict, Any

from fastapi import Depends
from sqlmodel import Session, select

from backend.app.core.database import get_session
from backend.app.core.exceptions import DataNotFoundError, AdapterError, ParameterError, FactorError
from backend.app.models.strategy_model import StrategyModel
from backend.app.schemas.strategy import Strategy, StrategyCreate, StrategyUpdate
from backend.app.abupy_adapter import StrategyExecutor
from backend.app.abupy_adapter.factors_converter import FactorsConverter
from backend.app.services.base_service import BaseService


class StrategyService(BaseService):
    """策略服务类，提供基于数据库的策略CRUD操作"""
    """策略服务类，提供基于数据库的策略CRUD操作"""
    
    def __init__(self, session: Session):
        """初始化策略服务
        
        Args:
            session: SQLModel数据库会话
        """
        super().__init__(session)
    
    def create_strategy(self, strategy_data: StrategyCreate) -> Strategy:
        """创建新策略，确保嵌套模型被正确序列化为字典"""
        import logging
        try:
            # 预校验：检查买入和卖出因子的有效性
            if strategy_data.buy_factors:
                FactorsConverter.validate_factors(strategy_data.buy_factors)
            if strategy_data.sell_factors:
                FactorsConverter.validate_factors(strategy_data.sell_factors)
            # 1. 将所有嵌套的Pydantic对象转换为JSON兼容的字典
            buy_factors_dict = [bf.model_dump() for bf in strategy_data.buy_factors] if strategy_data.buy_factors else []
            sell_factors_dict = [sf.model_dump() for sf in strategy_data.sell_factors] if strategy_data.sell_factors else []
            position_strategy_dict = strategy_data.position_strategy.model_dump() if strategy_data.position_strategy else None
            
            # Umpire rules可能也需要转换，如果它们是Pydantic模型的话
            # 安全处理umpire_rules字段，支持None、空列表或Pydantic对象列表
            umpire_rules_dict = []
            if strategy_data.umpire_rules:
                if isinstance(strategy_data.umpire_rules, list):
                    # 如果是列表，检查每个元素是否有model_dump方法
                    for ur in strategy_data.umpire_rules:
                        if hasattr(ur, 'model_dump'):
                            umpire_rules_dict.append(ur.model_dump())
                        else:
                            # 如果已经是字典，直接使用
                            umpire_rules_dict.append(ur)
                else:
                    # 如果不是列表，设为空列表
                    umpire_rules_dict = []

            current_time = datetime.now()

            # 2. 使用转换后的数据创建数据库模型实例
            db_strategy = StrategyModel(
                id=uuid.uuid4().hex,
                name=strategy_data.name,
                description=strategy_data.description,
                create_time=current_time,
                update_time=current_time,
                is_public=strategy_data.is_public,
                
                # 使用字典列表填充JSON字段
                buy_factors=buy_factors_dict,
                sell_factors=sell_factors_dict,
                position_strategy=position_strategy_dict,
                umpire_rules=umpire_rules_dict,
                
                # 这两个字段已经是JSON兼容类型
                parameters=strategy_data.parameters,
                tags=strategy_data.tags
            )

            # 3. 数据库操作
            self.session.add(db_strategy)
            self.session.commit()
            self.session.refresh(db_strategy)

            # 4. 从数据库模型安全地转换回API Schema并返回
            return db_strategy.to_schema_strategy()

        except Exception as e:
            self.session.rollback()
            logging.error(f"创建策略失败: {e}", exc_info=True)
            # 可以考虑抛出一个更具体的数据库操作异常
            raise
    
    def get_strategy_by_id(self, strategy_id: str) -> Strategy:
        """通过ID获取策略

        Args:
            strategy_id: 策略ID

        Returns:
            找到的策略对象
            
        Raises:
            DataNotFoundError: 如果未找到策略
        """
        # 从数据库获取策略，使用 session.get 以确保最高效的主键查询
        import logging
        logging.info(f"[StrategyService] 正在查询策略，ID: {strategy_id}, 类型: {type(strategy_id)}")
        db_strategy = self.session.get(StrategyModel, strategy_id)
        
        if not db_strategy:
            self._handle_strategy_not_found(strategy_id)
        
        logging.info(f"[StrategyService] 查询成功：已找到策略 {db_strategy.name} (ID: {db_strategy.id})。")
        
        # 转换为API模型并返回
        return db_strategy.to_schema_strategy()
    
    def get_all_strategies(self) -> List[Strategy]:
        """获取所有策略

        Returns:
            所有策略对象的列表
        """
        statement = select(StrategyModel)
        db_strategies = self.session.exec(statement).all()
        return [db_strategy.to_schema_strategy() for db_strategy in db_strategies]

    def get_strategies(self, skip: int = 0, limit: int = 100) -> List[Strategy]:
        """获取策略列表，支持分页

        Args:
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            策略对象列表
        """
        # 从数据库获取策略并应用分页，按创建时间倒序排列
        statement = select(StrategyModel).order_by(StrategyModel.create_time.desc()).offset(skip).limit(limit)
        db_strategies = self.session.exec(statement).all()
        
        # 转换为API模型并返回
        return [db_strategy.to_schema_strategy() for db_strategy in db_strategies]
    
    def get_all_strategies_paginated(self, skip: int, limit: int, owner: Optional[str] = None, is_public: Optional[bool] = None) -> Tuple[List[Strategy], int]:
        """分页获取所有策略，并支持筛选

        Args:
            skip: 跳过的记录数
            limit: 每页数量
            owner: 策略所有者 (可选)
            is_public: 是否公开 (可选)

        Returns:
            包含策略列表和总数的元组
        """
        # from sqlalchemy import func # 如果之前没有导入
        from sqlmodel import func

        query = select(StrategyModel)
        if owner is not None:
            query = query.where(StrategyModel.owner == owner)
        if is_public is not None:
            query = query.where(StrategyModel.is_public == is_public)

        # 查询总数
        # 创建一个副本用于计数，避免影响主查询的列
        count_query = select(func.count()).select_from(query.subquery())
        total = self.session.exec(count_query).one()

        # 查询当页数据，按创建时间倒序排列
        statement = query.order_by(StrategyModel.create_time.desc()).offset(skip).limit(limit)
        results = self.session.exec(statement).all()
        strategies = [db_strategy.to_schema_strategy() for db_strategy in results]

        return strategies, total

    def update_strategy(self, strategy_id: str, strategy_data: StrategyUpdate) -> Strategy:
        """更新策略，确保嵌套模型被正确序列化
        
        Args:
            strategy_id: 策略ID
            strategy_data: 要更新的策略数据
            
        Returns:
            更新后的策略对象
            
        Raises:
            DataNotFoundError: 如果未找到策略
        """
        import logging
        db_strategy = self.session.get(StrategyModel, strategy_id)
        if not db_strategy:
            self._handle_strategy_not_found(strategy_id)

        try:
            # 预校验：检查买入和卖出因子的有效性（如果有更新的话）
            if hasattr(strategy_data, 'buy_factors') and strategy_data.buy_factors is not None:
                FactorsConverter.validate_factors(strategy_data.buy_factors)
            if hasattr(strategy_data, 'sell_factors') and strategy_data.sell_factors is not None:
                FactorsConverter.validate_factors(strategy_data.sell_factors)
                
            # 获取用户提交的、需要更新的字段的字典
            update_data = strategy_data.model_dump(exclude_unset=True)

            for key, value in update_data.items():
                # 对嵌套的Pydantic模型进行特殊处理，转换为字典
                if key == "buy_factors" and value is not None:
                    # 检查是否已经是字典列表，如果是则直接使用，否则转换
                    if value and isinstance(value[0], dict):
                        setattr(db_strategy, key, value)
                    else:
                        setattr(db_strategy, key, [bf.model_dump() for bf in value])
                elif key == "sell_factors" and value is not None:
                    # 检查是否已经是字典列表，如果是则直接使用，否则转换
                    if value and isinstance(value[0], dict):
                        setattr(db_strategy, key, value)
                    else:
                        setattr(db_strategy, key, [sf.model_dump() for sf in value])
                elif key == "position_strategy" and value is not None:
                    # 检查是否已经是字典，如果是则直接使用，否则转换
                    if isinstance(value, dict):
                        setattr(db_strategy, key, value)
                    else:
                        setattr(db_strategy, key, value.model_dump())
                elif key == "umpire_rules" and value is not None:
                    # 检查是否已经是字典列表，如果是则直接使用，否则转换
                    if value and isinstance(value[0], dict):
                        setattr(db_strategy, key, value)
                    else:
                        setattr(db_strategy, key, [ur.model_dump() for ur in value])
                else:
                    # 其他字段直接赋值
                    setattr(db_strategy, key, value)

            # 手动设置update_time为当前时间
            db_strategy.update_time = datetime.now()
            
            self.session.add(db_strategy)
            self.session.commit()
            # 关键修复：在commit后refresh，以从数据库加载最新状态（包括自动更新的时间戳）
            self.session.refresh(db_strategy)

            return db_strategy.to_schema_strategy()

        except Exception as e:
            self.session.rollback()
            logging.error(f"更新策略 (ID: {strategy_id}) 失败: {e}", exc_info=True)
            raise

    def delete_strategy(self, strategy_id: str) -> bool:
        """删除策略

        Args:
            strategy_id: 要删除的策略ID

        Returns:
            如果删除成功返回True

        Raises:
            DataNotFoundError: 如果未找到策略
        """
        db_strategy = self.session.get(StrategyModel, strategy_id)
        if not db_strategy:
            self._handle_strategy_not_found(strategy_id)

        self.session.delete(db_strategy)
        self.session.commit()
        return True

    def execute_strategy(self, strategy_id: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行策略回测

        Args:
            strategy_id: 要执行的策略ID
            market_data: 市场数据，包含股票代码、回测时间范围等

        Returns:
            策略执行结果
            
        Raises:
            DataNotFoundError: 如果未找到策略
            AdapterError: 如果执行过程中发生错误
            ParameterError: 如果参数有误
            FactorError: 如果因子处理过程出错
        """
        import logging
        try:
            # 获取策略
            strategy = self.get_strategy_by_id(strategy_id)
            
            # 调用策略执行器执行回测
            return StrategyExecutor.execute_strategy(strategy, market_data)
        except DataNotFoundError as e:
            logging.error(f"[StrategyService] 在执行策略 {strategy_id} 期间捕获到 DataNotFoundError: {e}", exc_info=True)
            raise e
        except TypeError as e:
            # 专门捕获pandas兼容性相关的TypeError
            error_msg = str(e)
            if 'timedelta64' in error_msg or 'comparison' in error_msg.lower():
                logging.error(f"[StrategyService] 检测到pandas兼容性错误 - 策略ID: {strategy_id}, 错误: {error_msg}", exc_info=True)
                logging.error(f"[StrategyService] 市场数据: {market_data}")
                raise AdapterError(
                    message=f"pandas兼容性错误: {error_msg}。请检查ABuPickTimeWorker补丁是否正确应用。",
                    error_code="PANDAS_COMPATIBILITY_ERROR"
                )
            else:
                logging.error(f"[StrategyService] 其他TypeError - 策略ID: {strategy_id}, 错误: {error_msg}", exc_info=True)
                raise AdapterError(message=f"类型错误: {error_msg}", error_code="TYPE_ERROR")
        except (AdapterError, ParameterError, FactorError) as e:
            # 对于业务逻辑错误，直接传递，不需要回滚
            raise e
        except Exception as e:
            # 对于未预期的异常，包装为AdapterError以提供更清晰的错误信息
            logging.error(f"[StrategyService] 未预期异常 - 策略ID: {strategy_id}, 错误: {str(e)}", exc_info=True)
            raise AdapterError(message=f"执行策略时发生未预期的错误: {str(e)}", error_code="UNEXPECTED_EXECUTION_ERROR")
        finally:
            # 注意: 执行策略通常是只读操作，不需要提交或回滚
            pass

def get_strategy_service(session: Session = Depends(get_session)) -> StrategyService:
    """FastAPI 依赖注入函数，用于获取 StrategyService 实例"""
    return StrategyService(session=session)
