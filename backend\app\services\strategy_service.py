"""  
策略管理服务模块

提供策略的CRUD操作，包括创建、查询、更新和删除策略的功能。
使用SQLModel和SQLite实现数据库持久化存储。
"""

import uuid
import time
from datetime import datetime
from typing import List, Optional, Tuple, Dict, Any

from fastapi import Depends
from sqlmodel import Session, select

from backend.app.core.database import get_session
from backend.app.core.exceptions import DataNotFoundError, AdapterError, ParameterError, FactorError
from backend.app.models.strategy_model import StrategyModel
from backend.app.schemas.strategy import Strategy, StrategyCreate, StrategyUpdate
from backend.app.abupy_adapter import StrategyExecutor


class StrategyService:
    """策略服务类，提供基于数据库的策略CRUD操作"""
    
    def __init__(self, session: Session):
        """初始化策略服务
        
        Args:
            session: SQLModel数据库会话
        """
        self.session = session
    
    def create_strategy(self, strategy_data: StrategyCreate) -> Strategy:
        """创建新策略

        Args:
            strategy_data: 策略创建数据模型

        Returns:
            创建成功的策略对象
            
        Raises:
            FactorError: 如果提供的因子类型无效
            Exception: 数据库操作失败时抛出异常
        """
        try:
            # 注意：因子类型的验证已经移至 FactorsConverter.convert_to_abu_factors 方法中
            # 这里不再进行重复验证，以支持直接使用 abupy 原生因子类名

            # 生成唯一ID
            strategy_id = uuid.uuid4().hex
            
            # 获取当前时间作为创建和更新时间
            current_time = datetime.now()
            
            # 创建新策略对象
            strategy = Strategy(
                id=strategy_id,
                name=strategy_data.name,
                description=strategy_data.description,
                create_time=current_time,
                update_time=current_time,
                is_public=strategy_data.is_public,
                buy_factors=strategy_data.buy_factors,
                sell_factors=strategy_data.sell_factors,
                position_strategy=strategy_data.position_strategy,
                umpire_rules=strategy_data.umpire_rules,
                parameters=strategy_data.parameters,
                tags=strategy_data.tags
            )
            
            # 转换为数据库模型并存储
            db_strategy = StrategyModel.from_schema_strategy(strategy)
            self.session.add(db_strategy)
            self.session.commit()
            self.session.refresh(db_strategy)
            
            # 转换回API模型并返回
            return db_strategy.to_schema_strategy()

        except FactorError as e:
            # 因子错误不需要回滚，因为还没有进行数据库操作
            raise e
        except Exception as e:
            self.session.rollback()
            raise e
        finally:
            # 注意: 在使用依赖注入的session时不要在此关闭session
            # 因为session会在请求结束时由FastAPI自动关闭
            pass
    
    def get_strategy_by_id(self, strategy_id: str) -> Strategy:
        """通过ID获取策略

        Args:
            strategy_id: 策略ID

        Returns:
            找到的策略对象
            
        Raises:
            DataNotFoundError: 如果未找到策略
        """
        # 从数据库获取策略，使用 session.get 以确保最高效的主键查询
        import logging
        logging.info(f"[StrategyService] 正在查询策略，ID: {strategy_id}, 类型: {type(strategy_id)}")
        db_strategy = self.session.get(StrategyModel, strategy_id)
        
        if not db_strategy:
            logging.warning(f"[StrategyService] 查询失败：未在数据库中找到ID为 {strategy_id} 的策略。")
            raise DataNotFoundError(message=f"未找到ID为 {strategy_id} 的策略", error_code="STRATEGY_NOT_FOUND")
        
        logging.info(f"[StrategyService] 查询成功：已找到策略 {db_strategy.name} (ID: {db_strategy.id})。")
        
        # 转换为API模型并返回
        return db_strategy.to_schema_strategy()
    
    def get_all_strategies(self) -> List[Strategy]:
        """获取所有策略

        Returns:
            所有策略对象的列表
        """
        statement = select(StrategyModel)
        db_strategies = self.session.exec(statement).all()
        return [db_strategy.to_schema_strategy() for db_strategy in db_strategies]

    def get_strategies(self, skip: int = 0, limit: int = 100) -> List[Strategy]:
        """获取策略列表，支持分页

        Args:
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            策略对象列表
        """
        # 从数据库获取策略并应用分页
        statement = select(StrategyModel).offset(skip).limit(limit)
        db_strategies = self.session.exec(statement).all()
        
        # 转换为API模型并返回
        return [db_strategy.to_schema_strategy() for db_strategy in db_strategies]
    
    def get_all_strategies_paginated(self, skip: int, limit: int, owner: Optional[str] = None, is_public: Optional[bool] = None) -> Tuple[List[Strategy], int]:
        """分页获取所有策略，并支持筛选

        Args:
            skip: 跳过的记录数
            limit: 每页数量
            owner: 策略所有者 (可选)
            is_public: 是否公开 (可选)

        Returns:
            包含策略列表和总数的元组
        """
        # from sqlalchemy import func # 如果之前没有导入
        from sqlmodel import func

        query = select(StrategyModel)
        if owner is not None:
            query = query.where(StrategyModel.owner == owner)
        if is_public is not None:
            query = query.where(StrategyModel.is_public == is_public)

        # 查询总数
        # 创建一个副本用于计数，避免影响主查询的列
        count_query = select(func.count()).select_from(query.subquery())
        total = self.session.exec(count_query).one()

        # 查询当页数据
        statement = query.offset(skip).limit(limit)
        results = self.session.exec(statement).all()
        strategies = [db_strategy.to_schema_strategy() for db_strategy in results]

        return strategies, total

    def update_strategy(self, strategy_id: str, strategy_data: StrategyUpdate) -> Strategy:
        """更新策略

        Args:
            strategy_id: 要更新的策略ID
            strategy_data: 更新的策略数据

        Returns:
            更新后的策略对象

        Raises:
            DataNotFoundError: 如果未找到策略
            Exception: 数据库操作失败时抛出异常
        """
        db_strategy = self.session.get(StrategyModel, strategy_id)
        if not db_strategy:
            raise DataNotFoundError(f"Strategy with id {strategy_id} not found")

        update_data = strategy_data.model_dump(exclude_unset=True)
        
        # 单独处理复杂字段，确保正确更新
        if 'buy_factors' in update_data:
            db_strategy.buy_factors = [factor.dict() for factor in strategy_data.buy_factors]
            del update_data['buy_factors']
            
        if 'sell_factors' in update_data:
            db_strategy.sell_factors = [factor.dict() for factor in strategy_data.sell_factors]
            del update_data['sell_factors']

        if 'position_strategy' in update_data and strategy_data.position_strategy is not None:
            db_strategy.position_strategy = strategy_data.position_strategy.dict()
            del update_data['position_strategy']
        
        if 'umpire_rules' in update_data:
            db_strategy.umpire_rules = strategy_data.umpire_rules
            del update_data['umpire_rules']

        # 更新其他简单字段
        for key, value in update_data.items():
            setattr(db_strategy, key, value)

        db_strategy.update_time = datetime.now()

        self.session.add(db_strategy)
        self.session.commit()
        self.session.refresh(db_strategy)

        return db_strategy.to_schema_strategy()

    def delete_strategy(self, strategy_id: str) -> None:
        """删除指定ID的策略

        Args:
            strategy_id: 要删除的策略ID

        Raises:
            DataNotFoundError: 如果未找到指定ID的策略
            Exception: 如果数据库操作失败
        """
        db_strategy = self.session.get(StrategyModel, strategy_id)
        if not db_strategy:
            raise DataNotFoundError(f"Strategy with id {strategy_id} not found")

        self.session.delete(db_strategy)
        self.session.commit()

    def execute_strategy(self, strategy_id: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行策略回测

        Args:
            strategy_id: 要执行的策略ID
            market_data: 市场数据，包含股票代码、回测时间范围等

        Returns:
            策略执行结果
            
        Raises:
            DataNotFoundError: 如果未找到策略
            AdapterError: 如果执行过程中发生错误
            ParameterError: 如果参数有误
            FactorError: 如果因子处理过程出错
        """
        try:
            # 获取策略
            strategy = self.get_strategy_by_id(strategy_id)
            
            # 调用策略执行器执行回测
            return StrategyExecutor.execute_strategy(strategy, market_data)
        except DataNotFoundError as e:
            import logging
            logging.error(f"[StrategyService] 在执行策略 {strategy_id} 期间捕获到 DataNotFoundError: {e}", exc_info=True)
            raise e
        except (AdapterError, ParameterError, FactorError) as e:
            # 对于业务逻辑错误，直接传递，不需要回滚
            raise e
        except Exception as e:
            # 对于未预期的异常，包装为AdapterError以提供更清晰的错误信息
            raise AdapterError(message=f"执行策略时发生未预期的错误: {str(e)}", error_code="UNEXPECTED_EXECUTION_ERROR")
        finally:
            # 注意: 执行策略通常是只读操作，不需要提交或回滚
            pass

def get_strategy_service(session: Session = Depends(get_session)) -> StrategyService:
    """FastAPI 依赖注入函数，用于获取 StrategyService 实例"""
    return StrategyService(session=session)
