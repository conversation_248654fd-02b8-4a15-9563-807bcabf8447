import os
from pathlib import Path

def print_directory_tree(start_path, indent='', exclude_dirs=None):
    """打印目录树状结构，排除指定目录"""
    if exclude_dirs is None:
        exclude_dirs = ['.venv', '__pycache__', '.git',".env"]
    
    # 只显示源代码文件
    source_exts = ('.py')
    
    # 获取目录内容并排序
    try:
        contents = sorted(os.listdir(start_path))
    except PermissionError:
        return
    
    for i, item in enumerate(contents):
        path = os.path.join(start_path, item)
        is_last = i == len(contents) - 1
        
        # 跳过排除目录
        if os.path.isdir(path) and item in exclude_dirs:
            continue
            
        # 打印当前项
        if is_last:
            print(indent + '└── ' + item)
            new_indent = indent + '    '
        else:
            print(indent + '├── ' + item)
            new_indent = indent + '│   '
        
        # 如果是目录则递归处理
        if os.path.isdir(path):
            print_directory_tree(path, new_indent, exclude_dirs)
        # 如果是源代码文件则显示
        elif item.endswith(source_exts):
            continue

if __name__ == '__main__':
    print("后端源代码结构:")
    base_path = str(Path(__file__).parent)
    print_directory_tree(base_path)
