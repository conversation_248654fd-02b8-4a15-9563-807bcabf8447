import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { BacktestTestUtils } from '../../utils/BacktestTestUtils';
import type { BacktestMetrics, EquityPoint, Trade } from '../../../src/api/types/backtest';

describe('BacktestTestUtils - Extended TDD', () => {
  // ==================== 从TDD版本继承的基础测试 ====================
  describe('基础功能 (继承自TDD)', () => {
    describe('基础数据处理', () => {
      it('应该能验证数值精度 - TDD', () => {
        // 测试有效数值
        const validResult = BacktestTestUtils.validateNumericalPrecision(123.45, '测试数值');
        expect(validResult.isValid).toBe(true);
        expect(validResult.issues).toHaveLength(0);
        
        // 测试NaN
        const nanResult = BacktestTestUtils.validateNumericalPrecision(NaN, 'NaN测试');
        expect(nanResult.isValid).toBe(false);
        expect(nanResult.issues[0]).toContain('为NaN');
      });

      it('应该能验证百分比数值精度 - TDD', () => {
        // 测试相等的百分比
        expect(() => {
          BacktestTestUtils.expectToBeCloseToPercentage(0.1234, 0.1234, 2);
        }).not.toThrow();
        
        // 测试不相等的百分比
        expect(() => {
          BacktestTestUtils.expectToBeCloseToPercentage(0.1, 0.2, 2);
        }).toThrow('百分比数值不匹配');
      });

      it('应该能验证基础金融指标逻辑 - TDD', () => {
        const validMetrics: BacktestMetrics = {
          total_trades: 10,
          winning_trades: 6,
          losing_trades: 4,
          win_rate: 0.6,
          total_return: 0.15,
          annual_return: 0.15,
          max_drawdown: -0.05,
          sharpe_ratio: 1.2,
          profit_loss_ratio: 1.5,
          volatility: 0.2,
          avg_holding_period: 5
        };
        
        expect(() => {
          BacktestTestUtils.validateFinancialMetricsLogic(validMetrics);
        }).not.toThrow();
      });

      it('应该能检测无效的金融指标 - TDD', () => {
        const invalidMetrics: BacktestMetrics = {
          total_trades: 10,
          winning_trades: 6,
          losing_trades: 3, // 错误：6 + 3 ≠ 10
          win_rate: 0.6,
          total_return: 0.15,
          annual_return: 0.15,
          max_drawdown: -0.05,
          sharpe_ratio: 1.2,
          profit_loss_ratio: 1.5,
          volatility: 0.2,
          avg_holding_period: 5
        };
        
        expect(() => {
          BacktestTestUtils.validateFinancialMetricsLogic(invalidMetrics);
        }).toThrow('盈利交易数 + 亏损交易数 必须等于总交易数');
      });
    });

    describe('基础状态管理', () => {
      it('应该能等待DOM更新完成 - TDD', async () => {
        const startTime = performance.now();
        await BacktestTestUtils.waitForDOMUpdate();
        const endTime = performance.now();
        
        // 验证等待确实发生了
        expect(endTime - startTime).toBeGreaterThan(0);
      });

      it('应该能等待Promise队列清空 - TDD', async () => {
        let resolved = false;
        
        // 创建一个异步操作
        Promise.resolve().then(() => {
          resolved = true;
        });
        
        // 等待Promise队列清空
        await BacktestTestUtils.flushPromises();
        
        expect(resolved).toBe(true);
      });

      it('应该能测量操作响应时间 - TDD', async () => {
        const testOperation = async () => {
          await new Promise(resolve => setTimeout(resolve, 10));
          return 'test result';
        };
        
        const { result, duration } = await BacktestTestUtils.measureResponseTime(testOperation);
        
        expect(result).toBe('test result');
        expect(duration).toBeGreaterThan(5); // 至少5ms
        expect(duration).toBeLessThan(50); // 不超过50ms
      });

      it('应该能处理操作失败的响应时间测量 - TDD', async () => {
        const failingOperation = async () => {
          await new Promise(resolve => setTimeout(resolve, 5));
          throw new Error('测试错误');
        };
        
        await expect(BacktestTestUtils.measureResponseTime(failingOperation))
          .rejects.toThrow('测试错误');
      });
    });

    describe('基础Mock工具', () => {
      it('应该能创建网络错误场景 - TDD', () => {
        const scenarios = BacktestTestUtils.createNetworkErrorScenarios();
        
        expect(scenarios).toHaveLength(6);
        expect(scenarios[0].name).toBe('网络连接超时');
        expect(scenarios[1].name).toBe('服务器内部错误 (500)');
        
        // 验证每个场景都有必要的方法
        scenarios.forEach(scenario => {
          expect(scenario.setup).toBeTypeOf('function');
          expect(scenario.cleanup).toBeTypeOf('function');
        });
      });

      it('应该能创建数据源错误场景 - TDD', () => {
        const scenario = BacktestTestUtils.createDataSourceErrorScenario('tushare_quota_exceeded');
        
        expect(scenario.setup).toBeTypeOf('function');
        expect(scenario.cleanup).toBeTypeOf('function');
        expect(scenario.getExpectedError()).toBeInstanceOf(Error);
        expect(scenario.getStatusCode()).toBe(429);
        expect(scenario.getRetryAfter()).toBe(3600);
      });

      it('应该能创建数据源降级Mock - TDD', () => {
        const fallbackMock = BacktestTestUtils.createDataSourceFallbackMock(
          'tushare',
          'abu_legacy',
          'quota_exceeded'
        );
        
        expect(fallbackMock.success).toBe(true);
        expect(fallbackMock.data.requested_data_source).toBe('tushare');
        expect(fallbackMock.data.actual_data_source).toBe('abu_legacy');
        expect(fallbackMock.data.fallback_occurred).toBe(true);
        expect(fallbackMock.data.fallback_reason).toBe('quota_exceeded');
      });

      it('应该能创建Abu策略验证Mock - TDD', () => {
        const validMock = BacktestTestUtils.createAbuStrategyValidationMock('trend_follow', true);
        
        expect(validMock.success).toBe(true);
        expect(validMock.data.strategy_type).toBe('trend_follow');
        expect(validMock.data.abu_params).toBeDefined();
        expect(validMock.data.abu_params.period).toBe(20);
        
        const invalidMock = BacktestTestUtils.createAbuStrategyValidationMock('trend_follow', false);
        expect(invalidMock.error).toBeInstanceOf(Error);
      });
    });

    describe('基础验证工具', () => {
      it('应该能验证权益曲线基础数据 - TDD', () => {
        const validCurve: EquityPoint[] = [
          { date: '2024-01-01', equity: 100000, drawdown: 0 },
          { date: '2024-01-02', equity: 101000, drawdown: 0 },
          { date: '2024-01-03', equity: 99000, drawdown: -1.98 }
        ];
        
        expect(() => {
          BacktestTestUtils.validateEquityCurveContinuity(validCurve);
        }).not.toThrow();
      });

      it('应该能检测权益曲线日期顺序错误 - TDD', () => {
        const invalidCurve: EquityPoint[] = [
          { date: '2024-01-02', equity: 100000, drawdown: 0 },
          { date: '2024-01-01', equity: 101000, drawdown: 0 } // 日期倒序
        ];
        
        expect(() => {
          BacktestTestUtils.validateEquityCurveContinuity(invalidCurve);
        }).toThrow('日期顺序错误');
      });

      it('应该能验证Abu策略配置 - TDD', () => {
        const validConfig = {
          strategy_type: 'trend_follow',
          abu_params: {
            period: 20,
            threshold: 0.02
          },
          data_source: 'tushare'
        };
        
        const result = BacktestTestUtils.validateAbuStrategyConfig(validConfig);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('应该能检测无效的Abu策略配置 - TDD', () => {
        const invalidConfig = {
          strategy_type: 'invalid_strategy',
          abu_params: null,
          data_source: 'unknown_source'
        };
        
        const result = BacktestTestUtils.validateAbuStrategyConfig(invalidConfig);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0]).toContain('无效的abu策略类型');
      });
    });

    describe('基础API验证', () => {
      it('应该能验证数据源降级逻辑 - TDD', () => {
        const validResponse = {
          data: {
            fallback_occurred: true,
            requested_data_source: 'tushare',
            actual_data_source: 'abu_legacy',
            fallback_reason: 'quota_exceeded',
            data_quality_score: 0.85
          }
        };
        
        const result = BacktestTestUtils.validateDataSourceFallback(validResponse);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('应该能检测数据源降级配置错误 - TDD', () => {
        const invalidResponse = {
          data: {
            fallback_occurred: true,
            requested_data_source: 'tushare',
            actual_data_source: 'tushare', // 错误：降级后数据源相同
            fallback_reason: 'quota_exceeded'
          }
        };
        
        const result = BacktestTestUtils.validateDataSourceFallback(invalidResponse);
        expect(result.isValid).toBe(false);
        expect(result.errors[0]).toContain('降级发生时请求的数据源不应等于实际数据源');
      });
    });
  });

  // ==================== 新增的扩展测试 ====================
  describe('数据处理扩展', () => {
    it('应该能验证复杂数据结构 - Extended TDD', () => {
      // 测试多层嵌套的金融指标验证
      const complexMetrics = {
        total_trades: 50,
        winning_trades: 32,
        losing_trades: 18,
        win_rate: 0.64,
        total_return: 0.25,
        annual_return: 0.18,
        max_drawdown: -0.08,
        sharpe_ratio: 1.8,
        profit_loss_ratio: 2.1,
        volatility: 0.15,
        avg_holding_period: 12,
        beta: 1.2
      };
      
      // 验证基础逻辑
      expect(() => {
        BacktestTestUtils.validateFinancialMetricsLogic(complexMetrics);
      }).not.toThrow();
      
      // 验证数值精度
      const precisionResult = BacktestTestUtils.validateNumericalPrecision(
        complexMetrics.sharpe_ratio, 
        '夏普比率'
      );
      expect(precisionResult.isValid).toBe(true);
      
      // 验证极端市场条件
      const extremeResult = BacktestTestUtils.validateExtremeMarketConditions(
        complexMetrics, 
        'high_volatility'
      );
      expect(extremeResult.warnings).toHaveLength(0); // 高波动期间的合理指标
    });

    it('应该能处理数据转换错误 - Extended TDD', () => {
      // 测试浮点数精度问题
      const problematicValue = 0.1 + 0.2; // JavaScript浮点数精度问题
      const precisionResult = BacktestTestUtils.validateNumericalPrecision(
        problematicValue,
        '浮点数计算结果'
      );
      
      // 应该检测到精度问题
      expect(precisionResult.isValid).toBe(false);
      expect(precisionResult.issues[0]).toContain('精度过高');
      
      // 测试极值处理
      const extremeValues = [Infinity, -Infinity, Number.MAX_VALUE, Number.MIN_VALUE];
      extremeValues.forEach((value, index) => {
        const result = BacktestTestUtils.validateNumericalPrecision(value, `极值测试${index}`);
        if (value === Infinity || value === -Infinity) {
          expect(result.isValid).toBe(false);
        }
      });
    });

    it('应该能验证大数据集性能 - Extended TDD', () => {
      // 模拟大数据集处理
      const dataSize = 10000;
      const processingTime = 500; // 500ms
      const memoryUsage = 5 * 1024 * 1024; // 5MB
      
      const performanceResult = BacktestTestUtils.validateLargeDatasetPerformance(
        dataSize,
        processingTime,
        memoryUsage
      );
      
      expect(performanceResult.metrics.recordsPerSecond).toBeGreaterThan(1000);
      expect(performanceResult.metrics.timePerRecord).toBeLessThan(1);
      expect(performanceResult.isValid).toBe(true);
      
      // 测试性能不达标的情况
      const slowResult = BacktestTestUtils.validateLargeDatasetPerformance(
        1000,
        5000, // 5秒处理1000条记录，性能不达标
        undefined
      );
      
      expect(slowResult.isValid).toBe(false);
      expect(slowResult.warnings.length).toBeGreaterThan(0);
      expect(slowResult.warnings[0]).toContain('处理速度偏慢');
    });
  });

  describe('状态管理扩展', () => {
    it('应该能验证状态转换有效性 - Extended TDD', async () => {
      // 模拟一个简单的状态管理对象
      const mockStore = {
        isLoading: false,
        hasError: false,
        data: null,
        setLoading: (loading: boolean) => { mockStore.isLoading = loading; },
        setError: (error: boolean) => { mockStore.hasError = error; },
        setData: (data: any) => { mockStore.data = data; }
      };
      
      // 定义状态转换操作
      const loadDataOperation = async () => {
        mockStore.setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 10));
        mockStore.setData({ result: 'success' });
        mockStore.setLoading(false);
        return mockStore.data;
      };
      
      // 验证原子性状态转换
      await BacktestTestUtils.verifyAtomicStateTransition(
        mockStore,
        loadDataOperation,
        {
          before: { isLoading: false, hasError: false, data: null },
          after: { isLoading: false, hasError: false, data: { result: 'success' } }
        }
      );
      
      expect(mockStore.data).toEqual({ result: 'success' });
    });

    it('应该能处理状态冲突 - Extended TDD', async () => {
      // 模拟状态冲突场景
      const conflictStore = {
        operationCount: 0,
        isProcessing: false,
        startOperation: () => {
          if (conflictStore.isProcessing) {
            throw new Error('操作已在进行中');
          }
          conflictStore.isProcessing = true;
          conflictStore.operationCount++;
        },
        endOperation: () => {
          conflictStore.isProcessing = false;
        }
      };
      
      // 测试正常操作
      const normalOperation = async () => {
        conflictStore.startOperation();
        await new Promise(resolve => setTimeout(resolve, 5));
        conflictStore.endOperation();
        return 'completed';
      };
      
      await BacktestTestUtils.verifyAtomicStateTransition(
        conflictStore,
        normalOperation,
        {
          before: { operationCount: 0, isProcessing: false },
          after: { operationCount: 1, isProcessing: false }
        }
      );
      
      // 测试冲突操作
      const conflictOperation = async () => {
        conflictStore.startOperation(); // 第一次调用
        conflictStore.startOperation(); // 第二次调用应该抛出错误
        return 'should not reach here';
      };
      
      await BacktestTestUtils.verifyAtomicStateTransition(
        conflictStore,
        conflictOperation,
        {
          before: { operationCount: 1, isProcessing: false },
          after: { operationCount: 1, isProcessing: false }, // 状态应该保持不变
          error: { operationCount: 2, isProcessing: true } // 错误状态
        }
      );
    });
  });

  describe('错误处理扩展', () => {
    it('应该能验证极端市场条件 - Extended TDD', () => {
      // 测试金融危机场景
      const crisisMetrics: BacktestMetrics = {
        total_trades: 25,
        winning_trades: 8,
        losing_trades: 17,
        win_rate: 0.32,
        total_return: -0.35,
        annual_return: -0.28,
        max_drawdown: -0.45,
        sharpe_ratio: -1.2,
        profit_loss_ratio: 0.6,
        volatility: 0.45,
        avg_holding_period: 8
      };
      
      const crisisResult = BacktestTestUtils.validateExtremeMarketConditions(
        crisisMetrics,
        'crisis'
      );
      
      expect(crisisResult.isValid).toBe(true); // 危机期间的合理指标
      
      // 测试黑天鹅事件
      const blackSwanMetrics: BacktestMetrics = {
        total_trades: 15,
        winning_trades: 3,
        losing_trades: 12,
        win_rate: 0.2,
        total_return: -0.55,
        annual_return: -0.48,
        max_drawdown: -0.65,
        sharpe_ratio: -2.5,
        profit_loss_ratio: 0.3,
        volatility: 0.8,
        avg_holding_period: 3
      };
      
      const blackSwanResult = BacktestTestUtils.validateExtremeMarketConditions(
        blackSwanMetrics,
        'black_swan'
      );
      
      expect(blackSwanResult.isValid).toBe(true); // 黑天鹅事件的合理指标
    });

    it('应该能处理并发操作错误 - Extended TDD', async () => {
      // 测试并发操作
      let operationCounter = 0;
      const concurrentOperation = async () => {
        operationCounter++;
        await new Promise(resolve => setTimeout(resolve, Math.random() * 20));
        
        // 模拟偶发错误
        if (operationCounter % 3 === 0) {
          throw new Error(`操作 ${operationCounter} 失败`);
        }
        
        return `操作 ${operationCounter} 成功`;
      };
      
      const concurrencyResult = await BacktestTestUtils.testConcurrentOperations(
        5, // 5个并发操作
        concurrentOperation
      );
      
      expect(concurrencyResult.results).toHaveLength(5);
      expect(concurrencyResult.successRate).toBeGreaterThan(0); // 至少有部分成功
      expect(concurrencyResult.successRate).toBeLessThan(100); // 有部分失败
      expect(concurrencyResult.totalDuration).toBeGreaterThan(0);
      
      // 验证失败的操作包含错误信息
      const failedOperations = concurrencyResult.results.filter(r => !r.success);
      failedOperations.forEach(op => {
        expect(op.error).toBeDefined();
        expect(op.error).toContain('失败');
      });
    });
  });

  describe('工具函数扩展', () => {
    it('应该能执行批量数据操作 - Extended TDD', () => {
      // 测试批量Abu策略配置验证
      const strategyConfigs = [
        {
          strategy_type: 'trend_follow',
          abu_params: { period: 20, threshold: 0.02 },
          data_source: 'tushare'
        },
        {
          strategy_type: 'mean_reversion',
          abu_params: { window: 14, z_score: 2.0 },
          data_source: 'abu_legacy'
        },
        {
          strategy_type: 'pair_trading',
          abu_params: { correlation_threshold: 0.8, z_score: 1.5 },
          data_source: 'tushare'
        }
      ];
      
      const validationResults = strategyConfigs.map((config, index) => {
        const result = BacktestTestUtils.validateAbuStrategyConfig(config);
        return {
          index,
          config: config.strategy_type,
          isValid: result.isValid,
          errorCount: result.errors.length,
          warningCount: result.warnings.length
        };
      });
      
      // 验证所有配置都有效
      validationResults.forEach(result => {
        expect(result.isValid).toBe(true);
        expect(result.errorCount).toBe(0);
      });
      
      // 验证批量处理的完整性
      expect(validationResults).toHaveLength(3);
      expect(validationResults.map(r => r.config)).toEqual([
        'trend_follow',
        'mean_reversion', 
        'pair_trading'
      ]);
    });

    it('应该能验证交易记录与权益曲线一致性 - Extended TDD', () => {
      // 创建模拟交易记录
      const trades: Trade[] = [
        {
          id: '1',
          symbol: 'AAPL',
          side: 'buy',
          quantity: 100,
          price: 150.0,
          amount: 15000,
          commission: 5.0,
          timestamp: '2024-01-02T09:30:00Z'
        },
        {
          id: '2',
          symbol: 'AAPL',
          side: 'sell',
          quantity: 100,
          price: 155.0,
          amount: 15500,
          commission: 5.0,
          timestamp: '2024-01-05T15:30:00Z'
        }
      ];
      
      // 创建对应的权益曲线
      const equityCurve: EquityPoint[] = [
        { date: '2024-01-01', equity: 100000, drawdown: 0 },
        { date: '2024-01-02', equity: 85000, drawdown: -15.0 }, // 买入后现金减少
        { date: '2024-01-03', equity: 85500, drawdown: -14.5 }, // 股价上涨
        { date: '2024-01-04', equity: 86000, drawdown: -14.0 },
        { date: '2024-01-05', equity: 100490, drawdown: 0.49 }  // 卖出后获利
      ];
      
      // 验证交易记录与权益曲线的一致性
      expect(() => {
        BacktestTestUtils.validateTradesEquityConsistency(trades, equityCurve);
      }).not.toThrow();
      
      // 测试不一致的情况
      const inconsistentTrades: Trade[] = [
        {
          id: '3',
          symbol: 'TSLA',
          side: 'buy',
          quantity: 50,
          price: 200.0,
          amount: 10000,
          commission: 5.0,
          timestamp: '2023-12-31T09:30:00Z' // 时间超出权益曲线范围
        }
      ];
      
      expect(() => {
        BacktestTestUtils.validateTradesEquityConsistency(inconsistentTrades, equityCurve);
      }).toThrow('交易时间超出权益曲线范围');
    });
  });

  // 清理工作
  afterEach(() => {
    vi.restoreAllMocks();
  });
});