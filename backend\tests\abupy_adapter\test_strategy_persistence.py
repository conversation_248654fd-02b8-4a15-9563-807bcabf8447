"""
策略持久化测试模块

测试策略保存和加载功能
"""
import os
import json
import pytest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor
from backend.app.abupy_adapter.strategy_adapter import StrategyAdapter
from backend.app.core.exceptions import AdapterError

# 创建测试策略的辅助函数
def create_test_strategy():
    """创建用于测试的策略对象"""
    return Strategy(
        id="test_strategy_id",
        name="测试策略",
        description="用于测试的策略",
        buy_factors=[
            BuyFactor(
                name="AbuFactorBuyBreak",
                factor_class="FactorBuyBreak",
                parameters={"xd": 60},
                factor_type="buy"
            )
        ],
        sell_factors=[
            SellFactor(
                name="AbuFactorSellBreak",
                factor_class="FactorSellBreak",
                parameters={"xd": 20},
                factor_type="sell"
            )
        ],
        parameters={"initial_capital": 1000000}
    )

class TestStrategyPersistence:
    """策略持久化测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        # 删除临时目录
        shutil.rmtree(self.temp_dir)
    
    def test_save_strategy_with_path(self):
        """测试保存策略到指定路径"""
        strategy = create_test_strategy()
        file_path = os.path.join(self.temp_dir, "test_strategy.json")
        
        # 保存策略
        saved_path = StrategyAdapter.save_strategy(strategy, file_path)
        
        # 验证返回的路径正确
        assert saved_path == file_path
        
        # 验证文件已创建
        assert os.path.exists(file_path)
        
        # 验证文件内容正确
        with open(file_path, "r", encoding="utf-8") as f:
            saved_data = json.load(f)
            assert saved_data["id"] == strategy.id
            assert saved_data["name"] == strategy.name
            assert len(saved_data["buy_factors"]) == 1
            assert len(saved_data["sell_factors"]) == 1
            assert saved_data["parameters"]["initial_capital"] == 1000000
    
    def test_save_strategy_default_path(self):
        """测试保存策略到默认路径"""
        strategy = create_test_strategy()
        
        # 创建一个临时目录作为home目录
        temp_home = os.path.join(self.temp_dir, "home")
        os.makedirs(temp_home, exist_ok=True)
        
        # 模拟os.path.expanduser返回我们的临时home目录
        with patch("os.path.expanduser", return_value=temp_home):
            # 保存策略
            saved_path = StrategyAdapter.save_strategy(strategy)
            
            # 验证文件已创建
            assert os.path.exists(saved_path)
            assert saved_path.startswith(temp_home)
            assert strategy.id in saved_path
            
            # 验证文件内容正确
            with open(saved_path, "r", encoding="utf-8") as f:
                saved_data = json.load(f)
                assert saved_data["id"] == strategy.id
    
    def test_save_strategy_error(self):
        """测试保存策略时出错"""
        strategy = create_test_strategy()
        
        # 模拟open函数抛出异常
        with patch("builtins.open", side_effect=PermissionError("权限不足")):
            with pytest.raises(AdapterError, match="保存策略时出错"):
                StrategyAdapter.save_strategy(strategy, os.path.join(self.temp_dir, "test.json"))
    
    def test_load_strategy(self):
        """测试加载策略"""
        original_strategy = create_test_strategy()
        file_path = os.path.join(self.temp_dir, "test_strategy.json")
        
        # 先保存策略
        StrategyAdapter.save_strategy(original_strategy, file_path)
        
        # 加载策略
        loaded_strategy = StrategyAdapter.load_strategy(file_path)
        
        # 验证加载的策略与原策略一致
        assert loaded_strategy.id == original_strategy.id
        assert loaded_strategy.name == original_strategy.name
        assert loaded_strategy.description == original_strategy.description
        assert len(loaded_strategy.buy_factors) == len(original_strategy.buy_factors)
        assert len(loaded_strategy.sell_factors) == len(original_strategy.sell_factors)
        assert loaded_strategy.parameters["initial_capital"] == original_strategy.parameters["initial_capital"]
    
    def test_load_strategy_file_not_exist(self):
        """测试加载不存在的策略文件"""
        non_existent_file = os.path.join(self.temp_dir, "non_existent.json")
        
        with pytest.raises(AdapterError, match="策略文件不存在"):
            StrategyAdapter.load_strategy(non_existent_file)
    
    def test_load_strategy_invalid_json(self):
        """测试加载无效的JSON文件"""
        invalid_json_file = os.path.join(self.temp_dir, "invalid.json")
        
        # 创建一个无效的JSON文件
        with open(invalid_json_file, "w", encoding="utf-8") as f:
            f.write("{这不是有效的JSON}")
        
        with pytest.raises(AdapterError, match="解析策略文件时出错"):
            StrategyAdapter.load_strategy(invalid_json_file)
    
    def test_load_strategy_invalid_data(self):
        """测试加载数据格式不正确的策略文件"""
        invalid_data_file = os.path.join(self.temp_dir, "invalid_data.json")
        
        # 创建一个格式不符合Strategy的JSON文件
        with open(invalid_data_file, "w", encoding="utf-8") as f:
            json.dump({"name": "只有名称的策略"}, f)
        
        # 使用patch来模拟Strategy的__init__方法抛出ValidationError
        with patch("app.schemas.strategy.Strategy.__init__", side_effect=ValueError("缺少必填字段")):
            with pytest.raises(AdapterError, match="加载策略时出错"):
                StrategyAdapter.load_strategy(invalid_data_file)
