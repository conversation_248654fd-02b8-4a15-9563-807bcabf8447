## 核心测试原则
### 1. 测试驱动开发（TDD）模式
#### TDD三阶段策略
- **红阶段（Red）**：编写失败的测试，采用"元测试"(Meta-Test)策略
  - 通过完全Mock验证测试脚本本身的逻辑正确性
  - 确保测试架构设计合理，能够捕获预期的交互行为
  - 测试通过是预期行为，验证测试用例的完整性
- **绿阶段（Green）**：实现最小必要的功能代码让测试通过
  - 移除或减少Mock，使用真实的组件和store实现
  - 实现真实的功能代码，确保业务逻辑正确
- **重构阶段（Refactor）**：优化代码质量，保持测试通过
  - 快速迭代 ：运行测试 → 分析失败原因 → 修复代码 → 重新测试
  - 保护现有功能 ：确保新功能不破坏已有的通过测试
### 2. 测试场景设计
#### 场景分类原则
- **核心业务流程**：重点测试主要用户操作路径（如发起回测、查看历史）
- **边界与错误处理**：测试各种边界条件和异常情况
- **UI组件验证**：确保界面元素正确渲染和交互
- **数据流验证**：验证组件间数据传递和状态同步
#### 测试用例设计
- 业务逻辑验证 ：重点测试核心业务功能（如策略保存、更新、回测执行）
- 边界条件测试 ：测试空数据、无效输入、缺失配置等边界情况
- 错误处理验证 ：确保错误情况下的正确行为（如验证失败时抛出异常）
- 交互行为测试 ：验证用户操作触发的API调用、路由跳转等行为
## 技术实现原则
### 3. Mock策略设计
#### "元测试"阶段Mock原则
- **完全Mock策略**：在TDD红阶段，完全模拟所有依赖
  - 使用 `vi.mock()` 模拟整个组件和store
  - 创建简化的Mock实现，专注于验证测试逻辑
  - 确保Mock能够正确响应测试中的所有交互
#### Mock组件设计
- 简化响应式逻辑 ：避免复杂的Vue响应式系统，使用简单的数据绑定
- 正确处理Props ：特别注意响应式引用（RefImpl）的解包，使用 props.strategy?.value || props.strategy
- 事件模拟 ：确保Mock组件能正确触发和处理事件（如 update-strategy 、 save-strategy ）
- DOM元素标识 ：为测试元素添加正确的 data-testid 属性
#### Store Mock设计
- **状态Mock**：提供必要的响应式状态（ref、reactive）
- **方法Mock**：使用 `vi.fn()` 创建可验证的Mock函数
- **初始化Mock**：确保Mock store在每个测试中都有正确的初始状态
### 4. 数据管理策略
#### 数据工厂设计
- **统一数据工厂** ：使用工厂模式创建测试数据，确保数据结构一致性
- **方法完整性** ：确保工厂类包含测试所需的所有创建方法（如 createValidStrategy）
- **数据一致性** ：工厂生成的数据应与实际API契约保持一致
#### 测试数据管理
- **明确ID字段** ：确保测试实体包含必要的标识字段（如 strategy.id、task.id）
- **固定测试数据** ：对于需要精确验证的场景，使用固定的ID而非动态生成
- **数据同步机制** ：确保组件间的数据传递和更新正确同步
- **字段映射** ：注意不同上下文中字段名的差异（如 task_id vs id）
### 5. 测试环境配置
- 正确的测试运行环境 ：在PowerShell环境下使用 npx vitest run 命令
- 依赖管理 ：确保所有必要的测试依赖（Vue Test Utils、Vitest等）正确安装
- 文件路径处理 ：使用正确的相对路径引用组件和工具
## 调试和问题解决
### 6. 系统化调试方法
- 逐步排查 ：从简单到复杂，逐步定位问题根源
- 日志输出 ：适当添加console.log来跟踪数据流和事件触发
- DOM检查 ：验证DOM元素是否正确渲染和更新
- 响应式系统理解 ：深入理解Vue3的响应式机制，正确处理ref和reactive
### 7. 常见问题模式
#### Mock相关问题
- **模块导入错误** ：确保Mock的模块路径正确，避免 "Cannot find module" 错误
- **Mock函数缺失** ：确保所有测试中调用的方法都在Mock中定义
- **Mock返回值** ：确保Mock函数返回正确的数据结构和类型
#### Vue组件问题
- **响应式引用处理** ：Mock组件接收到RefImpl对象时需要正确解包
- **事件传播** ：确保组件间的事件能正确传播和处理
- **DOM更新时机** ：使用 nextTick() 等待DOM更新完成
#### 数据一致性问题
- **测试数据一致性** ：确保测试数据与实际业务逻辑要求一致
- **字段名匹配** ：注意API响应和组件期望的字段名差异
- **ID生成策略** ：动态生成的ID可能导致测试断言失败，需要使用固定值
### 8. 业务逻辑验证
- 强制验证规则 ：如策略必须包含买入因子才能保存
- 数据完整性 ：确保更新操作不会丢失或覆盖重要数据
- 状态管理 ：正确调用Store的相应方法（createStrategy vs updateStrategy）
## 最佳实践总结
### 9. 代码组织
#### 文件结构
- **模块化设计** ：将Mock组件、测试数据、工具函数分离
- **工厂类管理** ：为不同业务域创建专门的数据工厂（如 SimpleStrategyDataFactory、SimpleBacktestDataFactory）
- **测试文件命名** ：使用清晰的命名约定（如 ComponentName.feature.integration.test.ts）
#### 测试结构
- **清晰的测试结构** ：每个测试用例有明确的设置、执行、验证阶段
- **场景分组** ：按业务场景对测试用例进行分组
- **可重用性** ：设计可在多个测试中复用的组件和工具
### 10. 性能和维护
#### 开发效率
- **最小化Mock复杂度** ：只模拟测试所需的最小功能
- **避免过度工程化** ：不要为了测试而过度复杂化实现
- **快速反馈循环** ：确保测试能够快速运行并提供明确的错误信息
#### 文档和注释
- **"元测试"标识** ：在TDD红阶段的测试文件中明确标注这是"元测试"实现
- **阶段说明** ：说明当前测试处于TDD的哪个阶段，以及下一步需要做什么
- **设计决策记录** ：记录重要的Mock策略选择和问题解决方案
- **迁移指南** ：为从红阶段到绿阶段的迁移提供清晰的指导

## TDD红阶段实践经验总结
### 11. "元测试"实施要点
#### 核心理念
- **测试优先于实现** ：在没有任何功能代码的情况下，先验证测试脚本的正确性
- **Mock即规范** ：通过Mock定义组件和API的预期接口和行为
- **测试通过是目标** ：红阶段的测试通过证明测试逻辑正确，而非功能实现

#### 实施步骤
1. **完全Mock所有依赖** ：使用 `vi.mock()` 模拟所有外部依赖
2. **创建简化的Mock实现** ：专注于测试逻辑验证，避免复杂的业务逻辑
3. **验证测试架构** ：确保测试能够正确捕获用户交互和系统响应
4. **添加元测试标识** ：在测试文件中明确标注这是"元测试"实现

### 12. 常见陷阱和解决方案
#### 数据工厂问题
- **问题** ：测试中调用不存在的工厂方法（如 `createValidStrategy`）
- **解决** ：确保工厂类包含所有测试需要的创建方法
- **预防** ：在编写测试前先检查或创建必要的工厂方法

#### Mock模块路径问题
- **问题** ：`Cannot find module` 错误，通常是Mock路径不正确
- **解决** ：使用正确的相对路径或别名路径（如 `@/stores/useStrategyStore`）
- **预防** ：在Mock之前先确认模块的实际路径

#### 动态数据导致的断言失败
- **问题** ：使用动态生成的ID（如时间戳）导致测试断言失败
- **解决** ：在测试中使用固定的ID值，或者重写工厂方法的返回值
- **预防** ：对于需要精确验证的场景，始终使用可预测的测试数据

### 13. AI实施指导清单
#### 开始前检查
- [ ] 确认测试需求文档和用户故事
- [ ] 检查现有的数据工厂是否包含所需方法
- [ ] 确认组件和store的导入路径
- [ ] 准备测试数据和Mock策略

#### 实施过程
- [ ] 创建完整的Mock实现（组件、store、路由）
- [ ] 编写测试用例，覆盖所有业务场景
- [ ] 运行测试，确保所有用例通过
- [ ] 添加"元测试"标识和说明注释

#### 完成后验证
- [ ] 所有测试用例都能通过
- [ ] Mock实现简洁且专注于测试逻辑
- [ ] 测试覆盖了核心业务流程和边界情况
- [ ] 文档注释清晰说明了TDD阶段和下一步计划

### 14. 从红阶段到绿阶段的迁移指南
#### 迁移准备
1. **分析Mock依赖** ：识别哪些Mock需要替换为真实实现
2. **确定实现优先级** ：从核心业务逻辑开始，逐步实现功能
3. **保持测试稳定** ：确保在迁移过程中测试仍能正常运行

#### 迁移策略
- **渐进式替换** ：逐个替换Mock，而不是一次性全部替换
- **保持向后兼容** ：新的实现应该能够通过现有的测试
- **增量验证** ：每替换一个Mock后立即运行测试验证

#### 迁移完成标志
- 所有Mock都被真实实现替换
- 测试仍然全部通过
- 功能代码能够正确处理真实的用户交互
- 业务逻辑符合需求规范