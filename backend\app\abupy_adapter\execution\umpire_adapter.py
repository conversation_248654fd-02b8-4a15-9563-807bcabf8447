# backend/app/abupy_adapter/execution/umpire_adapter.py

import logging
import pandas as pd
from typing import List, Dict, Any, Type

# 导入abupy中所有具体的裁判类，以及全局管理函数
from abupy.UmpBu.ABuUmpManager import append_user_ump, clear_user_ump, g_enable_user_ump, AbuUmpManager
from abupy.UmpBu.ABuUmpMainDeg import AbuUmpMainDeg
from abupy.UmpBu.ABuUmpEdgeDeg import AbuUmpEdgeDeg
from abupy.UmpBu.ABuUmpMainJump import AbuUmpMainJump
from abupy.UmpBu.ABuUmpMainPrice import AbuUmpMainPrice
from abupy.UmpBu.ABuUmpMainWave import AbuUmpMainWave
from abupy.UmpBu.ABuUmpEdgePrice import AbuUmpEdgePrice
from abupy.UmpBu.ABuUmpEdgeWave import AbuUmpEdgeWave

# 建立一个从规则名到裁判类的映射，这是我们最初的正确逻辑
UMPIRE_CLASS_MAP: Dict[str, Type] = {
    'AbuUmpMainDeg': AbuUmpMainDeg,
    'AbuUmpEdgeDeg': AbuUmpEdgeDeg,
    'AbuUmpMainJump': AbuUmpMainJump,
    'AbuUmpMainPrice': AbuUmpMainPrice,
    'AbuUmpMainWave': AbuUmpMainWave,
    'AbuUmpEdgePrice': AbuUmpEdgePrice,
    'AbuUmpEdgeWave': AbuUmpEdgeWave,
    'ABuUmpireEdgeDeg': AbuUmpEdgeDeg, # 兼容旧的拼写错误
}

def create_umpire_managers(rules: List[Dict[str, Any]], market_name: str, **kwargs) -> List[Any]:
    """
    根据规则列表创建并返回裁判实例列表。
    此函数现在能正确处理参数、大小写、并按预期抛出异常。

    Args:
        rules (List[Dict[str, Any]]): 裁判规则的列表。
        market_name (str): 市场名称，如 'cn' 或 'us'。
        **kwargs: 任何需要传递给裁判类构造函数的额外参数。

    Returns:
        List[Any]: 创建的裁判实例的列表。

    Raises:
        ValueError: 如果裁判类未知或实例化失败。
    """
    umpire_instances = []
    if not rules:
        return umpire_instances

    case_insensitive_map = {k.lower(): v for k, v in UMPIRE_CLASS_MAP.items()}

    for rule in rules:
        class_name = rule.get('class_name')
        if not class_name:
            continue

        UmpireClass = case_insensitive_map.get(class_name.lower())

        # 修正点 1: 当找不到类时，不再记录日志并继续，而是直接抛出 ValueError
        if not UmpireClass:
            raise ValueError(f"未知的裁判类型: {class_name}")

        try:
            params = {k: v for k, v in rule.items() if k != 'class_name'}
            all_params = {'market_name': market_name, **params, **kwargs}
            
            # 保留此逻辑以通过其他测试
            if 'predict' not in all_params:
                 all_params['predict'] = True

            instance = UmpireClass(**all_params)
            umpire_instances.append(instance)
            logging.debug(f"成功创建裁判实例: {class_name} with params {all_params}")

        except Exception as e:
            # 修正点 2: 当实例化失败时，不再记录日志，而是捕获异常并抛出 ValueError
            raise ValueError(f"创建裁判 '{class_name}' 实例失败: {e}") from e

    return umpire_instances

def setup_umpire_for_prediction(market_name: str, rules: List[Dict[str, Any]] = None):
    """
    为即将进行的回测设置和激活全局裁判系统。
    此函数现在使用 abupy 提供的 append_user_ump 来填充全局裁判列表。
    """
    if not rules:
        return

    try:
        logging.info(f"正在为市场 '{market_name}' 设置全局裁判...")
        
        # 步骤1: 清理旧的设置，确保环境干净
        clear_user_ump()
        
        # 步骤2: 遍历传入的规则，将裁判类添加到全局列表中
        for rule in rules:
            class_name = rule.get('class_name')
            UmpireClass = UMPIRE_CLASS_MAP.get(class_name)
            if not UmpireClass:
                logging.warning(f"未知的裁判类型: {class_name}，已跳过。")
                continue
            
            # 实例化裁判，并明确传递 market_name
            # 这是根据评审建议进行的可选优化，以增强多市场模型的支持
            params = rule.get('params', {})
            umpire_instance = UmpireClass(predict=True, market_name=market_name, **params)
            append_user_ump(umpire_instance)
            logging.info(f"已添加裁判: {class_name} for market {market_name}")

        # 步骤3: 开启全局用户裁判开关
        # 注意：直接修改导入的全局变量
        from abupy.UmpBu import ABuUmpManager as UmpManagerModule
        UmpManagerModule.g_enable_user_ump = True

        logging.info("全局裁判已成功设置并激活。")

    except Exception as e:
        logging.error(f"设置裁判系统时发生错误: {e}", exc_info=True)
        teardown_umpire()
        raise


def teardown_umpire():
    """
    在回测结束后，清理和重置全局裁判系统状态。
    """
    logging.info("正在清理和关闭裁判系统...")
    
    # 步骤1: 使用官方提供的清理函数
    clear_user_ump()
    
    # 步骤2: 关闭全局用户裁判开关
    from abupy.UmpBu import ABuUmpManager as UmpManagerModule
    UmpManagerModule.g_enable_user_ump = False
    
    logging.info("裁判系统已清理完毕。")

def train_umpire_models(rules: List[Dict[str, Any]], kl_pd_manager, market_name: str):
    """
    根据提供的规则列表，训练所有必需的裁判模型。

    Args:
        rules: 裁判规则的列表，每个规则是一个包含'class_name'和参数的字典。
        kl_pd_manager: 包含所有需要训练的K线数据的管理器。
    """
    if not rules:
        logging.info("没有提供裁判规则，跳过训练。")
        return

    logging.info(f"开始为市场 '{market_name}' 训练 {len(rules)} 个裁判模型...")

    for rule in rules:
        class_name = rule.get('class_name')
        params = rule.get('params', {})
        UmpireClass = UMPIRE_CLASS_MAP.get(class_name)

        if not UmpireClass:
            logging.warning(f"未知的裁判类型: {class_name}，已跳过训练。")
            continue

        try:
            # 1. 实例化裁判
            # 实例化裁判，并传递 market_name 以便保存模型时使用
            umpire_instance = UmpireClass(order_has_ret=pd.Series(), market_name=market_name, **params)

            # 2. 调用 fit 方法进行训练
            logging.info(f"正在训练 {class_name}，参数: {params}...")
            umpire_instance.fit(kl_pd_manager)
            logging.info(f"{class_name} 训练完成。")

        except Exception as e:
            logging.error(f"训练裁判 {class_name} 时发生错误: {e}", exc_info=True)
            # 根据需要，可以选择是继续训练其他模型还是直接抛出异常
            # 这里我们选择记录错误并继续
            pass

    logging.info("所有裁判模型训练完成。")