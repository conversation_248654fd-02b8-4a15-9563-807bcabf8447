#!/usr/bin/env python3
"""
测试文件行数统计的准确性
"""

def test_file_line_count(file_path):
    """测试文件行数统计"""
    print(f"测试文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 方法1: 按换行符分割
        lines_method1 = content.split('\n')
        total_lines_method1 = len(lines_method1)
        
        # 方法2: 按行读取
        with open(file_path, 'r', encoding='utf-8') as f:
            lines_method2 = f.readlines()
        total_lines_method2 = len(lines_method2)
        
        # 方法3: 逐行计数
        with open(file_path, 'r', encoding='utf-8') as f:
            total_lines_method3 = sum(1 for line in f)
        
        print(f"方法1 (split('\\n')): {total_lines_method1} 行")
        print(f"方法2 (readlines()): {total_lines_method2} 行")
        print(f"方法3 (逐行计数): {total_lines_method3} 行")
        
        # 检查最后一行是否为空
        if content.endswith('\n'):
            print("文件以换行符结尾")
        else:
            print("文件不以换行符结尾")
        
        # 分析空行和注释行
        empty_lines = 0
        comment_lines = 0
        code_lines = 0
        
        for line in lines_method1:
            stripped = line.strip()
            if not stripped:
                empty_lines += 1
            elif stripped.startswith('#'):
                comment_lines += 1
            else:
                code_lines += 1
        
        print(f"空行: {empty_lines}")
        print(f"注释行: {comment_lines}")
        print(f"代码行: {code_lines}")
        print(f"总计: {empty_lines + comment_lines + code_lines}")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    # 测试几个关键文件
    test_files = [
        "backend/app/abupy_adapter/symbol_adapter.py",
        "backend/app/abupy_adapter/strategy_executor.py"
    ]
    
    for file_path in test_files:
        test_file_line_count(file_path)
        print("-" * 50)
