import axios from 'axios';

// 创建 axios 实例
const request = axios.create({
  baseURL: 'http://127.0.0.1:8000/api', // API 的 base_url
  timeout: 5000, // 请求超时时间
});

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 后端API遵循ResponseSchema，实际数据在response.data.data中
    const res = response.data;
    if (res.code === 0) {
      return res.data;
    } else {
      // 处理错误，例如显示一个通知
      console.error('API Error:', res.message);
      return Promise.reject(new Error(res.message || 'Error'));
    }
  },
  error => {
    console.error('Network Error:', error);
    return Promise.reject(error);
  }
);

export default request;