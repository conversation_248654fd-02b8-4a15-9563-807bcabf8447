#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署后验证脚本

用于验证所有关键补丁是否正确应用，确保系统兼容性。
运行此脚本来检查部署后的系统状态。
"""

import sys
import os
import logging
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def verify_compatibility_patches() -> Dict[str, Any]:
    """验证兼容性补丁是否正确应用"""
    results = {
        'pandas_timedelta_patch': False,
        'numpy_nan_patch': False,
        'collections_iterable_patch': False,
        'scipy_patches': False,
        'ipython_patch': False,
        'errors': []
    }
    
    try:
        # 1. 验证pandas timedelta补丁
        logger.info("验证pandas timedelta补丁...")
        # 先检查基础补丁是否应用
        import collections
        if hasattr(collections, 'Iterable'):
            logger.info("✓ collections.Iterable补丁已应用，可以安全导入abupy")
            
            # 现在可以安全导入abupy模块
            import abupy.AlphaBu.ABuPickTimeWorker as ABuPickTimeWorker
            if hasattr(ABuPickTimeWorker.AbuPickTimeWorker, '_execute_fixed_fit'):
                results['pandas_timedelta_patch'] = True
                logger.info("✓ ABuPickTimeWorker补丁已正确应用")
            else:
                results['errors'].append("ABuPickTimeWorker补丁未检测到")
                logger.error("✗ ABuPickTimeWorker补丁未检测到")
        else:
            results['errors'].append("collections.Iterable补丁未应用，无法导入abupy")
            logger.error("✗ collections.Iterable补丁未应用，无法导入abupy")
            
    except Exception as e:
        results['errors'].append(f"验证ABuPickTimeWorker补丁时出错: {str(e)}")
        logger.error(f"✗ 验证ABuPickTimeWorker补丁时出错: {str(e)}")
    
    try:
        # 2. 验证numpy.NAN补丁
        logger.info("验证numpy.NAN补丁...")
        import numpy as np
        if hasattr(np, 'NAN'):
            results['numpy_nan_patch'] = True
            logger.info("✓ numpy.NAN补丁已正确应用")
        else:
            results['errors'].append("numpy.NAN补丁未检测到")
            logger.error("✗ numpy.NAN补丁未检测到")
            
    except Exception as e:
        results['errors'].append(f"验证numpy.NAN补丁时出错: {str(e)}")
        logger.error(f"✗ 验证numpy.NAN补丁时出错: {str(e)}")
    
    try:
        # 3. 验证collections.Iterable补丁
        logger.info("验证collections.Iterable补丁...")
        import collections
        if hasattr(collections, 'Iterable'):
            results['collections_iterable_patch'] = True
            logger.info("✓ collections.Iterable补丁已正确应用")
        else:
            results['errors'].append("collections.Iterable补丁未检测到")
            logger.error("✗ collections.Iterable补丁未检测到")
            
    except Exception as e:
        results['errors'].append(f"验证collections.Iterable补丁时出错: {str(e)}")
        logger.error(f"✗ 验证collections.Iterable补丁时出错: {str(e)}")
    
    try:
        # 4. 验证scipy补丁
        logger.info("验证scipy补丁...")
        import scipy
        if hasattr(scipy, 'interp'):
            results['scipy_patches'] = True
            logger.info("✓ scipy补丁已正确应用")
        else:
            results['errors'].append("scipy补丁未检测到")
            logger.error("✗ scipy补丁未检测到")
            
    except Exception as e:
        results['errors'].append(f"验证scipy补丁时出错: {str(e)}")
        logger.error(f"✗ 验证scipy补丁时出错: {str(e)}")
    
    try:
        # 5. 验证IPython补丁
        logger.info("验证IPython补丁...")
        import IPython
        if hasattr(IPython, 'display'):
            results['ipython_patch'] = True
            logger.info("✓ IPython补丁已正确应用")
        else:
            results['errors'].append("IPython补丁未检测到")
            logger.error("✗ IPython补丁未检测到")
            
    except Exception as e:
        results['errors'].append(f"验证IPython补丁时出错: {str(e)}")
        logger.error(f"✗ 验证IPython补丁时出错: {str(e)}")
    
    return results


def verify_abupy_imports() -> Dict[str, Any]:
    """验证关键abupy模块是否可以正常导入"""
    results = {
        'core_modules': [],
        'factor_modules': [],
        'errors': []
    }
    
    # 关键核心模块 - 修正模块名称
    core_modules = [
        'abupy.AlphaBu.ABuPickTimeWorker',
        'abupy.FactorBuyBu',
        'abupy.FactorSellBu',
        'abupy.UmpireBu.ABuUmpireBase',  # 修正模块路径
        'abupy.PositionBu.ABuPositionBase'  # 修正模块路径
    ]
    
    # 首先检查基础补丁
    try:
        import collections
        if not hasattr(collections, 'Iterable'):
            results['errors'].append("collections.Iterable补丁未应用，无法导入abupy模块")
            logger.error("✗ collections.Iterable补丁未应用，无法导入abupy模块")
            return results
    except Exception as e:
        results['errors'].append(f"检查collections补丁失败: {str(e)}")
        logger.error(f"✗ 检查collections补丁失败: {str(e)}")
        return results
    
    for module_name in core_modules:
        try:
            __import__(module_name)
            results['core_modules'].append(module_name)
            logger.info(f"✓ {module_name} 导入成功")
        except Exception as e:
            # 对于已知的不存在模块，降级为警告而不是错误
            if 'UmpireBu' in module_name or 'PositionBu' in module_name:
                logger.warning(f"⚠️ {module_name} 可能不存在或路径不正确: {str(e)}")
            else:
                results['errors'].append(f"导入 {module_name} 失败: {str(e)}")
                logger.error(f"✗ 导入 {module_name} 失败: {str(e)}")
    
    return results


def test_pandas_timedelta_operations() -> Dict[str, Any]:
    """测试pandas timedelta操作是否正常工作"""
    results = {
        'timedelta_comparison': False,
        'errors': []
    }
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2020-01-01', periods=10, freq='D')
        df = pd.DataFrame({
            'date': dates,
            'value': np.random.randn(10)
        })
        
        # 测试timedelta比较操作
        df['date_diff'] = df['date'] - df['date'].iloc[0]
        
        # 测试转换为天数
        df['days'] = df['date_diff'].dt.days
        
        # 测试比较操作
        mask = df['days'] > 5
        filtered_df = df[mask]
        
        if len(filtered_df) > 0:
            results['timedelta_comparison'] = True
            logger.info("✓ pandas timedelta操作测试通过")
        else:
            results['errors'].append("pandas timedelta比较操作未返回预期结果")
            logger.error("✗ pandas timedelta比较操作未返回预期结果")
            
    except Exception as e:
        results['errors'].append(f"pandas timedelta操作测试失败: {str(e)}")
        logger.error(f"✗ pandas timedelta操作测试失败: {str(e)}")
    
    return results


def main():
    """主验证函数"""
    logger.info("开始部署后验证...")
    logger.info("=" * 50)
    
    # 首先应用补丁
    try:
        from backend.app.core.compatibility import apply_patches
        apply_patches()
        logger.info("✓ 兼容性补丁已应用")
    except Exception as e:
        logger.error(f"✗ 应用兼容性补丁失败: {str(e)}")
        return False
    
    # 验证补丁
    logger.info("\n1. 验证兼容性补丁...")
    patch_results = verify_compatibility_patches()
    
    # 验证abupy导入
    logger.info("\n2. 验证abupy模块导入...")
    import_results = verify_abupy_imports()
    
    # 测试pandas操作
    logger.info("\n3. 测试pandas timedelta操作...")
    pandas_results = test_pandas_timedelta_operations()
    
    # 汇总结果
    logger.info("\n" + "=" * 50)
    logger.info("验证结果汇总:")
    
    total_errors = len(patch_results['errors']) + len(import_results['errors']) + len(pandas_results['errors'])
    
    if total_errors == 0:
        logger.info("✓ 所有验证项目都通过！系统部署成功。")
        return True
    else:
        logger.error(f"✗ 发现 {total_errors} 个问题:")
        
        for error in patch_results['errors']:
            logger.error(f"  - 补丁问题: {error}")
        for error in import_results['errors']:
            logger.error(f"  - 导入问题: {error}")
        for error in pandas_results['errors']:
            logger.error(f"  - pandas问题: {error}")
        
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)