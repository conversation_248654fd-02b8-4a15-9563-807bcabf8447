import pytest
import re
from unittest.mock import patch, MagicMock, call
import importlib as real_importlib_module_for_test
import inspect as real_inspect_module_for_test
import inspect
from backend.app.abupy_adapter.compatibility_patch import apply_patches
from backend.app.schemas.market import KlineData, KlineItem

apply_patches()
from typing import List, Dict, Any

from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor
from backend.app.abupy_adapter.strategy_adapter import StrategyAdapter, FactorError, ParameterError
import pandas as pd
from datetime import datetime, timedelta
import numpy as np # For np.log

def create_sample_kl_df(days=252):
    end_date = datetime.today()
    # Ensure start_date is reasonably in the past to get enough business days
    # Multiply by 1.5 and add a buffer of a few days to be safe
    start_date_approx = end_date - timedelta(days=int(days * 1.5) + 20) 
    dates = pd.date_range(start=start_date_approx, end=end_date, freq='B')
    if len(dates) > days:
        dates = dates[-days:]
    elif not dates.empty:
        # If not enough dates, pad from the earliest date backward if possible
        # This is a simple pad, real scenarios might need more robust handling
        missing_days = days - len(dates)
        pad_dates_before = pd.date_range(end=dates[0] - pd.Timedelta(days=1), periods=missing_days, freq='B')
        dates = pad_dates_before.union(dates)
    else: # Handle case where date range yields no business days (e.g. start=end on weekend)
        # Create a fallback range of 'days' business days ending today
        dates = pd.date_range(end=end_date, periods=days, freq='B')

    data = {
        'open': [10.0 + i * 0.01 for i in range(len(dates))],
        'high': [10.1 + i * 0.01 for i in range(len(dates))],
        'low': [9.9 + i * 0.01 for i in range(len(dates))],
        'close': [10.05 + i * 0.01 for i in range(len(dates))],
        'volume': [100000 + i * 100 for i in range(len(dates))],
        'p_change': [0.005 + (i % 5) * 0.001 for i in range(len(dates))]
    }
    df = pd.DataFrame(data, index=dates)
    df.index.name = 'date' # ABuSymbolPd sets index name to 'date'
    df['pre_close'] = df['close'].shift(1).fillna(df['open'])
    df['log_ret'] = np.log(df['close'] / df['pre_close'])
    df['atr21'] = 0.5  # dummy value
    df['atr14'] = 0.4  # dummy value
    # Ensure columns are in a typical order, though not strictly necessary for mock
    df = df[['open', 'high', 'low', 'close', 'pre_close', 'volume', 'p_change', 'log_ret', 'atr21', 'atr14']]
    return df

from backend.app.core.exceptions import AdapterError, FactorError, ParameterError

import sys 

def test_can_import_abupy_directly():
    try:
        import abupy
        from abupy.AlphaBu import ABuPickTimeExecute
        from abupy.AlphaBu.ABuPickTimeExecute import do_symbols_with_same_factors
        import abupy.FactorBuyBu
        from abupy.FactorBuyBu import AbuFactorBuyBreak
        assert AbuFactorBuyBreak is not None
    except ImportError as e:
        pytest.fail(f"Failed to import abupy or its submodules: {e}")
    except Exception as e:
        pytest.fail(f"Unexpected exception during abupy import checks: {e}")

def create_sample_strategy(
    strategy_id: str = "test_strategy_id",
    name: str = "Test Strategy",
    description: str = "A test strategy",
    buy_factors: List[BuyFactor] = None,
    sell_factors: List[SellFactor] = None,
    parameters: Dict[str, Any] = None
) -> Strategy:
    if buy_factors is None:
        buy_factors = [
            BuyFactor(
                name="AbuFactorBuyBreak",
                factor_class="FactorBuyBreak",
                parameters={"xd": 60},
                factor_type="buy"
            )
        ]
    if parameters is None:
        parameters = {"initial_capital": 1000000}
        
    return Strategy(
        id=strategy_id,
        name=name,
        description=description,
        buy_factors=buy_factors,
        sell_factors=sell_factors if sell_factors else [],
        parameters=parameters
    )

class TestStrategyAdapterExecuteStrategy:

    @patch('backend.app.services.market_service.MarketService')
    @patch('backend.app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_success_with_trades(self, mock_do_symbols, mock_market_service_class):
        """
        测试策略执行成功并产生交易的场景。
        此版本使用符合新版 StrategyExecutor 期望的、正确的 mock 数据结构。
        """
        # --- 1. 伪造底层abupy核心函数的“原始情报” ---
        # 新的真实代码期望 do_symbols_with_same_factors 返回一个元组 (orders_pd, action_pd)
        
        # 伪造订单DataFrame (orders_pd)
        mock_orders_df = pd.DataFrame({
            'symbol': ['SH600000', 'SH600000'],
            'order_id': [1, 2],
            'price': [100, 110],
            'buy_date': [pd.Timestamp('2023-02-01'), pd.Timestamp('2023-03-01')],
            'sell_date': [pd.Timestamp('2023-02-10'), pd.Timestamp('2023-03-15')],
        })
        
        # 伪造资金活动DataFrame (action_pd)
        mock_action_df = pd.DataFrame({
            'capital_blance': [1000000, 900000, 1100000, 1200000]
        })

        # 配置 mock，让它返回我们伪造的元组
        mock_do_symbols.return_value = (mock_orders_df, mock_action_df)

        # --- 2. 伪造上游 MarketService 的数据 (保持不变) ---
        mock_service_instance = mock_market_service_class.return_value
        mock_kline_obj = KlineData(
            symbol="FAKESYMBOL", name="假股票", market="CN", period="D",
            data=[KlineItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)],
            latest_date='20230101'
        )
        mock_service_instance.get_kline_data.return_value = mock_kline_obj

        # --- 3. 准备输入参数 (保持不变) ---
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"],
            "benchmark_symbol": "sh000300",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000
        }

        # --- 4. 执行并进行最终的、详细的断言 ---
        result = StrategyAdapter.execute_strategy(strategy, market_data)

        assert result["status"] == "success"
        assert result["message"] == "策略执行完成"
        assert len(result["results"]) == 1
        
        first_result = result["results"][0]
        assert first_result["symbol"] == "SH600000"
        assert first_result["orders_count"] == 2
        assert first_result["message"] == "交易完成"
        
        summary = result["execution_summary"]
        assert summary["initial_capital"] == 1000000
        assert summary["final_capital"] == 1200000  # 最终资金来自我们伪造的 mock_action_df
        assert summary["total_trades"] == 2

    @patch('backend.app.services.market_service.MarketService')
    @patch('backend.app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_success_no_trades(self, mock_do_symbols, mock_market_service_class):
        """
        测试策略执行成功但未产生任何交易的场景。
        此版本使用符合新版 StrategyExecutor 期望的、正确的 mock 数据结构。
        """
        # --- 1. 伪造一个“无交易”的“原始情报” ---
        # 伪造一个空的订单 DataFrame
        mock_orders_df = pd.DataFrame()
        
        # 伪造一个只有初始资金的资金活动 DataFrame
        mock_action_df = pd.DataFrame({'capital_blance': [1000000]})

        # 配置 mock，让它返回我们伪造的“无交易”元组
        mock_do_symbols.return_value = (mock_orders_df, mock_action_df)

        # --- 2. 伪造上游 MarketService 的数据 (同上一个测试) ---
        mock_service_instance = mock_market_service_class.return_value
        mock_kline_obj = KlineData(
            symbol="FAKESYMBOL", name="假股票", market="CN", period="D",
            data=[KlineItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)],
            latest_date='20230101'
        )
        mock_service_instance.get_kline_data.return_value = mock_kline_obj

        # --- 3. 准备输入参数 (同上一个测试) ---
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"],
            "benchmark_symbol": "sh000300",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000
        }

        # --- 4. 执行并进行“无交易”场景的断言 ---
        result = StrategyAdapter.execute_strategy(strategy, market_data)

        assert result["status"] == "success"
        assert len(result["results"]) == 1
        
        first_result = result["results"][0]
        assert first_result["orders_count"] == 0
        assert first_result["message"] == "无交易"
        
        summary = result["execution_summary"]
        assert summary["initial_capital"] == 1000000
        assert summary["final_capital"] == 1000000 # 无交易，最终资金等于初始资金
        assert summary["total_trades"] == 0

    def test_execute_strategy_missing_symbols(self):
        strategy = create_sample_strategy()
        market_data = {
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }
        expected_msg_part = "市场数据中缺少必要的参数: 'choice_symbols'"
        with pytest.raises(ParameterError, match=expected_msg_part):
            StrategyAdapter.execute_strategy(strategy, market_data)

    def test_execute_strategy_missing_start_date(self):
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"],
            "end_date": "2023-12-31",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }
        expected_msg_part = "市场数据中缺少必要的参数: 'start_date'"
        with pytest.raises(ParameterError, match=expected_msg_part):
            StrategyAdapter.execute_strategy(strategy, market_data)

    def test_execute_strategy_missing_end_date(self):
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }
        expected_msg_part = "市场数据中缺少必要的参数: 'end_date'"
        with pytest.raises(ParameterError, match=expected_msg_part):
            StrategyAdapter.execute_strategy(strategy, market_data)
            
    @patch('backend.app.abupy_adapter.strategy_executor.MarketService')
    @patch('backend.app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_missing_capital_in_market_data_uses_strategy_params(self, mock_do_symbols, mock_market_service_class):
        # 模拟结果
        mock_result_tuple = MagicMock()
        mock_result_tuple.symbol = "SH600000"
        mock_orders_pd = MagicMock()
        mock_orders_pd.empty = True
        mock_result_tuple.orders_pd = mock_orders_pd
        mock_result_tuple.capital = None
        mock_result_tuple.benchmark = None
        mock_do_symbols.return_value = [mock_result_tuple]

        # 配置新的 mock
        # 伪造一个 MarketService 实例
        mock_service_instance = mock_market_service_class.return_value
        # 伪造一个 KlineData 对象
        mock_kline_obj = KlineData(
            symbol="SH600000",
            data=[KlineDataItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)]
        )
        # 让 get_kline_data 方法返回这个伪造对象
        mock_service_instance.get_kline_data.return_value = mock_kline_obj

        strategy = create_sample_strategy(parameters={"initial_capital": 750000})
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "benchmark_symbol": "sh000300",
        }

        result = StrategyAdapter.execute_strategy(strategy, market_data)
        
        # 验证使用了策略参数中的资金
        assert result["parameters_used"]["capital"] == 750000
        
        # 验证 mock 被调用
        mock_do_symbols.assert_called_once()
        call_kwargs = mock_do_symbols.call_args[1]
        assert call_kwargs['capital'] == 750000

    @patch('backend.app.abupy_adapter.strategy_executor.MarketService')
    def test_execute_strategy_missing_capital_in_both(self, mock_market_service_class):
        # 配置新的 mock
        # 伪造一个 MarketService 实例
        mock_service_instance = mock_market_service_class.return_value
        # 伪造一个 KlineData 对象
        mock_kline_obj = KlineData(
            symbol="SH600000",
            data=[KlineDataItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)]
        )
        # 让 get_kline_data 方法返回这个伪造对象
        mock_service_instance.get_kline_data.return_value = mock_kline_obj

        strategy = create_sample_strategy(parameters={})
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "benchmark_symbol": "sh000300"
        }
        expected_error_msg = "市场数据和策略参数中都缺少资金参数 'capital' 或 'initial_capital'"
        with pytest.raises(ParameterError, match=re.escape(expected_error_msg)):
            StrategyAdapter.execute_strategy(strategy, market_data)

    @patch('backend.app.abupy_adapter.strategy_executor.MarketService')
    def test_execute_strategy_no_buy_factors(self, mock_market_service_class):
        # 配置新的 mock
        # 伪造一个 MarketService 实例
        mock_service_instance = mock_market_service_class.return_value
        # 伪造一个 KlineData 对象
        mock_kline_obj = KlineData(
            symbol="SH600000",
            data=[KlineDataItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)]
        )
        # 让 get_kline_data 方法返回这个伪造对象
        mock_service_instance.get_kline_data.return_value = mock_kline_obj
        strategy = create_sample_strategy(buy_factors=[])
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }
        expected_error_msg = "执行策略时遇到因子错误: 策略中必须至少包含一个买入因子"
        with pytest.raises(AdapterError, match=re.escape(expected_error_msg)):
            StrategyAdapter.execute_strategy(strategy, market_data)

    @patch('backend.app.abupy_adapter.strategy_executor.MarketService')
    @patch('backend.app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_abupy_exception(self, mock_do_symbols, mock_market_service_class):
        # 配置新的 mock
        # 伪造一个 MarketService 实例
        mock_service_instance = mock_market_service_class.return_value
        # 伪造一个 KlineData 对象
        mock_kline_obj = KlineData(
            symbol="SH600000",
            data=[KlineDataItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)]
        )
        # 让 get_kline_data 方法返回这个伪造对象
        mock_service_instance.get_kline_data.return_value = mock_kline_obj
        
        mock_abu_error_msg = "Abu内部模拟崩溃"
        mock_do_symbols.side_effect = Exception(mock_abu_error_msg)
        
        strategy_name = "Test Strategy for Abu Exception"
        strategy = create_sample_strategy(name=strategy_name)
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }
        
        # 调整期望的错误消息，因为实际错误可能包含更多信息
        with pytest.raises(AdapterError) as exc_info:
            StrategyAdapter.execute_strategy(strategy, market_data)
        
        # 验证错误消息包含关键内容
        error_msg = str(exc_info.value)
        assert "abu框架执行错误" in error_msg or "执行策略时遇到未知错误" in error_msg

    @patch('backend.app.abupy_adapter.strategy_executor.MarketService')
    def test_execute_strategy_invalid_factor_module(self, mock_market_service_class):
        # 配置新的 mock
        # 伪造一个 MarketService 实例
        mock_service_instance = mock_market_service_class.return_value
        # 伪造一个 KlineData 对象
        mock_kline_obj = KlineData(
            symbol="SH600000",
            data=[KlineDataItem(date='20230101', open=10, high=11, low=9, close=10.5, volume=1000)]
        )
        # 让 get_kline_data 方法返回这个伪造对象
        mock_service_instance.get_kline_data.return_value = mock_kline_obj
        
        with patch('backend.app.abupy_adapter.factors_converter.importlib.import_module') as mock_import_module:
            base_factor_name_from_schema = "NonExistentFactor"
            actual_module_name_suffix = f"ABu{base_factor_name_from_schema}"
            actual_class_name_to_get = f"Abu{base_factor_name_from_schema}"

            strategy = create_sample_strategy(
                buy_factors=[
                    BuyFactor(name=base_factor_name_from_schema, factor_class=base_factor_name_from_schema, factor_type="buy", parameters={})
                ]
            )
            market_data = {
                "choice_symbols": ["SH600000"],
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "capital": 1000000,
                "benchmark_symbol": "sh000300"
            }

            expected_import_path = f"{StrategyAdapter.BUY_FACTOR_MODULE}.{actual_module_name_suffix}"

            mock_submodule_instance = MagicMock()
            setattr(mock_submodule_instance, actual_class_name_to_get, MagicMock()) 

            def import_module_side_effect(module_name_called):
                if module_name_called == expected_import_path:
                    return mock_submodule_instance
                raise ImportError(f"Mock: Module {module_name_called} not found during test_execute_strategy_invalid_factor_module")

            mock_import_module.side_effect = import_module_side_effect

            raw_factor_error_msg = f"获取到的 '{expected_import_path}.{actual_class_name_to_get}' 不是一个有效的类 (type: MagicMock)."
            expected_adapter_error_msg = f"执行策略时遇到因子错误: {raw_factor_error_msg}"

            with pytest.raises(AdapterError, match=re.escape(expected_adapter_error_msg)):
                StrategyAdapter.execute_strategy(strategy, market_data)
            
            mock_import_module.assert_any_call(expected_import_path)

class TestStrategyAdapterGetAvailableAbuFactors:

    @pytest.mark.integration
    def test_get_buy_factors_only(self):
        """
        集成测试：测试仅获取买入因子列表。
        此测试依赖真实的 abupy 环境，因此被标记为集成测试，
        并默认跳过，应在特定集成测试阶段运行。
        """
        pytest.skip("集成测试，需在特定环境下运行")
    @patch.dict(sys.modules, {'abupy': None}) # 模拟 abupy 未安装或导入失败
    def test_get_factors_when_abu_import_failed(self):
        with pytest.raises(AdapterError, match="无法导入abu模块"):
            StrategyAdapter.get_available_abu_factors(use_cache=False)

    @patch('importlib.import_module') # Patch the actual stdlib function
    def test_get_factors_when_module_import_raises_error(self, mock_import_module_function):
        mock_import_module_function.side_effect = ImportError("Simulated module import failure")
        with pytest.raises(AdapterError, match=re.escape("TestStrategyAdapterGetAvailableAbuFactors Error: Simulated module import failure")):
            StrategyAdapter.get_available_abu_factors(factor_type="buy", use_cache=False)