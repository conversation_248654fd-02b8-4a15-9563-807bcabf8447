# backend/tests/abupy_adapter/test_strategy_adapter.py (最终驱魔重构版)

import sys
import pytest
import time
import threading
import types
from unittest.mock import patch, Mock, MagicMock

# --- 伪造的abupy依赖和基类 ---
class FakeAbuFactorBuyBase: pass
class FakeBuyPutMixin: pass
class FakeBuyCallMixin: pass
class FakeAbuFactorSellBase: pass

# --- 伪造的因子类 ---
class FactorWithParamsInfo(FakeAbuFactorBuyBase, FakeBuyCallMixin):
    _params_info = [{'name': 'p1', 'default': 1, 'comment': 'param1'}, {'name': 'p2', 'default': 'a'}]
    def __init__(self, p1, p2): pass

class FactorWithInitSelf(FakeAbuFactorBuyBase, FakeBuyPutMixin):
    _init_self = ('p1', 'p2')
    def __init__(self, cross_days, p1, p2: bool = True): pass

class FactorWithSimpleInit(FakeAbuFactorSellBase):
    def __init__(self, p_int: int, p_float=1.0): pass

class FactorWithNoParams(FakeAbuFactorBuyBase, FakeBuyPutMixin):
    def __init__(self): pass

class FactorWithOnlySelf(FakeAbuFactorBuyBase, FakeBuyPutMixin):
    def __init__(self): pass

class FactorWithArgsKwargs(FakeAbuFactorBuyBase, FakeBuyPutMixin):
    def __init__(self, *args, **kwargs): pass

class NotAFactor: pass

# --- 伪造的模块 ---
mock_factor_module_buy = types.ModuleType('buy_factor')
mock_factor_module_buy.FactorWithParamsInfo = FactorWithParamsInfo
mock_factor_module_buy.FactorWithInitSelf = FactorWithInitSelf
mock_factor_module_buy.FactorWithNoParams = FactorWithNoParams
mock_factor_module_buy.FactorWithOnlySelf = FactorWithOnlySelf
mock_factor_module_buy.FactorWithArgsKwargs = FactorWithArgsKwargs
mock_factor_module_buy.NotAFactor = NotAFactor

mock_factor_module_sell = types.ModuleType('sell_factor')
mock_factor_module_sell.FactorWithSimpleInit = FactorWithSimpleInit

# --- 核心Mock设置 ---
FAKE_MODULES = {
    'abupy': Mock(),
    'abupy.FactorBuyBu.ABuFactorBuyBase': Mock(
        AbuFactorBuyBase=FakeAbuFactorBuyBase, 
        BuyCallMixin=FakeBuyCallMixin, 
        BuyPutMixin=FakeBuyPutMixin
    ),
    'abupy.FactorSellBu.ABuFactorSellBase': Mock(
        AbuFactorSellBase=FakeAbuFactorSellBase
    ),
    'abupy.FactorBuyBu': Mock(__file__='/fake/path/abupy/FactorBuyBu/__init__.py'),
    'abupy.FactorSellBu': Mock(__file__='/fake/path/abupy/FactorSellBu/__init__.py'),
    'abupy.FactorBuyBu.buy_factor': mock_factor_module_buy,
    'abupy.FactorSellBu.sell_factor': mock_factor_module_sell,
    'abupy.FactorBuyBu.factor_one': mock_factor_module_buy,
    'abupy.FactorBuyBu.factor_two': mock_factor_module_sell,
}

# --- 导入被测模块 ---
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor
from backend.app.abupy_adapter.strategy_adapter import StrategyAdapter, AdapterError

# --- 测试 Fixtures ---
@pytest.fixture(autouse=True)
def setup_and_teardown():
    with patch.dict(sys.modules, FAKE_MODULES):
        yield
    StrategyAdapter.clear_factors_cache()

# --- 测试 _get_factor_params --- 
@pytest.mark.parametrize("factor_class, expected_params", [
    (FactorWithParamsInfo, [{'name': 'p1', 'type': 'int', 'default': 1, 'comment': 'param1'}, 
                             {'name': 'p2', 'type': 'str', 'default': 'a', 'comment': 'p2'}]),
    (FactorWithInitSelf, [{'name': 'p1', 'type': 'any', 'default': None, 'comment': 'p1'}, 
                           {'name': 'p2', 'type': 'bool', 'default': True, 'comment': 'p2'}]),
    (FactorWithSimpleInit, [{'name': 'p_int', 'type': 'int', 'default': None, 'comment': 'p_int'}, 
                             {'name': 'p_float', 'type': 'float', 'default': 1.0, 'comment': 'p_float'}]),
    (FactorWithNoParams, []),
    (FactorWithOnlySelf, []),
    (FactorWithArgsKwargs, []),
])
def test_get_factor_params(factor_class, expected_params):
    params = StrategyAdapter._get_factor_params(factor_class)
    assert len(params) == len(expected_params)
    
    expected_params_dict = {p['name']: p for p in expected_params}

    for name, p_info in params.items():
        assert name in expected_params_dict
        expected_p = expected_params_dict[name]
        assert p_info['type'] == expected_p['type']
        assert p_info['default'] == expected_p['default']
        # 注：comment的断言可以更灵活，因为实现可能会自动生成
        assert 'comment' in p_info

# --- 测试 _discover_factors ---
class TestDiscoverFactors:
    def test_discover_factors_normal_case(self):
        with patch('backend.app.abupy_adapter.strategy_adapter.os.walk') as mock_walk:
            mock_walk.return_value = [('/fake/path/abupy/FactorBuyBu', [], ['factor_one.py', 'factor_two.py'])]
            
            factors = StrategyAdapter._discover_factors(
                'buy', 
                'abupy.FactorBuyBu', 
                FakeAbuFactorBuyBase, 
                (FakeBuyCallMixin, FakeBuyPutMixin)
            )
            
            assert len(factors) == 5
            factor_names = sorted([f['name'] for f in factors])
            assert factor_names == ['FactorWithArgsKwargs', 'FactorWithInitSelf', 'FactorWithNoParams', 'FactorWithOnlySelf', 'FactorWithParamsInfo']

    def test_discover_factors_empty_directory(self):
        with patch('backend.app.abupy_adapter.strategy_adapter.os.walk') as mock_walk:
            mock_walk.return_value = []
            factors = StrategyAdapter._discover_factors('buy', 'abupy.FactorBuyBu', FakeAbuFactorBuyBase)
            assert factors == []
            mock_walk.assert_called_once()

    def test_discover_factors_skips_module_with_syntax_error(self):
        with patch('backend.app.abupy_adapter.strategy_adapter.os.walk') as mock_walk, \
             patch('backend.app.abupy_adapter.strategy_adapter.importlib.import_module') as mock_import:
            mock_walk.return_value = [('/fake/path/abupy/FactorBuyBu', [], ['good_factor.py', 'bad_factor.py'])]
            
            def import_side_effect(name):
                if name == 'abupy.FactorBuyBu.good_factor':
                    return mock_factor_module_buy
                if name == 'abupy.FactorBuyBu.bad_factor':
                    raise SyntaxError("mock syntax error")
                return FAKE_MODULES.get(name, MagicMock())
            mock_import.side_effect = import_side_effect
            
            factors = StrategyAdapter._discover_factors(
                'buy', 
                'abupy.FactorBuyBu', 
                FakeAbuFactorBuyBase,
                (FakeBuyCallMixin, FakeBuyPutMixin)
            )
            assert len(factors) == 5
            assert 'FactorWithParamsInfo' in [f['name'] for f in factors]

# --- 单元测试: 测试 get_available_abu_factors 的缓存和分支逻辑 ---
@patch('backend.app.abupy_adapter.strategy_adapter.StrategyAdapter._discover_factors')
class TestGetAvailableAbuFactorsUnit:

    def test_get_all_factors_no_cache(self, mock_discover):
        mock_discover.side_effect = [[{'name': 'BuyFactor'}], [{'name': 'SellFactor'}]]
        result = StrategyAdapter.get_available_abu_factors(use_cache=False)
        assert 'buy' in result and 'sell' in result
        assert result['buy'][0]['name'] == 'BuyFactor'
        assert result['sell'][0]['name'] == 'SellFactor'
        assert mock_discover.call_count == 2

    @pytest.mark.parametrize("factor_type", ["buy", "sell"])
    def test_get_single_type_factor(self, mock_discover, factor_type):
        mock_discover.return_value = [{'name': f'{factor_type.capitalize()}Factor'}]
        result = StrategyAdapter.get_available_abu_factors(factor_type=factor_type, use_cache=False)
        assert isinstance(result, list)
        assert result[0]['name'] == f'{factor_type.capitalize()}Factor'
        mock_discover.assert_called_once()

    def test_factor_cache_hit(self, mock_discover):
        mock_discover.return_value = [{'name': 'BuyFactor'}]
        StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
        mock_discover.assert_called_once()
        StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
        mock_discover.assert_called_once()

    def test_factor_cache_expiry(self, mock_discover):
        StrategyAdapter._cache_expiry = 0.01
        mock_discover.return_value = [{'name': 'BuyFactor'}]
        StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
        mock_discover.assert_called_once()
        time.sleep(0.02)
        StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
        assert mock_discover.call_count == 2
        StrategyAdapter._cache_expiry = 3600

    def test_clear_cache(self, mock_discover):
        mock_discover.return_value = [{'name': 'BuyFactor'}]
        StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
        mock_discover.assert_called_once()
        StrategyAdapter.clear_factors_cache()
        StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
        assert mock_discover.call_count == 2

    def test_partial_import_error_does_not_break_all(self, mock_discover):
        mock_discover.side_effect = [
            [{'name': 'BuyFactor'}], 
            MagicMock(side_effect=AdapterError("Sell import failed"))
        ]
        result = StrategyAdapter.get_available_abu_factors(use_cache=False)
        assert 'buy' in result and 'sell' in result
        assert result['buy'][0]['name'] == 'BuyFactor'
        assert result['sell'] == []

    def test_get_factors_when_abupy_is_not_installed(self, mock_discover):
        with patch.dict('sys.modules', {'abupy': None}):
            with pytest.raises(AdapterError, match="无法导入abupy模块"):
                StrategyAdapter.get_available_abu_factors(use_cache=False)
            mock_discover.assert_not_called()

    def test_set_cache_expiry(self, mock_discover):
        original_expiry = StrategyAdapter._cache_expiry
        try:
            StrategyAdapter.set_cache_expiry(10)
            assert StrategyAdapter._cache_expiry == 10
            with pytest.raises(ValueError):
                StrategyAdapter.set_cache_expiry(-1)
        finally:
             StrategyAdapter.set_cache_expiry(original_expiry)

    def test_cache_is_thread_safe(self, mock_discover):
        mock_discover.side_effect = lambda *args, **kwargs: (time.sleep(0.1), [{'name': 'BuyFactor'}])[1]
        barrier = threading.Barrier(5)
        errors = []
        def task():
            try:
                barrier.wait()
                StrategyAdapter.get_available_abu_factors(factor_type='buy', use_cache=True)
            except Exception as e:
                errors.append(e)
        threads = [threading.Thread(target=task) for _ in range(5)]
        for t in threads: t.start()
        for t in threads: t.join()
        assert not errors, f"线程中发生错误: {errors}"
        assert mock_discover.call_count == 1

# --- 集成测试: 验证 get_available_abu_factors 与 _discover_factors 的协作 ---
class TestGetAvailableAbuFactorsIntegration:
    def test_integration_finds_factors_correctly(self):
        """
        在一个干净的、没有mock干扰的环境中，测试完整的因子发现流程。
        """
        mock_walk_setup = [
            [('/fake/path/abupy/FactorBuyBu', [], ['buy_factor.py'])],
            [('/fake/path/abupy/FactorSellBu', [], ['sell_factor.py'])]
        ]
        with patch('backend.app.abupy_adapter.strategy_adapter.os.walk') as mock_walk:
            mock_walk.side_effect = mock_walk_setup
            
            # 执行
            result = StrategyAdapter.get_available_abu_factors(use_cache=False)
            
            # 断言
            assert 'buy' in result
            assert 'sell' in result
            
            assert len(result['buy']) == 5, f"Buy factors assertion failed. Found: {result['buy']}"
            buy_factor_names = sorted([f['name'] for f in result['buy']])
            assert buy_factor_names == ['FactorWithArgsKwargs', 'FactorWithInitSelf', 'FactorWithNoParams', 'FactorWithOnlySelf', 'FactorWithParamsInfo']
            
            assert len(result['sell']) == 1, f"Sell factors assertion failed. Found: {result['sell']}"
            assert result['sell'][0]['name'] == 'FactorWithSimpleInit'
            
            assert mock_walk.call_count == 2

# --- 测试策略持久化 (Save/Load) ---
@patch('backend.app.abupy_adapter.strategy_adapter.os.path.exists')
@patch('backend.app.abupy_adapter.strategy_adapter.os.makedirs')
@patch('builtins.open')
class TestStrategyPersistence:
    @pytest.fixture
    def sample_strategy(self):
        return Strategy(
            id='test-strat-123', name='Test Strategy',
            buy_factors=[BuyFactor(name='FactorA', class_name='FactorA', params={'p1': 1})],
            sell_factors=[SellFactor(name='FactorB', class_name='FactorB', params={'p2': 2})],
            parameters={'param_c': 3}
        )

    def test_save_strategy_success(self, mock_open, mock_makedirs, mock_exists, sample_strategy):
        mock_file = mock_open.return_value.__enter__.return_value
        file_path = '/fake/path/strategy.json'
        result_path = StrategyAdapter.save_strategy(sample_strategy, file_path)
        mock_makedirs.assert_called_once_with('/fake/path', exist_ok=True)
        mock_open.assert_called_once_with(file_path, 'w', encoding='utf-8')
        assert mock_file.write.call_count > 0 
        assert result_path == file_path

    def test_load_strategy_success(self, mock_open, mock_makedirs, mock_exists, sample_strategy):
        file_path = '/fake/path/strategy.json'
        strategy_json = sample_strategy.model_dump_json()
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = strategy_json
        loaded_strategy = StrategyAdapter.load_strategy(file_path)
        mock_exists.assert_called_once_with(file_path)
        mock_open.assert_called_once_with(file_path, 'r', encoding='utf-8')
        assert loaded_strategy == sample_strategy

    def test_load_strategy_file_not_found(self, mock_open, mock_makedirs, mock_exists):
        file_path = '/fake/path/non_existent.json'
        mock_exists.return_value = False
        with pytest.raises(AdapterError, match=f"策略文件不存在: {file_path}"):
            StrategyAdapter.load_strategy(file_path)

    def test_load_strategy_invalid_json(self, mock_open, mock_makedirs, mock_exists):
        file_path = '/fake/path/invalid.json'
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = '{"key": value}'
        with pytest.raises(AdapterError, match="解析策略文件时出错"):
            StrategyAdapter.load_strategy(file_path)