# backend/tests/abupy_adapter/test_strategy_adapter.py

import pytest
import re
from unittest.mock import patch, MagicMock
from typing import List, Dict, Any

import pandas as pd
from datetime import datetime, timedelta

from app.schemas.strategy import Strategy, BuyFactor, SellFactor
from app.schemas.market import KlineData, KlineItem
from app.abupy_adapter.strategy_adapter import StrategyAdapter
from app.core.exceptions import AdapterError, FactorError, ParameterError

# --- 辅助函数 ---

def create_mock_kline_data(days=30, symbol="FAKESYMBOL") -> KlineData:
    """
    创建一个健壮的、包含足够多数据的KlineData mock对象。
    确保数据量大于ATR计算周期(21)，以避免DataFrame变空。
    """
    mock_kline_items = []
    start_date = datetime(2023, 1, 1)
    for i in range(days):
        date = start_date + timedelta(days=i)
        mock_kline_items.append(KlineItem(
            date=date.strftime("%Y%m%d"),
            open=10 + i * 0.1,
            high=11 + i * 0.1,
            low=9 + i * 0.1,
            close=10.5 + i * 0.1,
            volume=1000 + i * 100
        ))
    
    return KlineData(
        symbol=symbol,
        name="假股票",
        market="CN",
        period="D",
        data=mock_kline_items,
        latest_date=(start_date + timedelta(days=days-1)).strftime("%Y%m%d")
    )

def create_sample_strategy(
    buy_factors: List[BuyFactor] = None,
    parameters: Dict[str, Any] = None,
    **kwargs
) -> Strategy:
    """创建用于测试的Strategy实例。"""
    if buy_factors is None:
        buy_factors = [
            BuyFactor(
                name="AbuFactorBuyBreak",
                class_name="FactorBuyBreak",
                parameters={"xd": 60},
                factor_type="buy"
            )
        ]
    if parameters is None:
        parameters = {"initial_capital": 1000000}
        
    strategy_data = {
        "id": "test_strategy_id",
        "name": "Test Strategy",
        "description": "A test strategy",
        "buy_factors": buy_factors,
        "sell_factors": kwargs.get("sell_factors", []),
        "parameters": parameters
    }
    strategy_data.update(kwargs)
    return Strategy(**strategy_data)


# --- 测试类 ---

class TestStrategyAdapterExecuteStrategy:

    @patch('backend.app.services.market_service.MarketService')
    @patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest')
    def test_execute_strategy_success_with_trades(self, mock_call_abupy, mock_market_service_class):
        # 1. 伪造abupy核心函数返回 - 使用完整的数据结构
        mock_orders_df = pd.DataFrame({
            'symbol': ['SH600000', 'SH600000'],
            'buy_date': [pd.Timestamp('2023-02-01'), pd.Timestamp('2023-03-01')],
            'buy_price': [10.0, 11.0],
            'sell_date': [pd.Timestamp('2023-02-10'), pd.Timestamp('2023-03-10')],
            'sell_price': [11.0, 12.0],
            'profit': [100.0, 100.0],
            'profit_cg': [0.1, 0.09],
            'buy_cnt': [100, 100],
            'buy_pos': [0.5, 0.5],
            'buy_factor': ['factor1', 'factor1'],
            'sell_factor': ['factor2', 'factor2'],
            'ml_power': [0, 0]
        })
        mock_action_df = pd.DataFrame({'capital_balance': [1000000, 900000, 1100000, 1200000]})
        mock_result = MagicMock()
        mock_result.capital = 1200000
        mock_call_abupy.return_value = (mock_orders_df, mock_action_df, mock_result)

        # 2. 伪造MarketService返回
        mock_service_instance = mock_market_service_class.return_value
        mock_service_instance.get_kline_data.return_value = create_mock_kline_data()

        # 3. 准备输入
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"], "benchmark_symbol": "sh000300",
            "start_date": "2023-01-01", "end_date": "2023-12-31", "capital": 1000000
        }

        # 4. 执行并断言
        result = StrategyAdapter.execute_strategy(strategy, market_data)

        assert result["status"] == "success"
        summary = result["execution_summary"]
        assert summary["initial_capital"] == 1000000
        assert summary["final_capital"] == 1200000
        assert summary["total_trades"] == 2
        mock_call_abupy.assert_called_once()

    @patch('backend.app.services.market_service.MarketService')
    @patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest')
    def test_execute_strategy_success_no_trades(self, mock_call_abupy, mock_market_service_class):
        # 1. 伪造abupy核心函数返回
        mock_orders_df = pd.DataFrame(columns=['symbol'])
        mock_action_df = pd.DataFrame({'capital_balance': [1000000]})
        mock_result = MagicMock()
        mock_result.capital = 1000000
        mock_call_abupy.return_value = (mock_orders_df, mock_action_df, mock_result)

        # 2. 伪造MarketService返回
        mock_service_instance = mock_market_service_class.return_value
        mock_service_instance.get_kline_data.return_value = create_mock_kline_data()

        # 3. 准备输入
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"], "benchmark_symbol": "sh000300",
            "start_date": "2023-01-01", "end_date": "2023-12-31", "capital": 1000000
        }

        # 4. 执行并断言
        result = StrategyAdapter.execute_strategy(strategy, market_data)
        
        assert result["status"] == "success"
        summary = result["execution_summary"]
        assert summary["total_trades"] == 0
        assert summary["final_capital"] == 1000000
        # 验证结果中包含无交易信息
        assert result["results"][0]["message"] == "无交易"
        mock_call_abupy.assert_called_once()


    # --- 以下是参数校验测试 ---
    def test_execute_strategy_missing_params(self):
        strategy = create_sample_strategy()
        
        # 缺少 choice_symbols
        market_data_no_symbols = {"start_date": "2023-01-01", "end_date": "2023-12-31", "capital": 1000000, "benchmark_symbol": "sh000300"}
        with pytest.raises(ParameterError, match="缺少必要参数: 股票池, 起始日期, 结束日期, 或基准"):
            StrategyAdapter.execute_strategy(strategy, market_data_no_symbols)
            
        # 缺少 start_date
        market_data_no_start = {"choice_symbols": ["SH600000"], "end_date": "2023-12-31", "capital": 1000000, "benchmark_symbol": "sh000300"}
        with pytest.raises(ParameterError, match="缺少必要参数: 股票池, 起始日期, 结束日期, 或基准"):
            StrategyAdapter.execute_strategy(strategy, market_data_no_start)
            
        # 缺少 end_date
        market_data_no_end = {"choice_symbols": ["SH600000"], "start_date": "2023-01-01", "capital": 1000000, "benchmark_symbol": "sh000300"}
        with pytest.raises(ParameterError, match="缺少必要参数: 股票池, 起始日期, 结束日期, 或基准"):
            StrategyAdapter.execute_strategy(strategy, market_data_no_end)

    @patch('backend.app.services.market_service.MarketService')
    @patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest')
    def test_execute_strategy_missing_capital_in_market_data_uses_strategy_params(self, mock_call_abupy, mock_market_service_class):
        mock_orders_df = pd.DataFrame(columns=['symbol'])
        mock_action_df = pd.DataFrame({'capital_balance': [750000]})
        mock_result = MagicMock()
        mock_result.capital = 750000
        mock_call_abupy.return_value = (mock_orders_df, mock_action_df, mock_result)
        mock_market_service_class.return_value.get_kline_data.return_value = create_mock_kline_data()
        
        strategy = create_sample_strategy(parameters={"initial_capital": 750000})
        market_data = {
            "choice_symbols": ["SH600000"], "start_date": "2023-01-01",
            "end_date": "2023-12-31", "benchmark_symbol": "sh000300"
        }

        result = StrategyAdapter.execute_strategy(strategy, market_data)
        
        assert result["parameters_used"]["capital"] == 750000
        mock_call_abupy.assert_called_once()
        # 从位置参数中获取capital_obj
        # call_abupy_backtest的参数顺序是: symbols, benchmark, capital, ...
        call_args = mock_call_abupy.call_args.args
        capital_obj_passed = call_args[2] # 第三个位置参数
        assert capital_obj_passed.read_cash == 750000

    def test_execute_strategy_missing_capital_in_both(self):
        strategy = create_sample_strategy(parameters={}) # 策略中也无capital
        market_data = {
            "choice_symbols": ["SH600000"], "start_date": "2023-01-01",
            "end_date": "2023-12-31", "benchmark_symbol": "sh000300"
        }
        with pytest.raises(ParameterError, match="未提供资金参数"):
            StrategyAdapter.execute_strategy(strategy, market_data)

    @patch('backend.app.services.market_service.MarketService')
    def test_execute_strategy_no_buy_factors(self, mock_market_service_class):
        mock_market_service_class.return_value.get_kline_data.return_value = create_mock_kline_data()
        strategy = create_sample_strategy(buy_factors=[])
        market_data = {
            "choice_symbols": ["SH600000"], "start_date": "2023-01-01",
            "end_date": "2023-12-31", "capital": 1000000, "benchmark_symbol": "sh000300"
        }
        with pytest.raises(FactorError, match="策略必须包含至少一个买入因子"):
            StrategyAdapter.execute_strategy(strategy, market_data)

    @patch('backend.app.services.market_service.MarketService')
    @patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest')
    def test_execute_strategy_abupy_exception(self, mock_call_abupy, mock_market_service_class):
        mock_market_service_class.return_value.get_kline_data.return_value = create_mock_kline_data()
        
        mock_abu_error_msg = "策略执行失败: CapitalClass init benchmark kl_pd is None"
        mock_call_abupy.side_effect = ValueError("CapitalClass init benchmark kl_pd is None")
        
        strategy = create_sample_strategy()
        market_data = {
            "choice_symbols": ["SH600000"], "start_date": "2023-01-01",
            "end_date": "2023-12-31", "capital": 1000000, "benchmark_symbol": "sh000300"
        }
        
        with pytest.raises(AdapterError, match=mock_abu_error_msg):
            StrategyAdapter.execute_strategy(strategy, market_data)

    @patch('backend.app.services.market_service.MarketService')
    def test_execute_strategy_invalid_factor_module(self, mock_market_service_class):
        mock_market_service_class.return_value.get_kline_data.return_value = create_mock_kline_data()
        
        strategy = create_sample_strategy(
            buy_factors=[BuyFactor(name="不存在的因子", class_name="NonExistentFactor")]
        )
        market_data = {
            "choice_symbols": ["SH600000"], "start_date": "2023-01-01",
            "end_date": "2023-12-31", "capital": 1000000, "benchmark_symbol": "sh000300"
        }
        
        # 期望的错误是FactorError被包装在AdapterError中
        with pytest.raises(AdapterError, match=r"因子.*NonExistentFactor.*配置或实例化时发生致命错误"):
            StrategyAdapter.execute_strategy(strategy, market_data)