import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 获取项目根目录
# D:/.../abu_modern/backend/app -> D:/.../abu_modern/backend -> D:/.../abu_modern
# os.path.dirname()会返回指定路径的目录部分
# 这行代码的意思是获取当前文件所在目录的父目录的父目录，即项目根目录
# 比如当前文件是 D:\智能投顾\量化相关\abu_modern\backend\app\config.py
# os.path.dirname(__file__) 就是 D:\智能投顾\量化相关\abu_modern\backend\app
# os.path.dirname(os.path.dirname(__file__)) 就是 D:\智能投顾\量化相关\abu_modern\backend
# os.path.dirname(os.path.dirname(os.path.dirname(__file__))) 就是 D:\智能投顾\量化相关\abu_modern
# 所以，下面这行代码可以动态地找到项目的根目录
# 无论你在哪里运行这个项目，它都能正确地找到.env文件
# 这是非常健壮的写法
# BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# .env 文件在项目根目录，即 D:\智能投顾\量化相关\abu_modern\.env
# 因此，我们需要获取项目根目录
# __file__ 是当前文件的路径，即 D:\智能投顾\量化相关\abu_modern\backend\app\config.py
# os.path.dirname(__file__) 是 D:\智能投顾\量化相关\abu_modern\backend\app
# os.path.dirname(os.path.dirname(__file__)) 是 D:\智能投顾\量化相关\abu_modern\backend
# os.path.dirname(os.path.dirname(os.path.dirname(__file__))) 是 D:\智能投顾\量化相关\abu_modern
# 所以，.env文件的路径就是 os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')
# load_dotenv(os.path.join(BASE_DIR, '.env'))
# 更加简单的方式是，让dotenv自己去找.env文件
# load_dotenv()会自动在当前目录和上级目录中寻找.env文件
# 所以，我们只需要简单地调用load_dotenv()即可
# 这种方式更加简洁，也更加健壮
# 如果你的.env文件不在项目根目录，那么你需要指定路径
# load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '.env'))
# 假设.env文件在项目根目录，即 D:\智能投顾\量化相关\abu_modern\.env
# 那么我们就可以直接调用load_dotenv()
# Python会自动向上寻找.env文件
load_dotenv()

class Settings(BaseSettings):
    # Tushare API Token
    # 从环境变量中读取TUSHARE_TOKEN，如果没有设置，则默认为 "your_tushare_token_here"
    TUSHARE_TOKEN: str = os.getenv("TUSHARE_TOKEN", "your_tushare_token_here")

    # abupy 本地数据存储目录
    # 从环境变量中读取ABUPY_DATA_DIR，如果没有设置，则默认为 "D:/abu/data"
    ABUPY_DATA_DIR: str = os.getenv("ABUPY_DATA_DIR", "D:/abu/data")
    
    # 可以在这里添加更多的应用配置
    # 例如：
    # APP_NAME: str = "军师AI - 智能投顾"
    # API_V1_STR: str = "/api/v1"

    class Config:
        # Pydantic的配置类，告诉它从.env文件加载环境变量
        # case_sensitive = True # 环境变量名大小写敏感
        # env_file = ".env" # 指定.env文件名
        # env_file_encoding = 'utf-8' # 指定.env文件编码
        pass

# 创建一个全局可用的settings实例
settings = Settings()