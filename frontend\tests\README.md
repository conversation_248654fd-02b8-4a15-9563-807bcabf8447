# 前端测试文档

本文档描述了前端项目的测试架构、测试类型和运行方法。

## 📁 测试目录结构

```
tests/
├── api/                    # API层测试
│   └── backtest.test.ts   # 回测API测试
├── components/            # 组件测试
│   ├── BacktestForm.test.ts
│   └── BacktestResults.test.ts
├── stores/               # 状态管理测试
│   └── useBacktestStore.test.ts
├── utils/                # 工具函数测试
│   └── formatters.test.ts
├── integration/          # 集成测试
│   └── backtest-workflow.test.ts
├── e2e/                  # 端到端测试
│   └── backtest-e2e.test.ts
├── factories/            # 测试数据工厂 (新增)
│   └── BacktestDataFactory.ts
├── utils/                # 测试工具类 (新增)
│   └── BacktestTestUtils.ts
├── mocks/                # MSW API模拟 (新增)
│   └── backtestHandlers.ts
├── setup.ts              # 测试环境设置
└── README.md             # 本文档
```

## 🧪 测试类型

### 1. 单元测试 (Unit Tests)
**目录**: `tests/api/`, `tests/stores/`, `tests/utils/`

- **API测试**: 测试HTTP请求、响应处理、错误处理
- **Store测试**: 测试状态管理逻辑、数据流、计算属性
- **工具函数测试**: 测试纯函数、格式化器、辅助方法

**特点**:
- 快速执行
- 隔离性强
- 覆盖率高
- 专注于单一功能

### 2. 组件测试 (Component Tests)
**目录**: `tests/components/`

- 测试Vue组件的渲染
- 测试用户交互
- 测试props和events
- 测试组件状态变化

**特点**:
- 使用Vue Test Utils
- 模拟用户操作
- 验证DOM输出
- 测试组件生命周期

### 3. 集成测试 (Integration Tests)
**目录**: `tests/integration/`

- 测试组件间交互
- 测试数据流
- 测试Store与组件的集成
- 测试完整的业务流程

**特点**:
- 跨组件测试
- 真实的数据流
- 业务场景覆盖
- 中等执行速度

### 4. 端到端测试 (E2E Tests)
**目录**: `tests/e2e/`

- 模拟真实用户操作
- 测试完整的用户旅程
- 测试可访问性
- 测试性能和响应性

**特点**:
- 最接近真实使用
- 全流程覆盖
- 执行时间较长
- 高置信度

## 🚀 运行测试

### 基本命令

```bash
# 运行所有测试
npm test

# 运行特定类型的测试
npm run test:unit          # 单元测试
npm run test:components    # 组件测试
npm run test:integration   # 集成测试
npm run test:e2e          # 端到端测试
npm run test:api          # API测试
npm run test:stores       # Store测试
npm run test:utils        # 工具函数测试

# 生成覆盖率报告
npm run test:coverage

# 监听模式
npm run test:watch

# CI环境运行
npm run test:ci
```

### 高级用法

```bash
# 使用自定义脚本
node scripts/test.js [command] [options]

# 可用选项
--watch      # 监听文件变化
--coverage   # 生成覆盖率报告
--reporter   # 指定报告器
--continue   # 失败时继续运行

# 示例
node scripts/test.js unit --watch
node scripts/test.js all --coverage --continue
```

## 📊 测试覆盖率

项目设置了以下覆盖率目标：

- **分支覆盖率**: ≥ 85%
- **函数覆盖率**: ≥ 85%
- **行覆盖率**: ≥ 85%
- **语句覆盖率**: ≥ 85%

覆盖率报告生成在 `coverage/` 目录下：
- `coverage/index.html` - HTML报告
- `coverage/lcov.info` - LCOV格式
- `coverage/coverage-final.json` - JSON格式

## 🛠️ 测试工具和框架

### 核心框架
- **Vitest**: 测试运行器和断言库
- **Vue Test Utils**: Vue组件测试工具
- **jsdom**: DOM环境模拟
- **MSW**: API模拟

### 辅助工具
- **Pinia**: 状态管理测试
- **Element Plus**: UI组件测试
- **TypeScript**: 类型安全

### 测试基础设施 (新增)
- **BacktestDataFactory**: 类型安全的测试数据生成工厂
- **BacktestTestUtils**: 统一的测试工具类和断言方法
- **MSW Handlers**: API模拟处理器，支持状态化响应

## 📝 编写测试的最佳实践

### 1. 测试命名
```typescript
// ✅ 好的命名
describe('BacktestForm', () => {
  it('should validate required fields before submission', () => {
    // ...
  });
});

// ❌ 不好的命名
describe('Form', () => {
  it('should work', () => {
    // ...
  });
});
```

### 2. 测试结构
```typescript
// 使用 AAA 模式：Arrange, Act, Assert
it('should calculate total return correctly', () => {
  // Arrange - 准备测试数据
  const initialCapital = 100000;
  const finalCapital = 120000;
  
  // Act - 执行操作
  const result = calculateTotalReturn(initialCapital, finalCapital);
  
  // Assert - 验证结果
  expect(result).toBe(0.20);
});
```

### 3. Mock使用
```typescript
// ✅ 使用数据工厂生成一致的测试数据
import { BacktestDataFactory } from '../factories/BacktestDataFactory';
const mockResult = BacktestDataFactory.createBacktestResult('profitable');

// ✅ 使用MSW进行API模拟（自动配置）
// MSW处理器已在setup.ts中自动配置

// ✅ 使用全局测试工具
const mockResult = global.testUtils.createMockBacktestResult();
```

### 4. 异步测试
```typescript
// ✅ 使用专用的等待工具
import { BacktestTestUtils } from '../utils/BacktestTestUtils';

// 等待回测完成
await BacktestTestUtils.waitForBacktestCompletion(wrapper);

// 等待DOM更新
await BacktestTestUtils.waitForDOMUpdate();

// 清空Promise队列
await BacktestTestUtils.flushPromises();
```

### 5. 数据验证
```typescript
// ✅ 使用内置的验证方法
import { BacktestDataFactory } from '../factories/BacktestDataFactory';

// 验证金融指标一致性
BacktestDataFactory.validateMetricsConsistency(metrics);

// 验证权益曲线完整性
BacktestDataFactory.validateEquityCurveIntegrity(curve, initialCapital, finalCapital);
```

### 6. 性能测试
```typescript
// ✅ 使用性能测试工具
import { BacktestTestUtils } from '../utils/BacktestTestUtils';

const { result, duration } = await BacktestTestUtils.measureResponseTime(async () => {
  return await processLargeDataset();
});

expect(duration).toBeLessThan(1000); // 应在1秒内完成
```

## 🏭 测试基础设施详细说明

### BacktestDataFactory - 测试数据工厂

提供类型安全的测试数据生成，确保业务逻辑一致性：

```typescript
// 创建标准回测配置
const config = BacktestDataFactory.createValidConfig();

// 创建特定场景的回测结果
const profitableResult = BacktestDataFactory.createBacktestResult('profitable');
const lossResult = BacktestDataFactory.createBacktestResult('loss');

// 创建大数据集用于性能测试
const largeEquityCurve = BacktestDataFactory.createLargeEquityCurve(10000);
```

### BacktestTestUtils - 测试工具类

提供统一的测试工具和断言方法：

```typescript
// 智能等待策略
await BacktestTestUtils.waitForBacktestCompletion(wrapper);

// 状态转换验证
await BacktestTestUtils.verifyAtomicStateTransition(store, operation, expectedStates);

// 金融数据验证
BacktestTestUtils.validateFinancialMetricsLogic(metrics);
```

### MSW API模拟处理器

自动配置的API模拟，支持：
- 真实网络延迟模拟
- 状态化回测进度
- 错误场景覆盖
- 参数验证

```typescript
// 设置特定测试场景
import { setupTestScenario } from '../mocks/backtestHandlers';
setupTestScenario('network_delay');
```

## 📖 完整示例

### 组件测试示例
```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { BacktestDataFactory } from '../factories/BacktestDataFactory';
import { BacktestTestUtils } from '../utils/BacktestTestUtils';
import BacktestForm from '@/components/BacktestForm.vue';

describe('BacktestForm', () => {
  let wrapper: any;
  
  beforeEach(() => {
    wrapper = mount(BacktestForm);
  });
  
  it('应该成功提交有效的回测配置', async () => {
    // 使用数据工厂生成测试数据
    const config = BacktestDataFactory.createValidConfig('normal');
    
    // 模拟用户输入
    await BacktestTestUtils.simulateUserInput(
      wrapper.find('[data-testid="symbol-input"]'),
      config.symbol
    );
    
    // 提交表单
    await BacktestTestUtils.simulateUserClick(
      wrapper.find('[data-testid="submit-button"]')
    );
    
    // 等待处理完成
    await BacktestTestUtils.waitForDOMUpdate();
    
    // 验证结果
    expect(wrapper.emitted('submit')).toBeTruthy();
  });
});
```

更多详细的使用示例和API文档，请参考各个模块的JSDoc注释。
it('should handle async operations', async () => {
  const promise = asyncFunction();
  await nextTick();
  
  const result = await promise;
  expect(result).toBeDefined();
});
```

## 🔧 测试配置

### Vitest配置
配置文件：`vitest.config.ts`

主要配置项：
- 测试环境：jsdom
- 覆盖率提供者：v8
- 全局变量：启用
- 设置文件：`tests/setup.ts`

### 测试设置
文件：`tests/setup.ts`

包含：
- 全局Mock设置
- Vue Test Utils配置
- 测试工具函数
- 环境变量设置

## 🐛 调试测试

### 1. 使用调试器
```typescript
// 在测试中添加断点
it('should debug this test', () => {
  debugger; // 在浏览器开发者工具中会暂停
  // 测试代码
});
```

### 2. 查看组件输出
```typescript
// 打印组件HTML
console.log(wrapper.html());

// 查看组件数据
console.log(wrapper.vm.$data);
```

### 3. 运行单个测试
```bash
# 运行特定文件
npx vitest tests/components/BacktestForm.test.ts

# 运行特定测试用例
npx vitest -t "should validate required fields"
```

## 📈 持续集成

### CI配置建议
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:ci
      - uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

### 质量门禁
- 所有测试必须通过
- 覆盖率不能低于设定阈值
- 不能有TypeScript错误
- ESLint检查通过

## 🤝 贡献指南

### 添加新测试
1. 确定测试类型和目录
2. 创建测试文件
3. 编写测试用例
4. 运行测试确保通过
5. 检查覆盖率

### 修改现有测试
1. 理解现有测试逻辑
2. 保持测试的独立性
3. 更新相关文档
4. 确保所有测试通过

### 测试审查清单
- [ ] 测试命名清晰
- [ ] 覆盖主要场景
- [ ] 包含边界情况
- [ ] 错误处理测试
- [ ] 异步操作正确处理
- [ ] Mock使用合理
- [ ] 测试独立性
- [ ] 性能考虑

## 📚 参考资源

- [Vitest官方文档](https://vitest.dev/)
- [Vue Test Utils文档](https://test-utils.vuejs.org/)
- [Testing Library最佳实践](https://testing-library.com/docs/guiding-principles)
- [JavaScript测试最佳实践](https://github.com/goldbergyoni/javascript-testing-best-practices)

---

如有问题或建议，请提交Issue或联系开发团队。