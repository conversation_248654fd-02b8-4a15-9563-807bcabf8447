# UmpBu (裁判系统) 技术实现指南

## 1. 核心工作原理

UmpBu（Umpire System，裁判系统）是abupy中用于风险控制的核心模块。其主要思想是利用机器学习模型，通过分析历史交易数据，学习到可能导致亏损的交易模式（特征），并在未来的回测或实盘中，对符合这些模式的交易信号进行拦截，从而避免潜在的亏损，提高策略的胜率和稳定性。

**核心流程如下：**

1.  **特征工程**: 从历史交易订单（`orders_pd`）中提取与交易行为相关的特征。这些特征可以包括K线形态（如角度、波动）、价格位置等。`ABuMLFeature` 模块负责此工作。
2.  **模型训练**: 将提取的特征和交易结果（盈利/亏损）作为训练数据，训练一个或多个分类模型（如GMM、SVM等）。UmpBu中的“裁判”类（如 `AbuUmpMainDeg`、`AbuUmpEdgeDeg`）封装了这些模型的训练和预测逻辑。
3.  **裁判介入**: 在新的回测任务中，当一个买入或卖出信号产生后，系统并不会立即执行交易。而是先将当前的行情数据和交易信号转换成与训练时相同的特征向量。
4.  **决策判断**: 将特征向量输入到已经训练好的“裁判”模型中进行预测。如果模型预测该笔交易属于“高风险”（即大概率亏损）模式，则“裁判”会“否决”这笔交易，交易被拦截，不会执行。反之，如果模型认为风险在可接受范围内，则交易被允许执行。

## 2. “裁判系统”在回测流程中的作用

“裁判系统”在回测流程中的干预点位于**买入或卖出信号已经产生，但尚未生成最终交易订单**的环节。

具体来说，在 `ABuPickTimeWorker`（择时工作单元）中，当一个买入/卖出因子（`AbuFactorBuyBase`/`AbuFactorSellBase` 的子类）找到了一个符合条件的交易信号时，会触发其内部的 `ump_manger`（`AbuUmpManager`的实例）。`ump_manger` 会调用所有已配置的“裁判”对这个潜在的交易机会进行审查。只有所有“裁判”都“同意”后，这笔交易才会被最终确认和执行。

## 3. ABuUmpManager (裁判管理器)

`ABuUmpManager` 是裁判系统的核心调度器。它的主要职责是：

*   **持有和管理裁判实例**: 在因子（`factor`）初始化时，`AbuUmpManager` 会被创建，并根据全局配置（`ABuEnv`中的设置）或用户自定义配置，加载相应的裁判类实例。
*   **按类型筛选**: `ABuUmpManager` 会根据当前因子的类型（买入因子/卖出因子）来筛选对应的裁判。例如，一个买入因子只会使用 `BuyUmpMixin` 类型的裁判。
*   **提供统一接口**: 因子通过调用 `ump_manger` 的 `check_ump` 系列方法来触发裁判的决策过程。`ump_manger` 内部会遍历所有激活的裁判，并综合它们的意见，返回最终的决策结果（是否拦截）。
*   **懒加载（Lazy Loading）**: `ABuUmpManager` 使用 `@LazyFunc` 装饰器来延迟裁判实例的创建，只有在第一次实际使用某个裁判时，才会真正地去实例化它，提高了效率。

## 4. “裁判”的种类与区别

abupy提供了两大类裁判：**主裁判 (Main Umpire)** 和 **边裁判 (Edge Umpire)**。

### 主裁判 (AbuUmpMainBase的子类)

*   **核心思想**: 主裁判关注的是“大概率亏损”的模式。它使用聚类算法（如GMM）将历史交易特征划分成不同的簇。如果某个簇内的交易绝大多数（例如，超过65%）都是亏损的，那么这个簇就被标记为“危险区域”。当新交易的特征落入这个“危险区域”时，主裁判就会判定其为高风险并进行拦截。
*   **决策方式**: “一票否决制”。只要新交易命中了任何一个被标记为“危险”的分类簇，交易就会被拦截。
*   **典型例子**:
    *   `AbuUmpMainDeg`: 角度主裁，根据K线的角度特征来判断风险。
    *   `AbuUmpMainJump`: 跳空主裁，根据是否存在跳空缺口来判断风险。
    *   `AbuUmpMainPrice`: 价格主裁，根据价格所处的位置（如是否接近前期高点/低点）来判断。
    *   `AbuUmpMainWave`: 波动主裁，根据价格的波动率特征来判断。

### 边裁判 (AbuUmpEdgeBase的子类)

*   **核心思想**: 边裁判关注的是“极端情况”。它首先对历史交易的盈亏进行排序，然后将盈利最高和亏损最大的一部分交易（例如，前后各23.6%）分别标记为“顶端盈利”和“底端亏损”。边裁判学习的是这两类极端情况的特征。
*   **决策方式**: “相似度投票制”。当一个新交易出现时，边裁判会计算其特征与所有“顶端盈利”和“底端亏损”样本的相似度。然后，根据最相似的一批历史样本（“近邻”）进行投票。如果“近邻”中“底端亏损”的样本占了压倒性多数（例如，超过61.8%），则认为该交易有很大概率成为又一个极端亏损案例，从而进行拦截。
*   **典型例子**:
    *   `AbuUmpEdgeDeg`: 角度边裁，分析极端盈亏交易的角度特征。
    *   `AbuUmpEdgePrice`: 价格边裁，分析极端盈亏交易的价格位置特征。
    *   `AbuUmpEdgeWave`: 波动边裁，分析极端盈亏交易的波动率特征。

### 区别总结

| 特性 | 主裁判 (Main Umpire) | 边裁判 (Edge Umpire) |
| :--- | :--- | :--- |
| **关注点** | 大概率亏损的普遍模式 | 极端盈利/亏损的特殊模式 |
| **决策模型** | 聚类 + 分类簇命中 | K-近邻 + 相似度投票 |
| **决策逻辑** | 落入“危险区域”即拦截 | 与“极端亏损”模式高度相似才拦截 |
| **作用** | 过滤掉常见的、可识别的亏损陷阱 | 防范“黑天鹅”式的极端亏损事件 |

## 5. 如何配置和使用

在abupy中，配置和使用裁判系统并不通过 `run_loop_back` 的直接参数，而是通过**全局变量**和**专门的管理接口**来实现。这提供了更大的灵活性。

核心步骤如下：

1.  **开启用户裁判功能**: 设置全局开关 `ABuUmpManager.g_enable_user_ump = True`。
2.  **开启特征记录**: 设置全局开关 `ABuEnv.g_enable_ml_feature = True`。这是因为裁判系统依赖于交易过程中的特征数据，必须先开启记录功能。
3.  **清空旧配置**: 在每次回测前，调用 `ABuUmpManager.clear_user_ump()` 清理之前可能存在的裁判配置，避免干扰。
4.  **实例化并添加裁判**: 创建你需要使用的裁判类的实例。**关键点**：在创建实例时，必须设置 `predict=True` 来表明这是用于预测的裁判，并使用 `market_name` 参数指定要加载哪个已经训练好的模型文件（这个 `market_name` 对应于训练时 `ump_main_clf_dump` 或 `ump_edge_clf_dump` 中使用的 `market_name`）。
5.  **注册裁判**: 使用 `ABuUmpManager.append_user_ump(ump_object)` 将实例化的裁判对象添加的全局裁判列表中。

### 完整可执行代码示例

以下代码演示了如何在一个 `run_loop_back` 回测任务中，同时配置一个主裁判（`AbuUmpMainDeg`）和一个边裁判（`AbuUmpEdgeDeg`）。

**前置条件**: 假设你已经通过 `AbuUmpMainBase.ump_main_clf_dump` 和 `AbuUmpEdgeBase.ump_edge_clf_dump` 训练好了模型，并分别以 `market_name='my_main_ump'` 和 `market_name='my_edge_ump'` 的名称保存了模型文件。

```python
# -*- encoding:utf-8 -*-
import numpy as np
import pandas as pd

from abupy import abu, ABuSymbolPd, ABuMarketDrawing, ABuKLUtil
from abupy import AbuFactorBuyBreak, AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop
from abupy import ABuUmpManager, ABuEnv
from abupy import AbuUmpMainDeg, AbuUmpEdgeDeg

def prepare_ump_training_data():
    """准备一个虚拟的裁判训练数据，实际使用中应来自真实回测"""
    # 仅为演示，创建虚拟的ump模型文件
    # 在实际应用中，你需要先运行一个回测，生成orders_pd，然后调用ump_main_clf_dump和ump_edge_clf_dump进行训练
    from abupy import AbuUmpMainBase, AbuUmpEdgeBase, AbuMetricsBase
    
    # 假设这是从一次回测中得到的orders_pd
    # 注意：这里的数据是随机生成的，不具备任何真实含义，仅用于能跑通流程
    num_orders = 200
    orders_data = {
        'symbol': ['usTSLA'] * num_orders,
        'buy_date': pd.to_datetime(np.random.choice(pd.date_range('2022-01-01', '2023-01-01'), num_orders)),
        'buy_price': np.random.uniform(100, 200, num_orders),
        'buy_cnt': [100] * num_orders,
        'sell_date': pd.to_datetime(np.random.choice(pd.date_range('2023-01-02', '2023-06-01'), num_orders)),
        'sell_price': np.random.uniform(80, 220, num_orders),
        'sell_type': ['win'] * num_orders,
        'key': np.arange(num_orders),
        'result': np.random.choice([-1, 1], num_orders), # -1 loss, 1 win
        'buy_deg_ang42': np.random.randn(num_orders) * 10,
        'buy_deg_ang252': np.random.randn(num_orders) * 10,
        'buy_deg_ang60': np.random.randn(num_orders) * 10,
        'buy_deg_ang21': np.random.randn(num_orders) * 10,
    }
    orders_pd = pd.DataFrame(orders_data)
    orders_pd['profit'] = (orders_pd['sell_price'] - orders_pd['buy_price']) * orders_pd['buy_cnt']
    
    # 需要通过AbuMetricsBase计算'profit_cg'等度量
    metrics = AbuMetricsBase(orders_pd, None, None, None)
    metrics.fit_metrics_order()
    orders_pd_trained = metrics.orders_pd

    print("正在生成演示用的主裁判模型文件...")
    # 训练并保存主裁判
    AbuUmpMainBase.ump_main_clf_dump(orders_pd_trained, market_name='my_main_ump', brust_min=False, show_info=False)
    print("主裁判模型文件生成完毕。")

    print("正在生成演示用的边裁判模型文件...")
    # 训练并保存边裁判
    AbuUmpEdgeBase.ump_edge_clf_dump(orders_pd_trained, market_name='my_edge_ump', show_info=False)
    print("边裁判模型文件生成完毕。\n")


# 1. 先准备好用于演示的裁判模型文件
prepare_ump_training_data()

# 2. 设置回测参数
read_cash = 1000000
# 使用沙盒数据
stock_pool = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA', 'usVIPS']

# 买入因子
buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak},
               {'xd': 42, 'class': AbuFactorBuyBreak}]

# 卖出因子
sell_factors = [{'stop_loss_n': 1.0, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop},
                {'class': AbuFactorPreAtrNStop},
                {'class': AbuFactorCloseAtrNStop}]

# 3. *****核心步骤：配置并启用裁判系统*****
print("配置裁判系统中...")
# 3.1. 开启用户自定义裁判开关
ABuUmpManager.g_enable_user_ump = True
# 3.2. 开启特征记录功能（裁判系统依赖此功能）
ABuEnv.g_enable_ml_feature = True
# 3.3. (最佳实践) 每次运行前清空之前的裁判设置
ABuUmpManager.clear_user_ump()

# 3.4. 实例化需要使用的裁判
# 实例化主裁判，predict=True代表用于预测，market_name指定加载哪个模型
ump_main = AbuUmpMainDeg(predict=True, market_name='my_main_ump')
# 实例化边裁判
ump_edge = AbuUmpEdgeDeg(predict=True, market_name='my_edge_ump')

# 3.5. 将裁判实例添加到管理器中
ABuUmpManager.append_user_ump(ump_main)
ABuUmpManager.append_user_ump(ump_edge)

print(f"已配置 {len(ABuUmpManager._g_extend_ump_list)} 个裁判: {ump_main}, {ump_edge}")
print("裁判系统配置完毕，开始执行回测...\n")

# 4. 执行回测
abu_result_tuple, kl_pd_manager = abu.run_loop_back(read_cash, buy_factors, sell_factors, stock_picks=None, choice_symbols=stock_pool, n_folds=2)

# 5. 分析回测结果
if abu_result_tuple is not None:
    from abupy import ABuMetricsBase
    metrics = ABuMetricsBase(*abu_result_tuple)
    metrics.fit_metrics()
    metrics.plot_returns_cmp(only_show_returns=True)

# 6. (最佳实践) 回测结束后，可以关闭开关，避免影响其他不使用裁判的回测
ABuUmpManager.g_enable_user_ump = False
ABuEnv.g_enable_ml_feature = False
print("\n回测完成，已关闭裁判系统开关。")

```
