// 简化数据工厂 - Client
// 用于TDD快速测试数据生成

import type { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Client数据工厂类
 * 提供API客户端相关的mock数据生成方法
 */
export class SimpleClientDataFactory {
  /**
   * 创建基础API响应数据
   */
  static createApiResponse<T>(data: T, status: number = 200, statusText: string = 'OK'): AxiosResponse<T> {
    return {
      data,
      status,
      statusText,
      headers: {
        'content-type': 'application/json',
        'x-request-id': this.generateRequestId()
      },
      config: this.createRequestConfig(),
      request: {}
    } as AxiosResponse<T>;
  }

  /**
   * 创建请求配置
   */
  static createRequestConfig(overrides: Partial<AxiosRequestConfig> = {}): AxiosRequestConfig {
    return {
      url: '/api/test',
      method: 'GET',
      baseURL: '/',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      ...overrides
    };
  }

  /**
   * 创建成功响应数据
   */
  static createSuccessResponse<T>(data: T): AxiosResponse<T> {
    return this.createApiResponse(data, 200, 'OK');
  }

  /**
   * 创建错误响应数据
   */
  static createErrorResponse(status: number = 500, message: string = 'Internal Server Error'): AxiosResponse<any> {
    return this.createApiResponse(
      {
        error: {
          code: status,
          message,
          timestamp: new Date().toISOString()
        }
      },
      status,
      message
    );
  }

  /**
   * 创建网络错误
   */
  static createNetworkError(): Error {
    const error = new Error('Network Error');
    (error as any).code = 'NETWORK_ERROR';
    (error as any).isAxiosError = true;
    return error;
  }

  /**
   * 创建超时错误
   */
  static createTimeoutError(): Error {
    const error = new Error('timeout of 10000ms exceeded');
    (error as any).code = 'ECONNABORTED';
    (error as any).isAxiosError = true;
    return error;
  }

  /**
   * 创建取消请求错误
   */
  static createCancelError(): Error {
    const error = new Error('Request canceled');
    (error as any).code = 'ERR_CANCELED';
    (error as any).isAxiosError = true;
    return error;
  }

  /**
   * 创建不同HTTP状态码的响应
   */
  static createHttpStatusResponse(status: number): AxiosResponse<any> {
    const statusMessages: Record<number, string> = {
      200: 'OK',
      201: 'Created',
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      422: 'Unprocessable Entity',
      500: 'Internal Server Error',
      502: 'Bad Gateway',
      503: 'Service Unavailable'
    };

    const message = statusMessages[status] || 'Unknown Status';
    const isSuccess = status >= 200 && status < 300;

    return this.createApiResponse(
      isSuccess 
        ? { message: 'Success', status }
        : { error: { code: status, message } },
      status,
      message
    );
  }

  /**
   * 创建大数据响应（用于性能测试）
   */
  static createLargeDataResponse(size: number = 1000): AxiosResponse<any> {
    const largeData = Array.from({ length: size }, (_, index) => ({
      id: index + 1,
      name: `Item ${index + 1}`,
      description: `Description for item ${index + 1}`.repeat(10),
      data: Array.from({ length: 10 }, (_, i) => Math.random())
    }));

    return this.createSuccessResponse({
      data: largeData,
      total: size,
      page: 1,
      pageSize: size
    });
  }

  /**
   * 创建分页响应数据
   */
  static createPaginatedResponse<T>(
    data: T[], 
    page: number = 1, 
    pageSize: number = 20, 
    total?: number
  ): AxiosResponse<any> {
    const totalCount = total || data.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = data.slice(startIndex, endIndex);

    return this.createSuccessResponse({
      data: paginatedData,
      pagination: {
        page,
        pageSize,
        total: totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
        hasNext: endIndex < totalCount,
        hasPrev: page > 1
      }
    });
  }

  /**
   * 创建延迟响应（用于测试加载状态）
   */
  static createDelayedResponse<T>(data: T, delay: number = 1000): Promise<AxiosResponse<T>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(this.createSuccessResponse(data));
      }, delay);
    });
  }

  /**
   * 创建请求头数据
   */
  static createHeaders(overrides: Record<string, string> = {}): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      'X-Request-ID': this.generateRequestId(),
      ...overrides
    };
  }

  /**
   * 生成请求ID
   */
  static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建表单数据
   */
  static createFormData(data: Record<string, any>): FormData {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value instanceof File) {
        formData.append(key, value);
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          formData.append(`${key}[${index}]`, String(item));
        });
      } else {
        formData.append(key, String(value));
      }
    });
    return formData;
  }

  /**
   * 创建文件上传数据
   */
  static createFileUploadData(fileName: string = 'test.txt', content: string = 'test content'): File {
    const blob = new Blob([content], { type: 'text/plain' });
    return new File([blob], fileName, { type: 'text/plain' });
  }

  /**
   * 验证请求配置
   */
  static validateRequestConfig(config: AxiosRequestConfig): boolean {
    const requiredFields = ['url', 'method'];
    
    for (const field of requiredFields) {
      if (!(field in config)) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // 验证URL格式
    if (typeof config.url !== 'string' || config.url.length === 0) {
      console.error('Invalid URL format');
      return false;
    }

    // 验证HTTP方法
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    if (!validMethods.includes(config.method?.toUpperCase() || '')) {
      console.error('Invalid HTTP method');
      return false;
    }

    return true;
  }

  /**
   * 创建并发请求数据
   */
  static createConcurrentRequests(count: number = 5): Array<() => Promise<AxiosResponse<any>>> {
    return Array.from({ length: count }, (_, index) => 
      () => Promise.resolve(this.createSuccessResponse({
        requestId: index + 1,
        timestamp: new Date().toISOString(),
        data: `Response ${index + 1}`
      }))
    );
  }

  /**
   * 创建重试场景数据
   */
  static createRetryScenario(maxRetries: number = 3): {
    attempt: number;
    shouldSucceed: (attempt: number) => boolean;
    getResponse: (attempt: number) => AxiosResponse<any> | Error;
  } {
    return {
      attempt: 0,
      shouldSucceed: (attempt: number) => attempt >= maxRetries,
      getResponse: (attempt: number) => {
        if (attempt >= maxRetries) {
          return this.createSuccessResponse({ message: 'Success after retries', attempt });
        } else {
          return this.createNetworkError();
        }
      }
    };
  }
}

// 导出便捷方法
export const {
  createApiResponse,
  createRequestConfig,
  createSuccessResponse,
  createErrorResponse,
  createNetworkError,
  createTimeoutError,
  createCancelError,
  createHttpStatusResponse,
  createLargeDataResponse,
  createPaginatedResponse,
  createDelayedResponse,
  createHeaders,
  generateRequestId,
  createFormData,
  createFileUploadData,
  validateRequestConfig,
  createConcurrentRequests,
  createRetryScenario
} = SimpleClientDataFactory;