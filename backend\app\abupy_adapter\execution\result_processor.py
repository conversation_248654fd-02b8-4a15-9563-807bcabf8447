import pandas as pd
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from app.schemas.strategy import PerformanceMetrics

def calculate_performance_metrics(orders_pd: pd.DataFrame, capital_obj: AbuCapital, benchmark_obj: AbuBenchmark) -> PerformanceMetrics:
    """
    根据订单数据和资金对象计算详细的性能指标。
    
    参数:
        orders_pd: 交易订单DataFrame
        capital_obj: 资金对象
        benchmark_obj: 基准对象
        
    返回:
        PerformanceMetrics: 包含所有计算指标的Pydantic模型实例
    """
    import math
    from datetime import datetime, timedelta
    import numpy as np
    
    # 如果没有交易订单，返回默认的性能指标对象
    if orders_pd is None or orders_pd.empty:
        return PerformanceMetrics()
        
    try:
        # 获取初始资金和基准数据
        initial_capital = float(capital_obj.read_cash)
        benchmark_df = benchmark_obj.kl_pd
        
        # 确保基准数据有日期索引
        if not pd.api.types.is_datetime64_any_dtype(benchmark_df.index):
            if 'date' in benchmark_df.columns:
                benchmark_df['date'] = pd.to_datetime(benchmark_df['date'])
                benchmark_df = benchmark_df.set_index('date')
        
        # 1. 构建每日市值曲线
        # 获取回测的开始和结束日期
        start_date = benchmark_df.index.min()
        end_date = benchmark_df.index.max()
        
        # 创建日期范围的DataFrame作为基础
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        portfolio_df = pd.DataFrame(index=date_range)
        portfolio_df.index.name = 'date'
        
        # 初始化资金曲线
        portfolio_df['cash'] = initial_capital  # 现金
        portfolio_df['position_value'] = 0.0    # 持仓市值
        portfolio_df['total_value'] = initial_capital  # 总市值
        
        # 按交易日期对订单排序
        orders_pd = orders_pd.sort_values('buy_date' if 'buy_date' in orders_pd.columns else 'date')
        
        # 处理每个交易订单，更新资金曲线
        positions = {}  # 当前持仓 {symbol: {price: 价格, volume: 数量}}
        
        for _, order in orders_pd.iterrows():
            # 获取交易信息
            symbol = order['symbol']
            if 'buy_date' in order and not pd.isna(order['buy_date']):
                buy_date = pd.to_datetime(order['buy_date'])
                buy_price = float(order['buy_price'])
                buy_volume = float(order['buy_cnt']) if 'buy_cnt' in order else float(order['buy_volume'] if 'buy_volume' in order else 0)
            else:
                # 如果没有买入日期，可能是导入的订单或其他格式
                continue
                
            # 卖出信息可能为空（未平仓的持仓）
            sell_date = None
            if 'sell_date' in order and not pd.isna(order['sell_date']):
                sell_date = pd.to_datetime(order['sell_date'])
                sell_price = float(order['sell_price'])
            
            # 买入操作 - 更新现金和持仓
            buy_value = buy_price * buy_volume
            
            # 确保买入日期在日期范围内
            if buy_date in portfolio_df.index:
                # 更新买入日的现金和持仓
                portfolio_df.loc[buy_date:, 'cash'] -= buy_value
                
                # 更新持仓
                if symbol not in positions:
                    positions[symbol] = {'volume': 0, 'cost': 0}
                positions[symbol]['volume'] += buy_volume
                positions[symbol]['cost'] = buy_price  # 简化处理，使用最后买入价作为成本价
                
                # 更新买入日及之后的持仓市值
                portfolio_df.loc[buy_date:, 'position_value'] += buy_value
            
            # 卖出操作 - 更新现金和持仓
            if sell_date is not None and sell_date in portfolio_df.index:
                sell_value = sell_price * buy_volume  # 假设全部卖出
                
                # 更新卖出日的现金和持仓
                portfolio_df.loc[sell_date:, 'cash'] += sell_value
                portfolio_df.loc[sell_date:, 'position_value'] -= buy_value  # 简化处理，使用买入价值

        # 每日更新持仓市值
        # 为了简化，我们假设股票价格在回测期间保持不变（这在实际中是不现实的）
        # 实际应用中，需要每日获取市场价格来更新持仓市值
        for date in portfolio_df.index:
            current_position_value = 0
            for symbol, pos_info in positions.items():
                # 简化处理：假设价格不变
                current_position_value += pos_info['cost'] * pos_info['volume']
            portfolio_df.loc[date, 'position_value'] = current_position_value

        # 重新计算总市值
        portfolio_df['total_value'] = portfolio_df['cash'] + portfolio_df['position_value']

        # 2. 计算每日收益率
        portfolio_df['daily_return'] = portfolio_df['total_value'].pct_change().fillna(0)

        # 3. 计算累计收益率
        portfolio_df['cumulative_return'] = (1 + portfolio_df['daily_return']).cumprod() - 1

        # 4. 计算基准的每日和累计收益率
        benchmark_df['daily_return'] = benchmark_df['close'].pct_change().fillna(0)
        benchmark_df['cumulative_return'] = (1 + benchmark_df['daily_return']).cumprod() - 1

        # 提取最终的性能指标
        final_cumulative_return = portfolio_df['cumulative_return'].iloc[-1]
        benchmark_cumulative_return = benchmark_df['cumulative_return'].iloc[-1]

        # 5. 计算年化收益率
        days = (end_date - start_date).days
        annualized_return = (1 + final_cumulative_return) ** (365.0 / days) - 1 if days > 0 else 0
        benchmark_annualized_return = (1 + benchmark_cumulative_return) ** (365.0 / days) - 1 if days > 0 else 0

        # 6. 计算最大回撤
        cumulative_max = portfolio_df['cumulative_return'].cummax()
        drawdown = cumulative_max - portfolio_df['cumulative_return']
        max_drawdown = drawdown.max()

        # 7. 计算年化波动率
        annualized_volatility = portfolio_df['daily_return'].std() * math.sqrt(365)

        # 8. 计算夏普比率 (假设无风险利率为0)
        sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0

        # 9. 计算胜率和盈亏比
        closed_trades = orders_pd[orders_pd['sell_date'].notna()].copy()
        closed_trades['profit'] = (closed_trades['sell_price'] - closed_trades['buy_price']) * closed_trades['buy_cnt']
        wins = closed_trades[closed_trades['profit'] > 0]
        losses = closed_trades[closed_trades['profit'] < 0]
        win_rate = len(wins) / len(closed_trades) if len(closed_trades) > 0 else 0
        average_profit = wins['profit'].mean() if not wins.empty else 0
        average_loss = abs(losses['profit'].mean()) if not losses.empty else 0
        profit_loss_ratio = average_profit / average_loss if average_loss > 0 else 0

        # 10. 计算Alpha和Beta
        # 合并策略和基准的收益率
        merged_returns = pd.concat([portfolio_df['daily_return'], benchmark_df['daily_return']], axis=1).dropna()
        merged_returns.columns = ['strategy', 'benchmark']
        
        # 计算协方差和方差
        covariance = merged_returns['strategy'].cov(merged_returns['benchmark'])
        benchmark_variance = merged_returns['benchmark'].var()
        
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
        
        # 计算Alpha (年化)
        alpha = annualized_return - (0 + beta * (benchmark_annualized_return - 0)) # 假设无风险利率为0

        # 11. 计算信息比率
        tracking_error = (merged_returns['strategy'] - merged_returns['benchmark']).std() * math.sqrt(365)
        information_ratio = (annualized_return - benchmark_annualized_return) / tracking_error if tracking_error > 0 else 0

        return PerformanceMetrics(
            cumulative_return=final_cumulative_return,
            annualized_return=annualized_return,
            benchmark_return=benchmark_cumulative_return,
            benchmark_annualized_return=benchmark_annualized_return,
            alpha=alpha,
            beta=beta,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            annualized_volatility=annualized_volatility,
            information_ratio=information_ratio,
            win_rate=win_rate,
            profit_loss_ratio=profit_loss_ratio,
            total_trades=len(closed_trades)
        )

    except Exception as e:
        # 实际实现中应添加更详细的错误处理
        return PerformanceMetrics()