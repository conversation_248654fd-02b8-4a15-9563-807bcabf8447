import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: vi.fn(),
  ElMessageBox: {
    confirm: vi.fn()
  }
}))

import { ErrorHandler, ERROR_TYPES } from '@/utils/errorHandler'
import { ElMessage, ElMessageBox } from 'element-plus'

// Mock console methods
const mockConsole = {
  error: vi.fn(),
  warn: vi.fn(),
  log: vi.fn()
}

Object.assign(console, mockConsole)

describe('ErrorHandler', () => {
  const mockElMessage = vi.mocked(ElMessage)
  const mockElMessageBox = vi.mocked(ElMessageBox)

  beforeEach(() => {
    mockElMessage.mockClear()
    mockElMessageBox.confirm.mockClear()
    mockConsole.error.mockClear()
    mockConsole.warn.mockClear()
    mockConsole.log.mockClear()
  })

  describe('ERROR_TYPES常量', () => {
    it('应该包含所有预定义的错误类型', () => {
      expect(ERROR_TYPES.NETWORK_ERROR).toBe('NETWORK_ERROR')
      expect(ERROR_TYPES.VALIDATION_ERROR).toBe('VALIDATION_ERROR')
      expect(ERROR_TYPES.BUSINESS_ERROR).toBe('BUSINESS_ERROR')
      expect(ERROR_TYPES.SYSTEM_ERROR).toBe('SYSTEM_ERROR')
      expect(ERROR_TYPES.AUTH_ERROR).toBe('AUTH_ERROR')
    })
  })

  describe('showError', () => {
    it('应该显示错误消息', () => {
      const message = '测试错误消息'
      ErrorHandler.showError(message)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: message,
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该记录错误到控制台', () => {
      const message = '测试错误消息'
      ErrorHandler.showError(message)
      
      expect(mockConsole.error).toHaveBeenCalledWith('[ErrorHandler]', message)
    })

    it('应该处理空消息', () => {
      ErrorHandler.showError('')
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '',
         type: 'error',
         duration: 3000,
         showClose: true
       })
      expect(mockConsole.error).toHaveBeenCalledWith('[ErrorHandler]', '')
    })
  })

  describe('showSuccess', () => {
    it('应该显示成功消息', () => {
      const message = '操作成功'
      ErrorHandler.showSuccess(message)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: message,
         type: 'success',
         duration: 3000,
         showClose: true
       })
    })

    it('应该记录成功消息到控制台', () => {
      const message = '操作成功'
      ErrorHandler.showSuccess(message)
      
      expect(mockConsole.log).toHaveBeenCalledWith('[ErrorHandler]', message)
    })
  })

  describe('showWarning', () => {
    it('应该显示警告消息', () => {
      const message = '警告消息'
      ErrorHandler.showWarning(message)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: message,
         type: 'warning',
         duration: 3000,
         showClose: true
       })
    })

    it('应该记录警告消息到控制台', () => {
      const message = '警告消息'
      ErrorHandler.showWarning(message)
      
      expect(mockConsole.warn).toHaveBeenCalledWith('[ErrorHandler]', message)
    })
  })

  describe('confirm', () => {
    it('应该显示确认对话框', async () => {
      const message = '确认删除吗？'
      const title = '确认操作'
      
      mockElMessageBox.confirm.mockResolvedValue('confirm')
      
      const result = await ErrorHandler.confirm(message, title)
      
      expect(mockElMessageBox.confirm).toHaveBeenCalledWith(
        message,
        title,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      expect(result).toBe(true)
    })

    it('应该处理用户取消操作', async () => {
      const message = '确认删除吗？'
      
      mockElMessageBox.confirm.mockRejectedValue(new Error('用户取消'))
      
      const result = await ErrorHandler.confirm(message)
      
      expect(mockElMessageBox.confirm).toHaveBeenCalled()
      expect(result).toBe(false)
    })

    it('应该使用默认标题', async () => {
      const message = '确认操作吗？'
      
      mockElMessageBox.confirm.mockResolvedValue('confirm')
      
      await ErrorHandler.confirm(message)
      
      expect(mockElMessageBox.confirm).toHaveBeenCalledWith(
        message,
        '确认',
        expect.any(Object)
      )
    })
  })

  describe('handleApiError', () => {
    it('应该处理网络错误', () => {
      const error = {
        code: 'NETWORK_ERROR',
        message: '网络连接失败'
      }
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '网络连接失败，请检查网络设置',
         type: 'error',
         duration: 3000,
         showClose: true
       })
      expect(mockConsole.error).toHaveBeenCalledWith('[API Error]', error)
    })

    it('应该处理认证错误', () => {
      const error = {
        code: 'AUTH_ERROR',
        message: '认证失败'
      }
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '认证失败，请重新登录',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理业务错误', () => {
      const error = {
        code: 'BUSINESS_ERROR',
        message: '业务逻辑错误'
      }
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '业务逻辑错误',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理未知错误', () => {
      const error = {
        code: 'UNKNOWN_ERROR',
        message: '未知错误'
      }
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '系统错误，请稍后重试',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理没有code的错误', () => {
      const error = {
        message: '普通错误消息'
      }
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '普通错误消息',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理Error对象', () => {
      const error = new Error('JavaScript错误')
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: 'JavaScript错误',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理字符串错误', () => {
      const error = '字符串错误消息'
      
      ErrorHandler.handleApiError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '字符串错误消息',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })
  })

  describe('handleBusinessError', () => {
    it('应该处理业务错误并显示自定义消息', () => {
      const error = new Error('业务错误')
      const customMessage = '自定义错误消息'
      
      ErrorHandler.handleBusinessError(error, customMessage)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: customMessage,
         type: 'error',
         duration: 3000,
         showClose: true
       })
      expect(mockConsole.error).toHaveBeenCalledWith('[Business Error]', error)
    })

    it('应该使用错误对象的消息作为默认', () => {
      const error = new Error('业务错误消息')
      
      ErrorHandler.handleBusinessError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '业务错误消息',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理没有消息的错误', () => {
      const error = new Error()
      
      ErrorHandler.handleBusinessError(error)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '操作失败',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })
  })

  describe('safeExecute', () => {
    it('应该成功执行函数并返回结果', async () => {
      const mockFn = vi.fn().mockResolvedValue('成功结果')
      
      const result = await ErrorHandler.safeExecute(mockFn)
      
      expect(result).toBe('成功结果')
      expect(mockFn).toHaveBeenCalled()
    })

    it('应该捕获并处理异步函数的错误', async () => {
      const error = new Error('异步错误')
      const mockFn = vi.fn().mockRejectedValue(error)
      
      const result = await ErrorHandler.safeExecute(mockFn)
      
      expect(result).toBeNull()
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '异步错误',
         type: 'error',
         duration: 3000,
         showClose: true
       })
      expect(mockConsole.error).toHaveBeenCalledWith('[Safe Execute]', error)
    })

    it('应该捕获并处理同步函数的错误', async () => {
      const error = new Error('同步错误')
      const mockFn = vi.fn().mockImplementation(() => {
        throw error
      })
      
      const result = await ErrorHandler.safeExecute(mockFn)
      
      expect(result).toBeNull()
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '同步错误',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该传递参数给执行函数', async () => {
      const mockFn = vi.fn().mockResolvedValue('结果')
      const args = ['arg1', 'arg2', 'arg3']
      
      await ErrorHandler.safeExecute(mockFn, ...args)
      
      expect(mockFn).toHaveBeenCalledWith(...args)
    })
  })

  describe('withErrorHandling', () => {
    it('应该返回包装后的函数', () => {
      const originalFn = vi.fn()
      const wrappedFn = ErrorHandler.withErrorHandling(originalFn)
      
      expect(typeof wrappedFn).toBe('function')
    })

    it('应该在包装函数中处理错误', async () => {
      const error = new Error('包装函数错误')
      const originalFn = vi.fn().mockRejectedValue(error)
      const wrappedFn = ErrorHandler.withErrorHandling(originalFn)
      
      const result = await wrappedFn()
      
      expect(result).toBeUndefined()
      expect(mockElMessage).toHaveBeenCalledWith({
         message: '包装函数错误',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该传递参数和返回值', async () => {
      const originalFn = vi.fn().mockResolvedValue('返回值')
      const wrappedFn = ErrorHandler.withErrorHandling(originalFn)
      
      const result = await wrappedFn('arg1', 'arg2')
      
      expect(originalFn).toHaveBeenCalledWith('arg1', 'arg2')
      expect(result).toBe('返回值')
    })
  })

  describe('assert', () => {
    it('应该在条件为true时不抛出错误', () => {
      expect(() => {
        ErrorHandler.assert(true, '不应该抛出错误')
      }).not.toThrow()
    })

    it('应该在条件为false时抛出错误', () => {
      expect(() => {
        ErrorHandler.assert(false, '应该抛出错误')
      }).toThrow('应该抛出错误')
    })

    it('应该使用默认错误消息', () => {
      expect(() => {
        ErrorHandler.assert(false)
      }).toThrow('断言失败')
    })

    it('应该处理复杂条件', () => {
      const obj = { value: 10 }
      
      expect(() => {
        ErrorHandler.assert(obj.value > 5, '值应该大于5')
      }).not.toThrow()
      
      expect(() => {
        ErrorHandler.assert(obj.value > 15, '值应该大于15')
      }).toThrow('值应该大于15')
    })
  })

  describe('dev', () => {
    let originalEnv: string | undefined
    
    beforeEach(() => {
      originalEnv = process.env.NODE_ENV
    })
    
    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('应该在开发环境中输出日志', () => {
      process.env.NODE_ENV = 'development'
      
      ErrorHandler.dev.log('test message')
      ErrorHandler.dev.warn('warning message')
      ErrorHandler.dev.error('error message')
      
      expect(mockConsole.log).toHaveBeenCalled()
      expect(mockConsole.warn).toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalled()
    })

    it('应该在生产环境中不输出日志', () => {
      process.env.NODE_ENV = 'production'
      
      ErrorHandler.dev.log('test message')
      ErrorHandler.dev.warn('warning message')
      ErrorHandler.dev.error('error message')
      
      // 在生产环境中，dev 方法不应该调用 console
      expect(mockConsole.log).not.toHaveBeenCalled()
      expect(mockConsole.warn).not.toHaveBeenCalled()
      expect(mockConsole.error).not.toHaveBeenCalled()
    })

    it('应该提供开发环境日志方法', () => {
      expect(typeof ErrorHandler.dev.log).toBe('function')
      expect(typeof ErrorHandler.dev.warn).toBe('function')
      expect(typeof ErrorHandler.dev.error).toBe('function')
    })

    it('应该在开发环境中调用console方法', () => {
      process.env.NODE_ENV = 'development'
      
      ErrorHandler.dev.log('test message', { data: 'test' })
      ErrorHandler.dev.warn('warning message')
      ErrorHandler.dev.error('error message', new Error('test'))
      
      expect(mockConsole.log).toHaveBeenCalled()
      expect(mockConsole.warn).toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalled()
    })
  })

  describe('边界情况和错误处理', () => {
    it('应该处理null和undefined错误', () => {
      const result1 = ErrorHandler.handleApiError(null as any)
      const result2 = ErrorHandler.handleApiError(undefined as any)
      
      expect(result1.code).toBe(ERROR_TYPES.UNKNOWN_ERROR)
      expect(result1.message).toBe('未知错误')
      expect(result2.code).toBe(ERROR_TYPES.UNKNOWN_ERROR)
      expect(result2.message).toBe('未知错误')
    })

    it('应该处理空对象错误', () => {
      const result = ErrorHandler.handleApiError({})
      
      expect(result.code).toBe(ERROR_TYPES.UNKNOWN_ERROR)
      expect(result.message).toBe('未知错误')
    })

    it('应该处理循环引用的错误对象', () => {
      const circularError: any = { message: '循环引用错误' }
      circularError.self = circularError
      
      let result: any
      expect(() => {
        result = ErrorHandler.handleApiError(circularError)
      }).not.toThrow()
      
      expect(result.code).toBe(ERROR_TYPES.UNKNOWN_ERROR)
      expect(result.message).toBe('循环引用错误')
    })

    it('应该处理非常长的错误消息', () => {
      const longMessage = 'a'.repeat(1000)
      
      ErrorHandler.showError(longMessage)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: longMessage,
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })

    it('应该处理特殊字符的错误消息', () => {
      const specialMessage = '错误：<script>alert("xss")</script>'
      
      ErrorHandler.showError(specialMessage)
      
      expect(mockElMessage).toHaveBeenCalledWith({
         message: specialMessage,
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })
  })

  describe('静态方法调用', () => {
    it('应该支持多次静态方法调用', () => {
      ErrorHandler.showError('错误1')
      ErrorHandler.showError('错误2')
      
      expect(mockElMessage).toHaveBeenCalledTimes(2)
      expect(mockElMessage).toHaveBeenNthCalledWith(1, {
         message: '错误1',
         type: 'error',
         duration: 3000,
         showClose: true
       })
       expect(mockElMessage).toHaveBeenNthCalledWith(2, {
         message: '错误2',
         type: 'error',
         duration: 3000,
         showClose: true
       })
    })
  })
})