import logging
import os
import psutil
import pandas as pd
from typing import Dict, Any, Optional

# 导入abupy核心模块
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.BetaBu import AbuPositionBase, AbuAtrPosition, AbuKellyPosition, AbuPtPosition
from abupy.UmpBu.ABuUmpManager import AbuUmpManager, g_enable_user_ump
from abupy.CoreBu import ABuEnv
from abupy.MarketBu.ABuSymbol import code_to_symbol
from abupy.AlphaBu import ABuPickTimeExecute
from abupy.UmpBu import ABuUmpManager as UmpManagerModule # 导入整个模块，并给一个清晰的别名
from abupy.UmpBu.ABuUmpManager import AbuUmpManager      # 继续导入类，以备后用
from fastapi import HTTPException
from abupy.TradeBu.ABuKLManager import AbuKLManager


# 导入项目内部模块
from .umpire_adapter import setup_umpire_for_prediction, teardown_umpire
from ..exceptions import Adapter<PERSON>rror, ParameterError, FactorError, SymbolError, DataNotFoundError
from app.constants.messages import StrategyMessages
from .abupy_caller import call_abupy_backtest
from .result_processor import calculate_performance_metrics
from app.core.config import settings
from ..factors_converter import FactorsConverter
from app.schemas.strategy import Strategy
from app.schemas.market import KlineData
from app.utils.symbol_util import to_abupy_symbol_format, from_abupy_symbol_format, to_tushare_symbol_format

from ..data_adapter import convert_kline_data_to_abu_df
from app.services.market.kline_provider import KlineProvider
# 净化和格式化DataFrame函数


class StrategyExecutor:
    """
    作为执行策略的门面，协调数据准备、abupy调用和结果处理。
    """



    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any],
                         factors_converter: Optional[Any] = None) -> Dict[str, Any]:
        """
        [最终稳定版] 通过完全的数据注入和对abupy的精确补丁来执行策略。
        """
        # ========================= 1. [核心修改] 设置并确保清理全局状态 =========================
    
        # 保存原始的全局状态
        original_fetch_mode = ABuEnv.g_data_fetch_mode
        original_ump_enable = UmpManagerModule.g_enable_user_ump
        original_ml_enable = ABuEnv.g_enable_ml_feature
        
        logging.info(f"开始执行策略回测: {strategy.name} (ID: {strategy.id})")
        try:
            # ========================= 1. 环境与参数准备 =========================
            
           # 在try块的开始，强制设置我们需要的全局状态
            from abupy.CoreBu.ABuEnv import EMarketDataFetchMode
            ABuEnv.g_data_fetch_mode = EMarketDataFetchMode.E_DATA_FETCH_FORCE_LOCAL
            # 即使我们通过umpire_adapter来管理，在这里也强制关闭以防万一
            UmpManagerModule.g_enable_user_ump = False 
            ABuEnv.g_enable_ml_feature = False
            logging.info("已临时强制设置abupy为本地数据模式，并关闭UMP。")

            if factors_converter is None:
                factors_converter = FactorsConverter()
            
            abu_buy_factors = factors_converter.convert_to_abu_factors(strategy.buy_factors)
            if not abu_buy_factors: raise FactorError(StrategyMessages.NO_BUY_FACTORS)
            abu_sell_factors = factors_converter.convert_to_abu_factors(strategy.sell_factors) if strategy.sell_factors else []

            capital_val = market_data.get('capital') or market_data.get('initial_capital')
            if capital_val is None: raise ParameterError(StrategyMessages.MISSING_CAPITAL)
            
            choice_symbols_api = market_data.get("choice_symbols", [])
            start_date_str = market_data.get("start_date")
            end_date_str = market_data.get("end_date")
            benchmark_symbol_api = market_data.get("benchmark_symbol")
            
            if not all([choice_symbols_api, start_date_str, end_date_str, benchmark_symbol_api]):
                raise ParameterError("缺少必要参数: 股票列表, 起始日期, 结束日期, 或基准股票")

            final_choice_symbols = [to_abupy_symbol_format(s) for s in choice_symbols_api]
            final_benchmark_symbol = to_abupy_symbol_format(benchmark_symbol_api)

            # ========================= 2. [核心重构] 完全离线数据注入 =========================

            logging.info("--- [DEBUG] 正在进入完全离线数据注入模式 V17.1 ---")

            # A. 手动预加载所有需要的数据
            from app.services.market.kline_provider import KlineProvider

            # C. 手动预加载所有需要的数据
            all_symbols_to_load = list(set(final_choice_symbols + [final_benchmark_symbol]))
            logging.info(f"--- [DEBUG] 预加载以下所有股票的K线数据: {all_symbols_to_load}")
            
            all_kline_data_df: Dict[str, pd.DataFrame] = {}

            for symbol in all_symbols_to_load:
                # 使用智能数据加载方法：优先本地，不足时自动从tushare补全
                kline_data = KlineProvider.get_kline_data_smart(symbol, start_date=start_date_str, end_date=end_date_str)
                kl_df_clean = convert_kline_data_to_abu_df(kline_data, symbol)
                
                if kl_df_clean is not None and not kl_df_clean.empty:

                    all_kline_data_df[symbol] = kl_df_clean
                    logging.info(f"--- [DEBUG] 成功为 {symbol} 加载并转换了 {len(kl_df_clean)} 条K线数据")
                else:
                    raise DataNotFoundError(f"无法为股票 '{symbol}' 在指定周期内获取到K线数据。")

            # D. 创建一个“数据完备”的Benchmark对象
            benchmark_kl_pd = all_kline_data_df.get(final_benchmark_symbol)
            if benchmark_kl_pd is None:
                 raise DataNotFoundError(f"基准指数 '{final_benchmark_symbol}' 的K线数据加载失败。")
            
            benchmark_kl_pd.name = final_benchmark_symbol
            logging.info(f"--- [DEBUG] 已为benchmark_kl_pd设置.name属性: {benchmark_kl_pd.name}")

            # ******** 在这之后，调用AbuBenchmark之前，插入以下诊断代码 ********

            logging.info("\n" + "="*50)
            logging.info("--- [BENCHMARK_DEBUG] Preparing to initialize AbuBenchmark ---")
            logging.info(f"--- [BENCHMARK_DEBUG] Type of benchmark_kl_pd: {type(benchmark_kl_pd)}")
            logging.info(f"--- [BENCHMARK_DEBUG] Shape of benchmark_kl_pd: {benchmark_kl_pd.shape}")
            logging.info(f"--- [BENCHMARK_DEBUG] Index type of benchmark_kl_pd: {type(benchmark_kl_pd.index)}")
            logging.info(f"--- [BENCHMARK_DEBUG] Columns of benchmark_kl_pd: {benchmark_kl_pd.columns.tolist()}")
            logging.info("--- [BENCHMARK_DEBUG] First 5 rows of benchmark_kl_pd:")
            logging.info("\n" + benchmark_kl_pd.head().to_string())
            logging.info("--- [BENCHMARK_DEBUG] Last 5 rows of benchmark_kl_pd:")
            logging.info("\n" + benchmark_kl_pd.tail().to_string())
            logging.info("--- [BENCHMARK_DEBUG] Info of benchmark_kl_pd:")
            # 使用StringIO来捕获info()的输出，避免格式混乱
            from io import StringIO
            buffer = StringIO()
            benchmark_kl_pd.info(buf=buffer)
            logging.info("\n" + buffer.getvalue())
            logging.info("="*50 + "\n")

            # *************************调试结束*******************************************


            benchmark = AbuBenchmark(benchmark_kl_pd=benchmark_kl_pd)
            benchmark.kl_pd.name = final_benchmark_symbol
            logging.info(f"--- [DEBUG] 成功创建了已注入数据的基准对象")

            # D. 创建Capital对象
            capital = AbuCapital(capital_val, benchmark)
            kl_pd_manager = AbuKLManager(benchmark, capital)
            for symbol, kl_df in all_kline_data_df.items():
                kl_pd_manager.pick_kl_pd_dict[symbol] = kl_df
            logging.info(f"--- [DEBUG] 成功创建并向KLManager注入了 {len(all_kline_data_df)} 组K线数据")

            # ========================= 3. 调用核心择时函数 =========================
            
            # 事前验证日志 - 打印最终准备的因子信息
            logging.info("--- [FACTOR_DEBUG] Final factors prepared for backtest engine:")
            logging.info(f"--- [FACTOR_DEBUG] Number of buy factors: {len(abu_buy_factors)}")
            logging.info(f"--- [FACTOR_DEBUG] Buy factors content: {abu_buy_factors}")
            logging.info(f"--- [FACTOR_DEBUG] Number of sell factors: {len(abu_sell_factors)}")
            logging.info(f"--- [FACTOR_DEBUG] Sell factors content: {abu_sell_factors}")
            
            # ========================= [防呆保护] 数据最终校验 =========================
            logging.info("--- [DATA_VALIDATION] 开始进行数据最终校验 ---")
            
            # 校验基准数据
            if not benchmark_kl_pd.index.is_monotonic_increasing:
                logging.warning(f"--- [DATA_VALIDATION] 基准数据 {final_benchmark_symbol} 索引非升序，正在修复...")
                benchmark_kl_pd.sort_index(inplace=True)
                logging.info(f"--- [DATA_VALIDATION] 基准数据 {final_benchmark_symbol} 已修复为升序")
            
            # 校验所有股票数据
            for symbol, kl_df in all_kline_data_df.items():
                if not kl_df.index.is_monotonic_increasing:
                    logging.warning(f"--- [DATA_VALIDATION] 股票数据 {symbol} 索引非升序，正在修复...")
                    kl_df.sort_index(inplace=True)
                    logging.info(f"--- [DATA_VALIDATION] 股票数据 {symbol} 已修复为升序")
                    # 更新KLManager中的数据
                    kl_pd_manager.pick_kl_pd_dict[symbol] = kl_df
                
                # 输出数据范围用于调试
                logging.info(f"--- [DATA_VALIDATION] {symbol} 数据范围: {kl_df.index[0]} 到 {kl_df.index[-1]}, 共{len(kl_df)}条")
            
            logging.info("--- [DATA_VALIDATION] 数据最终校验完成 ---")
            
            # ========================= [日期修复] 强制修正所有 kl_pd 的 date 列 =========================
            def force_fix_date_columns(df: pd.DataFrame) -> pd.DataFrame:
                df = df.copy()
                # 1) 索引必须是 DatetimeIndex（你的索引现在是对的）
                if not isinstance(df.index, pd.DatetimeIndex):
                    # 兜底：从已有 date 列重建索引
                    if 'date' in df.columns:
                        try:
                            df.index = pd.to_datetime(df['date'].astype(str), format='%Y%m%d', errors='raise')
                        except Exception:
                            df.index = pd.to_datetime(df['date'], errors='raise')
                    df.index.name = 'datetime'
                df.sort_index(inplace=True)

                # 2) 关键：把 date 列覆盖为"正确的 Timestamp"，彻底抹掉 1970 纳秒时间
                df['date'] = df.index.tz_localize(None)
                df['date'] = pd.to_datetime(df['date'])  # 确保 dtype 为 datetime64[ns]

                # 3) 同时提供 int 版日期，避免别处还需要整型
                df['date_int'] = df.index.strftime('%Y%m%d').astype(int)

                return df

            # 修正所有股票和基准
            benchmark_kl_pd = force_fix_date_columns(benchmark_kl_pd)
            for sym in list(all_kline_data_df.keys()):
                all_kline_data_df[sym] = force_fix_date_columns(all_kline_data_df[sym])

            # 更新 KLManager 中的数据（你已有注入，这里再覆盖一次）
            kl_pd_manager.pick_kl_pd_dict.update(all_kline_data_df)

            # 诊断一下 dtype（可临时打印为 DEBUG）
            for sym, kl in all_kline_data_df.items():
                logging.info(f"[DATE_FIX] {sym} date dtype={kl['date'].dtype}, head date={kl['date'].head(1).iloc[0]}")
            logging.info(f"[DATE_FIX] benchmark date dtype={benchmark_kl_pd['date'].dtype}, head date={benchmark_kl_pd['date'].head(1).iloc[0]}")
            
            # ========================= [信号验证] 3日突破信号统计 =========================
            logging.info("--- [SIGNAL_VALIDATION] 开始验证3日突破信号 ---")
            
            for symbol, kl_df in all_kline_data_df.items():
                try:
                    if 'high' in kl_df.columns and 'close' in kl_df.columns and len(kl_df) >= 4:
                        # 计算3日最高价（前移1天，避免未来函数）
                        hh3 = kl_df['high'].rolling(window=3, min_periods=3).max().shift(1)
                        # 计算突破信号：当日收盘价 > 前3日最高价
                        brk = kl_df['close'] > hh3
                        signal_count = int(brk.sum())
                        
                        logging.info(f"--- [SIGNAL_VALIDATION] {symbol}: 3日突破信号数={signal_count}")
                        
                        if signal_count > 0:
                            first_signal_date = brk[brk].index.min()
                            last_signal_date = brk[brk].index.max()
                            logging.info(f"--- [SIGNAL_VALIDATION] {symbol}: 首次信号={first_signal_date}, 最后信号={last_signal_date}")
                            
                            # 显示前3个信号的详细信息
                            signal_dates = brk[brk].index[:3]
                            for date in signal_dates:
                                close_price = kl_df.loc[date, 'close']
                                hh3_value = hh3.loc[date]
                                if pd.notna(hh3_value) and hh3_value > 0:
                                    breakthrough_pct = ((close_price/hh3_value-1)*100)
                                    logging.info(f"--- [SIGNAL_VALIDATION] {symbol} {date.date()}: 收盘={close_price:.3f}, 前3日最高={hh3_value:.3f}, 突破幅度={breakthrough_pct:.2f}%")
                        else:
                            logging.warning(f"--- [SIGNAL_VALIDATION] {symbol}: ❌ 未发现3日突破信号！")
                            # 进一步诊断
                            zero_close = (kl_df['close'] == 0).sum()
                            zero_high = (kl_df['high'] == 0).sum()
                            if zero_close > 0 or zero_high > 0:
                                logging.error(f"--- [SIGNAL_VALIDATION] {symbol}: 发现零值数据 - close零值={zero_close}, high零值={zero_high}")
                            
                            close_range = f"[{kl_df['close'].min():.3f}, {kl_df['close'].max():.3f}]"
                            high_range = f"[{kl_df['high'].min():.3f}, {kl_df['high'].max():.3f}]"
                            logging.info(f"--- [SIGNAL_VALIDATION] {symbol}: close范围={close_range}, high范围={high_range}")
                    else:
                        logging.warning(f"--- [SIGNAL_VALIDATION] {symbol}: 数据不足或缺少必要列")
                        
                except Exception as e:
                    logging.error(f"--- [SIGNAL_VALIDATION] {symbol}: 信号计算出错 - {e}")
            
            logging.info("--- [SIGNAL_VALIDATION] 3日突破信号验证完成 ---")
            # ==================================验证3日突破信号结束========================
            
            # ========================= [资金验证] 检查资金设置 =========================
            logging.info("--- [CAPITAL_VALIDATION] 开始验证资金设置 ---")
            logging.info(f"--- [CAPITAL_VALIDATION] 初始资金: {capital_val:,.2f}")
            logging.info(f"--- [CAPITAL_VALIDATION] 基准标的: {final_benchmark_symbol}")
            logging.info(f"--- [CAPITAL_VALIDATION] 目标标的数量: {len(final_choice_symbols)}")
            logging.info(f"--- [CAPITAL_VALIDATION] 目标标的: {final_choice_symbols}")
            
            # 检查每个标的的最小交易单位（假设为100股）
            min_trade_unit = 100
            for symbol in final_choice_symbols:
                if symbol in all_kline_data_df:
                    kl_df = all_kline_data_df[symbol]
                    if not kl_df.empty and 'close' in kl_df.columns:
                        latest_price = kl_df['close'].iloc[-1]
                        min_trade_value = latest_price * min_trade_unit
                        logging.info(f"--- [CAPITAL_VALIDATION] {symbol}: 最新价格={latest_price:.3f}, 最小交易金额={min_trade_value:.2f}")
                        
                        if min_trade_value > capital_val * 0.1:  # 如果单笔最小交易超过总资金的10%
                            logging.warning(f"--- [CAPITAL_VALIDATION] {symbol}: ⚠️ 最小交易金额过大，可能影响下单")
            
            logging.info("--- [CAPITAL_VALIDATION] 资金设置验证完成 ---")
            # ============================================验证资金设置结束=============================
            # =======================================验证择时函数开始==================================
            logging.info("--- [DEBUG] 即将调用核心择时函数 do_symbols_with_same_factors ---")
            # 使用abupy_caller.py中的函数，以便获得详细的调试日志
            orders_pd, action_pd, _ = call_abupy_backtest(
                abupy_choice_symbols=final_choice_symbols,
                benchmark_obj=benchmark,
                capital_obj=capital,
                abu_buy_factors=abu_buy_factors,
                abu_sell_factors=abu_sell_factors,
                all_raw_kline_data=all_kline_data_df
            )
            abu_result_tuple = (orders_pd, action_pd, _)
            logging.info("--- [DEBUG] 核心择时函数调用完成 ---")
            # ==============================验证择时开始结束==============================
            # ========================= 4. 结果处理 =========================
            
            if abu_result_tuple is None:
                raise AdapterError("abupy核心引擎返回空结果，可能无任何交易产生或发生内部错误。")

            orders_pd, action_pd, _ = abu_result_tuple

            if orders_pd is None or (hasattr(orders_pd, 'empty') and orders_pd.empty):
                raise AdapterError("回测执行完毕，但未产生任何有效交易。请检查您的策略因子、股票池和回测周期设置。")

            final_choice_symbols_tushare = [to_tushare_symbol_format(s) for s in choice_symbols_api]
            
            return cls._process_and_format_results(
                orders_pd, action_pd, capital, benchmark,
                final_choice_symbols_tushare, capital_val, start_date_str, end_date_str
            )

        except (DataNotFoundError, FactorError, ParameterError, SymbolError, AdapterError) as e:
            logging.error(f"策略执行失败: {e}", exc_info=False)
            raise HTTPException(status_code=422, detail=str(e))
        except Exception as e:
            logging.error(f"策略执行过程中发生未知错误: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"{StrategyMessages.EXECUTION_FAILED}: {str(e)}")
        finally:
            # 无论成功还是失败，都确保将全局状态恢复到调用之前的样子
            ABuEnv.g_data_fetch_mode = original_fetch_mode
            UmpManagerModule.g_enable_user_ump = original_ump_enable
            ABuEnv.g_enable_ml_feature = original_ml_enable
            logging.info("已恢复abupy的原始全局状态。")
            teardown_umpire()

    @staticmethod
    def _configure_position_management(strategy: Strategy):
        AbuPositionBase.g_default_pos_class = None
        if hasattr(strategy, 'position_strategy') and strategy.position_strategy:
            position_config = strategy.position_strategy.dict()
            class_name = position_config.get('class_name')
            parameters = position_config.get('parameters', {})
            if class_name:
                position_class_map = {
                    "AbuAtrPosition": AbuAtrPosition,
                    "AbuKellyPosition": AbuKellyPosition,
                    "AbuPtPosition": AbuPtPosition
                }
                target_class = position_class_map.get(class_name)
                if target_class:
                    AbuPositionBase.g_default_pos_class = target_class
                    if parameters:
                        for key, value in parameters.items():
                            if hasattr(target_class, key):
                                setattr(target_class, key, value)
                            else:
                                logging.warning(f"仓位管理类{class_name} 没有参数 {key}")
                    logging.info(f"已应用全局仓位管理策略: {class_name} with params {parameters}")
                else:
                    logging.warning(f"未找到名为{class_name} 的仓位管理类")

    @staticmethod
    def _process_and_format_results(orders_pd, action_pd, capital, benchmark, choice_symbols_tushare, capital_val, start_date_str, end_date_str):
        logging.info(f"Entering _process_and_format_results. orders_pd is None: {orders_pd is None}, action_pd is None: {action_pd is None}")
        if orders_pd is not None and not orders_pd.empty:
            # 使用StringIO捕获info()的输出，避免直接打印到控制台
            from io import StringIO
            buffer = StringIO()
            orders_pd.info(buf=buffer)
            logging.info(f"orders_pd info:\n{buffer.getvalue()}")
        if action_pd is not None and not action_pd.empty:
            from io import StringIO
            buffer = StringIO()
            action_pd.info(buf=buffer)
            logging.info(f"action_pd info:\n{buffer.getvalue()}")

        processed_results, execution_summary = [], {}
        initial_capital = capital.read_cash

        if orders_pd is not None and not orders_pd.empty:
            performance_metrics = calculate_performance_metrics(orders_pd, capital, benchmark).model_dump()
            # 防御性地获取final_capital
            # 修正final_capital的提取方�?
            if action_pd is not None and not action_pd.empty and 'capital_balance' in action_pd.columns and not action_pd['capital_balance'].dropna().empty:
                final_capital = action_pd['capital_balance'].dropna().iloc[-1]
            else:
                logging.warning("回测结果'action_pd'中未找到'capital_balance'列或该列为空，最终资金将使用初始资金。")
                final_capital = initial_capital

            execution_summary = {
                "total_symbols": len(choice_symbols_tushare),
                "symbols_with_trades": orders_pd['symbol'].nunique(),
                "total_trades": len(orders_pd),
                "initial_capital": float(initial_capital),
                "final_capital": float(final_capital),
                **performance_metrics
            }

            orders_pd_dict = orders_pd.to_dict('records')
            for order in orders_pd_dict:
                order['symbol'] = from_abupy_symbol_format(order['symbol'], choice_symbols_tushare)
            
            for symbol in choice_symbols_tushare:
                symbol_orders = [o for o in orders_pd_dict if o['symbol'] == symbol]
                if symbol_orders:
                    processed_results.append({
                        "symbol": symbol,
                        "orders_count": len(symbol_orders),
                        "message": "交易完成",
                        "orders": symbol_orders
                    })
        else:
            execution_summary = {
                "total_symbols": len(choice_symbols_tushare),
                "symbols_with_trades": 0,
                "total_trades": 0,
                "initial_capital": float(initial_capital),
                "final_capital": float(initial_capital)
            }

        symbols_with_trades = {res['symbol'] for res in processed_results}
        for symbol in choice_symbols_tushare:
            if symbol not in symbols_with_trades:
                processed_results.append({
                    "symbol": symbol,
                    "orders_count": 0,
                    "message": "无交易",
                    "orders": []
                })

        return {
            "status": "success",
            "message": StrategyMessages.EXECUTION_SUCCESS,
            "results": sorted(processed_results, key=lambda x: x['symbol']),
            "execution_summary": execution_summary,
            "parameters_used": {
                "initial_capital": float(capital_val),
                "symbols": choice_symbols_tushare,
                "start_date": start_date_str,
                "end_date": end_date_str
            }
        }
