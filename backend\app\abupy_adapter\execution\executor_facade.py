import logging
import pandas as pd
from typing import Dict, Any, Optional

# 导入abupy核心模块
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.BetaBu import AbuPositionBase, AbuAtrPosition, AbuKellyPosition, AbuPtPosition

# 导入项目内部模块
from .umpire_adapter import create_umpire_managers
from ..exceptions import AdapterError, ParameterError, FactorError, SymbolError, DataNotFoundError
from .data_preprocessor import kline_data_to_dataframe
from .abupy_caller import call_abupy_backtest
from .result_processor import calculate_performance_metrics
from ..factors_converter import FactorsConverter
from app.schemas.strategy import Strategy
from app.services.market_service import MarketService
from app.utils.symbol_util import to_abupy_symbol_format, from_abupy_symbol_format, to_tushare_symbol_format

class StrategyExecutor:
    """
    作为执行策略的门面，协调数据准备、abupy调用和结果处理。
    """



    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any],
                         factors_converter: Optional[Any] = None) -> Dict[str, Any]:
        """
        [最终稳定版] 通过完全的数据注入和对abupy的精确补丁来执行策略。
        """
        market_service = MarketService()
        try:
            if factors_converter is None:
                factors_converter = FactorsConverter

            # 1. 参数校验和转换
            abu_buy_factors = factors_converter.convert_to_abu_factors(strategy.buy_factors)
            if not abu_buy_factors: raise FactorError("策略必须包含至少一个买入因子")
            abu_sell_factors = factors_converter.convert_to_abu_factors(strategy.sell_factors) if strategy.sell_factors else []

            capital_val = market_data.get('initial_capital')
            if capital_val is None:
                capital_val = strategy.parameters.get('initial_capital')

            if capital_val is None: raise ParameterError("未提供初始资金 (initial_capital)")

            choice_symbols_api = market_data.get("choice_symbols", [])
            start_date_str = market_data.get("start_date")
            end_date_str = market_data.get("end_date")
            benchmark_symbol_api = market_data.get("benchmark_symbol")
            
            if not all([choice_symbols_api, start_date_str, end_date_str, benchmark_symbol_api]):
                raise ParameterError("缺少必要参数: 股票池, 起始日期, 结束日期, 或基准")

            choice_symbols_tushare = [to_tushare_symbol_format(s) for s in choice_symbols_api]
            benchmark_symbol_tushare = to_tushare_symbol_format(benchmark_symbol_api)
            start_date = pd.to_datetime(start_date_str).strftime('%Y%m%d')
            end_date = pd.to_datetime(end_date_str).strftime('%Y%m%d')

            # 2. 数据加载
            all_raw_kline_data = {}
            loaded_data = {s: kline_data_to_dataframe(
                market_service.get_kline_data(
                    symbol=s, 
                    start_date=start_date, 
                    end_date=end_date, 
                    data_source=market_data.get("data_source")
                )
            ) for s in set([benchmark_symbol_tushare] + choice_symbols_tushare)}
            
            for symbol_tushare, df in loaded_data.items():
                if df is not None and not df.empty:
                    all_raw_kline_data[symbol_tushare] = df

            if benchmark_symbol_tushare not in all_raw_kline_data:
                raise DataNotFoundError(f"基准 '{benchmark_symbol_api}' 的K线数据未能加载。")

            # 3. 初始化abupy对象
            # 从我们已经加载好的数据中，找到基准的DataFrame
            benchmark_df = all_raw_kline_data.get(benchmark_symbol_tushare)
            if benchmark_df is None or benchmark_df.empty:
                raise DataNotFoundError(f"基准 '{benchmark_symbol_api}' 的K线数据在已加载数据中未找到或为空。")

            # 直接将DataFrame注入到AbuBenchmark中
            benchmark_obj = AbuBenchmark(benchmark_kl_pd=benchmark_df)
            capital_obj = AbuCapital(init_cash=capital_val, benchmark=benchmark_obj)

            # 4. 配置仓位和裁判系统
            cls._configure_position_management(strategy)
            umpire_instances = cls._configure_umpire_system(strategy, market_data)

            # 5. 调用核心回测引擎
            abupy_choice_symbols = [to_abupy_symbol_format(s) for s in choice_symbols_tushare]
            orders_pd, action_pd, _ = call_abupy_backtest(
                abupy_choice_symbols, benchmark_obj, capital_obj,
                abu_buy_factors, abu_sell_factors, all_raw_kline_data, umpire_instances
            )

            # 6. 处理和返回结果
            return cls._process_and_format_results(
                orders_pd, action_pd, capital_obj, benchmark_obj,
                choice_symbols_tushare, capital_val, start_date_str, end_date_str
            )

        except (DataNotFoundError, FactorError, ParameterError, SymbolError, AdapterError) as e:
            logging.error(f"策略执行失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logging.error(f"策略执行过程中发生未知错误: {e}", exc_info=True)
            raise AdapterError(message=f"策略执行失败: {str(e)}")

    @staticmethod
    def _configure_position_management(strategy: Strategy):
        AbuPositionBase.g_default_pos_class = None
        if hasattr(strategy, 'position_strategy') and strategy.position_strategy:
            position_config = strategy.position_strategy.dict()
            class_name = position_config.get('class_name')
            parameters = position_config.get('parameters', {})
            if class_name:
                position_class_map = {
                    "AbuAtrPosition": AbuAtrPosition,
                    "AbuKellyPosition": AbuKellyPosition,
                    "AbuPtPosition": AbuPtPosition
                }
                target_class = position_class_map.get(class_name)
                if target_class:
                    AbuPositionBase.g_default_pos_class = target_class
                    if parameters:
                        for key, value in parameters.items():
                            if hasattr(target_class, key):
                                setattr(target_class, key, value)
                            else:
                                logging.warning(f"仓位管理类 {class_name} 没有参数 {key}")
                    logging.info(f"已应用全局仓位管理策略: {class_name} with params {parameters}")
                else:
                    logging.warning(f"未找到名为 {class_name} 的仓位管理类")

    @staticmethod
    def _configure_umpire_system(strategy: Strategy, market_data: Dict[str, Any]):
        if hasattr(strategy, 'umpire_rules') and strategy.umpire_rules:
            try:
                market_name = market_data.get('market', 'cn')
                return create_umpire_managers(strategy.umpire_rules, market_name=market_name)
            except ValueError as e:
                raise AdapterError(f"裁判系统配置失败: {e}")
        return []

    @staticmethod
    def _process_and_format_results(orders_pd, action_pd, capital_obj, benchmark_obj, choice_symbols_tushare, capital_val, start_date_str, end_date_str):
        logging.info(f"Entering _process_and_format_results. orders_pd is None: {orders_pd is None}, action_pd is None: {action_pd is None}")
        if orders_pd is not None and not orders_pd.empty:
            # 使用StringIO捕获info()的输出，避免直接打印到控制台
            from io import StringIO
            buffer = StringIO()
            orders_pd.info(buf=buffer)
            logging.info(f"orders_pd info:\n{buffer.getvalue()}")
        if action_pd is not None and not action_pd.empty:
            from io import StringIO
            buffer = StringIO()
            action_pd.info(buf=buffer)
            logging.info(f"action_pd info:\n{buffer.getvalue()}")

        processed_results, execution_summary = [], {}
        initial_capital = capital_obj.read_cash

        if orders_pd is not None and not orders_pd.empty:
            performance_metrics = calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj).model_dump()
            final_capital = action_pd['capital_balance'].iloc[-1] if action_pd is not None and not action_pd.empty else initial_capital

            execution_summary = {
                "total_symbols": len(choice_symbols_tushare),
                "symbols_with_trades": orders_pd['symbol'].nunique(),
                "total_trades": len(orders_pd),
                "initial_capital": float(initial_capital),
                "final_capital": float(final_capital),
                **performance_metrics
            }

            orders_pd_dict = orders_pd.to_dict('records')
            for order in orders_pd_dict:
                order['symbol'] = from_abupy_symbol_format(order['symbol'], choice_symbols_tushare)
            
            for symbol in choice_symbols_tushare:
                symbol_orders = [o for o in orders_pd_dict if o['symbol'] == symbol]
                if symbol_orders:
                    processed_results.append({
                        "symbol": symbol,
                        "orders_count": len(symbol_orders),
                        "message": "交易完成",
                        "orders": symbol_orders
                    })
        else:
            execution_summary = {
                "total_symbols": len(choice_symbols_tushare),
                "symbols_with_trades": 0,
                "total_trades": 0,
                "initial_capital": float(initial_capital),
                "final_capital": float(initial_capital)
            }

        symbols_with_trades = {res['symbol'] for res in processed_results}
        for symbol in choice_symbols_tushare:
            if symbol not in symbols_with_trades:
                processed_results.append({
                    "symbol": symbol,
                    "orders_count": 0,
                    "message": "无交易",
                    "orders": []
                })

        return {
            "status": "success",
            "message": "策略执行成功完成。",
            "results": sorted(processed_results, key=lambda x: x['symbol']),
            "execution_summary": execution_summary,
            "parameters_used": {
                "initial_capital": float(capital_val),
                "symbols": choice_symbols_tushare,
                "start_date": start_date_str,
                "end_date": end_date_str
            }
        }