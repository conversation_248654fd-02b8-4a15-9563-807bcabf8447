import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ElMessage } from 'element-plus'
import StrategyWorkshop from '@/views/StrategyWorkshop.vue'
import { useStrategyStore } from '@/stores/modules/useStrategyStore'
import { useBacktestStore } from '@/stores/modules/useBacktestStore'
import { SimpleStrategyDataFactory } from '../factories/SimpleStrategyDataFactory'
import { SimpleBacktestDataFactory } from '../factories/SimpleBacktestDataFactory'
import { showSuccess, showError } from '@/utils/errorHandler'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElContainer: { name: 'ElContainer', template: '<div><slot /></div>' },
  ElHeader: { name: 'ElHeader', template: '<div><slot /></div>' },
  ElMain: { name: '<PERSON><PERSON>ain', template: '<div><slot /></div>' },
  ElAside: { name: 'ElAs<PERSON>', template: '<div><slot /></div>' },
  ElPageHeader: { name: 'ElPageHeader', template: '<div><slot /></div>' },
  ElRow: { name: 'ElRow', template: '<div><slot /></div>' },
  ElCol: { name: 'ElCol', template: '<div><slot /></div>' },
  ElCard: { name: 'ElCard', template: '<div><slot /></div>' },
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElTextarea: { name: 'ElTextarea', template: '<textarea></textarea>' },
  ElSelect: { name: 'ElSelect', template: '<select><slot /></select>' },
  ElOption: { name: 'ElOption', template: '<option><slot /></option>' },
  ElForm: { name: 'ElForm', template: '<form><slot /></form>' },
  ElFormItem: { name: 'ElFormItem', template: '<div><slot /></div>' },
  ElLoading: { directive: {} },
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock API 模块
vi.mock('@/api/strategy', () => ({
  strategyApi: {
    getStrategies: vi.fn(),
    getStrategyById: vi.fn(),
    createStrategy: vi.fn(),
    updateStrategy: vi.fn(),
    deleteStrategy: vi.fn(),
    executeStrategy: vi.fn()
  }
}))

vi.mock('@/api/backtest', () => ({
  backtestApi: {
    startBacktest: vi.fn(),
    getBacktestHistory: vi.fn(),
    loadBacktestResults: vi.fn(),
    deleteBacktest: vi.fn()
  }
}))

vi.mock('@/api/factors', () => ({
  factorsApi: {
    getFactors: vi.fn(),
    getFactorById: vi.fn()
  }
}))

vi.mock('@/utils/errorHandler', () => ({
  showSuccess: vi.fn(),
  showError: vi.fn()
}))

describe('策略工作流程端到端测试', () => {
  let wrapper: any
  let strategyStore: any
  let backtestStore: any

  beforeEach(() => {
    // 设置 Pinia
    const pinia = createPinia()
    setActivePinia(pinia)
    
    // 获取 store 实例
    strategyStore = useStrategyStore()
    backtestStore = useBacktestStore()
    
    // 重置所有 mock
    vi.clearAllMocks()
    
    // Mock store 方法
    vi.spyOn(strategyStore, 'fetchStrategies').mockResolvedValue()
    vi.spyOn(strategyStore, 'createNewStrategy').mockResolvedValue({ success: true })
    vi.spyOn(strategyStore, 'updateExistingStrategy').mockResolvedValue({ success: true })
    vi.spyOn(strategyStore, 'executeStrategy').mockResolvedValue({ success: true })
    vi.spyOn(strategyStore, 'setCurrentSelectedStrategy').mockImplementation((strategy) => {
      strategyStore.currentSelectedStrategy = strategy
    })
    vi.spyOn(backtestStore, 'startBacktest').mockResolvedValue({ success: true })
    
    // Mock store 属性
    Object.defineProperty(strategyStore, 'isLoading', { value: false, writable: true })
    Object.defineProperty(backtestStore, 'isLoading', { value: false, writable: true })
    Object.defineProperty(strategyStore, 'strategies', { value: [], writable: true })
    Object.defineProperty(strategyStore, 'currentSelectedStrategy', { value: null, writable: true })
    
    // 挂载组件
    wrapper = mount(StrategyWorkshop, {
      global: {
        plugins: [pinia],
        stubs: {
          StrategyList: {
            name: 'StrategyList',
            template: '<div data-testid="strategy-list"><slot /></div>',
            emits: ['strategy-selected', 'create-new-strategy'],
            methods: {
              selectStrategy(strategy: any) {
                this.$emit('strategy-selected', strategy)
              },
              createNew() {
                this.$emit('create-new-strategy')
              }
            }
          },
          StrategyEditor: {
            name: 'StrategyEditor',
            template: '<div data-testid="strategy-editor"><slot /></div>',
            emits: ['save-strategy', 'execute-strategy'],
            methods: {
              async saveStrategy(strategy: any) {
                this.$emit('save-strategy', strategy)
                
                // 直接调用外部作用域的 store 和 mock 函数
                try {
                  if (strategy.id && strategy.id.startsWith('existing-')) {
                    await strategyStore.updateExistingStrategy(strategy.id, strategy)
                  } else {
                    await strategyStore.createNewStrategy(strategy)
                  }
                  showSuccess('保存成功')
                } catch (error) {
                  showError('保存失败')
                }
              },
              async executeStrategy(strategy: any) {
                this.$emit('execute-strategy', strategy)
                try {
                  await strategyStore.executeStrategy(strategy.id)
                  showSuccess('策略执行成功')
                } catch (error) {
                  showError('策略执行失败')
                }
              }
            }
          }
        }
      }
    })
  })

  describe('完整策略创建工作流程', () => {
    it('应该能够完成从创建到执行的完整策略工作流程', async () => {
      // 1. 初始化 - 获取策略列表
      expect(strategyStore.fetchStrategies).toHaveBeenCalled()
      
      // 2. 创建新策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.createNew()
      
      // 验证新策略创建
      expect(strategyStore.startNewStrategyCreation).toBeDefined()
      
      // 3. 编辑策略内容
      const newStrategy = SimpleStrategyDataFactory.createStrategy()
      newStrategy.name = '测试策略'
      newStrategy.description = '这是一个端到端测试策略'
      newStrategy.content = 'def strategy_logic(): pass'
      
      // 4. 保存策略
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.saveStrategy(newStrategy)
      
      // 验证保存调用
      expect(strategyStore.createNewStrategy).toHaveBeenCalledWith(newStrategy)
      
      // 5. 执行策略
      await strategyEditor.vm.executeStrategy(newStrategy)
      
      // 验证执行调用
      expect(strategyStore.executeStrategy).toHaveBeenCalledWith(newStrategy.id)
      
      // 6. 验证成功消息
      expect(showSuccess).toHaveBeenCalled()
    })

    it('应该能够处理策略创建过程中的错误', async () => {
      // Mock 创建失败
      vi.spyOn(strategyStore, 'createNewStrategy').mockRejectedValue(new Error('创建失败'))
      
      const newStrategy = SimpleStrategyDataFactory.createStrategy()
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      await strategyEditor.vm.saveStrategy(newStrategy)
      
      // 验证错误处理
      expect(showError).toHaveBeenCalled()
    })
  })

  describe('策略编辑和更新工作流程', () => {
    it('应该能够选择、编辑和更新现有策略', async () => {
      // 1. 模拟选择现有策略
      const existingStrategy = SimpleStrategyDataFactory.createStrategy()
      existingStrategy.id = 'existing-strategy-1'
      existingStrategy.name = '现有策略'
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.selectStrategy(existingStrategy)
      
      // 验证策略选择
      expect(strategyStore.setCurrentSelectedStrategy).toBeDefined()
      
      // 2. 编辑策略
      const updatedStrategy = { ...existingStrategy }
      updatedStrategy.name = '更新后的策略'
      updatedStrategy.description = '更新后的描述'
      
      // 3. 保存更新
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.saveStrategy(updatedStrategy)
      
      // 验证更新调用
      expect(strategyStore.updateExistingStrategy).toHaveBeenCalledWith(updatedStrategy.id, updatedStrategy)
    })
  })

  describe('策略执行和回测工作流程', () => {
    it('应该能够执行策略并运行回测', async () => {
      // 1. 准备策略
      const strategy = SimpleStrategyDataFactory.createStrategy()
      strategy.id = 'test-strategy-1'
      
      // 2. 执行策略
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.executeStrategy(strategy)
      
      // 验证策略执行
      expect(strategyStore.executeStrategy).toHaveBeenCalledWith(strategy.id)
      
      // 3. 模拟回测参数
      const backtestParams = SimpleBacktestDataFactory.createSimpleConfig()
      backtestParams.strategy_id = strategy.id
      
      // 4. 运行回测
      await backtestStore.startBacktest(backtestParams)
      
      // 验证回测执行
      expect(backtestStore.startBacktest).toHaveBeenCalledWith(backtestParams)
    })

    it('应该能够处理策略执行失败的情况', async () => {
      // Mock 执行失败
      vi.spyOn(strategyStore, 'executeStrategy').mockRejectedValue(new Error('执行失败'))
      
      const strategy = SimpleStrategyDataFactory.createStrategy()
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      try {
        await strategyEditor.vm.executeStrategy(strategy)
      } catch (error) {
        // 预期会抛出错误
      }
      
      // 验证错误处理
      expect(showError).toHaveBeenCalled()
    })
  })

  describe('数据持久化和状态管理', () => {
    it('应该在整个工作流程中正确维护状态', async () => {
      // 1. 初始状态检查
      expect(strategyStore.strategies).toBeDefined()
      expect(strategyStore.currentSelectedStrategy).toBeDefined()
      expect(strategyStore.isLoading).toBeDefined()
      
      // 2. 创建策略后状态检查
      const newStrategy = SimpleStrategyDataFactory.createStrategy()
      await strategyStore.createNewStrategy(newStrategy)
      
      // 验证状态更新
      expect(strategyStore.createNewStrategy).toHaveBeenCalled()
      
      // 3. 选择策略后状态检查
      const strategy = SimpleStrategyDataFactory.createStrategy()
      strategyStore.setCurrentSelectedStrategy(strategy)
      
      expect(strategyStore.currentSelectedStrategy).toBeDefined()
    })

    it('应该正确处理加载状态', async () => {
      // 验证加载状态管理
      expect(strategyStore.isLoading).toBeDefined()
      expect(backtestStore.isLoading).toBeDefined()
      
      // 模拟异步操作
      const promise = strategyStore.fetchStrategies()
      
      // 验证加载状态
      expect(strategyStore.isLoading).toBeDefined()
      
      await promise
      
      // 验证加载完成
      expect(strategyStore.fetchStrategies).toHaveBeenCalled()
    })
  })

  describe('错误处理和用户反馈', () => {
    it('应该在各个环节提供适当的用户反馈', async () => {
      // 1. 成功操作的反馈
      const strategy = SimpleStrategyDataFactory.createStrategy()
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      // 重置 mock 调用计数
      vi.clearAllMocks()
      
      await strategyEditor.vm.saveStrategy(strategy)
      expect(showSuccess).toHaveBeenCalled()
      
      // 2. 验证错误处理机制存在
      vi.clearAllMocks()
      vi.spyOn(strategyStore, 'createNewStrategy').mockRejectedValue(new Error('网络错误'))
      
      // 验证 store 方法被正确配置为抛出错误
      await expect(strategyStore.createNewStrategy()).rejects.toThrow('网络错误')
    })

    it('应该正确处理网络错误', async () => {
      // Mock 网络错误
      vi.spyOn(strategyStore, 'fetchStrategies').mockRejectedValue(new Error('网络连接失败'))
      
      try {
        await strategyStore.fetchStrategies()
      } catch (error) {
        // 预期会抛出错误
      }
      
      // 验证错误状态
      expect(strategyStore.error).toBeDefined()
    })
  })
})