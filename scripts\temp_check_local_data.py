import sys
import os
import traceback

print("--- 环境深度侦察报告 ---")

# 1. 打印当前工作目录
current_working_dir = os.getcwd()
print(f"当前工作目录 (os.getcwd()):\n{current_working_dir}\n")

# 2. 打印完整的Python搜索路径
print("Python 解释器搜索路径 (sys.path):")
for path in sys.path:
    print(f"- {path}")
print("------------------------\n")

# 3. 诊断并尝试修复路径问题
# 我们的项目根目录应该是当前工作目录
project_root = current_working_dir

if project_root not in sys.path:
    print(f"诊断结果：项目根目录 '{project_root}' 不在 sys.path 中。")
    print("战术修正：正在手动将项目根目录添加到搜索路径的最前端...\n")
    sys.path.insert(0, project_root)
else:
    print("诊断结果：项目根目录已在 sys.path 中，路径配置看起来是正确的。\n")

# 4. 在诊断和修复后，再次尝试执行核心任务
try:
    print("正在尝试导入 'backend.app.config.settings'...")
    from backend.app.config import settings
    print("[成功] 'settings' 模块导入成功！")

    print(f"\n正在从本地数据目录加载: {settings.ABUPY_DATA_DIR}")
    from backend.app.services.market_service import MarketService

    def check_local_data_loading():
        # 重要：请将 'us.TSLA' 替换为您本地确实存在的一个股票代码
        symbol_to_check = '000300.SH'
        start_date = '20210101'
        end_date = '20210131'   
        print(f"\n正在尝试为符号 '{symbol_to_check}' 加载本地数据...")
        try:
            # 1. 根据蓝图，建造一个实际的服务站实例
            market_service = MarketService()
            
            # 2. 命令这个建好的服务站去执行任务
            kline_df = market_service.get_kline_data(
                symbol=symbol_to_check,
                start_date=start_date,
                end_date=end_date,
                data_source='local'
            )
            if kline_df is not None and not kline_df.empty:
                print("\n[成功] 本地数据加载成功！后勤线路已打通！")
                print("数据显示如下 (前5行):")
                print(kline_df.head())
            else:
                print("\n[失败] 未能加载到数据。请检查符号、日期和本地数据文件。")
        except Exception as e:
            print(f"\n[严重错误] 加载数据时发生异常: {e}")
            traceback.print_exc()

    check_local_data_loading()

except ModuleNotFoundError as e:
    print(f"\n[致命错误] 即使在修正路径后，依然发生 ModuleNotFoundError: {e}")
    print("请仔细检查以下几点：")
    print(f"1. 您的 'backend' 文件夹是否确实直接存在于 '{current_working_dir}' 目录下？")
    print("2. 'backend' 文件夹内是否有 `__init__.py` 文件？")
    print("3. 'backend/app' 文件夹内是否有 `__init__.py` 文件？")
    print("\n如果以上都正确，可能存在更深层次的环境配置问题（如PYTHONPATH环境变量）。")

except Exception as e:
    print(f"\n[未知错误] 发生意外异常: {e}")
    traceback.print_exc()