import axios from 'axios';
import type { AxiosRequestConfig } from 'axios';
import { useAppStore } from '@/stores';
import { getActivePinia } from 'pinia';

// 创建基础 axios 实例
const baseClient = axios.create({
  baseURL: 'http://localhost:8000', // 后端API服务器地址
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 只设置响应拦截器
baseClient.interceptors.response.use(
  response => {
    // 后端API直接返回数据，不需要额外处理
    console.log('API响应拦截器收到数据:', response.data);
    return response.data;
  },
  error => {
    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应了错误状态码
      const { data } = error.response;
      let errorMessage = '请求失败';
      
      if (typeof data === 'string') {
        errorMessage = data;
      } else if (data && typeof data === 'object' && 'message' in data) {
        errorMessage = data.message;
      }
      
      throw new Error(errorMessage);
    } else if (error.request) {
      // 网络错误
      throw new Error('网络连接失败');
    } else {
      // 其他错误
      throw new Error(error.message || '未知错误');
    }
  }
);

// 创建包装的客户端，手动管理loading状态
class ApiClient {
  private getAppStore() {
    // 确保获取当前活跃的Pinia实例中的store
    return useAppStore(getActivePinia());
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const appStore = this.getAppStore();
    appStore.setLoading(true);
    
    try {
      const response = await baseClient.request({ ...config, method: 'GET', url });
      appStore.setLoading(false);
      return response as T;
    } catch (error) {
      appStore.setLoading(false);
      throw error;
    }
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const appStore = this.getAppStore();
    appStore.setLoading(true);
    
    try {
      const response = await baseClient.request({ ...config, method: 'POST', url, data });
      appStore.setLoading(false);
      return response as T;
    } catch (error) {
      appStore.setLoading(false);
      throw error;
    }
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const appStore = this.getAppStore();
    appStore.setLoading(true);
    
    try {
      const response = await baseClient.request({ ...config, method: 'PUT', url, data });
      appStore.setLoading(false);
      return response as T;
    } catch (error) {
      appStore.setLoading(false);
      throw error;
    }
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const appStore = this.getAppStore();
    appStore.setLoading(true);
    
    try {
      const response = await baseClient.request({ ...config, method: 'DELETE', url });
      appStore.setLoading(false);
      return response as T;
    } catch (error) {
      appStore.setLoading(false);
      throw error;
    }
  }
}

export const apiClient = new ApiClient();

// 为了在没有Pinia上下文的测试环境中使用，我们还需要一个简化的版本
// 或者在测试中mock store

export default apiClient;