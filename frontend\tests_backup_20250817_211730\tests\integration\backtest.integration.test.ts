import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import BacktestView from '@/views/BacktestView.vue'
import { useBacktestStore } from '@/stores/useBacktestStore'
import * as backtestApi from '@/api/backtest'

// Mock API模块
vi.mock('@/api/backtest', () => ({
  runBacktest: vi.fn(),
  getBacktestResults: vi.fn(),
  stopBacktest: vi.fn()
}))

// Mock子组件
vi.mock('@/components/BacktestForm.vue', () => ({
  default: {
    name: 'BacktestForm',
    props: ['loading'],
    emits: ['submit', 'reset'],
    template: `
      <div data-testid="backtest-form">
        <button 
          @click="$emit('submit', {
            strategy_id: 'test-strategy-123',
            symbol: '000001.SZ',
            start_date: '2023-01-01',
            end_date: '2023-12-31',
            capital: 100000
          })"
          data-testid="submit-btn"
        >
          开始回测
        </button>
      </div>
    `
  }
}))

vi.mock('@/components/BacktestResults.vue', () => ({
  default: {
    name: 'BacktestResults',
    props: ['result', 'loading'],
    template: '<div data-testid="backtest-results">回测结果</div>'
  }
}))

vi.mock('@/components/BacktestAnalysis.vue', () => ({
  default: {
    name: 'BacktestAnalysis',
    props: ['result', 'metrics'],
    template: '<div data-testid="backtest-analysis">回测分析</div>'
  }
}))

const mockBacktestApi = vi.mocked(backtestApi)

describe('Backtest Integration Test', () => {
  let wrapper: any
  let store: any

  beforeEach(() => {
    // 创建新的Pinia实例
    const pinia = createPinia()
    setActivePinia(pinia)
    
    // 清除所有mock
    vi.clearAllMocks()
    
    // 挂载组件
    wrapper = mount(BacktestView, {
      global: {
        plugins: [pinia]
      }
    })
    
    store = useBacktestStore()
  })

  it('应该完成完整的回测流程：点击按钮 -> API调用 -> 数据格式转换', async () => {
    // Mock API成功响应
    const mockTaskResponse = {
      success: true,
      data: {
        id: 'task-123',
        strategy_id: 'test-strategy-123',
        status: 'pending',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      }
    }
    mockBacktestApi.runBacktest.mockResolvedValue(mockTaskResponse)

    // 验证初始状态
    expect(store.isBacktesting).toBe(false)
    expect(store.backtestResult).toBe(null)

    // 模拟点击回测按钮
    const submitBtn = wrapper.find('[data-testid="submit-btn"]')
    await submitBtn.trigger('click')

    // 等待store的startBacktest方法完成
    await vi.waitFor(() => {
      expect(mockBacktestApi.runBacktest).toHaveBeenCalled()
    }, { timeout: 1000 })

    // 验证API被正确调用
    expect(mockBacktestApi.runBacktest).toHaveBeenCalledWith({
      strategy_id: 'test-strategy-123',
      symbol: '000001.SZ',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      capital: 100000
    })

    // 验证store状态更新
    expect(store.currentBacktestTask).toEqual(mockTaskResponse.data)
  }, 5000)

  it('应该正确处理API调用失败的情况', async () => {
    // Mock API失败响应
    const mockError = new Error('Strategy not found')
    mockBacktestApi.runBacktest.mockRejectedValue(mockError)

    // 模拟点击回测按钮
    const submitBtn = wrapper.find('[data-testid="submit-btn"]')
    await submitBtn.trigger('click')

    // 等待错误处理完成
    await vi.waitFor(() => {
      expect(store.backtestError).toBe('Strategy not found')
    }, { timeout: 1000 })

    // 验证错误状态
    expect(store.isBacktesting).toBe(false)
    expect(store.currentBacktestTask).toBe(null)
  }, 5000)

  it('应该防止重复提交回测任务', async () => {
    // 设置store为正在回测状态
    store.isBacktesting = true

    // Mock API响应
    mockBacktestApi.runBacktest.mockResolvedValue({
      success: true,
      data: { id: 'task-123', status: 'pending' }
    })

    // 模拟点击回测按钮
    const submitBtn = wrapper.find('[data-testid="submit-btn"]')
    await submitBtn.trigger('click')

    // 等待异步操作完成
    await wrapper.vm.$nextTick()

    // 验证API没有被调用（因为已经在回测中）
    expect(mockBacktestApi.runBacktest).not.toHaveBeenCalled()
  })

  it('应该在有回测结果时显示结果和分析组件', async () => {
    // 设置mock回测结果
    const mockResult = {
      task_id: 'task-123',
      strategy_name: '测试策略',
      symbol: '000001.SZ',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      initial_capital: 100000,
      final_capital: 120000,
      total_return: 0.2,
      sharpe_ratio: 1.5,
      max_drawdown: 0.1,
      metrics: {},
      trades: [],
      positions: [],
      equity_curve: [],
      generated_at: '2023-12-31T23:59:59Z'
    }

    // 设置store状态
    store.backtestResult = mockResult

    // 重新渲染组件
    await wrapper.vm.$nextTick()

    // 验证结果组件显示
    expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(true)
  })
})