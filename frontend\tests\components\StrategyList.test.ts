import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import StrategyList from '../../src/components/StrategyWorkshop/StrategyList.vue'
import type { Strategy } from '../../src/api/types'

// Mock Element Plus components
vi.mock('element-plus', () => ({
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElSkeleton: { name: 'ElSkeleton', template: '<div>Loading...</div>' },
  ElAlert: { name: 'ElAlert', template: '<div class="el-alert"><slot /></div>' },
  ElCard: { name: 'ElCard', template: '<div class="el-card" @click="$emit(\'click\')" :class="$attrs.class"><slot /></div>' },
  ElTag: { name: 'ElTag', template: '<span class="el-tag"><slot /></span>' },
  ElIcon: { name: 'ElIcon', template: '<span class="el-icon"><slot /></span>' }
}))

// Mock utils
vi.mock('../../src/utils/dateUtils', () => ({
  formatDate: vi.fn((date: string) => '2023-01-01')
}))

// Mock Element Plus icons
vi.mock('@element-plus/icons-vue', () => ({
  ArrowDown: { name: 'ArrowDown', template: '<svg class="arrow-down-icon"><path /></svg>' }
}))

// Mock constants
vi.mock('../../src/constants/strategy', () => ({
  UI_TEXT: {
    MY_STRATEGIES: '我的策略',
    NEW_STRATEGY: '新建策略',
    PUBLIC_STRATEGY: '公开',
    PRIVATE_STRATEGY: '私有',
    NO_DESCRIPTION: '暂无描述'
  }
}))

describe('StrategyList', () => {
  const mockStrategies: Strategy[] = [
    {
      id: 'strategy-1',
      name: '策略一',
      description: '这是第一个策略',
      author: '用户A',
      is_public: true,
      create_time: '2023-01-01T00:00:00Z',
      update_time: '2023-01-01T00:00:00Z',
      factors: []
    },
    {
      id: 'strategy-2',
      name: '策略二',
      description: '这是第二个策略',
      author: '用户B',
      is_public: false,
      create_time: '2023-01-02T00:00:00Z',
      update_time: '2023-01-02T00:00:00Z',
      factors: []
    }
  ]

  const createWrapper = (props = {}) => {
    return mount(StrategyList, {
      props: {
        strategies: [],
        currentStrategy: null,
        loading: false,
        error: null,
        ...props
      },
      global: {
        stubs: {
          ElButton: false, // 不要stub ElButton，让它正常渲染
          ElSkeleton: true,
          ElAlert: true,
          ElCard: false, // 不要stub ElCard，让它正常渲染
          ElTag: false // 不要stub ElTag，让它正常渲染
        }
      }
    })
  }

  describe('渲染', () => {
    it('应该正确渲染组件标题', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.find('h3').text()).toBe('我的策略')
    })

    it('应该显示新建策略按钮', () => {
      const wrapper = createWrapper()
      
      const newStrategyBtn = wrapper.find('.new-strategy-btn')
      expect(newStrategyBtn.exists()).toBe(true)
      expect(newStrategyBtn.text()).toBe('新建策略')
    })

    it('应该在加载状态下显示骨架屏', () => {
      const wrapper = createWrapper({ loading: true })
      
      expect(wrapper.find('[data-testid="loading-indicator"]').exists()).toBe(true)
      expect(wrapper.find('el-skeleton-stub').exists()).toBe(true)
    })

    it('应该在错误状态下显示错误信息', () => {
      const wrapper = createWrapper({ error: '加载策略失败' })
      
      const errorAlert = wrapper.find('[data-testid="error-message"]')
      expect(errorAlert.exists()).toBe(true)
      expect(errorAlert.attributes('title')).toBe('加载策略失败')
    })

    it('应该显示策略列表', () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      expect(wrapper.find('[data-testid="strategy-table"]').exists()).toBe(true)
      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(2)
    })
  })

  describe('策略卡片', () => {
    it('应该正确显示策略信息', () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      const firstStrategyCard = wrapper.findAll('[data-testid="strategy-row"]')[0]
      const strategyName = firstStrategyCard.find('[data-testid="strategy-name"]')
      
      expect(strategyName.text()).toBe('策略一')
      expect(firstStrategyCard.text()).toContain('这是第一个策略')
      expect(firstStrategyCard.text()).toContain('作者: 用户A')
    })

    it('应该显示策略的公开/私有状态', () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      const strategyCards = wrapper.findAll('[data-testid="strategy-row"]')
      
      // 第一个策略是公开的
      expect(strategyCards[0].text()).toContain('公开')
      
      // 第二个策略是私有的
      expect(strategyCards[1].text()).toContain('私有')
    })

    it('应该显示格式化的创建时间', () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      const firstStrategyCard = wrapper.findAll('[data-testid="strategy-row"]')[0]
      expect(firstStrategyCard.text()).toContain('2023-01-01')
    })

    it('应该为没有描述的策略显示默认文本', () => {
      const strategiesWithoutDescription = [{
        ...mockStrategies[0],
        description: ''
      }]
      
      const wrapper = createWrapper({ strategies: strategiesWithoutDescription })
      
      // 检查整个组件文本中是否包含暂无描述，但排除标题
      const cardContent = wrapper.find('[data-testid="strategy-row"]')
      expect(cardContent.text()).toContain('暂无描述')
    })
  })

  describe('策略选择', () => {
    it('应该高亮显示当前选中的策略', () => {
      const wrapper = createWrapper({ 
        strategies: mockStrategies,
        currentStrategy: mockStrategies[0]
      })
      
      const firstStrategyCard = wrapper.findAll('[data-testid="strategy-row"]')[0]
      expect(firstStrategyCard.classes()).toContain('is-active')
    })

    it('应该在点击策略卡片时发出select-strategy事件', async () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      const firstStrategyCard = wrapper.findAll('[data-testid="strategy-row"]')[0]
      await firstStrategyCard.trigger('click')
      
      expect(wrapper.emitted('select-strategy')).toBeTruthy()
      expect(wrapper.emitted('select-strategy')?.[0]).toEqual([mockStrategies[0]])
    })

    it('应该正确处理策略选择状态变化', async () => {
      const wrapper = createWrapper({ 
        strategies: mockStrategies,
        currentStrategy: null
      })
      
      // 初始状态下没有策略被选中
      expect(wrapper.find('.is-active').exists()).toBe(false)
      
      // 更新当前策略
      await wrapper.setProps({ currentStrategy: mockStrategies[1] })
      
      const secondStrategyCard = wrapper.findAll('[data-testid="strategy-row"]')[1]
      expect(secondStrategyCard.classes()).toContain('is-active')
    })
  })

  describe('新建策略', () => {
    it('应该在点击新建策略按钮时发出create-strategy事件', async () => {
      const wrapper = createWrapper()
      
      const newStrategyBtn = wrapper.find('.new-strategy-btn')
      await newStrategyBtn.trigger('click')
      
      expect(wrapper.emitted('create-strategy')).toBeTruthy()
    })
  })

  describe('空状态', () => {
    it('应该在没有策略时显示空列表', () => {
      const wrapper = createWrapper({ strategies: [] })
      
      const strategyTable = wrapper.find('[data-testid="strategy-table"]')
      expect(strategyTable.exists()).toBe(true)
      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(0)
    })
  })

  describe('响应式行为', () => {
    it('应该响应strategies prop的变化', async () => {
      const wrapper = createWrapper({ strategies: [] })
      
      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(0)
      
      await wrapper.setProps({ strategies: mockStrategies })
      
      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(2)
    })

    it('应该响应loading状态的变化', async () => {
      const wrapper = createWrapper({ loading: false })
      
      expect(wrapper.find('[data-testid="loading-indicator"]').exists()).toBe(false)
      
      await wrapper.setProps({ loading: true })
      
      expect(wrapper.find('[data-testid="loading-indicator"]').exists()).toBe(true)
    })

    it('应该响应error状态的变化', async () => {
      const wrapper = createWrapper({ error: null })
      
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(false)
      
      await wrapper.setProps({ error: '网络错误' })
      
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
    })
  })

  describe('样式和交互', () => {
    it('应该为策略卡片应用正确的CSS类', () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      const strategyCards = wrapper.findAll('[data-testid="strategy-row"]')
      strategyCards.forEach(card => {
        expect(card.classes()).toContain('strategy-card')
      })
    })

    it('应该在hover时显示阴影效果', () => {
      const wrapper = createWrapper({ strategies: mockStrategies })
      
      const firstStrategyCard = wrapper.findAll('[data-testid="strategy-row"]')[0]
      expect(firstStrategyCard.attributes('shadow')).toBe('hover')
    })
  })

  describe('边界情况', () => {
    it('应该处理策略数据中的null值', () => {
      const strategiesWithNulls = [{
        ...mockStrategies[0],
        description: null as any,
        author: null as any
      }]
      
      const wrapper = createWrapper({ strategies: strategiesWithNulls })
      
      const cardContent = wrapper.find('[data-testid="strategy-row"]')
      expect(cardContent.text()).toContain('暂无描述')
      expect(wrapper.text()).toContain('作者: ')
    })

    it('应该处理同时有loading和error状态', async () => {
      const wrapper = createWrapper({ loading: true, error: '错误信息' })
      
      // loading状态优先显示
      expect(wrapper.find('[data-testid="loading-indicator"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(false)
      
      await wrapper.setProps({ loading: false })
      
      // loading结束后显示错误
      expect(wrapper.find('[data-testid="loading-indicator"]').exists()).toBe(false)
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
    })
  })
})