import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import BacktestForm from '@/components/BacktestForm.vue'
import type { BacktestConfig } from '@/api/types/backtest'

describe('BacktestForm.vue', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    wrapper = mount(BacktestForm)
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染表单标题', () => {
      expect(wrapper.find('h2').text()).toBe('回测配置')
    })

    it('应该渲染所有必需的表单字段', () => {
      expect(wrapper.find('[data-testid="strategy-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="symbol-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="start-date-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="end-date-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="capital-input"]').exists()).toBe(true)
    })

    it('应该渲染操作按钮', () => {
      expect(wrapper.find('[data-testid="reset-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="submit-btn"]').exists()).toBe(true)
    })
  })

  describe('表单验证测试', () => {
    it('初始状态下提交按钮应该被禁用', () => {
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('填写完整表单后提交按钮应该可用', async () => {
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-01-01')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="capital-input"]').setValue('100000')
      await nextTick()

      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeUndefined()
    })

    it('开始日期晚于结束日期时应该禁用提交', async () => {
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-01-01')
      await wrapper.find('[data-testid="capital-input"]').setValue('100000')
      await nextTick()

      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })
  })

  describe('用户交互测试', () => {
    it('应该正确处理表单提交', async () => {
      const mockConfig: BacktestConfig = {
        strategy_name: '测试策略',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        initial_capital: 100000
      }

      // 填写表单
      await wrapper.find('[data-testid="strategy-input"]').setValue(mockConfig.strategy_name)
      await wrapper.find('[data-testid="symbol-input"]').setValue(mockConfig.symbol)
      await wrapper.find('[data-testid="start-date-input"]').setValue(mockConfig.start_date)
      await wrapper.find('[data-testid="end-date-input"]').setValue(mockConfig.end_date)
      await wrapper.find('[data-testid="capital-input"]').setValue(mockConfig.initial_capital.toString())
      await nextTick()

      // 提交表单
      await wrapper.find('form').trigger('submit.prevent')

      // 验证事件
      const submitEvents = wrapper.emitted('submit')
      expect(submitEvents).toHaveLength(1)
      expect(submitEvents![0][0]).toEqual(mockConfig)
    })

    it('应该正确处理表单重置', async () => {
      // 先填写一些数据
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      
      // 点击重置
      await wrapper.find('[data-testid="reset-btn"]').trigger('click')
      await nextTick()

      // 验证表单被清空
      expect(wrapper.find('[data-testid="strategy-input"]').element.value).toBe('')
      expect(wrapper.find('[data-testid="symbol-input"]').element.value).toBe('')
      
      // 验证重置事件
      const resetEvents = wrapper.emitted('reset')
      expect(resetEvents).toHaveLength(1)
    })
  })

  describe('Props测试', () => {
    it('loading为true时应该显示加载状态', async () => {
      await wrapper.setProps({ loading: true })
      
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.text()).toBe('运行中...')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('loading为false时应该显示正常状态', async () => {
      await wrapper.setProps({ loading: false })
      
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.text()).toBe('开始回测')
    })
  })

  describe('表单字段测试', () => {
    it('应该正确处理数字输入', async () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      await capitalInput.setValue('200000')
      
      expect(capitalInput.element.value).toBe('200000')
    })

    it('应该有正确的输入约束', () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      expect(capitalInput.attributes('min')).toBe('1000')
      expect(capitalInput.attributes('step')).toBe('1000')
      expect(capitalInput.attributes('type')).toBe('number')
    })

    it('应该有正确的占位符文本', () => {
      expect(wrapper.find('[data-testid="strategy-input"]').attributes('placeholder')).toBe('请输入策略名称')
      expect(wrapper.find('[data-testid="symbol-input"]').attributes('placeholder')).toBe('如：000001.SZ')
      expect(wrapper.find('[data-testid="capital-input"]').attributes('placeholder')).toBe('100000')
    })
  })

  describe('可访问性测试', () => {
    it('所有输入字段应该有对应的标签', () => {
      const labels = wrapper.findAll('label')
      const inputs = wrapper.findAll('input')
      
      expect(labels.length).toBe(inputs.length)
      
      labels.forEach((label, index) => {
        const forAttr = label.attributes('for')
        const input = inputs[index]
        expect(input.attributes('id')).toBe(forAttr)
      })
    })

    it('必填字段应该有required属性', () => {
      const requiredInputs = wrapper.findAll('input[required]')
      expect(requiredInputs.length).toBe(5) // 所有字段都是必填的
    })
  })

  describe('样式和布局测试', () => {
    it('应该具有正确的CSS类', () => {
      expect(wrapper.find('.backtest-form').exists()).toBe(true)
      expect(wrapper.find('.form-container').exists()).toBe(true)
      expect(wrapper.find('.form-actions').exists()).toBe(true)
    })

    it('日期字段应该使用网格布局', () => {
      expect(wrapper.find('.form-row').exists()).toBe(true)
    })
  })

  describe('回测业务特有验证逻辑测试', () => {
    it('应该验证股票代码格式', async () => {
      // 测试无效的股票代码格式
      await wrapper.find('[data-testid="symbol-input"]').setValue('INVALID')
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-01-01')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="capital-input"]').setValue('100000')
      await nextTick()

      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('应该验证回测时间范围的合理性', async () => {
      // 测试时间范围过短（少于30天）
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-01-01')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-01-15')
      await wrapper.find('[data-testid="capital-input"]').setValue('100000')
      await nextTick()

      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('应该验证初始资金的最小值限制', async () => {
      // 测试资金过少
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-01-01')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="capital-input"]').setValue('500')
      await nextTick()

      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('应该验证策略名称的唯一性', async () => {
      // 模拟策略名称重复的情况
      const wrapperWithProps = mount(BacktestForm, {
        props: {
          existingStrategies: ['测试策略', '另一个策略']
        }
      })

      await wrapperWithProps.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapperWithProps.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await wrapperWithProps.find('[data-testid="start-date-input"]').setValue('2023-01-01')
      await wrapperWithProps.find('[data-testid="end-date-input"]').setValue('2023-12-31')
      await wrapperWithProps.find('[data-testid="capital-input"]').setValue('100000')
      await nextTick()

      const submitBtn = wrapperWithProps.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
      
      wrapperWithProps.unmount()
    })
  })

  describe('表单状态管理复杂性测试', () => {
    it('应该正确处理表单状态的级联更新', async () => {
      // 测试修改开始日期时自动调整结束日期
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-06-01')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-05-01')
      await nextTick()

      // 验证结束日期被自动调整
      const endDateInput = wrapper.find('[data-testid="end-date-input"]')
      expect(new Date(endDateInput.element.value) >= new Date('2023-06-01')).toBe(true)
    })

    it('应该正确处理表单字段间的依赖关系', async () => {
      // 测试选择不同股票时资金建议的变化
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await nextTick()
      
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      const suggestedCapital = capitalInput.attributes('placeholder')
      
      await wrapper.find('[data-testid="symbol-input"]').setValue('000002.SZ')
      await nextTick()
      
      // 验证资金建议可能发生变化
      expect(capitalInput.attributes('placeholder')).toBeDefined()
    })

    it('应该正确处理表单的脏状态检测', async () => {
      // 初始状态应该是干净的
      expect(wrapper.vm.isDirty).toBe(false)
      
      // 修改任何字段后应该变为脏状态
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试')
      await nextTick()
      
      expect(wrapper.vm.isDirty).toBe(true)
      
      // 重置后应该恢复干净状态
      await wrapper.find('[data-testid="reset-btn"]').trigger('click')
      await nextTick()
      
      expect(wrapper.vm.isDirty).toBe(false)
    })
  })

  describe('错误处理和用户反馈测试', () => {
    it('应该显示字段级别的错误信息', async () => {
      // 输入无效数据
      await wrapper.find('[data-testid="capital-input"]').setValue('-1000')
      await wrapper.find('[data-testid="capital-input"]').trigger('blur')
      await nextTick()

      // 验证错误信息显示
      const errorMessage = wrapper.find('[data-testid="capital-error"]')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('初始资金必须大于')
    })

    it('应该显示表单级别的错误信息', async () => {
      // 填写冲突的数据
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-01-01')
      await nextTick()

      // 验证表单错误信息
      const formError = wrapper.find('[data-testid="form-error"]')
      expect(formError.exists()).toBe(true)
      expect(formError.text()).toContain('开始日期不能晚于结束日期')
    })

    it('应该正确处理网络错误', async () => {
      // 模拟网络错误
      const wrapperWithError = mount(BacktestForm, {
        props: {
          error: '网络连接失败，请检查网络设置'
        }
      })

      const errorAlert = wrapperWithError.find('[data-testid="error-alert"]')
      expect(errorAlert.exists()).toBe(true)
      expect(errorAlert.text()).toContain('网络连接失败')
      
      wrapperWithError.unmount()
    })

    it('应该提供重试机制', async () => {
      const wrapperWithError = mount(BacktestForm, {
        props: {
          error: '提交失败',
          canRetry: true
        }
      })

      const retryBtn = wrapperWithError.find('[data-testid="retry-btn"]')
      expect(retryBtn.exists()).toBe(true)
      
      await retryBtn.trigger('click')
      
      const retryEvents = wrapperWithError.emitted('retry')
      expect(retryEvents).toHaveLength(1)
      
      wrapperWithError.unmount()
    })
  })

  describe('数据类型和格式处理测试', () => {
    it('应该正确处理数字格式化', async () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      
      // 输入带逗号的数字
      await capitalInput.setValue('1,000,000')
      await capitalInput.trigger('blur')
      await nextTick()
      
      // 验证数字被正确解析
      expect(wrapper.vm.formData.initial_capital).toBe(1000000)
    })

    it('应该正确处理日期格式', async () => {
      const startDateInput = wrapper.find('[data-testid="start-date-input"]')
      
      // 输入不同格式的日期
      await startDateInput.setValue('2023/01/01')
      await startDateInput.trigger('blur')
      await nextTick()
      
      // 验证日期被标准化
      expect(wrapper.vm.formData.start_date).toBe('2023-01-01')
    })

    it('应该正确处理股票代码格式化', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      // 输入小写股票代码
      await symbolInput.setValue('000001.sz')
      await symbolInput.trigger('blur')
      await nextTick()
      
      // 验证代码被转换为大写
      expect(wrapper.vm.formData.symbol).toBe('000001.SZ')
    })

    it('应该正确处理策略名称的空白字符', async () => {
      const strategyInput = wrapper.find('[data-testid="strategy-input"]')
      
      // 输入包含前后空格的策略名
      await strategyInput.setValue('  测试策略  ')
      await strategyInput.trigger('blur')
      await nextTick()
      
      // 验证空格被去除
      expect(wrapper.vm.formData.strategy_name).toBe('测试策略')
    })
  })

  describe('加载状态完整性测试', () => {
    it('加载时应该禁用所有交互元素', async () => {
      await wrapper.setProps({ loading: true })
      await nextTick()
      
      // 验证所有输入字段被禁用
      const inputs = wrapper.findAll('input')
      inputs.forEach(input => {
        expect(input.attributes('disabled')).toBeDefined()
      })
      
      // 验证按钮被禁用
      expect(wrapper.find('[data-testid="submit-btn"]').attributes('disabled')).toBeDefined()
      expect(wrapper.find('[data-testid="reset-btn"]').attributes('disabled')).toBeDefined()
    })

    it('应该显示加载进度指示器', async () => {
      await wrapper.setProps({ 
        loading: true,
        loadingProgress: 45
      })
      await nextTick()
      
      const progressBar = wrapper.find('[data-testid="loading-progress"]')
      expect(progressBar.exists()).toBe(true)
      expect(progressBar.attributes('value')).toBe('45')
    })

    it('应该显示加载状态的详细信息', async () => {
      await wrapper.setProps({ 
        loading: true,
        loadingMessage: '正在验证股票代码...'
      })
      await nextTick()
      
      const loadingMessage = wrapper.find('[data-testid="loading-message"]')
      expect(loadingMessage.exists()).toBe(true)
      expect(loadingMessage.text()).toBe('正在验证股票代码...')
    })
  })

  describe('用户体验优化测试', () => {
    it('应该支持键盘导航', async () => {
      const firstInput = wrapper.find('[data-testid="strategy-input"]')
      await firstInput.trigger('keydown.tab')
      await nextTick()
      
      // 验证焦点移动到下一个字段
      const secondInput = wrapper.find('[data-testid="symbol-input"]')
      expect(document.activeElement).toBe(secondInput.element)
    })

    it('应该提供智能提示功能', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      // 输入部分股票代码
      await symbolInput.setValue('0000')
      await symbolInput.trigger('input')
      await nextTick()
      
      // 验证提示列表出现
      const suggestions = wrapper.find('[data-testid="symbol-suggestions"]')
      expect(suggestions.exists()).toBe(true)
    })

    it('应该支持表单数据的自动保存', async () => {
      // 填写部分数据
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await nextTick()
      
      // 模拟页面刷新
      wrapper.unmount()
      wrapper = mount(BacktestForm, {
        props: {
          autoSave: true
        }
      })
      
      // 验证数据被恢复
      expect(wrapper.find('[data-testid="strategy-input"]').element.value).toBe('测试策略')
      expect(wrapper.find('[data-testid="symbol-input"]').element.value).toBe('000001.SZ')
    })
  })

  describe('边界条件和异常情况测试', () => {
    it('应该处理极大数值的资金输入', async () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      
      // 输入极大数值
      await capitalInput.setValue('999999999999')
      await capitalInput.trigger('blur')
      await nextTick()
      
      // 验证数值被正确处理或限制
      const value = wrapper.vm.formData.initial_capital
      expect(value).toBeLessThanOrEqual(1000000000) // 假设最大限制为10亿
    })

    it('应该处理特殊字符的输入', async () => {
      const strategyInput = wrapper.find('[data-testid="strategy-input"]')
      
      // 输入包含特殊字符的策略名
      await strategyInput.setValue('测试<script>alert("xss")</script>策略')
      await strategyInput.trigger('blur')
      await nextTick()
      
      // 验证特殊字符被过滤
      expect(wrapper.vm.formData.strategy_name).not.toContain('<script>')
    })

    it('应该处理网络中断的情况', async () => {
      // 模拟网络中断
      const wrapperWithNetworkError = mount(BacktestForm, {
        props: {
          networkStatus: 'offline'
        }
      })
      
      // 验证离线提示
      const offlineMessage = wrapperWithNetworkError.find('[data-testid="offline-message"]')
      expect(offlineMessage.exists()).toBe(true)
      
      // 验证表单被禁用
      const submitBtn = wrapperWithNetworkError.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
      
      wrapperWithNetworkError.unmount()
    })
  })
})