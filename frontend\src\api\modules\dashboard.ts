import { apiClient } from "../client";

export interface MarketPerformance {
  date: string[];
  value: number[];
}

export interface DashboardSummary {
  today_gain: number;
  active_strategies: number;
  total_turnover_wan: number;
  signals_count: number;
  market_performance: MarketPerformance;
}

export function getDashboardSummary() {
  return apiClient.get<DashboardSummary>('/dashboard/summary');
}