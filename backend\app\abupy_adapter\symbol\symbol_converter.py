# -*- coding: utf-8 -*-
"""
符号转换器模块
"""
from typing import Tuple
from backend.app.core.exceptions import SymbolError

# 假设这些常量在其他地方定义
MARKET_PREFIXES = {
    'sh': 'CN',
    'sz': 'CN',
    'us': 'US',
    'hk': 'HK',
    'cn': 'CN'
}

CN_INDEX_CODES = {
    'sh000001': '上证指数',
    '000001.SH': '上证指数',
    'sh000300': '沪深300',
    '000300.SH': '沪深300',
    'sz399001': '深证成指',
    '399001.SZ': '深证成指',
    'sz399006': '创业板指',
    '399006.SZ': '创业板指'
}

US_INDEX_CODES = {
    'us.dji': '道琼斯工业平均指数',
    'us.ixic': '纳斯达克综合指数',
    'us.inx': '标普500'
}

HK_INDEX_CODES = {
    'hkhsi': '恒生指数',
    'hkhscei': '国企指数',
    'hkhscci': '红筹指数'
}

class SymbolConverter:
    """格式转换器，如 sh600519 -> 600519.SH"""

    @staticmethod
    def normalize_symbol(symbol: str) -> Tuple[str, str]:
        """
        标准化股票代码，并返回市场类型
        
        Args:
            symbol: 原始股票代码。支持多种格式：
                    A股: 'sh600000', '600000'
                    港股: 'hk0700', '0700', '0700.HK'
                    美股: 'usAAPL', 'AAPL'
            
        Returns:
            Tuple[str, str]: (标准化后的代码, 市场类型)
            
        Raises:
            SymbolError: 当代码格式无效或无法标准化时
        """
        # 验证输入
        if not symbol or not symbol.strip():
            raise SymbolError(message="股票代码不能为空")
        
        # 处理后缀格式，如'0700.HK', '000300.SH'
        if '.' in symbol:
            code, market_suffix = symbol.split('.')
            if market_suffix.upper() == 'HK':
                # 港股格式
                normalized_symbol = f"hk{code}"
                return normalized_symbol, 'HK'
            elif market_suffix.upper() in ['SH', 'SZ']:
                # 处理A股后缀格式，如'600000.SH', '000300.SH'
                # 对于指数代码，保持原格式以匹配本地数据
                if symbol in CN_INDEX_CODES:
                    return symbol, 'CN'
                # 对于普通股票，转换为abu格式
                normalized_symbol = f"{market_suffix.lower()}{code}"
                return normalized_symbol, 'CN'
            # 其他格式的后缀也可以在这里处理
        
        # 已经是标准格式的代码
        if symbol.startswith(('sh', 'sz', 'us', 'hk')):
            market = MARKET_PREFIXES.get(symbol[:2].lower(), 'CN')
            return symbol, market
            
        # 纯数字代码转换
        if symbol.isdigit():
            if len(symbol) == 6:
                # 6位纯数字，根据首位判断沪深
                if symbol.startswith('6'):
                    return f"sh{symbol}", 'CN'
                elif symbol.startswith('0') or symbol.startswith('3'):
                    return f"sz{symbol}", 'CN'
                else:
                    # 其他情况默认为上海
                    return f"sh{symbol}", 'CN'
            elif len(symbol) == 5 or len(symbol) == 4:
                # 5位或4位纯数字，视为港股
                return f"hk{symbol}", 'HK'
                
        # 常见指数代码
        for index_dict in [CN_INDEX_CODES, US_INDEX_CODES, HK_INDEX_CODES]:
            if symbol.lower() in index_dict:
                # 此处需要一个方法从symbol获取market type，暂时硬编码
                if symbol.lower().startswith(('sh', 'sz')):
                    market = 'CN'
                elif symbol.lower().startswith('us'):
                    market = 'US'
                elif symbol.lower().startswith('hk'):
                    market = 'HK'
                else:
                    market = 'Unknown'
                return symbol.lower(), market
        
        # 如果所有方法都失败，抛出异常
        raise SymbolError(
            message=f"无法标准化股票代码 {symbol}",
            data={"symbol": symbol, "supported_formats": [
                "A股: sh/sz + 6位数字, 6位数字", 
                "港股: hk + 4/5位数字, 4/5位数字, 数字.HK",
                "美股: us + 股票代码"
            ]}
        )
