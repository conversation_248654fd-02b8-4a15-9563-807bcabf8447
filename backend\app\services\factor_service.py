"""
因子管理服务模块

提供因子的管理功能，包括获取可用的买入因子和卖出因子列表。
"""

from typing import List, Optional
from backend.app.schemas.strategy import BuyFactor, SellFactor, FactorListResponse
from backend.app.abupy_adapter.strategy_adapter import StrategyAdapter
from backend.app.core.exceptions import AdapterError

class FactorService:
    """因子服务类，提供因子管理功能"""

    @classmethod
    def get_available_factors(cls, factor_type: Optional[str] = None) -> FactorListResponse:
        """获取可用的因子列表

        Args:
            factor_type: 因子类型 ('buy' or 'sell')

        Returns:
            包含买入因子和卖出因子的因子列表响应对象
        """
        try:
            raw_factors_data = StrategyAdapter.get_available_abu_factors(factor_type=factor_type)
            
            buy_factors = []
            sell_factors = []

            if isinstance(raw_factors_data, dict):
                # 处理返回字典的情况 (factor_type is None)
                buy_factors_data = raw_factors_data.get('buy', [])
                sell_factors_data = raw_factors_data.get('sell', [])
                
                for factor_data in buy_factors_data:
                    buy_factors.append(BuyFactor(**factor_data))
                for factor_data in sell_factors_data:
                    sell_factors.append(SellFactor(**factor_data))
            
            elif isinstance(raw_factors_data, list):
                # 处理返回列表的情况 (factor_type is 'buy' or 'sell')
                for factor_data in raw_factors_data:
                    if factor_data.get('factor_type') == 'buy':
                        buy_factors.append(BuyFactor(**factor_data))
                    else:
                        sell_factors.append(SellFactor(**factor_data))


            return FactorListResponse(buy_factors=buy_factors, sell_factors=sell_factors)

        except AdapterError as e:
            # Re-raise adapter errors to be handled by the API layer
            raise e
