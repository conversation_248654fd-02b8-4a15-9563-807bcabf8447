"""
因子管理服务模块

提供因子的管理功能，包括获取可用的买入因子和卖出因子列表。
目前返回硬编码的示例因子，后续将从abu原始框架中获取真实因子。
"""

from backend.app.schemas.strategy import BuyFactor, FactorListResponse, SellFactor


class FactorService:
    """因子服务类，提供因子管理功能"""
    
    @classmethod
    def get_available_factors(cls, factor_type: str = None) -> FactorListResponse:
        """获取可用的因子列表
        
        Returns:
            包含买入因子和卖出因子的因子列表响应对象
            
        Note:
            目前返回硬编码的示例因子，后续将从abu原始框架中获取真实因子
        """
        # 示例买入因子列表
        buy_factors = [
            BuyFactor(
                id="buy_demo_1",
                name="示例买入因子1",
                description="这是一个示例买入因子，用于演示",
                factor_class="AbuFactorBuyDemo1",
                parameters={"p1": 0.01, "p2": 10}
            ),
            BuyFactor(
                id="buy_demo_2",
                name="示例买入因子2",
                description="另一个示例买入因子",
                factor_class="AbuFactorBuyDemo2",
                parameters={"p1": 0.02, "p2": 20}
            ),
            BuyFactor(
                id="buy_breakout",
                name="突破买入",
                description="基于价格突破的买入因子",
                factor_class="AbuFactorBuyBreak",
                parameters={"xd": 21, "factor_name": "突破买入"}
            ),
        ]
        
        # 示例卖出因子列表
        sell_factors = [
            SellFactor(
                id="sell_demo_1",
                name="示例卖出因子1",
                description="这是一个示例卖出因子，用于演示",
                factor_class="AbuFactorSellDemo1",
                parameters={"p1": 0.01, "p2": 10}
            ),
            SellFactor(
                id="sell_demo_2",
                name="示例卖出因子2",
                description="另一个示例卖出因子",
                factor_class="AbuFactorSellDemo2",
                parameters={"p1": 0.02, "p2": 20}
            ),
            SellFactor(
                id="sell_timeout",
                name="超时卖出",
                description="基于持有时间的卖出因子",
                factor_class="AbuFactorSellTimeout",
                parameters={"holding_days": 5, "factor_name": "超时卖出"}
            ),
        ]
        
        # 根据因子类型返回相应的因子列表
        if factor_type == "buy":
            return FactorListResponse(buy_factors=buy_factors, sell_factors=[])
        elif factor_type == "sell":
            return FactorListResponse(buy_factors=[], sell_factors=sell_factors)
        else:
            # 如果没有指定类型或类型无效，返回所有因子
            return FactorListResponse(buy_factors=buy_factors, sell_factors=sell_factors)
