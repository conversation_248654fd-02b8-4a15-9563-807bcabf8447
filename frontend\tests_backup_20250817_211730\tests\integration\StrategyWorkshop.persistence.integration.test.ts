import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import StrategyWorkshop from '@/components/StrategyWorkshop/index.vue'
import { useStrategyStore } from '@/stores/useStrategyStore'
import { SimpleStrategyDataFactory } from '../factories/SimpleStrategyDataFactory'
import type { Strategy } from '@/api/types'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElPageHeader: {
    name: 'ElPageHeader',
    props: ['title', 'content'],
    template: '<div class="el-page-header"><div class="el-page-header__title">{{ title }}</div><div class="el-page-header__content">{{ content }}</div></div>'
  }
}))

// Mock 子组件
vi.mock('@/components/StrategyWorkshop/StrategyList.vue', () => ({
  default: {
    name: 'StrategyList',
    props: ['strategies', 'currentStrategy', 'loading', 'error'],
    emits: ['select-strategy', 'create-strategy'],
    template: `
      <div class="strategy-list-mock" data-testid="strategy-list">
        <div v-if="loading" data-testid="loading-indicator">Loading...</div>
        <div v-else-if="error" data-testid="error-message">{{ error }}</div>
        <div v-else data-testid="strategy-table">
          <div 
            v-for="strategy in strategies" 
            :key="strategy.id"
            data-testid="strategy-row"
            @click="$emit('select-strategy', strategy)"
          >
            <span data-testid="strategy-name">{{ strategy.name }}</span>
          </div>
        </div>
        <button 
          class="new-strategy-btn" 
          @click="$emit('create-strategy')"
          data-testid="new-strategy-btn"
        >
          新建策略
        </button>
      </div>
    `
  }
}))

vi.mock('@/components/StrategyWorkshop/StrategyEditor.vue', () => ({
  default: {
    name: 'StrategyEditor',
    props: ['strategy', 'loading'],
    emits: ['save-strategy', 'update-strategy', 'create-strategy'],
    template: `
      <div class="strategy-editor-mock" data-testid="strategy-editor">
        <div v-if="loading">Loading editor...</div>
        <div v-else-if="strategy" class="editor-content">
          <h2>{{ getStrategyName() }}</h2>
          <div class="strategy-info">
            <input 
              :value="getStrategyName()" 
              @input="handleNameInput"
              data-testid="strategy-name-input"
              placeholder="策略名称"
            />
            <textarea 
              :value="getStrategyDescription()" 
              @input="handleDescriptionInput"
              data-testid="strategy-description-input"
              placeholder="策略描述"
            />
          </div>
          <div class="factors-section">
            <div class="buy-factors" data-testid="buy-factors-section">
              <h3>买入因子</h3>
              <div v-for="(factor, index) in getBuyFactors()" :key="index">
                <span data-testid="buy-factor-name">{{ factor.class_name }}</span>
                <button @click="removeBuyFactor(index)" data-testid="remove-buy-factor">删除</button>
              </div>
              <button @click="addBuyFactor" data-testid="add-buy-factor">添加买入因子</button>
            </div>
            <div class="sell-factors" data-testid="sell-factors-section">
              <h3>卖出因子</h3>
              <div v-for="(factor, index) in getSellFactors()" :key="index">
                <span data-testid="sell-factor-name">{{ factor.class_name }}</span>
                <button @click="removeSellFactor(index)" data-testid="remove-sell-factor">删除</button>
              </div>
              <button @click="addSellFactor" data-testid="add-sell-factor">添加卖出因子</button>
            </div>
          </div>
          <button @click="handleSave" data-testid="save-strategy-btn">保存策略</button>
        </div>
        <div v-else class="empty-state" data-testid="empty-state">
          <p>请选择或创建一个策略</p>
          <button @click="$emit('create-strategy')" data-testid="create-strategy-btn">新建策略</button>
        </div>
      </div>
    `,
    setup(props: any, { expose, emit }: any) {
      const { nextTick, computed, ref, watch } = require('vue')
      
      // 使用响应式引用来强制更新
      const forceUpdate = ref(0)
      
      // 监听props变化
      watch(() => props.strategy, (newStrategy) => {
        console.log('Strategy prop changed:', newStrategy)
        forceUpdate.value++
      }, { deep: true, immediate: true })
      
      // 确保响应式的getter方法
      const getStrategyName = () => {
        forceUpdate.value // 触发响应式依赖
        const strategy = props.strategy?.value || props.strategy
        const name = strategy?.name || ''
        console.log('getStrategyName called, name:', name)
        return name
      }
      
      const getStrategyDescription = () => {
        forceUpdate.value // 触发响应式依赖
        const strategy = props.strategy?.value || props.strategy
        return strategy?.description || ''
      }
      
      const getBuyFactors = () => {
        forceUpdate.value // 触发响应式依赖
        // 处理响应式引用
        const strategy = props.strategy?.value || props.strategy
        const factors = strategy?.buy_factors || []
        console.log('getBuyFactors called, props.strategy:', props.strategy)
        console.log('getBuyFactors called, strategy:', strategy)
        console.log('getBuyFactors called, factors:', factors)
        console.log('getBuyFactors called, factors length:', factors.length)
        return factors
      }
      
      const getSellFactors = () => {
        forceUpdate.value // 触发响应式依赖
        const strategy = props.strategy?.value || props.strategy
        return strategy?.sell_factors || []
      }
      
      // 处理名称输入
      const handleNameInput = async (event: Event) => {
        const target = event.target as HTMLInputElement
        const value = target.value
        
        const strategy = props.strategy?.value || props.strategy
        if (strategy) {
          strategy.name = value
          emit('update-strategy', { ...strategy, name: value })
        }
        await nextTick()
      }
      
      // 处理描述输入
      const handleDescriptionInput = async (event: Event) => {
        const target = event.target as HTMLTextAreaElement
        const value = target.value
        
        const strategy = props.strategy?.value || props.strategy
        if (strategy) {
          strategy.description = value
          emit('update-strategy', { ...strategy, description: value })
        }
        await nextTick()
      }
      
      // 添加买入因子
      const addBuyFactor = async () => {
        const strategy = props.strategy?.value || props.strategy
        if (!strategy) return
        
        if (!strategy.buy_factors) {
          strategy.buy_factors = []
        }
        
        const newFactor = {
          id: `buy-${Date.now()}`,
          name: 'AbuFactorBuyBreak',
          class_name: 'AbuFactorBuyBreak',
          factor_type: 'buy' as const,
          parameters: {
            xd: 60,
            position: {
              class: 'AbuAtrPosition',
              atr_pos_base: 0.15,
              atr_base_price: 18,
              std_atr_threshold: 0.5
            }
          }
        }
        
        strategy.buy_factors.push(newFactor)
        console.log('Added buy factor, new length:', strategy.buy_factors.length)
        await nextTick()
      }
      
      // 删除买入因子
      const removeBuyFactor = async (index: number) => {
        const strategy = props.strategy?.value || props.strategy
        if (strategy?.buy_factors) {
          strategy.buy_factors.splice(index, 1)
          console.log('Removed buy factor, new length:', strategy.buy_factors.length)
          await nextTick()
        }
      }
      
      // 添加卖出因子
      const addSellFactor = async () => {
        const strategy = props.strategy?.value || props.strategy
        if (!strategy) return
        
        if (!strategy.sell_factors) {
          strategy.sell_factors = []
        }
        
        const newFactor = {
          id: `sell-${Date.now()}`,
          name: 'AbuFactorAtrNStop',
          class_name: 'AbuFactorAtrNStop',
          factor_type: 'sell' as const,
          parameters: {
            stop_loss_n: 0.5,
            stop_win_n: 3.0
          }
        }
        
        strategy.sell_factors.push(newFactor)
        await nextTick()
      }
      
      // 删除卖出因子
      const removeSellFactor = async (index: number) => {
        const strategy = props.strategy?.value || props.strategy
        if (strategy?.sell_factors) {
          strategy.sell_factors.splice(index, 1)
          await nextTick()
        }
      }
      
      // 处理保存
      const handleSave = async () => {
        const strategy = props.strategy?.value || props.strategy
        if (!strategy) return
        
        const buyFactors = getBuyFactors()
        console.log('handleSave - buy factors:', buyFactors)
        
        // 验证是否有买入因子
        const hasBuyFactors = buyFactors.length > 0
        
        if (!hasBuyFactors) {
          console.log('No buy factors, calling onSaveComplete(false)')
          onSaveComplete(false)
          return
        }
        
        // 构建最新的策略对象
        const updatedStrategy = {
          ...strategy,
          name: getStrategyName(),
          description: getStrategyDescription(),
          buy_factors: buyFactors,
          sell_factors: getSellFactors()
        }
        
        console.log('Strategy to save:', updatedStrategy)
        
        // 根据是否有ID来判断是创建还是更新
        if (strategy.id && strategy.id !== '') {
          console.log('Emitting update-strategy event')
          emit('update-strategy', updatedStrategy)
        } else {
          console.log('Emitting save-strategy event')
          emit('save-strategy', updatedStrategy)
        }
        
        await nextTick()
      }
      
      // 测试辅助方法
      const onSaveComplete = vi.fn()
      
      const setValue = async (field: string, value: string) => {
        const strategy = props.strategy?.value || props.strategy
        if (strategy) {
          if (field === 'name') {
            strategy.name = value
          } else if (field === 'description') {
            strategy.description = value
          }
          await nextTick()
        }
      }
      
      expose({
        onSaveComplete,
        setValue
      })
      
      return {
        getStrategyName,
        getStrategyDescription,
        getBuyFactors,
        getSellFactors,
        handleNameInput,
        handleDescriptionInput,
        addBuyFactor,
        removeBuyFactor,
        addSellFactor,
        removeSellFactor,
        handleSave,
        onSaveComplete
      }
    }
  }
}))



// Mock stores
vi.mock('@/stores/useStrategyStore')

describe('StrategyWorkshop.persistence.integration.test.ts - 策略保存功能集成测试', () => {
  let wrapper: VueWrapper<any>
  let mockStrategyStore: any
  let mockStrategies: Strategy[]

  beforeEach(async () => {
    const pinia = createPinia()
    setActivePinia(pinia)

    // 创建模拟策略数据
    mockStrategies = SimpleStrategyDataFactory.createStrategies(3)

    // 使用Vue的响应式系统创建mock store
    const { ref } = await import('vue')
    const currentSelectedStrategy = ref(null)
    
    // 模拟策略Store
    mockStrategyStore = {
      strategies: mockStrategies,
      currentSelectedStrategy,
      isLoading: false,
      error: null,
      fetchStrategies: vi.fn().mockResolvedValue(undefined),
      setCurrentSelectedStrategy: vi.fn().mockImplementation((strategy) => {
        currentSelectedStrategy.value = strategy
      }),
      startNewStrategyCreation: vi.fn().mockImplementation(() => {
        // 模拟创建新策略时设置默认值
        const defaultStrategy = {
          id: '',
          name: '新策略',
          description: '请添加买入和卖出因子来完善此策略',
          is_public: false,
          buy_factors: [],
          sell_factors: [],
          parameters: {}
        }
        currentSelectedStrategy.value = defaultStrategy
        return defaultStrategy
      }),
      createStrategy: vi.fn().mockResolvedValue({
        id: 'new-strategy-001',
        name: '新策略',
        description: '请添加买入和卖出因子来完善此策略',
        is_public: false,
        buy_factors: [],
        sell_factors: [],
        parameters: {}
      }),
      updateStrategy: vi.fn().mockResolvedValue({
        id: 'updated-strategy-001',
        name: '更新后的策略',
        description: '更新后的描述',
        is_public: false,
        buy_factors: [],
        sell_factors: [],
        parameters: {}
      })
    }

    vi.mocked(useStrategyStore).mockReturnValue(mockStrategyStore)
    
    // 挂载组件
    wrapper = mount(StrategyWorkshop)
    
    await nextTick()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('测试用例1：创建新策略', () => {
    it('应该能够创建新策略并调用createStrategy action', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 模拟用户点击"新建策略"按钮
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')

      // 验证startNewStrategyCreation被调用
      expect(mockStrategyStore.startNewStrategyCreation).toHaveBeenCalledTimes(1)

      // 等待策略编辑器更新
      await nextTick()

      // 确保策略编辑器有策略数据
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      expect(strategyEditor.props('strategy')).toBeDefined()
      expect(strategyEditor.props('strategy')).not.toBeNull()

      // 模拟用户在编辑器中输入策略名称和描述
      const nameInput = strategyEditor.find('[data-testid="strategy-name-input"]')
      const descriptionInput = strategyEditor.find('[data-testid="strategy-description-input"]')

      // 验证输入框存在
      expect(nameInput.exists()).toBe(true)
      expect(descriptionInput.exists()).toBe(true)

      // 设置策略名称和描述
    const strategyEditorVm = strategyEditor.vm as any
    strategyEditorVm.setValue('name', 'ATR突破策略V2')
    strategyEditorVm.setValue('description', '基于ATR的突破买入策略，带仓位管理')
    await nextTick()

      // 模拟用户添加买入因子
      const addBuyFactorBtn = strategyEditor.find('[data-testid="add-buy-factor"]')
      expect(addBuyFactorBtn.exists()).toBe(true)
      await addBuyFactorBtn.trigger('click')

      // 等待DOM更新
      await nextTick()

      // 模拟用户添加卖出因子
      const addSellFactorBtn = strategyEditor.find('[data-testid="add-sell-factor"]')
      expect(addSellFactorBtn.exists()).toBe(true)
      await addSellFactorBtn.trigger('click')

      // 等待DOM更新
      await nextTick()

      // 模拟用户点击"保存策略"按钮
      const saveBtn = strategyEditor.find('[data-testid="save-strategy-btn"]')
      expect(saveBtn.exists()).toBe(true)
      await saveBtn.trigger('click')

      // 等待保存操作完成
      await nextTick()

      // 验证createStrategy被调用
      expect(mockStrategyStore.createStrategy).toHaveBeenCalledTimes(1)

      // 验证传递给createStrategy的数据结构符合V2.0契约
      const strategyData = mockStrategyStore.createStrategy.mock.calls[0][0]

      // 基本信息验证
      expect(strategyData.name).toBe('ATR突破策略V2')
      expect(strategyData.description).toBe('基于ATR的突破买入策略，带仓位管理')

      // 验证买入因子结构
      expect(strategyData.buy_factors).toHaveLength(1)
      const buyFactor = strategyData.buy_factors[0]
      expect(buyFactor.class_name).toBe('AbuFactorBuyBreak')
      expect(buyFactor.parameters.xd).toBe(60)

      // 验证仓位管理参数在买入因子中（V2.0核心要求）
      expect(buyFactor.parameters.position).toBeDefined()
      expect(buyFactor.parameters.position.class).toBe('AbuAtrPosition')
      expect(buyFactor.parameters.position.atr_pos_base).toBe(0.15)
      expect(buyFactor.parameters.position.atr_base_price).toBe(18)
      expect(buyFactor.parameters.position.std_atr_threshold).toBe(0.5)

      // 验证卖出因子结构
      expect(strategyData.sell_factors).toHaveLength(1)
      const sellFactor = strategyData.sell_factors[0]
      expect(sellFactor.class_name).toBe('AbuFactorAtrNStop')
      expect(sellFactor.parameters.stop_loss_n).toBe(0.5)
      expect(sellFactor.parameters.stop_win_n).toBe(3.0)

      // 验证没有独立的position_strategy字段（V2.0要求）
      expect(strategyData).not.toHaveProperty('position_strategy')
    })

    it('应该能够创建包含完整V2.0数据契约的策略', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 模拟用户创建新策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 设置完整的策略信息
      await strategyEditor.find('[data-testid="strategy-name-input"]').setValue('完整策略V2')
      await strategyEditor.find('[data-testid="strategy-description-input"]').setValue('包含所有V2.0字段的完整策略')

      // 添加买入因子
      await strategyEditor.find('[data-testid="add-buy-factor"]').trigger('click')
      await nextTick()

      // 添加卖出因子
      await strategyEditor.find('[data-testid="add-sell-factor"]').trigger('click')
      await nextTick()

      // 保存策略
      await strategyEditor.find('[data-testid="save-strategy-btn"]').trigger('click')
      await nextTick()

      // 验证createStrategy被调用
      expect(mockStrategyStore.createStrategy).toHaveBeenCalledTimes(1)

      const strategyData = mockStrategyStore.createStrategy.mock.calls[0][0]

      // 验证V2.0数据契约的完整性
      expect(strategyData).toMatchObject({
        name: '完整策略V2',
        description: '包含所有V2.0字段的完整策略',
        buy_factors: expect.arrayContaining([
          expect.objectContaining({
            class_name: 'AbuFactorBuyBreak',
            parameters: expect.objectContaining({
              xd: 60,
              position: expect.objectContaining({
                class: 'AbuAtrPosition',
                atr_pos_base: 0.15,
                atr_base_price: 18,
                std_atr_threshold: 0.5
              })
            })
          })
        ]),
        sell_factors: expect.arrayContaining([
          expect.objectContaining({
            class_name: 'AbuFactorAtrNStop',
            parameters: expect.objectContaining({
              stop_loss_n: 0.5,
              stop_win_n: 3.0
            })
          })
        ])
      })

      // 验证仓位管理参数在买入因子中（V2.0核心要求）
      expect(strategyData.buy_factors[0].parameters.position).toBeDefined()
      expect(strategyData.buy_factors[0].parameters.position.class).toBe('AbuAtrPosition')
    })
  })

  describe('测试用例2：更新现有策略', () => {
    it('应该能够更新现有策略并调用updateStrategy action', async () => {
      // 设置当前选中的策略
      const existingStrategy = {
        ...mockStrategies[0],
        id: 'existing-strategy-001',
        name: '原始策略名称',
        description: '原始策略描述',
        buy_factors: [
          {
            id: 'buy-001',
            name: 'AbuFactorBuyBreak',
            class_name: 'AbuFactorBuyBreak',
            factor_type: 'buy' as const,
            parameters: {
              xd: 30,
              position: {
                class: 'AbuAtrPosition',
                atr_pos_base: 0.1,
                atr_base_price: 15,
                std_atr_threshold: 0.5
              }
            }
          }
        ],
        sell_factors: [
          {
            id: 'sell-001',
            name: 'AbuFactorAtrNStop',
            class_name: 'AbuFactorAtrNStop',
            factor_type: 'sell' as const,
            parameters: {
              stop_loss_n: 0.3,
              stop_win_n: 2.0
            }
          }
        ]
      }

      mockStrategyStore.currentSelectedStrategy.value = existingStrategy

      await nextTick()
      
      // 强制重新渲染组件
      await wrapper.vm.$forceUpdate()
      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      // 验证初始状态
      console.log('Strategy data:', JSON.stringify(existingStrategy, null, 2))
      console.log('Current selected strategy:', JSON.stringify(mockStrategyStore.currentSelectedStrategy.value, null, 2))
      let initialBuyFactors = strategyEditor.findAll('[data-testid="buy-factor-name"]')
      console.log('Initial buy factors count:', initialBuyFactors.length)
      console.log('Strategy editor HTML:', strategyEditor.html())
      expect(initialBuyFactors.length).toBe(1) // 应该有1个初始买入因子

      // 模拟用户修改策略名称
    const strategyEditorVm = strategyEditor.vm as any
    strategyEditorVm.setValue('name', '更新后的策略名称')
    await nextTick()

      // 先添加一个买入因子，确保删除后仍有买入因子
      const addBuyFactorBtn = strategyEditor.find('[data-testid="add-buy-factor"]')
      await addBuyFactorBtn.trigger('click')
      await nextTick()
      
      // 验证买入因子已添加
      let buyFactors = strategyEditor.findAll('[data-testid="buy-factor-name"]')
      console.log('Buy factors count after adding:', buyFactors.length)
      expect(buyFactors.length).toBe(2) // 原有1个 + 新增1个 = 2个

      // 模拟用户删除一个买入因子（选择第一个删除按钮）
      const removeBuyFactorBtns = strategyEditor.findAll('[data-testid="remove-buy-factor"]')
      expect(removeBuyFactorBtns.length).toBe(2) // 应该有2个删除按钮
      await removeBuyFactorBtns[0].trigger('click')

      // 等待DOM更新
      await nextTick()

      // 模拟用户点击"保存策略"按钮
      const saveBtn = strategyEditor.find('[data-testid="save-strategy-btn"]')
      await saveBtn.trigger('click')

      // 等待事件触发
      await nextTick()

      // 验证updateStrategy action被调用了
      expect(mockStrategyStore.updateStrategy).toHaveBeenCalledTimes(1)

      // 获取传递给updateStrategy的数据对象
      const updateStrategyCall = mockStrategyStore.updateStrategy.mock.calls[0]
      const strategyId = updateStrategyCall[0]
      const strategyData = updateStrategyCall[1]

      // 验证updateStrategy被调用时传递了正确的ID
      expect(strategyId).toBe(String(existingStrategy.id))

      // 验证数据对象符合StrategyUpdate模型定义
      expect(strategyData).toMatchObject({
        name: '更新后的策略名称',
        description: existingStrategy.description,
        buy_factors: expect.arrayContaining([
          expect.objectContaining({
            class_name: 'AbuFactorBuyBreak'
          })
        ]), // 删除一个后仍有买入因子
        sell_factors: expect.arrayContaining([
          expect.objectContaining({
            class_name: 'AbuFactorAtrNStop',
            parameters: expect.objectContaining({
              stop_loss_n: 0.3,
              stop_win_n: 2.0
            })
          })
        ])
      })

      // 验证买入因子数量减少了1个
      expect(strategyData.buy_factors).toHaveLength(1)

      // 验证卖出因子保持不变
      expect(strategyData.sell_factors).toHaveLength(1)
    })

    it('应该能够更新策略的因子参数', async () => {
      // 设置当前选中的策略
      const existingStrategy = {
        ...mockStrategies[0],
        name: '参数更新测试策略',
        description: '原始策略描述',
        buy_factors: [
          {
            id: 'buy-001',
            name: 'AbuFactorBuyBreak',
            class_name: 'AbuFactorBuyBreak',
            factor_type: 'buy' as const,
            parameters: {
              xd: 20,
              position: {
                class: 'AbuAtrPosition',
                atr_pos_base: 0.05,
                atr_base_price: 10,
                std_atr_threshold: 0.3
              }
            }
          }
        ],
        sell_factors: []
      }

      mockStrategyStore.currentSelectedStrategy.value = existingStrategy

      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 模拟用户修改策略名称和描述
    const strategyEditorVm = strategyEditor.vm as any
    strategyEditorVm.setValue('name', '参数更新测试策略')
    strategyEditorVm.setValue('description', '更新后的策略描述')
    await nextTick()

      // 模拟用户添加新的卖出因子
      const addSellFactorBtn = strategyEditor.find('[data-testid="add-sell-factor"]')
      await addSellFactorBtn.trigger('click')

      // 等待DOM更新
      await nextTick()

      // 保存策略
      const saveBtn = strategyEditor.find('[data-testid="save-strategy-btn"]')
      await saveBtn.trigger('click')

      // 等待事件触发
      await nextTick()

      // 验证updateStrategy被调用
      expect(mockStrategyStore.updateStrategy).toHaveBeenCalledTimes(1)

      const strategyData = mockStrategyStore.updateStrategy.mock.calls[0][1]

      // 验证更新后的数据
      expect(strategyData).toMatchObject({
        name: '参数更新测试策略',
        description: '更新后的策略描述',
        buy_factors: expect.arrayContaining([
          expect.objectContaining({
            class_name: 'AbuFactorBuyBreak',
            parameters: expect.objectContaining({
              xd: 20,
              position: expect.objectContaining({
                class: 'AbuAtrPosition',
                atr_pos_base: 0.05,
                atr_base_price: 10,
                std_atr_threshold: 0.3
              })
            })
          })
        ]),
        sell_factors: expect.arrayContaining([
          expect.objectContaining({
            class_name: 'AbuFactorAtrNStop',
            parameters: expect.objectContaining({
              stop_loss_n: 0.5,
              stop_win_n: 3.0
            })
          })
        ])
      })

      // 验证买入因子参数保持不变
      expect(strategyData.buy_factors[0].parameters.xd).toBe(20)
      expect(strategyData.buy_factors[0].parameters.position.atr_pos_base).toBe(0.05)

      // 验证新增的卖出因子
      expect(strategyData.sell_factors).toHaveLength(1)
      expect(strategyData.sell_factors[0].class_name).toBe('AbuFactorAtrNStop')
    })
  })

  describe('数据契约V2.0合规性验证', () => {
    it('应该确保仓位管理参数正确整合到买入因子中', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 创建新策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 添加买入因子
      await strategyEditor.find('[data-testid="add-buy-factor"]').trigger('click')
      await nextTick()

      // 保存策略
      await strategyEditor.find('[data-testid="save-strategy-btn"]').trigger('click')
      await nextTick()

      const strategyData = mockStrategyStore.createStrategy.mock.calls[0][0]

      // 验证V2.0核心要求：仓位管理参数必须在买入因子的parameters中
      expect(strategyData.buy_factors[0].parameters.position).toBeDefined()
      expect(strategyData.buy_factors[0].parameters.position.class).toBe('AbuAtrPosition')
      expect(strategyData.buy_factors[0].parameters.position).toHaveProperty('atr_pos_base')
      expect(strategyData.buy_factors[0].parameters.position).toHaveProperty('atr_base_price')
      expect(strategyData.buy_factors[0].parameters.position).toHaveProperty('std_atr_threshold')

      // 验证没有独立的position_strategy字段
      expect(strategyData).not.toHaveProperty('position_strategy')
    })

    it('应该支持不同的仓位管理类', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 创建新策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 添加买入因子
      await strategyEditor.find('[data-testid="add-buy-factor"]').trigger('click')
      await nextTick()

      // 保存策略
      await strategyEditor.find('[data-testid="save-strategy-btn"]').trigger('click')
      await nextTick()

      const strategyData = mockStrategyStore.createStrategy.mock.calls[0][0]

      // 验证默认使用AbuAtrPosition
      expect(strategyData.buy_factors[0].parameters.position.class).toBe('AbuAtrPosition')

      // 验证AbuAtrPosition的参数结构
      const positionParams = strategyData.buy_factors[0].parameters.position
      expect(positionParams).toHaveProperty('atr_pos_base')
      expect(positionParams).toHaveProperty('atr_base_price')
      expect(positionParams).toHaveProperty('std_atr_threshold')
    })
  })

  describe('错误处理和边界情况', () => {
    it('应该能够处理保存失败的情况', async () => {
      const saveError = new Error('保存失败')
      mockStrategyStore.createStrategy.mockRejectedValue(saveError)

      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 创建新策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 添加买入因子
      await strategyEditor.find('[data-testid="add-buy-factor"]').trigger('click')
      await nextTick()

      // 保存策略
      await strategyEditor.find('[data-testid="save-strategy-btn"]').trigger('click')
      await nextTick()

      // 验证createStrategy被调用
      expect(mockStrategyStore.createStrategy).toHaveBeenCalledTimes(1)

      // 验证错误被正确处理（通过onSaveComplete回调）
      const strategyEditorInstance = strategyEditor.vm
      expect(strategyEditorInstance.onSaveComplete).toBeDefined()
    })

    it('应该阻止保存没有买入因子的空策略', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 创建新策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      await nextTick()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 不添加任何因子，直接保存
      const saveBtn = strategyEditor.find('[data-testid="save-strategy-btn"]')
      await saveBtn.trigger('click')
      await nextTick()

      // 验证createStrategy没有被调用，因为缺少买入因子
      expect(mockStrategyStore.createStrategy).toHaveBeenCalledTimes(0)

      // 验证保存失败的回调被调用
      const strategyEditorInstance = strategyEditor.vm
      expect(strategyEditorInstance.onSaveComplete).toBeDefined()
    })
  })
})
