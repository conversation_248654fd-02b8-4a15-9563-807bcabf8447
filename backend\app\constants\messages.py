"""消息常量管理模块

统一管理系统中所有的错误消息、成功消息和提示信息，
提高代码的可维护性和一致性。
"""

class StrategyMessages:
    """策略相关消息常量"""
    
    # 成功消息
    EXECUTION_SUCCESS = "策略执行成功完成"
    CREATION_SUCCESS = "策略创建成功"
    UPDATE_SUCCESS = "策略更新成功"
    DELETE_SUCCESS = "策略删除成功"
    GET_SUCCESS = "策略获取成功"
    LIST_SUCCESS = "策略列表获取成功"
    
    # 错误消息
    EXECUTION_FAILED = "策略执行失败"
    FACTOR_CONVERSION_FAILED = "因子转换失败"
    MISSING_CAPITAL = "缺少初始资金参数"
    NO_BUY_FACTORS = "策略必须包含至少一个买入因子"
    INVALID_PARAMETERS = "策略参数无效"
    ABUPY_CORE_ERROR = "执行abupy回测时发生未知核心错误"
    NOT_FOUND = "未找到指定的策略"
    CREATION_FAILED = "策略创建失败"
    UPDATE_FAILED = "策略更新失败"
    DELETE_FAILED = "策略删除失败"
    
    # 验证消息
    INVALID_DATE_RANGE = "日期范围无效"
    INVALID_SYMBOL = "股票代码无效"
    INSUFFICIENT_DATA = "数据不足，无法执行策略"
    
    @staticmethod
    def strategy_not_found(strategy_id: str) -> str:
        """生成策略未找到的错误消息"""
        return f"未找到ID为 {strategy_id} 的策略"

class MarketDataMessages:
    """市场数据相关消息常量"""
    
    # 成功消息
    DATA_LOADED_SUCCESS = "市场数据加载成功"
    CACHE_HIT = "缓存命中，数据获取成功"
    
    # 错误消息
    DATA_LOAD_FAILED = "市场数据加载失败"
    SYMBOL_NOT_FOUND = "未找到指定股票代码的数据"
    DATA_SOURCE_ERROR = "数据源连接错误"
    CACHE_ERROR = "缓存操作失败"
    
    # 警告消息
    DATA_INCOMPLETE = "数据不完整，可能影响分析结果"
    OUTDATED_DATA = "数据已过期，建议更新"

class SystemMessages:
    """系统级消息常量"""
    
    # 通用成功消息
    OPERATION_SUCCESS = "操作成功完成"
    SAVE_SUCCESS = "保存成功"
    LOAD_SUCCESS = "加载成功"
    
    # 通用错误消息
    OPERATION_FAILED = "操作失败"
    INVALID_INPUT = "输入参数无效"
    PERMISSION_DENIED = "权限不足"
    RESOURCE_NOT_FOUND = "资源未找到"
    INTERNAL_ERROR = "系统内部错误"
    
    # 验证消息
    VALIDATION_FAILED = "数据验证失败"
    REQUIRED_FIELD_MISSING = "必填字段缺失"
    FORMAT_ERROR = "数据格式错误"

class DatabaseMessages:
    """数据库相关消息常量"""
    
    # 成功消息
    CONNECTION_SUCCESS = "数据库连接成功"
    TRANSACTION_SUCCESS = "事务提交成功"
    
    # 错误消息
    CONNECTION_FAILED = "数据库连接失败"
    TRANSACTION_FAILED = "事务执行失败"
    CONSTRAINT_VIOLATION = "数据约束违反"
    DUPLICATE_ENTRY = "数据重复"
    
    # 警告消息
    SLOW_QUERY = "查询执行缓慢"
    CONNECTION_POOL_FULL = "连接池已满"

# 便捷访问函数
def get_error_message(category: str, error_type: str, details: str = None) -> str:
    """获取格式化的错误消息
    
    Args:
        category: 消息类别 (strategy, market_data, system, database)
        error_type: 错误类型
        details: 详细信息
        
    Returns:
        str: 格式化的错误消息
    """
    message_classes = {
        'strategy': StrategyMessages,
        'market_data': MarketDataMessages,
        'system': SystemMessages,
        'database': DatabaseMessages
    }
    
    message_class = message_classes.get(category)
    if not message_class:
        return f"未知错误类别: {category}"
    
    base_message = getattr(message_class, error_type.upper(), f"未知错误类型: {error_type}")
    
    if details:
        return f"{base_message}: {details}"
    return base_message

def get_success_message(category: str, success_type: str) -> str:
    """获取成功消息
    
    Args:
        category: 消息类别
        success_type: 成功类型
        
    Returns:
        str: 成功消息
    """
    message_classes = {
        'strategy': StrategyMessages,
        'market_data': MarketDataMessages,
        'system': SystemMessages,
        'database': DatabaseMessages
    }
    
    message_class = message_classes.get(category)
    if not message_class:
        return f"未知消息类别: {category}"
    
    return getattr(message_class, success_type.upper(), f"未知成功类型: {success_type}")