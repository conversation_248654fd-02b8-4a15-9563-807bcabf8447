import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { setActivePinia, createPinia } from 'pinia';
import { getStrategies, getStrategy, createStrategy, updateStrategy, deleteStrategy } from '../../../src/api/strategy';
import { useAppStore } from '../../../src/stores/app';
import { SimpleStrategyDataFactory } from '../../factories/SimpleStrategyDataFactory';
import { strategyHandlers } from '../../mocks/simple/strategyHandlers';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../../src/api/types';

/**
 * 策略API测试套件 - Extended版本
 * 
 * 本测试文件在TDD基础上扩展业务逻辑测试，包括：
 * - 参数验证和边界条件
 * - 状态管理集成
 * - 复杂的错误处理场景
 * - 业务规则验证
 */

const server = setupServer(...strategyHandlers);

describe('Strategy API - Extended', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen();
  });

  afterEach(() => {
    vi.clearAllMocks();
    server.resetHandlers();
    const store = useAppStore();
    store.$reset();
  });

  afterAll(() => {
    server.close();
  });

  describe('getStrategies with parameters', () => {
    it('should handle pagination parameters correctly', async () => {
      const strategies = await getStrategies({ page: 1, page_size: 5 });
      expect(strategies).toBeDefined();
      expect(Array.isArray(strategies)).toBe(true);
    });

    it('should handle search and filter parameters', async () => {
      const strategies = await getStrategies({ 
        search: 'test',
        is_public: true,
        author: 'specific_author'
      });
      expect(strategies).toBeDefined();
    });

    it('should set and clear loading state correctly', async () => {
      const store = useAppStore();
      const promise = getStrategies();
      
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(true);
      
      await promise;
      await new Promise(resolve => setTimeout(resolve, 0));
      expect(store.isLoading).toBe(false);
    });
  });

  describe('createStrategy with validation', () => {
    it('should validate required fields', async () => {
      const invalidStrategy = SimpleStrategyDataFactory.createInvalid();
      await expect(createStrategy(invalidStrategy)).rejects.toThrow();
    });

    it('should validate factor configurations', async () => {
      const strategyWithInvalidFactors = SimpleStrategyDataFactory.createWithInvalidFactors();
      await expect(createStrategy(strategyWithInvalidFactors)).rejects.toThrow();
    });

    it('should validate parameter ranges', async () => {
      const strategyWithInvalidParams = SimpleStrategyDataFactory.createWithInvalidParameters();
      await expect(createStrategy(strategyWithInvalidParams)).rejects.toThrow();
    });
  });

  describe('updateStrategy with business rules', () => {
    it('should prevent updating public strategies by non-owners', async () => {
      const updates: UpdateStrategyRequest = { name: 'Unauthorized Update' };
      await expect(updateStrategy('public-strategy-id', updates)).rejects.toThrow('Access denied');
    });

    it('should handle concurrent modification conflicts', async () => {
      const updates: UpdateStrategyRequest = { name: 'Conflicted Update' };
      await expect(updateStrategy('conflict-strategy-id', updates)).rejects.toThrow('Update conflict');
    });
  });

  describe('deleteStrategy with constraints', () => {
    it('should prevent deleting strategies with active backtests', async () => {
      await expect(deleteStrategy('active-backtest-strategy')).rejects.toThrow('Cannot delete strategy with active backtests');
    });

    it('should prevent deleting public strategies by non-owners', async () => {
      await expect(deleteStrategy('public-strategy-non-owner')).rejects.toThrow('Access denied');
    });
  });

  describe('error handling and recovery', () => {
    it('should handle network timeout gracefully', async () => {
      server.use(
        http.get('/api/strategies', () => {
          return new Promise(() => {}); // Never resolves (timeout simulation)
        })
      );

      await expect(getStrategies()).rejects.toThrow('timeout');
    });

    it('should handle malformed response data', async () => {
      server.use(
        http.get('/api/strategies', () => {
          return HttpResponse.json({ invalid: 'response' });
        })
      );

      await expect(getStrategies()).rejects.toThrow();
    });
  });
});