import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import BacktestView from '@/views/BacktestView.vue'
import { useBacktestStore } from '@/stores/useBacktestStore'
import type { BacktestResult, BacktestConfig } from '@/api/types/backtest'

// 模拟子组件
vi.mock('@/components/BacktestForm.vue', () => ({
  default: {
    name: 'BacktestForm',
    template: '<div data-testid="backtest-form">BacktestForm</div>',
    emits: ['submit', 'reset'],
    props: ['loading']
  }
}))

vi.mock('@/components/BacktestResults.vue', () => ({
  default: {
    name: 'BacktestResults', 
    template: '<div data-testid="backtest-results">BacktestResults</div>',
    props: ['result', 'loading']
  }
}))

vi.mock('@/components/BacktestAnalysis.vue', () => ({
  default: {
    name: 'BacktestAnalysis',
    template: '<div data-testid="backtest-analysis">BacktestAnalysis</div>',
    props: ['result', 'metrics']
  }
}))

// 模拟回测Store
vi.mock('@/stores/useBacktestStore')

describe('BacktestView.vue - TDD基础测试', () => {
  let wrapper: VueWrapper<any>
  let mockBacktestStore: any

  const mockBacktestResult: BacktestResult = {
    task_id: 'test-task-001',
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 125000,
    total_return: 0.25,
    sharpe_ratio: 1.5,
    max_drawdown: 0.08,
    trades: [],
    daily_returns: []
  }

  beforeEach(() => {
    const pinia = createPinia()
    setActivePinia(pinia)

    // 修正：使用实际的store API
    mockBacktestStore = {
      isBacktesting: false,
      backtestResult: null,
      backtestError: '',
      currentBacktestTask: null,
      backtestProgress: 0,
      isLoadingResults: false,
      currentMetrics: null,
      startBacktest: vi.fn(),
      resetBacktestState: vi.fn(),
      loadBacktestResults: vi.fn(),
      stopCurrentBacktest: vi.fn(),
      clearError: vi.fn()
    }

    vi.mocked(useBacktestStore).mockReturnValue(mockBacktestStore)

    wrapper = mount(BacktestView, {
      global: {
        plugins: [pinia]
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染回测视图标题', () => {
      expect(wrapper.find('h1').text()).toBe('回测分析')
    })

    it('应该显示专业副标题', () => {
      expect(wrapper.find('.subtitle').text()).toBe('专业量化策略回测平台')
    })

    it('应该始终渲染回测表单', () => {
      expect(wrapper.find('[data-testid="backtest-form"]').exists()).toBe(true)
    })

    it('当没有结果时不应显示结果和分析组件', () => {
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(false)
      expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(false)
    })
  })

  describe('条件渲染测试', () => {
    it('当有回测结果时应显示结果组件', async () => {
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      await nextTick()

      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })

    it('当有回测结果时应显示分析组件', async () => {
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      await nextTick()

      expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(true)
    })
  })

  describe('Store集成测试', () => {
    it('应该正确使用回测Store', () => {
      expect(useBacktestStore).toHaveBeenCalled()
    })

    it('应该响应Store状态变化', async () => {
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      await nextTick()

      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
    })
  })

  describe('样式和布局测试', () => {
    it('应该具有正确的CSS类', () => {
      expect(wrapper.find('.backtest-view').exists()).toBe(true)
      expect(wrapper.find('.backtest-header').exists()).toBe(true)
      expect(wrapper.find('.backtest-content').exists()).toBe(true)
    })

    it('应该使用网格布局', () => {
      const content = wrapper.find('.backtest-content')
      expect(content.classes()).toContain('backtest-content')
    })
  })

  describe('生命周期测试', () => {
    it('组件挂载时应初始化状态', () => {
      expect(wrapper.exists()).toBe(true)
    })
  })
})