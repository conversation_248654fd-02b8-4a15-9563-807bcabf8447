import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import BacktestView from '@/views/BacktestView.vue'
import { useBacktestStore } from '@/stores/useBacktestStore'
import type { BacktestResult, BacktestConfig } from '@/api/types/backtest'
import { BacktestStatus } from '@/api/types/backtest'

// 模拟子组件
vi.mock('@/components/BacktestForm.vue', () => ({
  default: {
    name: 'BacktestForm',
    template: '<div data-testid="backtest-form">BacktestForm</div>',
    emits: ['submit', 'reset'],
    props: ['loading']
  }
}))

vi.mock('@/components/BacktestResults.vue', () => ({
  default: {
    name: 'BacktestResults', 
    template: '<div data-testid="backtest-results">BacktestResults</div>',
    props: ['result', 'loading']
  }
}))

vi.mock('@/components/BacktestAnalysis.vue', () => ({
  default: {
    name: 'BacktestAnalysis',
    template: '<div data-testid="backtest-analysis">BacktestAnalysis</div>',
    props: ['result', 'metrics']
  }
}))

// 模拟回测Store
vi.mock('@/stores/useBacktestStore')

describe('BacktestView.vue - 扩展功能测试', () => {
  let wrapper: VueWrapper<any>
  let mockBacktestStore: any

  const mockBacktestResult: BacktestResult = {
    task_id: 'test-task-001',
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 125000,
    total_return: 0.25,
    sharpe_ratio: 1.5,
    max_drawdown: 0.08,
    trades: [],
    daily_returns: []
  }

  const mockBacktestConfig: BacktestConfig = {
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000
  }

  beforeEach(() => {
    const pinia = createPinia()
    setActivePinia(pinia)

    // 修正：使用实际的store API
    mockBacktestStore = {
      isBacktesting: false,
      backtestResult: null,
      backtestError: '',
      currentBacktestTask: null,
      backtestProgress: 0,
      isLoadingResults: false,
      startBacktest: vi.fn(),
      resetBacktestState: vi.fn(),
      loadBacktestResults: vi.fn(),
      stopCurrentBacktest: vi.fn(),
      clearError: vi.fn()
    }

    vi.mocked(useBacktestStore).mockReturnValue(mockBacktestStore)

    wrapper = mount(BacktestView, {
      global: {
        plugins: [pinia]
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  // 完整回测业务流程测试
  describe('回测完整业务流程', () => {
    it('应该支持完整的回测执行流程', async () => {
      // 1. 初始状态验证
      expect(wrapper.find('[data-testid="backtest-form"]').exists()).toBe(true)
      expect(mockBacktestStore.isBacktesting).toBe(false)
      
      // 2. 提交回测配置
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)
      
      // 3. 验证startBacktest被调用
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
      
      // 4. 模拟回测运行状态
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      expect(form.props('loading')).toBe(true)
      
      // 5. 模拟回测完成
      mockBacktestStore.isBacktesting = false
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      
      // 6. 验证结果显示
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(true)
    })

    it('应该在回测运行时禁用表单提交', async () => {
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
    })

    it('应该处理回测中断场景', async () => {
      // 模拟回测运行中
      mockBacktestStore.isBacktesting = true
      mockBacktestStore.currentBacktestTask = { id: 'test-task', status: BacktestStatus.RUNNING }
      await wrapper.vm.$forceUpdate()
      
      // 验证可以停止回测
      expect(mockBacktestStore.stopCurrentBacktest).toBeDefined()
    })
  })

  // 回测错误处理测试
  describe('回测错误处理', () => {
    it('应该处理回测启动失败', async () => {
      const errorMessage = '策略配置无效'
      mockBacktestStore.startBacktest.mockRejectedValue(new Error(errorMessage))
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)
      
      // 等待异步操作完成
      await nextTick()
      
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
    })

    it('应该显示回测错误信息', async () => {
      const errorMessage = '回测执行失败'
      mockBacktestStore.backtestError = errorMessage
      await wrapper.vm.$forceUpdate()
      
      // 验证错误状态反映在UI上
      expect(mockBacktestStore.backtestError).toBe(errorMessage)
    })

    it('应该允许用户从错误状态恢复', async () => {
      mockBacktestStore.backtestError = '测试错误'
      await wrapper.vm.$forceUpdate()
      
      // 模拟清除错误
      mockBacktestStore.clearError()
      expect(mockBacktestStore.clearError).toHaveBeenCalled()
    })
  })

  // 组件Props传递验证
  describe('组件Props传递', () => {
    it('应该正确传递Props给Results组件', async () => {
      mockBacktestStore.backtestResult = mockBacktestResult
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const results = wrapper.findComponent({ name: 'BacktestResults' })
      expect(results.props('result')).toEqual(mockBacktestResult)
      expect(results.props('loading')).toBe(true)
    })
    
    it('应该正确传递Props给Analysis组件', async () => {
      const mockMetrics = { sharpe: 1.5, maxDrawdown: 0.08 }
      mockBacktestStore.backtestResult = mockBacktestResult
      mockBacktestStore.currentMetrics = mockMetrics
      await wrapper.vm.$forceUpdate()
      
      const analysis = wrapper.findComponent({ name: 'BacktestAnalysis' })
      expect(analysis.props('result')).toEqual(mockBacktestResult)
      expect(analysis.props('metrics')).toEqual(mockMetrics)
    })

    it('应该正确传递loading状态给Form组件', async () => {
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
    })
  })

  // 响应式更新测试
  describe('响应式更新测试', () => {
    it('Store状态变化应立即反映在UI上', async () => {
      // 初始状态：无结果
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(false)
      
      // 更新状态：有结果
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      
      // 验证UI更新
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })

    it('应该处理并发状态更新', async () => {
      // 模拟快速状态变化
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      mockBacktestStore.backtestResult = mockBacktestResult
      mockBacktestStore.isBacktesting = false
      await wrapper.vm.$forceUpdate()
      
      // 验证最终状态
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(false)
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })
  })

  describe('事件处理测试', () => {
    it('应该处理回测提交事件', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)

      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
    })

    it('应该处理表单重置事件', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('reset')

      expect(mockBacktestStore.resetBacktestState).toHaveBeenCalled()
    })
  })
})