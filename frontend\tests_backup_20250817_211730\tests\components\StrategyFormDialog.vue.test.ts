import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia, getActivePinia } from 'pinia'
import { nextTick } from 'vue'
import StrategyFormDialog from '@/components/StrategyFormDialog.vue'
import { useFactorsStore } from '@/stores/useFactorsStore'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElDialog: {
    name: 'ElDialog',
    props: ['modelValue', 'title', 'width'],
    template: '<div v-if="modelValue" class="el-dialog"><div class="el-dialog__header"><span class="el-dialog__title">{{ title }}</span></div><div class="el-dialog__body"><slot /></div><div class="el-dialog__footer"><slot name="footer" /></div></div>'
  },
  ElForm: {
    name: 'ElForm',
    props: ['model', 'rules', 'labelWidth'],
    template: '<form class="el-form"><slot /></form>'
  },
  ElFormItem: {
    name: 'ElFormItem',
    props: ['label', 'prop', 'required'],
    template: '<div class="el-form-item"><label class="el-form-item__label">{{ label }}</label><div class="el-form-item__content"><slot /></div></div>'
  },
  ElInput: {
    name: 'ElInput',
    props: ['modelValue', 'placeholder', 'type'],
    template: '<input class="el-input" :value="modelValue" :placeholder="placeholder" :type="type" />'
  },
  ElButton: {
    name: 'ElButton',
    props: ['type', 'size', 'disabled'],
    template: '<button class="el-button" :disabled="disabled"><slot /></button>'
  },
  ElSelect: {
    name: 'ElSelect',
    props: ['modelValue', 'placeholder'],
    template: '<select class="el-select"><option>{{ placeholder }}</option><slot /></select>'
  },
  ElOption: {
    name: 'ElOption',
    props: ['label', 'value'],
    template: '<option class="el-option" :value="value">{{ label }}</option>'
  },
  ElTag: {
    name: 'ElTag',
    props: ['type', 'size'],
    template: '<span class="el-tag"><slot /></span>'
  },
  ElRate: {
    name: 'ElRate',
    props: ['modelValue', 'disabled', 'showScore'],
    template: '<div class="el-rate">{{ modelValue || 0 }}</div>'
  },
  ElInputNumber: {
    name: 'ElInputNumber',
    props: ['modelValue', 'min', 'max', 'step', 'precision', 'disabled', 'placeholder'],
    template: '<input class="el-input-number" type="number" :value="modelValue" :min="min" :max="max" :step="step" :disabled="disabled" :placeholder="placeholder" />'
  }
}))

// 模拟因子Store
vi.mock('@/stores/useFactorsStore')

describe('StrategyFormDialog.vue - TDD测试', () => {
  let wrapper: VueWrapper<any>
  let mockFactorsStore: any

  beforeEach(async () => {
    // 避免重复创建 Pinia 实例
    if (!getActivePinia()) {
      const pinia = createPinia()
      setActivePinia(pinia)
    }

    // 模拟因子Store
    mockFactorsStore = {
      factors: [],
      isLoading: false,
      error: null,
      fetchFactors: vi.fn()
    }

    vi.mocked(useFactorsStore).mockReturnValue(mockFactorsStore)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('组件挂载时的行为测试 - TDD', () => {
    it('当组件挂载时，应调用 factorsStore.fetchFactors 获取因子数据 - TDD', async () => {
      // 设置初始状态
      mockFactorsStore.factors = []
      mockFactorsStore.isLoading = false
      mockFactorsStore.error = null

      // 挂载组件
      wrapper = mount(StrategyFormDialog, {
        props: {
          modelValue: true, // 对话框显示状态
          title: '新建策略'
        }
      })

      await nextTick()

      // 验证 fetchFactors 被调用
      expect(mockFactorsStore.fetchFactors).toHaveBeenCalledTimes(1)
    })

    it('组件挂载时，即使对话框初始为关闭状态，也应预加载因子数据 - TDD', async () => {
      // 设置对话框为关闭状态
      mockFactorsStore.factors = []
      mockFactorsStore.isLoading = false
      mockFactorsStore.error = null

      // 挂载组件（对话框关闭）
      wrapper = mount(StrategyFormDialog, {
        props: {
          modelValue: false, // 对话框关闭状态
          title: '新建策略'
        },

      })

      await nextTick()

      // 验证即使对话框关闭，也应该预加载因子数据
      expect(mockFactorsStore.fetchFactors).toHaveBeenCalledTimes(1)
    })
  })

  describe('对话框渲染测试 - TDD', () => {
    it('当 modelValue 为 true 时，应显示对话框 - TDD', async () => {
      wrapper = mount(StrategyFormDialog, {
        props: {
          modelValue: true,
          title: '策略配置'
        },

      })

      await nextTick()

      // 验证对话框存在且可见
      expect(wrapper.find('.el-dialog').exists()).toBe(true)
      expect(wrapper.find('.el-dialog__title').text()).toBe('策略配置')
    })

    it('当 modelValue 为 false 时，应隐藏对话框 - TDD', async () => {
      wrapper = mount(StrategyFormDialog, {
        props: {
          modelValue: false,
          title: '策略配置'
        },

      })

      await nextTick()

      // 验证对话框不存在或不可见
      expect(wrapper.find('.el-dialog').exists()).toBe(false)
    })
  })

  describe('表单交互测试 - TDD', () => {
    it('应该渲染策略名称输入框 - TDD', async () => {
      wrapper = mount(StrategyFormDialog, {
        props: {
          modelValue: true,
          title: '新建策略'
        },

      })

      await nextTick()

      // 验证表单元素存在
      expect(wrapper.find('.el-form').exists()).toBe(true)
      expect(wrapper.find('.el-input').exists()).toBe(true)
    })

    it('应该有保存和取消按钮 - TDD', async () => {
      wrapper = mount(StrategyFormDialog, {
        props: {
          modelValue: true,
          title: '新建策略'
        },

      })

      await nextTick()

      // 验证按钮存在
      const buttons = wrapper.findAll('.el-button')
      expect(buttons.length).toBeGreaterThanOrEqual(2) // 至少有保存和取消按钮
    })
  })

  describe('添加因子完整交互流程测试 - TDD', () => {
    let mockStrategyEditorStore: any

    beforeEach(() => {
      // 模拟 useStrategyEditorStore
      mockStrategyEditorStore = {
        currentSelectedStrategy: null,
        isEditing: false,
        addBuyFactor: vi.fn(),
        addSellFactor: vi.fn(),
        setCurrentSelectedStrategy: vi.fn(),
        clearCurrentSelectedStrategy: vi.fn()
      }

      // 设置全局mock（用于组件中的测试环境检测）
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      if (typeof window !== 'undefined') {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        window.__vitest_mocks__ = {
          strategyEditorStore: mockStrategyEditorStore
        }
      }

      // 模拟导入 useStrategyEditorStore，支持动态导入
      vi.doMock('@/stores/useStrategyEditorStore', () => ({
        useStrategyEditorStore: () => mockStrategyEditorStore,
        default: () => mockStrategyEditorStore
      }))
    })

    describe('测试场景1: 选择因子到参数配置的视图切换 - TDD', () => {
      it('用户选择因子并点击"下一步"后，应切换到参数配置视图 - TDD', async () => {
        // 设置带有因子的Mock数据
        const mockFactorWithParams = {
          id: 'factor-001',
          name: 'AbuDoubleMaBuy',
          description: '双均线交叉买入策略',
          factor_type: 'buy',
          class_name: 'AbuDoubleMaBuy',
          parameters: {
            fast_ma: { type: 'number', default: 5, comment: '短期均线' },
            slow_ma: { type: 'number', default: 20, comment: '长期均线' }
          }
        }

        mockFactorsStore.factors = [mockFactorWithParams]
        mockFactorsStore.isLoading = false
        mockFactorsStore.error = null

        // 不传递 dialogView prop，让组件自己管理内部状态
        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '添加买入因子',
            currentFactorType: 'buy'
          }
        })

        await nextTick()

        // 验证初始状态为选择视图
        expect(wrapper.find('[data-testid="factor-selection-dialog"]').exists()).toBe(true)
        expect(wrapper.find('[data-testid="factor-parameter-config-dialog"]').exists()).toBe(false)

        // 模拟用户选择因子
        const factorCard = wrapper.find('[data-testid="factor-item"]')
        expect(factorCard.exists()).toBe(true)
        await factorCard.trigger('click')

        await nextTick()

        // 验证"下一步"按钮存在且可点击
        const nextButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('下一步')
        )
        expect(nextButton).toBeDefined()
        expect(nextButton.attributes('disabled')).toBeFalsy()

        // 模拟点击"下一步"
        await nextButton.trigger('click')
        await nextTick()

        // 验证视图已切换到参数配置
        expect(wrapper.find('[data-testid="factor-selection-dialog"]').exists()).toBe(false)
        expect(wrapper.find('[data-testid="factor-parameter-config-dialog"]').exists()).toBe(true)

        // 验证参数配置表单已渲染
        expect(wrapper.find('.parameter-form-container').exists()).toBe(true)
        expect(wrapper.find('.el-form-item').exists()).toBe(true)

        // 验证参数表单项包含预期的字段
        const formItems = wrapper.findAll('.el-form-item')
        const formLabels = formItems.map(item => item.find('.el-form-item__label').text())
        expect(formLabels).toContain('短期均线')
        expect(formLabels).toContain('长期均线')
      })

      it('用户未选择因子时，"下一步"按钮应被禁用 - TDD', async () => {
        const mockFactor = {
          id: 'factor-002',
          name: 'AbuRSIBuy',
          description: 'RSI超卖买入策略',
          factor_type: 'buy',
          class_name: 'AbuRSIBuy'
        }

        mockFactorsStore.factors = [mockFactor]

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '添加买入因子'
          }
        })

        await nextTick()

        // 验证"下一步"按钮被禁用
        const nextButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('下一步')
        )
        expect(nextButton).toBeDefined()
        expect(nextButton.attributes('disabled')).toBeDefined()
      })

      it('对于无参数的因子，应直接显示"无需额外参数"提示 - TDD', async () => {
        const mockFactorNoParams = {
          id: 'factor-003',
          name: 'AbuSimpleBuy',
          description: '简单买入策略',
          factor_type: 'buy',
          class_name: 'AbuSimpleBuy',
          parameters: {} // 空参数
        }

        mockFactorsStore.factors = [mockFactorNoParams]

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '添加买入因子',
            dialogView: 'configuration',
            selectedFactor: mockFactorNoParams
          }
        })

        await nextTick()

        // 验证显示"无需额外参数"提示
        expect(wrapper.find('.no-parameters-container').exists()).toBe(true)
        expect(wrapper.text()).toContain('该因子无需额外参数')
        expect(wrapper.text()).toContain('点击"确认添加"将其添加到策略中')
      })
    })

    describe('测试场景2: 参数配置到最终确认的完整流程 - TDD', () => {
      it('用户填写参数并点击"确认添加"后，应调用 addBuyFactor 并关闭对话框 - TDD', async () => {
        const mockSelectedFactor = {
          id: 'factor-004',
          name: 'AbuDoubleMaBuy',
          description: '双均线交叉买入策略',
          factor_type: 'buy',
          class_name: 'AbuDoubleMaBuy',
          parameters: {
            fast_ma: { type: 'number', default: 5, comment: '短期均线' },
            slow_ma: { type: 'number', default: 20, comment: '长期均线' }
          }
        }

        // 模拟用户输入的参数值
        const userInputParams = {
          fast_ma: 10,
          slow_ma: 30
        }

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '参数配置 - 双均线交叉买入策略',
            dialogView: 'configuration',
            selectedFactor: mockSelectedFactor,
            formModel: userInputParams
          }
        })

        await nextTick()

        // 验证参数配置视图已渲染
        expect(wrapper.find('[data-testid="factor-parameter-config-dialog"]').exists()).toBe(true)

        // 模拟用户修改参数值
        const fastMaInput = wrapper.findAll('.el-input-number').find(input => 
          input.element.parentElement?.querySelector('.el-form-item__label')?.textContent === '短期均线'
        )
        const slowMaInput = wrapper.findAll('.el-input-number').find(input => 
          input.element.parentElement?.querySelector('.el-form-item__label')?.textContent === '长期均线'
        )

        if (fastMaInput) {
          await fastMaInput.setValue('10')
        }
        if (slowMaInput) {
          await slowMaInput.setValue('30')
        }

        await nextTick()

        // 查找并点击"确认添加"按钮
        const confirmButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('确认添加')
        )
        expect(confirmButton).toBeDefined()

        await confirmButton.trigger('click')
        await nextTick()

        // 验证 addBuyFactor 被调用并传递了正确的参数
        expect(mockStrategyEditorStore.addBuyFactor).toHaveBeenCalledTimes(1)
        
        const callArgs = mockStrategyEditorStore.addBuyFactor.mock.calls[0][0]
        expect(callArgs).toEqual({
          id: 'factor-004',
          name: 'AbuDoubleMaBuy',
          description: '双均线交叉买入策略',
          factor_type: 'buy',
          class_name: 'AbuDoubleMaBuy',
          parameters: userInputParams
        })

        // 验证对话框被关闭（通过 modelValue 事件触发）
        expect(wrapper.emitted('update:modelValue')).toBeTruthy()
        expect(wrapper.emitted('update:modelValue')[0]).toEqual([false])
      })

      it('用户添加卖出因子时，应调用 addSellFactor 而不是 addBuyFactor - TDD', async () => {
        const mockSellFactor = {
          id: 'factor-005',
          name: 'AbuRSISell',
          description: 'RSI超买卖出策略',
          factor_type: 'sell',
          class_name: 'AbuRSISell',
          parameters: {
            rsi_period: { type: 'number', default: 14, comment: 'RSI周期' },
            overbought_threshold: { type: 'number', default: 70, comment: '超买阈值' }
          }
        }

        const userInputParams = {
          rsi_period: 21,
          overbought_threshold: 80
        }

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '参数配置 - RSI超买卖出策略',
            dialogView: 'configuration',
            selectedFactor: mockSellFactor,
            currentFactorType: 'sell',
            formModel: userInputParams
          }
        })

        await nextTick()

        // 点击"确认添加"按钮
        const confirmButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('确认添加')
        )
        await confirmButton.trigger('click')
        await nextTick()

        // 验证调用了 addSellFactor 而不是 addBuyFactor
        expect(mockStrategyEditorStore.addSellFactor).toHaveBeenCalledTimes(1)
        expect(mockStrategyEditorStore.addBuyFactor).not.toHaveBeenCalled()

        const callArgs = mockStrategyEditorStore.addSellFactor.mock.calls[0][0]
        expect(callArgs.factor_type).toBe('sell')
        expect(callArgs.parameters).toEqual(userInputParams)
      })

      it('参数验证失败时，不应调用 add 方法或关闭对话框 - TDD', async () => {
        const mockFactor = {
          id: 'factor-006',
          name: 'AbuValidationTest',
          description: '参数验证测试因子',
          factor_type: 'buy',
          class_name: 'AbuValidationTest',
          parameters: {
            required_param: { type: 'number', required: true, comment: '必填参数' }
          }
        }

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '参数配置',
            dialogView: 'configuration',
            selectedFactor: mockFactor,
            formModel: {} // 空的表单数据，缺少必填参数
          }
        })

        await nextTick()

        // 点击"确认添加"按钮
        const confirmButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('确认添加')
        )
        await confirmButton.trigger('click')
        await nextTick()

        // 验证因参数验证失败，addBuyFactor 不被调用
        expect(mockStrategyEditorStore.addBuyFactor).not.toHaveBeenCalled()
        
        // 验证对话框未关闭
        expect(wrapper.emitted('update:modelValue')).toBeFalsy()
      })

      it('用户可以点击"上一步"返回因子选择视图 - TDD', async () => {
        const mockFactor = {
          id: 'factor-007',
          name: 'AbuTestFactor',
          description: '测试因子',
          factor_type: 'buy',
          class_name: 'AbuTestFactor'
        }

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '参数配置',
            dialogView: 'configuration',
            selectedFactor: mockFactor
          }
        })

        await nextTick()

        // 验证当前在参数配置视图
        expect(wrapper.find('[data-testid="factor-parameter-config-dialog"]').exists()).toBe(true)

        // 点击"上一步"按钮
        const backButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('上一步')
        )
        expect(backButton).toBeDefined()
        await backButton.trigger('click')
        await nextTick()

        // 验证返回到因子选择视图
        expect(wrapper.emitted('update:dialogView')).toBeTruthy()
        expect(wrapper.emitted('update:dialogView')[0]).toEqual(['selection'])
      })
    })

    describe('测试场景3: 关键交互测试 - 完整的用户操作流程', () => {
      it('应该测试用户填写hit_ml参数并点击"确认添加"后触发save事件和关闭对话框的完整交互流程', async () => {
        // 模拟包含hit_ml参数的因子
        const mockFactorWithHitMl = {
          id: 'factor-hit-ml-001',
          name: 'AbuHitMlFactor',
          description: '机器学习命中率因子',
          factor_type: 'buy',
          class_name: 'AbuHitMlFactor',
          parameters: {
            hit_ml: { 
              type: 'number', 
              default: 0.5, 
              comment: '机器学习命中率阈值',
              description: '机器学习命中率阈值'
            },
            other_param: {
              type: 'number',
              default: 10,
              comment: '其他参数'
            }
          }
        }

        // 设置mock数据
        mockFactorsStore.factors = [mockFactorWithHitMl]
        mockFactorsStore.isLoading = false
        mockFactorsStore.error = null

        // 1. 挂载组件，并为其传递必要的props（如modelValue=true）
        // 初始化表单数据，包含所有参数的默认值
        const initialFormModel = {
          hit_ml: 0.5,  // 默认值
          other_param: 10  // 默认值
        }
        
        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,  // 对话框显示状态
            title: '添加机器学习因子',
            dialogView: 'configuration',  // 直接进入参数配置视图
            selectedFactor: mockFactorWithHitMl,  // 传递选中的因子
            formModel: initialFormModel  // 初始化表单数据
          }
        })

        await nextTick()

        // 验证组件已正确挂载并显示参数配置视图
        expect(wrapper.find('[data-testid="factor-parameter-config-dialog"]').exists()).toBe(true)
        expect(wrapper.find('.parameter-form-container').exists()).toBe(true)

        // 2. 模拟用户在表单中输入数据（例如，为hit_ml参数输入一个值）
        // 查找hit_ml参数对应的输入框
        const formItems = wrapper.findAll('.el-form-item')
        let hitMlInput: any = null
        
        for (const item of formItems) {
          const label = item.find('.el-form-item__label').text()
          if (label.includes('机器学习命中率阈值') || label.includes('hit_ml')) {
            hitMlInput = item.find('.el-input-number')
            break
          }
        }

        expect(hitMlInput).toBeTruthy()

        // 直接更新组件的formModel，模拟用户输入
        const component = wrapper.vm as any
        component.formModel.hit_ml = 0.8
        await nextTick()

        // 验证表单数据已更新
        expect(component.formModel.hit_ml).toBe(0.8)

        // 3. 模拟用户点击"确认添加"按钮
        const confirmButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('确认添加')
        )
        expect(confirmButton).toBeDefined()
        expect(confirmButton.attributes('disabled')).toBeFalsy()

        await confirmButton.trigger('click')
        await nextTick()

        // 4. 断言：组件必须触发 (emit) 一个名为'save'的事件
        expect(wrapper.emitted('save')).toBeTruthy()
        expect(wrapper.emitted('save')).toHaveLength(1)

        // 5. 断言：随着'save'事件一起传递出去的数据，必须是一个包含了用户输入参数的、结构正确的因子对象
        const saveEventData = wrapper.emitted('save')[0][0]
        expect(saveEventData).toBeDefined()
        expect(saveEventData).toHaveProperty('buy_factors')
        expect(Array.isArray(saveEventData.buy_factors)).toBe(true)
        expect(saveEventData.buy_factors).toHaveLength(1)

        // 验证因子对象的结构和数据
        const addedFactor = saveEventData.buy_factors[0]
        expect(addedFactor).toHaveProperty('class_name', 'AbuHitMlFactor')
        expect(addedFactor).toHaveProperty('parameters')
        expect(addedFactor.parameters).toHaveProperty('hit_ml', 0.8)  // 用户输入的值
        expect(addedFactor.parameters).toHaveProperty('other_param')  // 其他参数也应该存在

        // 6. 断言：组件还必须触发一个事件来通知父组件关闭对话框
        expect(wrapper.emitted('update:modelValue')).toBeTruthy()
        expect(wrapper.emitted('update:modelValue')).toHaveLength(1)
        expect(wrapper.emitted('update:modelValue')[0]).toEqual([false])

        // 7. 额外验证：确保detail事件也被触发（用于向store传递数据）
        expect(wrapper.emitted('detail')).toBeTruthy()
        const detailEventData = wrapper.emitted('detail')[0][0]
        expect(detailEventData).toHaveProperty('id', 'factor-hit-ml-001')
        expect(detailEventData).toHaveProperty('name', 'AbuHitMlFactor')
        expect(detailEventData).toHaveProperty('class_name', 'AbuHitMlFactor')
        expect(detailEventData).toHaveProperty('factor_type', 'buy')
        expect(detailEventData.parameters).toHaveProperty('hit_ml', 0.8)

        // 8. 验证策略编辑器store的方法被正确调用
        expect(mockStrategyEditorStore.addBuyFactor).toHaveBeenCalledTimes(1)
        const storeCallData = mockStrategyEditorStore.addBuyFactor.mock.calls[0][0]
        expect(storeCallData.parameters.hit_ml).toBe(0.8)
      })

      it('应该测试卖出因子的完整交互流程', async () => {
        // 模拟卖出因子
        const mockSellFactorWithParams = {
          id: 'factor-sell-001',
          name: 'AbuSellMlFactor',
          description: '机器学习卖出因子',
          factor_type: 'sell',
          class_name: 'AbuSellMlFactor',
          parameters: {
            hit_ml: { 
              type: 'number', 
              default: 0.6, 
              comment: '卖出命中率阈值'
            }
          }
        }

        mockFactorsStore.factors = [mockSellFactorWithParams]

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '添加卖出因子',
            dialogView: 'configuration',
            selectedFactor: mockSellFactorWithParams,
            currentFactorType: 'sell',
            formModel: {}
          }
        })

        await nextTick()

        // 直接更新组件的formModel，模拟用户输入
        const component = wrapper.vm as any
        component.formModel.hit_ml = 0.7
        await nextTick()

        // 点击确认添加
        const confirmButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('确认添加')
        )
        await confirmButton.trigger('click')
        await nextTick()

        // 验证save事件
        expect(wrapper.emitted('save')).toBeTruthy()
        const saveEventData = wrapper.emitted('save')[0][0]
        expect(saveEventData).toHaveProperty('sell_factors')
        expect(saveEventData.sell_factors[0].parameters.hit_ml).toBe(0.7)

        // 验证关闭对话框
        expect(wrapper.emitted('update:modelValue')).toBeTruthy()
        expect(wrapper.emitted('update:modelValue')[0]).toEqual([false])

        // 验证调用了addSellFactor而不是addBuyFactor
        expect(mockStrategyEditorStore.addSellFactor).toHaveBeenCalledTimes(1)
        expect(mockStrategyEditorStore.addBuyFactor).not.toHaveBeenCalled()
      })

      it('应该测试无参数因子的完整交互流程', async () => {
        // 模拟无参数因子
        const mockNoParamsFactor = {
          id: 'factor-no-params-001',
          name: 'AbuSimpleFactor',
          description: '简单无参数因子',
          factor_type: 'buy',
          class_name: 'AbuSimpleFactor',
          parameters: {}  // 无参数
        }

        mockFactorsStore.factors = [mockNoParamsFactor]

        wrapper = mount(StrategyFormDialog, {
          props: {
            modelValue: true,
            title: '添加简单因子',
            dialogView: 'configuration',
            selectedFactor: mockNoParamsFactor,
            formModel: {}
          }
        })

        await nextTick()

        // 验证显示无参数提示
        expect(wrapper.find('.no-parameters-container').exists()).toBe(true)
        expect(wrapper.text()).toContain('该因子无需额外参数')

        // 点击确认添加
        const confirmButton = wrapper.findAll('.el-button').find(btn => 
          btn.text().includes('确认添加')
        )
        await confirmButton.trigger('click')
        await nextTick()

        // 验证save事件被触发
        expect(wrapper.emitted('save')).toBeTruthy()
        const saveEventData = wrapper.emitted('save')[0][0]
        expect(saveEventData.buy_factors[0].class_name).toBe('AbuSimpleFactor')
        expect(saveEventData.buy_factors[0].parameters).toEqual({})

        // 验证对话框关闭
        expect(wrapper.emitted('update:modelValue')).toBeTruthy()
        expect(wrapper.emitted('update:modelValue')[0]).toEqual([false])
      })
    })
  })
});
