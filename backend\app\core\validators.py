"""输入验证工具模块"""
import re
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, validator
from backend.app.core.exceptions import ParameterError


class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, field: str, message: str):
        self.field = field
        self.message = message
        super().__init__(f"{field}: {message}")


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_strategy_name(name: str) -> str:
        """验证策略名称"""
        if not name or not name.strip():
            raise ValidationError("name", "策略名称不能为空")
        
        name = name.strip()
        if len(name) < 2:
            raise ValidationError("name", "策略名称长度不能少于2个字符")
        
        if len(name) > 100:
            raise ValidationError("name", "策略名称长度不能超过100个字符")
        
        # 检查特殊字符
        if re.search(r'[<>"\'\/\\]', name):
            raise ValidationError("name", "策略名称不能包含特殊字符 < > \" ' / \\")
        
        return name
    
    @staticmethod
    def validate_strategy_description(description: Optional[str]) -> Optional[str]:
        """验证策略描述"""
        if description is None:
            return None
        
        description = description.strip()
        if len(description) > 1000:
            raise ValidationError("description", "策略描述长度不能超过1000个字符")
        
        return description if description else None
    
    @staticmethod
    def validate_strategy_type(strategy_type: str) -> str:
        """验证策略类型"""
        valid_types = ["trend_following", "mean_reversion", "momentum", "arbitrage", "custom"]
        
        if not strategy_type or strategy_type not in valid_types:
            raise ValidationError("type", f"策略类型必须是以下之一: {', '.join(valid_types)}")
        
        return strategy_type
    
    @staticmethod
    def validate_initial_capital(initial_capital: Union[int, float]) -> float:
        """验证初始资金"""
        try:
            capital = float(initial_capital)
        except (ValueError, TypeError):
            raise ValidationError("initial_capital", "初始资金必须是数字")
        
        if capital <= 0:
            raise ValidationError("initial_capital", "初始资金必须大于0")
        
        if capital > 1e12:  # 1万亿
            raise ValidationError("initial_capital", "初始资金不能超过1万亿")
        
        return capital
    
    @staticmethod
    def validate_factors(factors: Optional[List[Dict[str, Any]]]) -> Optional[List[Dict[str, Any]]]:
        """验证因子列表"""
        if not factors:
            return None
        
        if not isinstance(factors, list):
            raise ValidationError("factors", "因子必须是列表格式")
        
        if len(factors) > 50:
            raise ValidationError("factors", "因子数量不能超过50个")
        
        validated_factors = []
        for i, factor in enumerate(factors):
            if not isinstance(factor, dict):
                raise ValidationError(f"factors[{i}]", "因子必须是字典格式")
            
            # 验证必需字段
            if 'name' not in factor:
                raise ValidationError(f"factors[{i}]", "因子必须包含name字段")
            
            if 'weight' not in factor:
                raise ValidationError(f"factors[{i}]", "因子必须包含weight字段")
            
            # 验证因子名称
            factor_name = InputValidator.validate_factor_name(factor['name'])
            
            # 验证因子权重
            factor_weight = InputValidator.validate_factor_weight(factor['weight'])
            
            validated_factor = {
                'name': factor_name,
                'weight': factor_weight
            }
            
            # 可选字段
            if 'parameters' in factor:
                validated_factor['parameters'] = factor['parameters']
            
            validated_factors.append(validated_factor)
        
        return validated_factors
    
    @staticmethod
    def validate_factor_name(name: str) -> str:
        """验证因子名称"""
        if not name or not name.strip():
            raise ValidationError("factor_name", "因子名称不能为空")
        
        name = name.strip()
        if len(name) < 2:
            raise ValidationError("factor_name", "因子名称长度不能少于2个字符")
        
        if len(name) > 50:
            raise ValidationError("factor_name", "因子名称长度不能超过50个字符")
        
        # 只允许字母、数字、下划线和中文
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fff]+$', name):
            raise ValidationError("factor_name", "因子名称只能包含字母、数字、下划线和中文字符")
        
        return name
    
    @staticmethod
    def validate_factor_weight(weight: Union[int, float]) -> float:
        """验证因子权重"""
        try:
            weight_val = float(weight)
        except (ValueError, TypeError):
            raise ValidationError("factor_weight", "因子权重必须是数字")
        
        if weight_val < 0 or weight_val > 1:
            raise ValidationError("factor_weight", "因子权重必须在0到1之间")
        
        return weight_val
    
    @staticmethod
    def validate_pagination(page: int, size: int) -> tuple[int, int]:
        """验证分页参数"""
        if page < 1:
            raise ValidationError("page", "页码必须大于等于1")
        
        if size < 1:
            raise ValidationError("size", "每页大小必须大于等于1")
        
        if size > 100:
            raise ValidationError("size", "每页大小不能超过100")
        
        return page, size
    
    @staticmethod
    def sanitize_string(value: str) -> str:
        """清理字符串，防止XSS攻击"""
        if not value:
            return value
        
        # 移除潜在的危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', 'javascript:', 'data:', 'vbscript:']
        sanitized = value
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()


def validate_strategy_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证策略数据的便捷函数"""
    validator = InputValidator()
    validated_data = {}
    
    # 验证特定字段
    if 'name' in data:
        validated_data['name'] = validator.validate_strategy_name(data['name'])
    
    if 'description' in data:
        validated_data['description'] = validator.validate_strategy_description(data['description'])
    
    if 'type' in data:
        validated_data['type'] = validator.validate_strategy_type(data['type'])
    
    if 'initial_capital' in data:
        validated_data['initial_capital'] = validator.validate_initial_capital(data['initial_capital'])
    
    if 'factors' in data:
        validated_data['factors'] = validator.validate_factors(data['factors'])
    
    # 保留其他未明确验证的字段（如 is_public, buy_factors, sell_factors 等）
    for key, value in data.items():
        if key not in validated_data:
            validated_data[key] = value
    
    return validated_data