import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useStrategyStore } from '../../src/stores/modules/useStrategyStore'
import * as strategyApi from '../../src/api/modules/strategy'
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../src/types'
import { SimpleStrategyDataFactory } from '../factories/SimpleStrategyDataFactory'

// Mock API modules
vi.mock('../../src/api/modules/strategy', () => ({
  getStrategies: vi.fn(),
  getStrategy: vi.fn(),
  createStrategy: vi.fn(),
  updateStrategy: vi.fn(),
  deleteStrategy: vi.fn(),
  executeStrategy: vi.fn()
}))

const mockStrategyApi = vi.mocked(strategyApi)

describe('useStrategyStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const store = useStrategyStore()
      
      expect(store.strategies).toEqual([])
      expect(store.currentStrategy).toBeNull()
      expect(store.currentSelectedStrategy).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
    })
  })

  describe('fetchStrategies', () => {
    it('应该能够获取策略列表', async () => {
      const store = useStrategyStore()
      const strategies: Strategy[] = [
        SimpleStrategyDataFactory.createStrategy(),
        SimpleStrategyDataFactory.createStrategy()
      ]
      
      mockStrategyApi.getStrategies.mockResolvedValue(strategies)
      
      await store.fetchStrategies()
      
      expect(mockStrategyApi.getStrategies).toHaveBeenCalled()
      expect(store.strategies).toEqual(strategies)
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
    })

    it('应该处理获取策略列表失败的情况', async () => {
      const store = useStrategyStore()
      const error = new Error('获取策略列表失败')
      
      mockStrategyApi.getStrategies.mockRejectedValue(error)
      
      await store.fetchStrategies()
      
      expect(store.error).toBe('获取策略列表失败')
      expect(store.isLoading).toBe(false)
    })

    it('应该在请求期间设置loading状态', async () => {
      const store = useStrategyStore()
      let loadingDuringRequest = false
      
      mockStrategyApi.getStrategies.mockImplementation(async () => {
        loadingDuringRequest = store.isLoading
        return []
      })
      
      await store.fetchStrategies()
      
      expect(loadingDuringRequest).toBe(true)
      expect(store.isLoading).toBe(false)
    })
  })

  describe('fetchStrategyById', () => {
    it('应该能够根据ID获取策略', async () => {
      const store = useStrategyStore()
      const strategyId = 'test-strategy-id'
      const strategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      
      mockStrategyApi.getStrategy.mockResolvedValue(strategy)
      
      await store.fetchStrategyById(strategyId)
      
      expect(mockStrategyApi.getStrategy).toHaveBeenCalledWith(strategyId)
      expect(store.currentStrategy).toEqual(strategy)
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
    })

    it('应该处理获取策略失败的情况', async () => {
      const store = useStrategyStore()
      const strategyId = 'non-existent-strategy'
      const error = new Error('策略不存在')
      
      mockStrategyApi.getStrategy.mockRejectedValue(error)
      
      await store.fetchStrategyById(strategyId)
      
      expect(store.error).toBe('策略不存在')
      expect(store.isLoading).toBe(false)
    })
  })

  describe('createNewStrategy', () => {
    it('应该能够创建新策略', async () => {
      const store = useStrategyStore()
      const strategyData: CreateStrategyRequest = {
        name: '新策略',
        description: '这是一个新策略',
        buy_factors: [],
        sell_factors: []
      }
      const strategies: Strategy[] = [SimpleStrategyDataFactory.createStrategy()]
      
      mockStrategyApi.createStrategy.mockResolvedValue(undefined)
      mockStrategyApi.getStrategies.mockResolvedValue(strategies)
      
      await store.createNewStrategy(strategyData)
      
      expect(mockStrategyApi.createStrategy).toHaveBeenCalledWith(strategyData)
      expect(mockStrategyApi.getStrategies).toHaveBeenCalled()
      expect(store.strategies).toEqual(strategies)
    })

    it('应该处理创建策略失败的情况', async () => {
      const store = useStrategyStore()
      const strategyData: CreateStrategyRequest = {
        name: '',
        description: '',
        buy_factors: [],
        sell_factors: []
      }
      const error = new Error('策略名称不能为空')
      
      mockStrategyApi.createStrategy.mockRejectedValue(error)
      
      await store.createNewStrategy(strategyData)
      
      expect(store.error).toBe('策略名称不能为空')
      expect(store.isLoading).toBe(false)
    })
  })

  describe('updateExistingStrategy', () => {
    it('应该能够更新现有策略', async () => {
      const store = useStrategyStore()
      const strategyId = 'test-strategy-id'
      const strategyData: UpdateStrategyRequest = {
        name: '更新后的策略名称',
        description: '更新后的描述'
      }
      const strategies: Strategy[] = [SimpleStrategyDataFactory.createStrategy()]
      
      mockStrategyApi.updateStrategy.mockResolvedValue(undefined)
      mockStrategyApi.getStrategies.mockResolvedValue(strategies)
      
      await store.updateExistingStrategy(strategyId, strategyData)
      
      expect(mockStrategyApi.updateStrategy).toHaveBeenCalledWith(strategyId, strategyData)
      expect(mockStrategyApi.getStrategies).toHaveBeenCalled()
      expect(store.strategies).toEqual(strategies)
    })

    it('应该处理更新策略失败的情况', async () => {
      const store = useStrategyStore()
      const strategyId = 'non-existent-strategy'
      const strategyData: UpdateStrategyRequest = {
        name: '更新后的策略名称'
      }
      const error = new Error('策略不存在')
      
      mockStrategyApi.updateStrategy.mockRejectedValue(error)
      
      await store.updateExistingStrategy(strategyId, strategyData)
      
      expect(store.error).toBe('策略不存在')
      expect(store.isLoading).toBe(false)
    })
  })

  describe('deleteExistingStrategy', () => {
    it('应该能够删除策略', async () => {
      const store = useStrategyStore()
      const strategyId = 'test-strategy-id'
      const strategies: Strategy[] = []
      
      mockStrategyApi.deleteStrategy.mockResolvedValue({ success: true })
      mockStrategyApi.getStrategies.mockResolvedValue(strategies)
      
      await store.deleteExistingStrategy(strategyId)
      
      expect(mockStrategyApi.deleteStrategy).toHaveBeenCalledWith(strategyId)
      expect(mockStrategyApi.getStrategies).toHaveBeenCalled()
      expect(store.strategies).toEqual(strategies)
    })

    it('应该处理删除策略失败的情况', async () => {
      const store = useStrategyStore()
      const strategyId = 'non-existent-strategy'
      const error = new Error('策略不存在')
      
      mockStrategyApi.deleteStrategy.mockRejectedValue(error)
      
      await store.deleteExistingStrategy(strategyId)
      
      expect(store.error).toBe('策略不存在')
      expect(store.isLoading).toBe(false)
    })
  })

  describe('executeStrategy', () => {
    it('应该能够执行策略', async () => {
      const store = useStrategyStore()
      const strategyId = 'test-strategy-id'
      const config = {
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      }
      const expectedResponse = { task_id: 'execution-task-id' }
      
      mockStrategyApi.executeStrategy.mockResolvedValue(expectedResponse)
      
      const result = await store.executeStrategy(strategyId, config)
      
      expect(mockStrategyApi.executeStrategy).toHaveBeenCalledWith(strategyId, config)
      expect(result).toEqual(expectedResponse)
    })
  })

  describe('策略选择管理', () => {
    it('setCurrentSelectedStrategy应该设置当前选中的策略', () => {
      const store = useStrategyStore()
      const strategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      
      store.setCurrentSelectedStrategy(strategy)
      
      expect(store.currentSelectedStrategy).toEqual(strategy)
    })

    it('clearCurrentSelectedStrategy应该清除当前选中的策略', () => {
      const store = useStrategyStore()
      const strategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      
      store.currentSelectedStrategy = strategy
      store.clearCurrentSelectedStrategy()
      
      expect(store.currentSelectedStrategy).toBeNull()
    })

    it('fetchStrategiesAndSelectFirst应该获取策略并选中第一个', async () => {
      const store = useStrategyStore()
      const strategies: Strategy[] = [
        SimpleStrategyDataFactory.createStrategy(),
        SimpleStrategyDataFactory.createStrategy()
      ]
      
      mockStrategyApi.getStrategies.mockResolvedValue(strategies)
      
      await store.fetchStrategiesAndSelectFirst()
      
      expect(store.strategies).toEqual(strategies)
      expect(store.currentSelectedStrategy).toEqual(strategies[0])
    })

    it('fetchStrategiesAndSelectFirst在没有策略时不应该选中任何策略', async () => {
      const store = useStrategyStore()
      
      mockStrategyApi.getStrategies.mockResolvedValue([])
      
      await store.fetchStrategiesAndSelectFirst()
      
      expect(store.strategies).toEqual([])
      expect(store.currentSelectedStrategy).toBeNull()
    })
  })

  describe('新策略创建', () => {
    it('startNewStrategyCreation应该创建新策略模板', () => {
      const store = useStrategyStore()
      
      store.startNewStrategyCreation()
      
      expect(store.currentSelectedStrategy).toEqual({
        id: 'new',
        name: '',
        description: '',
        buy_factors: [],
        sell_factors: [],
        created_at: expect.any(String),
        updated_at: expect.any(String),
        is_active: true,
        performance_metrics: {
          total_return: 0,
          annual_return: 0,
          sharpe_ratio: 0,
          max_drawdown: 0,
          win_rate: 0
        }
      })
    })
  })

  describe('因子管理', () => {
    it('addExclusiveSellFactor应该添加独占卖出因子', () => {
      const store = useStrategyStore()
      const strategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      const buyFactorId = 'buy-factor-1'
      const sellFactor = { id: 'sell-factor-1', name: '卖出因子' }
      
      strategy.buy_factors = [{ id: buyFactorId, name: '买入因子', sell_factors: [] }]
      store.currentSelectedStrategy = strategy
      
      store.addExclusiveSellFactor(buyFactorId, sellFactor)
      
      const addedFactor = store.currentSelectedStrategy?.buy_factors[0].sell_factors[0]
      expect(addedFactor).toEqual(sellFactor)
      expect(store.currentSelectedStrategy?.buy_factors[0].sell_factors).toHaveLength(1)
    })

    it('removeExclusiveSellFactor应该移除独占卖出因子', () => {
      const store = useStrategyStore()
      const strategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      const buyFactorId = 'buy-factor-1'
      const sellFactorId = 'sell-factor-1'
      
      strategy.buy_factors = [{
        id: buyFactorId,
        name: '买入因子',
        sell_factors: [{ id: sellFactorId, name: '卖出因子' }]
      }]
      store.currentSelectedStrategy = strategy
      
      store.removeExclusiveSellFactor(buyFactorId, sellFactorId)
      
      expect(store.currentSelectedStrategy?.buy_factors[0].sell_factors).toEqual([])
    })

    it('getExclusiveSellFactors应该返回指定买入因子的卖出因子', () => {
      const store = useStrategyStore()
      const strategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      const buyFactorId = 'buy-factor-1'
      const sellFactors = [{ id: 'sell-factor-1', name: '卖出因子' }]
      
      strategy.buy_factors = [{
        id: buyFactorId,
        name: '买入因子',
        sell_factors: sellFactors
      }]
      store.currentSelectedStrategy = strategy
      
      const result = store.getExclusiveSellFactors(buyFactorId)
      
      expect(result).toEqual(sellFactors)
    })
  })
})