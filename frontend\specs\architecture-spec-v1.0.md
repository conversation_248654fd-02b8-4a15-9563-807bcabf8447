# abu_modern 前端架构与动态布局规范 (Structural & Dynamic Layout Spec V1.0)

## 1. 应用级骨架 (The Application Shell)

- 核心原则：整个应用共享一个唯一的、不可变的顶层骨架。
- 实现文件：App.vue
- 结构：App.vue 的模板中，只包含一个 `<router-view />`。所有页面布局逻辑，下放到各自的路由组件中。

```vue
<!-- App.vue -->
<template>
  <router-view />
</template>
```

---

## 2. 认证布局 vs. 主应用布局

- 核心原则：应用存在两种布局状态——“用户未登录”和“用户已登录”。
- 实现文件：layouts/AuthLayout.vue, layouts/DefaultLayout.vue
- 路由配置（router/index.ts）：

```ts
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
// import AuthLayout from '@/layouts/AuthLayout.vue'
// import DefaultLayout from '@/layouts/DefaultLayout.vue'
// import LoginView from '@/views/LoginView.vue'
// import DashboardView from '@/views/Dashboard.vue'
// import StrategyWorkshopView from '@/views/StrategyWorkshop.vue'

const routes = [
  {
    path: '/login',
    component: AuthLayout, // 使用认证布局
    children: [{ path: '', component: LoginView }]
  },
  {
    path: '/',
    component: DefaultLayout, // 使用主应用布局
    meta: { requiresAuth: true },
    children: [
      // 所有需要登录后访问的页面路由
      { path: 'dashboard', component: DashboardView },
      { path: 'workshop', component: StrategyWorkshopView }
      // ...
    ]
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

---

## 3. 主应用布局 (DefaultLayout.vue) —— “圣杯布局”

- 核心原则：采用经典“圣杯布局”，顶栏和侧边栏固定，主内容区独立滚动。

```vue
<!-- layouts/DefaultLayout.vue -->
<template>
  <div class="app-layout">
    <header class="app-header">...</header>
    <aside class="app-sidebar">...</aside>
    <main class="app-main-content">
      <router-view />
    </main>
  </div>
</template>

<style scoped>
.app-layout {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar content";
  grid-template-rows: 60px 1fr;          /* 顶栏高度60px，剩余高度给下面 */
  grid-template-columns: 220px 1fr;      /* 侧边栏宽度220px，剩余宽度给内容区 */
  height: 100vh;
  overflow: hidden;                      /* 关键：禁止整个页面滚动 */
}

.app-header {
  grid-area: header;
}

.app-sidebar {
  grid-area: sidebar;
  overflow-y: auto;                      /* 侧边栏内容多时，自己滚动 */
}

.app-main-content {
  grid-area: content;
  overflow-y: auto;                      /* 关键：主内容区独立滚动 */
  padding: 24px;
}
</style>
```

- 约束：
  - 任何页面级组件，不允许改变这个顶层布局。
  - 不允许在页面组件内部再嵌套一个带滚动条的 ElMain。

---

## 4. 页面级布局 — 两大模式

所有位于 /views 目录下的页面级组件，其内部布局必须遵循以下两种模式之一。

### 模式一：单面板布局 (Single-Panel Layout)

- 适用场景：仪表盘、回测报告、系统设置等信息展示或简单表单页面。
- 结构规范：

```vue
<!-- views/Dashboard.vue -->
<template>
  <div class="dashboard-page">
    <el-page-header title="仪表盘" />
    <el-row :gutter="16">
      <el-col :span="12">
        <el-card>...</el-card>
      </el-col>
      <!-- ... -->
    </el-row>
  </div>
</template>

<script setup lang="ts">
// ...
</script>

<style scoped>
.dashboard-page {
  /* 页面自定义样式 */
}
</style>
```

- 行为规范：整个页面内容，随着主内容区（.app-main-content）的滚动条滚动。

### 模式二：固定分栏布局 (Fixed-Split-Panel Layout)

- 适用场景：策略工场、选股器等，需要“列表-详情”联动的复杂交互页面。
- 核心原则：左右（或上下）分栏，各自独立滚动。

```vue
<!-- views/StrategyWorkshop.vue -->
<template>
  <div class="workshop-layout">
    <div class="workshop-left-panel">
      <!-- 策略列表 -->
    </div>
    <div class="workshop-right-panel">
      <!-- 策略编辑器 -->
      <div class="editor-header">
        <!-- 策略简介 / 顶部工具栏 -->
      </div>
      <div class="editor-content">
        <!-- 右侧主体（如 ElTabs） -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// ...
</script>

<style scoped>
.workshop-layout {
  display: flex;
  height: 100%;       /* 关键：撑满父容器(.app-main-content)的高度 */
  gap: 24px;          /* 栏间距 */
}

.workshop-left-panel {
  width: 320px;       /* 固定宽度 */
  flex-shrink: 0;
  overflow-y: auto;   /* 关键：左侧独立滚动 */
}

.workshop-right-panel {
  flex: 1;            /* 占据剩余宽度 */
  display: flex;
  flex-direction: column;
  overflow: hidden;   /* 关键：右侧本身不滚动 */
}

/* 在右侧内部，再定义滚动区域 */
.editor-header {
  flex-shrink: 0;
}

.editor-content {
  flex: 1;
  overflow-y: auto;   /* 关键：右侧的内容区独立滚动 */
}
</style>
```

- 约束：
  - 当需要“列表-详情”联动时，必须采用此布局模式。
  - 严禁将整个两栏式布局放在一个会随页面滚动的容器内。

---

## 5. 元素与容器的嵌套关系

- 顶层容器：
  - 任何页面的顶层 div，应有一个唯一的 class，如 .dashboard-page、.workshop-layout。
- 页面标题：
  - ElPageHeader 或自定义标题，应是顶层 div 的直接子元素。
- 布局容器：
  - 行（ElRow）：用于水平方向的栅格布局。
  - 卡片（ElCard）：用于包裹语义上独立的模块。不要用一个巨大的 ElCard 包裹整个页面。ElCard 应放置在 ElCol 或 flex/grid 的单元格内。
- 禁止的嵌套：
  - 严禁在 ElCard 内部再使用 ElRow 进行复杂的栅格布局。卡片内部应保持简单的垂直流布局。如果卡片内布局复杂，说明需要将其拆分为更小的组件。
  - 严禁出现“滚动条套滚动条”的情况。一个视图区域，只应有一个主滚动条。