// 策略管理API Mock处理器 - 严格按照API契约
// 路径、方法、数据结构完全按照backend/app/api/endpoints/strategy.py定义

import { http, HttpResponse } from 'msw';

export const strategyHandlers = [
  // POST /strategy -// 创建策略
  http.post('/api/strategy', async ({ request }) => {
    const data = await request.json() as any;
    
    if (!data.name) {
      return HttpResponse.json({
        success: false,
        message: "策略名称不能为空",
        error_code: "MISSING_NAME"
      }, { status: 400 });
    }
    
    if (!data.buy_factors || !Array.isArray(data.buy_factors) || data.buy_factors.length === 0) {
      return HttpResponse.json({
        success: false,
        message: "买入因子不能为空",
        error_code: "MISSING_BUY_FACTORS"
      }, { status: 400 });
    }
    
    return HttpResponse.json({
      success: true,
      message: "策略创建成功",
      data: {
        id: "strategy_" + Date.now(),
        name: data.name,
        description: data.description || "",
        buy_factors: data.buy_factors,
        sell_factors: data.sell_factors || [],
        position_management: data.position_management || {
          strategy_type: "equal_weight",
          max_position_ratio: 0.2,
          max_stocks_count: 5
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }, { status: 201 });
  }),

  // GET /strategy -// 获取策略列表
  http.get('/api/strategy', ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const page_size = parseInt(url.searchParams.get('page_size') || '20');
    const search = url.searchParams.get('search');
    
    const mockStrategies = [
      {
        id: "strategy_1",
        name: "双均线策略",
        description: "基于5日和20日均线的交叉策略",
        buy_factors: [
          {
            factor_name: "AbuFactorBuyBreak",
            params: { xd: 5 }
          }
        ],
        sell_factors: [
          {
            factor_name: "AbuFactorSellBreak",
            params: { xd: 20 }
          }
        ],
        position_management: {
          strategy_type: "equal_weight",
          max_position_ratio: 0.2,
          max_stocks_count: 5
        },
        created_at: "2024-01-01T10:00:00Z",
        updated_at: "2024-01-01T10:00:00Z"
      },
      {
        id: "strategy_2",
        name: "RSI策略",
        description: "基于RSI指标的超买超卖策略",
        buy_factors: [
          {
            factor_name: "AbuFactorBuyRSI",
            params: { rsi_period: 14, oversold_threshold: 30 }
          }
        ],
        sell_factors: [
          {
            factor_name: "AbuFactorSellRSI",
            params: { rsi_period: 14, overbought_threshold: 70 }
          }
        ],
        position_management: {
          strategy_type: "equal_weight",
          max_position_ratio: 0.15,
          max_stocks_count: 8
        },
        created_at: "2024-01-02T10:00:00Z",
        updated_at: "2024-01-02T10:00:00Z"
      }
    ];
    
    // 根据搜索条件过滤
    let filteredStrategies = mockStrategies;
    if (search) {
      filteredStrategies = filteredStrategies.filter(strategy => 
        strategy.name.includes(search) || strategy.description.includes(search)
      );
    }
    
    const total = filteredStrategies.length;
    const start = (page - 1) * page_size;
    const end = start + page_size;
    const paginatedStrategies = filteredStrategies.slice(start, end);
    
    return HttpResponse.json({
      success: true,
      message: "获取策略列表成功",
      data: paginatedStrategies,
      total: total,
      page: page,
      page_size: page_size,
      total_pages: Math.ceil(total / page_size)
    });
  }),

  // GET /strategy/{strategy_id} -// 获取单个策略详情
  http.get('/api/strategy/:strategy_id', ({ params }) => {
    const { strategy_id } = params;
    
    if (strategy_id === 'not-found') {
      return HttpResponse.json({
        success: false,
        message: "策略不存在",
        error_code: "STRATEGY_NOT_FOUND"
      }, { status: 404 });
    }
    
    return HttpResponse.json({
      success: true,
      message: "获取策略详情成功",
      data: {
        id: strategy_id,
        name: "双均线策略",
        description: "基于5日和20日均线的交叉策略",
        buy_factors: [
          {
            factor_name: "AbuFactorBuyBreak",
            params: { xd: 5 }
          }
        ],
        sell_factors: [
          {
            factor_name: "AbuFactorSellBreak",
            params: { xd: 20 }
          }
        ],
        position_management: {
          strategy_type: "equal_weight",
          max_position_ratio: 0.2,
          max_stocks_count: 5
        },
        created_at: "2024-01-01T10:00:00Z",
        updated_at: "2024-01-01T10:00:00Z"
      }
    });
  }),

  // PUT /strategy/{strategy_id} -// 更新策略
  http.put('/api/strategy/:strategy_id', async ({ params, request }) => {
    const { strategy_id } = params;
    const data = await request.json() as any;
    
    if (strategy_id === 'not-found') {
      return HttpResponse.json({
        success: false,
        message: "策略不存在",
        error_code: "STRATEGY_NOT_FOUND"
      }, { status: 404 });
    }
    
    if (data.name && data.name.trim() === '') {
      return HttpResponse.json({
        success: false,
        message: "策略名称不能为空",
        error_code: "INVALID_NAME"
      }, { status: 400 });
    }
    
    return HttpResponse.json({
      success: true,
      message: "策略更新成功",
      data: {
        id: strategy_id,
        name: data.name || "双均线策略",
        description: data.description || "基于5日和20日均线的交叉策略",
        buy_factors: data.buy_factors || [
          {
            factor_name: "AbuFactorBuyBreak",
            params: { xd: 5 }
          }
        ],
        sell_factors: data.sell_factors || [
          {
            factor_name: "AbuFactorSellBreak",
            params: { xd: 20 }
          }
        ],
        position_management: data.position_management || {
          strategy_type: "equal_weight",
          max_position_ratio: 0.2,
          max_stocks_count: 5
        },
        created_at: "2024-01-01T10:00:00Z",
        updated_at: new Date().toISOString()
      }
    });
  }),

  // DELETE /strategy/{strategy_id} -// 删除策略
  http.delete('/api/strategy/:strategy_id', ({ params }) => {
    const { strategy_id } = params;
    
    if (strategy_id === 'not-found') {
      return HttpResponse.json({
        success: false,
        message: "策略不存在",
        error_code: "STRATEGY_NOT_FOUND"
      }, { status: 404 });
    }
    
    return HttpResponse.json({
      success: true,
      message: "策略删除成功"
    });
  }),

  // POST /strategy/{strategy_id}/execute -// 执行策略回测
  http.post('/api/strategy/:strategy_id/execute', async ({ params, request }) => {
    const { strategy_id } = params;
    const data = await request.json() as any;
    
    if (strategy_id === 'not-found') {
      return HttpResponse.json({
        success: false,
        message: "策略不存在",
        error_code: "STRATEGY_NOT_FOUND"
      }, { status: 404 });
    }
    
    if (!data.choice_symbols || !Array.isArray(data.choice_symbols) || data.choice_symbols.length === 0) {
      return HttpResponse.json({
        success: false,
        message: "股票代码列表不能为空",
        error_code: "MISSING_CHOICE_SYMBOLS"
      }, { status: 400 });
    }
    
    if (!data.start_date || !data.end_date) {
      return HttpResponse.json({
        success: false,
        message: "开始日期和结束日期不能为空",
        error_code: "MISSING_DATE_RANGE"
      }, { status: 400 });
    }
    
    if (data.capital !== undefined && data.capital <= 0) {
      return HttpResponse.json({
        success: false,
        message: "初始资金必须大于0",
        error_code: "INVALID_CAPITAL"
      }, { status: 400 });
    }
    
    // 模拟回测结果
    const firstSymbol = data.choice_symbols[0];
    const capital = data.capital || 100000; // 默认资金
    return HttpResponse.json({
      success: true,
      message: "回测执行成功",
      data: {
        strategy_id: strategy_id,
        choice_symbols: data.choice_symbols,
        start_date: data.start_date,
        end_date: data.end_date,
        capital: capital,
        final_capital: capital * 1.15,
        total_return: 0.15,
        annual_return: 0.12,
        max_drawdown: -0.08,
        sharpe_ratio: 1.25,
        win_rate: 0.65,
        total_trades: 25,
        winning_trades: 16,
        losing_trades: 9,
        orders: [
          {
            buy_date: 20240101,
            sell_date: 20240115,
            buy_price: 10.5,
            sell_price: 11.2,
            symbol: firstSymbol
          }
        ],
        actions: {
          buy: [
            {
              date: "2024-01-01",
              symbol: firstSymbol,
              price: 10.5,
              quantity: 1000
            }
          ],
          sell: [
            {
              date: "2024-01-15",
              symbol: firstSymbol,
              price: 11.2,
              quantity: 1000
            }
          ]
        },
        start_cash: capital
      }
    });
  }),

  // GET /strategy/factors -// 获取可用因子列表
  http.get('/api/v1/strategy/factors/', () => {
    // 使用FactorsDataFactory提供的测试数据
    const allFactors = [
      {
         id: 'factor_1',
         name: 'AbuDoubleMaBuy',
         description: '双均线交叉策略 - 当短期均线上穿长期均线时买入',
         factor_type: 'buy',
         class_name: 'AbuDoubleMaBuy',
         parameters: {
           fast_ma: {
             name: 'fast_ma',
             type: 'number',
             description: '短期均线周期',
             default_value: 5,
             min_value: 1,
             max_value: 50
           },
           slow_ma: {
             name: 'slow_ma',
             type: 'number',
             description: '长期均线周期',
             default_value: 20,
             min_value: 10,
             max_value: 200
           }
         },
         is_builtin: true,
         is_active: true,
         usage_count: 15,
         rating: 4.5
       },
      {
          id: 'factor_2',
          name: 'AbuRSISell',
          description: 'RSI超买卖出策略 - 当RSI指标超买时卖出',
          factor_type: 'sell',
          class_name: 'AbuRSISell',
          parameters: {
            period: {
              name: 'period',
              type: 'number',
              description: 'RSI计算周期',
              default_value: 14,
              min_value: 5,
              max_value: 30
            },
            overbought: {
              name: 'overbought',
              type: 'number',
              description: '超买阈值',
              default_value: 70,
              min_value: 60,
              max_value: 90
            }
          },
          is_builtin: true,
          is_active: true,
          usage_count: 8,
          rating: 4.2
        },
      {
         id: 'factor_3',
         name: 'AbuMACDBuy',
         description: 'MACD金叉买入策略 - MACD指标出现金叉信号时买入',
         factor_type: 'buy',
         class_name: 'AbuMACDBuy',
         parameters: {},
         is_builtin: true,
         is_active: true,
         usage_count: 12,
         rating: 4.0
       },
      {
         id: 'factor_4',
         name: 'AbuFactorBuyBreak',
         description: '向上突破买入策略 - 价格突破前期高点时买入',
         factor_type: 'buy',
         class_name: 'AbuFactorBuyBreak',
         parameters: {
           xd: {
             name: 'xd',
             type: 'number',
             description: '突破周期',
             default_value: 5,
             min_value: 1,
             max_value: 30
           }
         },
         is_builtin: true,
         is_active: true,
         usage_count: 25,
         rating: 4.3
       },
      {
         id: 'factor_5',
         name: 'AbuFactorBuyPutBreak',
         description: '向下突破反转买入策略 - 价格跌破支撑后反弹买入',
         factor_type: 'buy',
         class_name: 'AbuFactorBuyPutBreak',
         parameters: {
           xd: {
             name: 'xd',
             type: 'number',
             description: '突破周期',
             default_value: 10,
             min_value: 5,
             max_value: 50
           }
         },
         is_builtin: true,
         is_active: true,
         usage_count: 18,
         rating: 3.9
       },
      {
         id: 'factor_6',
         name: 'AbuFactorBuyPutXDBK',
         description: 'XDBK买入策略 - 基于特定技术指标的买入信号',
         factor_type: 'buy',
         class_name: 'AbuFactorBuyPutXDBK',
         parameters: {},
         is_builtin: true,
         is_active: true,
         usage_count: 7,
         rating: 3.5
       }
    ];
    
    const buyFactors = allFactors.filter(factor => factor.factor_type === 'buy');
    const sellFactors = allFactors.filter(factor => factor.factor_type === 'sell');
    
    return HttpResponse.json({
      success: true,
      message: "获取因子列表成功",
      buy_factors: buyFactors,
      sell_factors: sellFactors
    });
  })
];