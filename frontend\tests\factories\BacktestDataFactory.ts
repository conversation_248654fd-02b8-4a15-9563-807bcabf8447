// tests/factories/BacktestDataFactory.ts

import type {
  BacktestConfig,
  BacktestResult,
  BacktestMetrics,
  BacktestTask,
  Trade,
  Position,
  EquityPoint,
  BacktestStatus
} from '../../src/api/types/backtest';
import { BacktestStatus as Status } from '../../src/api/types/backtest';
import {
  ApiSchemas,
  ContractValidator,
  type StrategyExecuteRequest,
  type StrategyExecuteResponse,
  type Order,
  type ExecutionSummary,
  type PerformanceMetrics as ContractMetrics
} from '../contracts/api-schemas';

/**
 * 回测数据工厂类
 * 提供类型安全的mock数据生成，确保业务逻辑一致性
 */
export class BacktestDataFactory {
  /**
   * 创建有效的回测配置
   * @param scenario 场景类型：normal(正常), edge_case(边界情况), invalid(无效数据)
   * @param overrides 覆盖的配置项
   * @returns 回测配置对象
   */
  static createValidConfig(
    scenario: 'normal' | 'edge_case' | 'invalid' = 'normal',
    overrides: Partial<BacktestConfig> = {}
  ): BacktestConfig {
    const baseConfig: BacktestConfig = {
      strategy_id: 'strategy-001',
      symbol: 'AAPL',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      capital: 100000,
      commission: 0.001,
      slippage: 0.001,
      benchmark: 'SPY',
      parameters: {
        fast_period: 12,
        slow_period: 26,
        signal_period: 9
      }
    };

    let scenarioConfig: Partial<BacktestConfig> = {};

    switch (scenario) {
      case 'edge_case':
        scenarioConfig = {
          capital: 1000, // 最小资金
          start_date: '2023-12-30', // 很短的时间范围
          end_date: '2023-12-31',
          commission: 0.01, // 高手续费
          slippage: 0.01 // 高滑点
        };
        break;
      case 'invalid':
        scenarioConfig = {
          capital: -1000, // 负数资金
          start_date: '2024-01-01', // 开始日期晚于结束日期
          end_date: '2023-12-31',
          commission: -0.001, // 负手续费
          symbol: '' // 空股票代码
        };
        break;
    }

    return { ...baseConfig, ...scenarioConfig, ...overrides };
  }

  /**
   * 创建符合后端API契约的策略执行请求
   * @param scenario 场景类型
   * @param overrides 覆盖参数
   */
  static createStrategyExecuteRequest(
    scenario: 'normal' | 'edge_case' | 'multi_symbol' = 'normal',
    overrides: Partial<StrategyExecuteRequest> = {}
  ): StrategyExecuteRequest {
    const baseRequest: StrategyExecuteRequest = {
      choice_symbols: ['000001.SZ'],
      start_date: '20230101',
      end_date: '20231231',
      capital: 1000000,
      benchmark_symbol: '000300.SH',
      data_source: 'local',
      n_folds: 1
    };

    let scenarioRequest: Partial<StrategyExecuteRequest> = {};

    switch (scenario) {
      case 'edge_case':
        scenarioRequest = {
          choice_symbols: ['000001.SZ'],
          start_date: '20230101',
          end_date: '20230131',
          capital: 5000  // 小于10000，会触发警告
        };
        break;
      case 'multi_symbol':
        scenarioRequest = {
          choice_symbols: ['000001.SZ', '000002.SZ', '600000.SH'],
          capital: 5000000
        };
        break;
    }

    const request = { ...baseRequest, ...scenarioRequest, ...overrides };
    
    // 验证请求符合API契约
    const isValid = ContractValidator.validateRequest(
      request,
      ApiSchemas.StrategyExecuteRequest,
      'StrategyExecuteRequest'
    );
    
    if (!isValid) {
      console.warn('生成的策略执行请求不符合API契约');
    }
    
    return request;
  }

  /**
   * 创建一致性验证的回测结果
   * @param scenario 场景类型：profitable(盈利), loss(亏损), mixed(混合), crisis(危机), precision(精度), large_dataset(大数据集)
   * @param overrides 覆盖的结果项
   * @returns 回测结果对象
   */
  static createBacktestResult(
    scenario: 'profitable' | 'loss' | 'mixed' | 'crisis' | 'precision' | 'large_dataset' | string = 'profitable',
    overrides: Partial<BacktestResult> = {}
  ): BacktestResult {
    const baseResult: BacktestResult = {
      task_id: 'task-001',
      strategy_name: 'MA Cross Strategy',
      symbol: 'AAPL',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      initial_capital: 100000,
      final_capital: 120000,
      metrics: this.createConsistentMetrics(scenario),
      trades: [],
      positions: [],
      equity_curve: [],
      generated_at: new Date().toISOString()
    };

    // 生成一致的交易记录
    baseResult.trades = this.createConsistentTrades(baseResult.metrics, scenario);
    
    // 生成一致的权益曲线
    baseResult.equity_curve = this.createConsistentEquityCurve(
      baseResult.initial_capital,
      baseResult.final_capital,
      baseResult.trades
    );

    // 生成持仓记录
    baseResult.positions = this.createPositions(scenario);

    return { ...baseResult, ...overrides };
  }

  /**
   * 创建符合后端API契约的策略执行响应
   * @param scenario 结果场景
   * @param request 对应的请求
   */
  static createStrategyExecuteResponse(
    scenario: 'profitable' | 'loss' | 'mixed' | 'no_trades' = 'profitable',
    request?: StrategyExecuteRequest
  ): StrategyExecuteResponse {
    const symbols = request?.choice_symbols || ['000001.SZ'];
    const initialCapital = request?.capital || 1000000;
    
    // 生成订单数据
    const orders = this.createContractOrders(scenario, symbols[0], 5);
    
    // 生成执行摘要
    const executionSummary = this.createExecutionSummary(scenario, symbols, initialCapital, orders);
    
    // 生成每个股票的结果
    const results = symbols.map(symbol => {
      // 确保股票代码格式符合Schema要求: (sh|sz)?\d{6}(\.S[HZ])?
      let formattedSymbol = symbol;
      if (symbol.match(/^\d{6}$/)) {
        formattedSymbol = `sz${symbol}`; // 添加sz前缀
      } else if (!symbol.match(/^(sh|sz)?\d{6}(\.S[HZ])?$/i)) {
        formattedSymbol = 'sz000001'; // 默认有效格式
      }
      
      return {
        symbol: formattedSymbol,
        orders_count: Math.max(symbol === symbols[0] ? orders.length : 0, 0),
        message: symbol === symbols[0] ? '交易完成' : '无交易信号',
        orders: symbol === symbols[0] ? orders.map(order => ({
          ...order,
          symbol: order.symbol.match(/^\d{6}$/) ? `sz${order.symbol}` : 
                  !order.symbol.match(/^(sh|sz)?\d{6}(\.S[HZ])?$/i) ? 'sz000001' : order.symbol
        })) : []
      };
    });

    const response: StrategyExecuteResponse = {
      success: true,
      message: '策略执行成功',
      data: {
        status: 'success',
        message: '策略执行完成',
        results,
        execution_summary: executionSummary,
        parameters_used: {
          initial_capital: initialCapital,
          symbols,
          start_date: request?.start_date ? request.start_date.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3') : '2023-01-01',
          end_date: request?.end_date ? request.end_date.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3') : '2023-12-31'
        }
      }
    };

    // 验证响应符合API契约
    const isValid = ContractValidator.validateResponse(
      response,
      ApiSchemas.StrategyExecuteResponse,
      'StrategyExecuteResponse'
    );
    
    if (!isValid) {
      console.warn('生成的策略执行响应不符合API契约');
    }

    return response;
  }

  /**
   * 创建符合契约的订单数据
   */
  private static createContractOrders(scenario: string, symbol: string, count: number): Order[] {
    const orders: Order[] = [];
    const baseDate = 20230101;
    
    for (let i = 0; i < count; i++) {
      const buyDate = baseDate + i * 30;
      const sellDate = buyDate + 15;
      const buyPrice = 10 + Math.random() * 5;
      const sellPrice = this.calculateSellPrice(buyPrice, scenario);
      
      // 确保股票代码格式符合Schema要求: (sh|sz)?\d{6}(\.S[HZ])?
      let formattedSymbol = symbol;
      if (symbol.match(/^\d{6}$/)) {
        formattedSymbol = `sz${symbol}`; // 添加sz前缀
      } else if (!symbol.match(/^(sh|sz)?\d{6}(\.S[HZ])?$/i)) {
        formattedSymbol = 'sz000001'; // 默认有效格式
      }
      
      orders.push({
        buy_date: buyDate,
        sell_date: sellDate,
        buy_price: Number(buyPrice.toFixed(2)),
        sell_price: Number(sellPrice.toFixed(2)),
        profit: Number(((sellPrice - buyPrice) * 100).toFixed(2)),
        symbol: formattedSymbol
      });
    }
    
    return orders;
  }

  /**
   * 创建执行摘要
   */
  private static createExecutionSummary(
    scenario: string,
    symbols: string[],
    initialCapital: number,
    orders: Order[]
  ): ExecutionSummary {
    const totalProfit = orders.reduce((sum, order) => sum + order.profit, 0);
    const finalCapital = initialCapital + totalProfit;
    const cumulativeReturn = totalProfit / initialCapital;
    
    const winningTrades = orders.filter(order => order.profit > 0).length;
    const winRate = orders.length > 0 ? Math.min(winningTrades / orders.length, 1) : 0;
    
    const profitTrades = orders.filter(order => order.profit > 0);
    const lossTrades = orders.filter(order => order.profit < 0);
    const avgProfit = profitTrades.length > 0 ? 
      profitTrades.reduce((sum, t) => sum + t.profit, 0) / profitTrades.length : 0;
    const avgLoss = lossTrades.length > 0 ? 
      Math.abs(lossTrades.reduce((sum, t) => sum + t.profit, 0) / lossTrades.length) : 1;
    
    const profitLossRatio = avgLoss > 0 ? avgProfit / avgLoss : 1;
    
    return {
      total_symbols: Math.max(symbols.length, 0),
      symbols_with_trades: Math.max(symbols.filter(s => orders.some(o => o.symbol === s)).length, 0),
      total_trades: Math.max(orders.length, 0),
      initial_capital: initialCapital,
      final_capital: finalCapital,
      cumulative_return: Number(cumulativeReturn.toFixed(4)),
      annualized_return: Number((cumulativeReturn * 365 / 252).toFixed(4)),
      max_drawdown: Number((Math.random() * 0.1).toFixed(4)),
      sharpe_ratio: Number((1 + Math.random()).toFixed(2)),
      win_rate: Number(Math.min(winRate, 1).toFixed(3)),
      profit_loss_ratio: Number(profitLossRatio.toFixed(2))
    };
  }

  /**
   * 根据场景计算卖出价格
   */
  private static calculateSellPrice(buyPrice: number, scenario: string): number {
    switch (scenario) {
      case 'profitable':
        return buyPrice * (1 + 0.05 + Math.random() * 0.1);
      case 'loss':
        return buyPrice * (1 - 0.05 - Math.random() * 0.1);
      case 'mixed':
        return buyPrice * (1 + (Math.random() - 0.5) * 0.2);
      default:
        return buyPrice * (1 + Math.random() * 0.1);
    }
  }

  /**
   * 创建回测历史数据
   * @param count 数据条数
   * @param scenarios 状态场景数组
   * @returns 回测历史项数组
   */
  static createBacktestHistory(
    count: number,
    scenarios: Array<'completed' | 'running' | 'failed'> = ['completed']
  ): BacktestTask[] {
    const history: BacktestTask[] = [];
    
    for (let i = 0; i < count; i++) {
      const scenario = scenarios[i % scenarios.length];
      const task = this.createBacktestTask(scenario, i);
      history.push(task);
    }

    return history.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  /**
   * 创建大数据集用于性能测试 - 权益曲线
   * @param size 数据点数量
   * @returns 权益曲线数组
   */
  static createLargeEquityCurve(size: number): EquityPoint[] {
    const curve: EquityPoint[] = [];
    const startDate = new Date('2020-01-01');
    const initialEquity = 100000;
    let currentEquity = initialEquity;
    let maxEquity = initialEquity;

    for (let i = 0; i < size; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      // 模拟随机波动，但整体趋势向上
      const dailyReturn = (Math.random() - 0.48) * 0.02; // 轻微正偏向
      currentEquity *= (1 + dailyReturn);
      maxEquity = Math.max(maxEquity, currentEquity);
      
      const drawdown = (currentEquity - maxEquity) / maxEquity;
      
      curve.push({
        date: date.toISOString().split('T')[0],
        equity: Math.round(currentEquity * 100) / 100,
        benchmark: Math.round(initialEquity * Math.pow(1.08, i / 365) * 100) / 100,
        drawdown: Math.round(drawdown * 10000) / 100 // 百分比，保留2位小数
      });
    }

    return curve;
  }

  /**
   * 创建大数据集用于性能测试 - 交易记录
   * @param size 交易数量
   * @returns 交易记录数组
   */
  static createLargeTrades(size: number): Trade[] {
    const trades: Trade[] = [];
    const symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'];
    const startDate = new Date('2023-01-01');
    
    for (let i = 0; i < size; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + Math.floor(i / 2)); // 每天最多2笔交易
      
      const symbol = symbols[i % symbols.length];
      const side = i % 2 === 0 ? 'buy' : 'sell';
      const price = 100 + Math.random() * 200;
      const quantity = Math.floor(Math.random() * 100) + 1;
      const amount = price * quantity;
      
      trades.push({
        id: `trade-${i.toString().padStart(6, '0')}`,
        symbol,
        side,
        quantity,
        price: Math.round(price * 100) / 100,
        amount: Math.round(amount * 100) / 100,
        commission: Math.round(amount * 0.001 * 100) / 100,
        timestamp: date.toISOString(),
        signal_type: side === 'buy' ? 'golden_cross' : 'death_cross',
        profit_loss: side === 'sell' ? Math.round((Math.random() - 0.4) * amount * 0.1 * 100) / 100 : undefined
      });
    }

    return trades.sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  // ==================== Abu项目特定方法 ====================
  
  /**
   * 创建abu策略配置
   * @param strategyType abu策略类型
   * @param overrides 覆盖参数
   */
  static createAbuStrategyConfig(
    strategyType: 'trend_follow' | 'mean_reversion' | 'pair_trading',
    overrides?: Partial<BacktestConfig>
  ): BacktestConfig {
    const baseConfig = this.createValidConfig('normal', overrides);
    
    return {
      ...baseConfig,
      ...overrides
    };
  }

  /**
   * 创建Tushare数据源配置
   * @param symbol 股票代码
   * @param overrides 覆盖参数
   */
  static createTushareDataConfig(
    symbol: string = 'AAPL',
    overrides?: Partial<BacktestConfig>
  ): BacktestConfig {
    return this.createValidConfig('normal', {
      symbol,
      ...overrides
    });
  }

  /**
   * 创建数据源降级场景配置
   * @param primarySource 主数据源
   * @param fallbackSource 降级数据源
   */
  static createDataSourceFallbackConfig(
    primarySource: 'tushare' | 'abu_legacy' = 'tushare',
    fallbackSource: 'abu_legacy' | 'tushare' = 'abu_legacy'
  ): BacktestConfig {
    return this.createValidConfig('normal', {
    });
  }

  /**
   * 创建abu策略执行响应
   * @param strategyType abu策略类型
   * @param scenario 场景类型
   */
  static createAbuStrategyResponse(
    strategyType: 'trend_follow' | 'mean_reversion' | 'pair_trading',
    scenario: 'profitable' | 'loss' | 'mixed' | 'no_trades' = 'profitable'
  ): StrategyExecuteResponse {
    const baseResponse = this.createStrategyExecuteResponse(scenario);
    
    return {
      ...baseResponse,
      data: {
        ...baseResponse.data
      }
    };
  }

  /**
   * 创建数据源切换响应
   * @param originalSource 原始数据源
   * @param actualSource 实际使用的数据源
   * @param reason 切换原因
   */
  static createDataSourceSwitchResponse(
    originalSource: string,
    actualSource: string,
    reason: string = 'quota_exceeded'
  ): StrategyExecuteResponse {
    const baseResponse = this.createStrategyExecuteResponse('profitable');
    
    return {
      ...baseResponse,
      data: {
        ...baseResponse.data
      }
    };
  }

  /**
   * 获取abu策略参数
   * @param strategyType 策略类型
   */
  private static getAbuStrategyParams(strategyType: string): Record<string, any> {
    const params: Record<string, Record<string, any>> = {
      trend_follow: {
        period: 20,
        threshold: 0.02,
        stop_loss: 0.05,
        take_profit: 0.10,
        ma_type: 'sma'
      },
      mean_reversion: {
        window: 14,
        z_score: 2.0,
        entry_threshold: 2.0,
        exit_threshold: 0.5,
        lookback_period: 252
      },
      pair_trading: {
        correlation_threshold: 0.8,
        z_score: 1.5,
        entry_z_score: 2.0,
        exit_z_score: 0.5,
        lookback_period: 60
      }
    };
    return params[strategyType] || {};
  }

  /**
   * 转换为Tushare代码格式
   * @param symbol 股票代码
   */
  private static convertToTushareCode(symbol: string): string {
    // 如果已经包含后缀，直接返回
    if (symbol.includes('.')) {
      return symbol;
    }
    
    // 美股代码添加.US后缀
    if (/^[A-Z]{1,5}$/.test(symbol)) {
      return `${symbol}.US`;
    }
    
    // A股代码添加相应后缀
    if (/^\d{6}$/.test(symbol)) {
      const code = symbol;
      if (code.startsWith('0') || code.startsWith('3')) {
        return `${code}.SZ`; // 深交所
      } else if (code.startsWith('6')) {
        return `${code}.SH`; // 上交所
      }
    }
    
    // 默认添加.US后缀
    return `${symbol}.US`;
  }

  /**
   * 验证回测指标的数学一致性
   * @param metrics 回测指标
   * @throws Error 如果数据不一致
   */
  static validateMetricsConsistency(metrics: BacktestMetrics): void {
    const errors: string[] = [];

    // 验证交易数量一致性
    if (metrics.winning_trades + metrics.losing_trades !== metrics.total_trades) {
      errors.push(`交易数量不一致: 盈利交易(${metrics.winning_trades}) + 亏损交易(${metrics.losing_trades}) ≠ 总交易数(${metrics.total_trades})`);
    }

    // 验证胜率计算
    const expectedWinRate = metrics.total_trades > 0 ? metrics.winning_trades / metrics.total_trades : 0;
    if (Math.abs(metrics.win_rate - expectedWinRate) > 0.001) {
      errors.push(`胜率计算错误: 期望 ${expectedWinRate.toFixed(3)}, 实际 ${metrics.win_rate.toFixed(3)}`);
    }

    // 验证数值范围
    if (metrics.win_rate < 0 || metrics.win_rate > 1) {
      errors.push(`胜率超出范围 [0,1]: ${metrics.win_rate}`);
    }

    if (metrics.max_drawdown > 0) {
      errors.push(`最大回撤应为负值: ${metrics.max_drawdown}`);
    }

    if (metrics.volatility < 0) {
      errors.push(`波动率不能为负: ${metrics.volatility}`);
    }

    if (errors.length > 0) {
      throw new Error(`回测指标验证失败:\n${errors.join('\n')}`);
    }
  }

  /**
   * 验证权益曲线的完整性
   * @param curve 权益曲线
   * @param initialCapital 初始资金
   * @param finalCapital 最终资金
   * @throws Error 如果数据不一致
   */
  static validateEquityCurveIntegrity(
    curve: EquityPoint[],
    initialCapital: number,
    finalCapital: number
  ): void {
    const errors: string[] = [];

    if (curve.length === 0) {
      errors.push('权益曲线为空');
      throw new Error(errors.join('\n'));
    }

    // 验证起始和结束值
    const firstPoint = curve[0];
    const lastPoint = curve[curve.length - 1];

    if (Math.abs(firstPoint.equity - initialCapital) > 0.01) {
      errors.push(`起始资金不匹配: 期望 ${initialCapital}, 实际 ${firstPoint.equity}`);
    }

    if (Math.abs(lastPoint.equity - finalCapital) > 0.01) {
      errors.push(`最终资金不匹配: 期望 ${finalCapital}, 实际 ${lastPoint.equity}`);
    }

    // 验证日期连续性
    for (let i = 1; i < curve.length; i++) {
      const prevDate = new Date(curve[i - 1].date);
      const currDate = new Date(curve[i].date);
      
      if (currDate <= prevDate) {
        errors.push(`日期顺序错误: ${curve[i - 1].date} >= ${curve[i].date}`);
      }
    }

    // 验证回撤计算
    let maxEquity = 0;
    for (const point of curve) {
      maxEquity = Math.max(maxEquity, point.equity);
      const expectedDrawdown = (point.equity - maxEquity) / maxEquity;
      
      if (Math.abs(point.drawdown - expectedDrawdown * 100) > 0.01) {
        errors.push(`回撤计算错误 ${point.date}: 期望 ${(expectedDrawdown * 100).toFixed(2)}%, 实际 ${point.drawdown.toFixed(2)}%`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`权益曲线验证失败:\n${errors.join('\n')}`);
    }
  }

  /**
   * 验证交易记录的一致性
   * @param trades 交易记录
   * @throws Error 如果数据不一致
   */
  static validateTradesConsistency(trades: Trade[]): void {
    const errors: string[] = [];

    // 验证交易记录的基本字段
    trades.forEach((trade, index) => {
      if (!trade.id || !trade.symbol || !trade.side) {
        errors.push(`交易记录 ${index} 缺少必要字段`);
      }

      if (trade.quantity <= 0) {
        errors.push(`交易记录 ${index} 数量无效: ${trade.quantity}`);
      }

      if (trade.price <= 0) {
        errors.push(`交易记录 ${index} 价格无效: ${trade.price}`);
      }

      if (Math.abs(trade.amount - trade.quantity * trade.price) > 0.01) {
        errors.push(`交易记录 ${index} 金额计算错误: ${trade.amount} ≠ ${trade.quantity} × ${trade.price}`);
      }

      if (!['buy', 'sell'].includes(trade.side)) {
        errors.push(`交易记录 ${index} 方向无效: ${trade.side}`);
      }
    });

    // 验证时间顺序
    for (let i = 1; i < trades.length; i++) {
      const prevTime = new Date(trades[i - 1].timestamp);
      const currTime = new Date(trades[i].timestamp);
      
      if (currTime < prevTime) {
        errors.push(`交易时间顺序错误: ${trades[i - 1].timestamp} > ${trades[i].timestamp}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`交易记录验证失败:\n${errors.join('\n')}`);
    }
  }

  // 私有辅助方法

  /**
   * 创建一致的回测指标
   */
  private static createConsistentMetrics(scenario: string): BacktestMetrics {
    let baseMetrics: BacktestMetrics;

    switch (scenario) {
      case 'profitable':
        baseMetrics = {
          total_trades: 50,
          winning_trades: 32,
          losing_trades: 18,
          win_rate: 0.64, // 32/50
          total_return: 0.20,
          annual_return: 0.20,
          max_drawdown: -0.05,
          sharpe_ratio: 1.5,
          profit_loss_ratio: 1.8,
          avg_holding_period: 15,
          volatility: 0.15,
          beta: 1.1,
          alpha: 0.05,
          information_ratio: 0.8
        };
        break;
      case 'loss':
        baseMetrics = {
          total_trades: 40,
          winning_trades: 15,
          losing_trades: 25,
          win_rate: 0.375, // 15/40
          total_return: -0.15,
          annual_return: -0.15,
          max_drawdown: -0.25,
          sharpe_ratio: -0.5,
          profit_loss_ratio: 0.6,
          avg_holding_period: 12,
          volatility: 0.22,
          beta: 1.3,
          alpha: -0.08,
          information_ratio: -0.4
        };
        break;
      case 'crisis':
        // 极端市场危机场景
        baseMetrics = {
          total_trades: 120,
          winning_trades: 25,
          losing_trades: 95,
          win_rate: 0.208, // 25/120
          total_return: -0.45,
          annual_return: -0.45,
          max_drawdown: -0.65,
          sharpe_ratio: -2.8,
          profit_loss_ratio: 0.3,
          avg_holding_period: 3,
          volatility: 0.85,
          beta: 2.5,
          alpha: -0.35,
          information_ratio: -1.8
        };
        break;
      case 'precision':
        // JavaScript精度测试场景
        baseMetrics = {
          total_trades: 1000,
          winning_trades: 501,
          losing_trades: 499,
          win_rate: 0.501, // 501/1000
          total_return: 0.000000001, // 极小收益
          annual_return: 0.000000001,
          max_drawdown: -0.000000001,
          sharpe_ratio: 0.000000001,
          profit_loss_ratio: 1.000000001,
          avg_holding_period: 1,
          volatility: 0.000000001,
          beta: 1.000000001,
          alpha: 0.000000001,
          information_ratio: 0.000000001
        };
        break;
      case 'large_dataset':
        // 大数据集场景
        baseMetrics = {
          total_trades: 100000,
          winning_trades: 55000,
          losing_trades: 45000,
          win_rate: 0.55, // 55000/100000
          total_return: 2.5,
          annual_return: 0.25,
          max_drawdown: -0.15,
          sharpe_ratio: 1.8,
          profit_loss_ratio: 1.4,
          avg_holding_period: 2,
          volatility: 0.20,
          beta: 1.05,
          alpha: 0.08,
          information_ratio: 1.2
        };
        break;
      default: // mixed
        baseMetrics = {
          total_trades: 60,
          winning_trades: 30,
          losing_trades: 30,
          win_rate: 0.5, // 30/60
          total_return: 0.05,
          annual_return: 0.05,
          max_drawdown: -0.12,
          sharpe_ratio: 0.3,
          profit_loss_ratio: 1.1,
          avg_holding_period: 18,
          volatility: 0.18,
          beta: 0.95,
          alpha: 0.01,
          information_ratio: 0.1
        };
    }

    return baseMetrics;
  }

  /**
   * 创建与指标一致的交易记录
   */
  private static createConsistentTrades(metrics: BacktestMetrics, scenario: string): Trade[] {
    const trades: Trade[] = [];
    const startDate = new Date('2023-01-01');
    
    // 根据指标创建对应数量的交易
    for (let i = 0; i < metrics.total_trades; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i * 7); // 每周一笔交易
      
      const isWinning = i < metrics.winning_trades;
      const side = i % 2 === 0 ? 'buy' : 'sell';
      const price = 100 + Math.random() * 50;
      const quantity = Math.floor(Math.random() * 100) + 10;
      const amount = price * quantity;
      
      let profitLoss: number | undefined;
      if (side === 'sell') {
        // 根据是否盈利设置盈亏
        profitLoss = isWinning 
          ? Math.random() * amount * 0.1 // 盈利0-10%
          : -Math.random() * amount * 0.05; // 亏损0-5%
      }
      
      trades.push({
        id: `trade-${(i + 1).toString().padStart(3, '0')}`,
        symbol: 'AAPL',
        side,
        quantity,
        price: Math.round(price * 100) / 100,
        amount: Math.round(amount * 100) / 100,
        commission: Math.round(amount * 0.001 * 100) / 100,
        timestamp: date.toISOString(),
        signal_type: side === 'buy' ? 'ma_cross_up' : 'ma_cross_down',
        profit_loss: profitLoss ? Math.round(profitLoss * 100) / 100 : undefined
      });
    }

    return trades;
  }

  /**
   * 创建一致的权益曲线
   */
  private static createConsistentEquityCurve(
    initialCapital: number,
    finalCapital: number,
    trades: Trade[]
  ): EquityPoint[] {
    const curve: EquityPoint[] = [];
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2023-12-31');
    const totalDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let currentEquity = initialCapital;
    let maxEquity = initialCapital;
    const totalReturn = (finalCapital - initialCapital) / initialCapital;
    
    for (let i = 0; i <= totalDays; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      // 线性增长 + 随机波动
      const progress = i / totalDays;
      const expectedEquity = initialCapital * (1 + totalReturn * progress);
      const noise = (Math.random() - 0.5) * initialCapital * 0.02; // ±2%随机波动
      currentEquity = expectedEquity + noise;
      
      maxEquity = Math.max(maxEquity, currentEquity);
      const drawdown = (currentEquity - maxEquity) / maxEquity * 100;
      
      curve.push({
        date: date.toISOString().split('T')[0],
        equity: Math.round(currentEquity * 100) / 100,
        benchmark: Math.round(initialCapital * Math.pow(1.08, i / 365) * 100) / 100,
        drawdown: Math.round(drawdown * 100) / 100
      });
    }

    return curve;
  }

  /**
   * 创建持仓记录
   */
  private static createPositions(scenario: string): Position[] {
    const symbols = ['AAPL', 'GOOGL', 'MSFT'];
    const positions: Position[] = [];
    
    symbols.forEach((symbol, index) => {
      const quantity = Math.floor(Math.random() * 100) + 10;
      const avgCost = 100 + Math.random() * 100;
      const currentPrice = scenario === 'profitable' 
        ? avgCost * (1 + Math.random() * 0.2) // 盈利20%以内
        : avgCost * (1 - Math.random() * 0.1); // 亏损10%以内
      
      const marketValue = quantity * currentPrice;
      const unrealizedPnl = quantity * (currentPrice - avgCost);
      
      positions.push({
        symbol,
        quantity,
        avg_cost: Math.round(avgCost * 100) / 100,
        market_value: Math.round(marketValue * 100) / 100,
        unrealized_pnl: Math.round(unrealizedPnl * 100) / 100,
        weight: Math.round((marketValue / 100000) * 10000) / 100 // 假设总资产10万
      });
    });
    
    return positions;
  }

  /**
   * 创建回测任务
   */
  private static createBacktestTask(scenario: string, index: number): BacktestTask {
    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - index);
    
    const config = this.createValidConfig();
    
    let status: BacktestStatus;
    let progress: number | undefined;
    let completedAt: string | undefined;
    let errorMessage: string | undefined;
    
    switch (scenario) {
      case 'running':
        status = Status.RUNNING;
        progress = Math.floor(Math.random() * 80) + 10; // 10-90%
        break;
      case 'failed':
        status = Status.FAILED;
        progress = Math.floor(Math.random() * 50) + 10; // 10-60%
        errorMessage = '数据获取失败：网络连接超时';
        break;
      default:
        status = Status.COMPLETED;
        progress = 100;
        completedAt = new Date(baseDate.getTime() + 3600000).toISOString(); // 1小时后完成
    }
    
    return {
      id: `task-${(index + 1).toString().padStart(3, '0')}`,
      strategy_id: config.strategy_id,
      strategy_name: `策略 ${index + 1}`,
      symbol: config.symbol,
      start_date: config.start_date,
      end_date: config.end_date,
      status,
      progress,
      created_at: baseDate.toISOString(),
      started_at: new Date(baseDate.getTime() + 60000).toISOString(), // 1分钟后开始
      completed_at: completedAt,
      error_message: errorMessage,
      config
    };
  }
}

/**
 * 回测历史项接口（用于历史列表）
 */
export interface BacktestHistoryItem {
  id: string;
  strategy_name: string;
  symbol: string;
  start_date: string;
  end_date: string;
  status: BacktestStatus;
  created_at: string;
  final_return?: number;
  max_drawdown?: number;
}