/**
 * 策略相关常量定义
 * 消除魔法值，提升代码可维护性
 */

// 策略状态常量
export const STRATEGY_STATES = {
  NEW: 'new',
  EDITING: 'editing',
  SAVING: 'saving',
  SAVED: 'saved',
  ERROR: 'error'
} as const

export type StrategyState = typeof STRATEGY_STATES[keyof typeof STRATEGY_STATES]

// 因子类型常量
export const FACTOR_TYPES = {
  BUY: 'buy',
  SELL: 'sell'
} as const

export type FactorType = typeof FACTOR_TYPES[keyof typeof FACTOR_TYPES]

// 策略ID前缀
export const STRATEGY_ID_PREFIXES = {
  TEMP: 'temp-',
  DRAFT: 'draft-'
} as const

// 对话框视图类型
export const DIALOG_VIEWS = {
  SELECTION: 'selection',
  CONFIGURATION: 'configuration'
} as const

export type DialogView = typeof DIALOG_VIEWS[keyof typeof DIALOG_VIEWS]

// 默认配置
export const DEFAULT_STRATEGY_CONFIG = {
  INITIAL_CAPITAL: 100000,
  MAX_NAME_LENGTH: 50,
  MAX_DESCRIPTION_LENGTH: 500,
  MIN_BUY_FACTORS: 1,
  MAX_FACTORS_PER_TYPE: 10
} as const

// 验证规则
export const VALIDATION_RULES = {
  STRATEGY_NAME: {
    REQUIRED: true,
    MIN_LENGTH: 1,
    MAX_LENGTH: DEFAULT_STRATEGY_CONFIG.MAX_NAME_LENGTH,
    PATTERN: /^[a-zA-Z0-9\u4e00-\u9fa5\s_-]+$/
  },
  STRATEGY_DESCRIPTION: {
    MAX_LENGTH: DEFAULT_STRATEGY_CONFIG.MAX_DESCRIPTION_LENGTH
  }
} as const

// 错误消息
export const ERROR_MESSAGES = {
  STRATEGY_NAME_REQUIRED: '策略名称不能为空',
  STRATEGY_NAME_TOO_LONG: `策略名称不能超过${DEFAULT_STRATEGY_CONFIG.MAX_NAME_LENGTH}个字符`,
  STRATEGY_NAME_INVALID: '策略名称包含无效字符',
  DESCRIPTION_TOO_LONG: `策略描述不能超过${DEFAULT_STRATEGY_CONFIG.MAX_DESCRIPTION_LENGTH}个字符`,
  BUY_FACTORS_REQUIRED: '至少需要添加一个买入因子',
  TOO_MANY_FACTORS: `每种类型的因子不能超过${DEFAULT_STRATEGY_CONFIG.MAX_FACTORS_PER_TYPE}个`,
  SAVE_FAILED: '保存策略失败，请重试',
  LOAD_FAILED: '加载策略失败，请刷新页面重试',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  VALIDATION_FAILED: '无法保存策略：验证失败'
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  STRATEGY_CREATED: '策略创建成功！',
  STRATEGY_SAVED: '策略保存成功！',
  STRATEGY_DELETED: '策略删除成功！',
  FACTOR_ADDED: '因子添加成功！',
  FACTOR_UPDATED: '因子更新成功！',
  FACTOR_DELETED: '因子删除成功！'
} as const

// UI文本
export const UI_TEXT = {
  PAGE_TITLE: '策略工场',
  PAGE_SUBTITLE: '平台的\'中央车站\'和用户的\'思想实验室\'',
  NEW_STRATEGY: '新建策略',
  SAVE_STRATEGY: '保存策略',
  CREATE_STRATEGY: '创建策略',
  EDIT_STRATEGY: '编辑策略',
  DELETE_STRATEGY: '删除策略',
  ADD_FACTOR: '添加因子',
  EDIT_FACTOR: '编辑',
  DELETE_FACTOR: '删除',
  BUY_RULES: '买入规则',
  SELL_RULES: '卖出规则',
  BASIC_INFO: '基础信息',
  STRATEGY_CONFIG: '核心配置',
  ADVANCED_SETTINGS: '高级设置',
  BACKTEST_CONFIG: '回测与方案',
  OPTIMIZATION_CONFIG: '参数优化',
  PUBLIC_STRATEGY: '公开策略',
  PRIVATE_STRATEGY: '私有策略',
  MY_STRATEGIES: '我的策略',
  LOADING: '加载中...',
  SAVING: '保存中...',
  NO_DESCRIPTION: '暂无描述',
  NO_PARAMETERS: '无参数',
  UNKNOWN_TIME: '未知时间'
} as const

// 工具函数：检查是否为新策略
export const isNewStrategy = (strategy: any): boolean => {
  return !strategy?.id || 
         strategy.id === '' || 
         strategy.id === undefined ||
         strategy.id === 'new' ||
         String(strategy.id).startsWith(STRATEGY_ID_PREFIXES.TEMP) ||
         String(strategy.id).startsWith(STRATEGY_ID_PREFIXES.DRAFT)
}

// 工具函数：生成临时ID
export const generateTempId = (): string => {
  return `${STRATEGY_ID_PREFIXES.TEMP}${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 工具函数：验证策略名称
export const validateStrategyName = (name: string): { isValid: boolean; message?: string } => {
  if (!name || name.trim() === '') {
    return { isValid: false, message: ERROR_MESSAGES.STRATEGY_NAME_REQUIRED }
  }
  
  if (name.length > VALIDATION_RULES.STRATEGY_NAME.MAX_LENGTH) {
    return { isValid: false, message: ERROR_MESSAGES.STRATEGY_NAME_TOO_LONG }
  }
  
  if (!VALIDATION_RULES.STRATEGY_NAME.PATTERN.test(name)) {
    return { isValid: false, message: ERROR_MESSAGES.STRATEGY_NAME_INVALID }
  }
  
  return { isValid: true }
}

// 工具函数：验证策略描述
export const validateStrategyDescription = (description: string): { isValid: boolean; message?: string } => {
  if (description && description.length > VALIDATION_RULES.STRATEGY_DESCRIPTION.MAX_LENGTH) {
    return { isValid: false, message: ERROR_MESSAGES.DESCRIPTION_TOO_LONG }
  }
  
  return { isValid: true }
}
