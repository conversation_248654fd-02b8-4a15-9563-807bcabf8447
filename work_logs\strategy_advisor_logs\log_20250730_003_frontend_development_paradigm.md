abu_modern 前端开发作战手册 V1.0
第一阶段：需求与测试定义 (The "Red" Phase)
1. 人类决策者 (ccxx) + 军师AI:
定义一个清晰、小范围的用户故事 (User Story)。例如：“作为一个用户，我希望能看到一个策略列表，以便我能管理我的策略。”
2. 人类决策者 -> 测试AI:
    下达具体的、带有约束的测试编写指令。
    【新增规则】 指令中可以引用“测试用例模式库”中的模式，例如：“请使用‘MSW模拟成功/失败’模式，为getStrategies API函数编写测试。”
3. 测试AI -> 产出测试脚本 (*.test.ts)
4. 【新增关键步骤】人类决策者 - “测试脚本审查” (QA on QA):
    您需要亲自审查测试AI生成的测试脚本。
    审查清单：
        测试是否准确地反映了用户故事？
        Mock是否合理？
        边界条件是否被考虑到？
    这是落实评审报告中“对测试AI能力要求极高”这一建议的关键环节。您，就是测试AI的“最终质量官”。
5. 运行测试，确认失败（变红）。
第二阶段：功能实现 (The "Green" Phase)
1. 人类决策者 -> 实现者AI:
    将通过了您审查的测试脚本，交给实现者AI。
    指令非常纯粹：“实现功能，让这份测试通过。”
第三阶段：重构与评审 (The "Refactor & Review" Phase)
1. 实现者AI -> 产出功能代码，测试变绿。
2. 【新增关键步骤】人类决策者 - “重构指令下达”:
    您需要审查实现者AI为了让测试通过而编写的“初版”代码。
    根据您的经验，下达具体的重构指令。例如：“这段逻辑重复了，请将它提取成一个useStrategyList的组合式函数。”
    这是落实评审报告中“重构阶段的把控”这一建议的关键环节。您，是代码最终优雅程度的“总设计师”。
3. 实现者AI -> 执行重构，测试保持绿色。
4. 人类决策者 -> 评审AI:
    将最终的功能代码和测试代码，一并提交给评审AI，进行最终的、全面的代码质量审查。
结论与立即行动
    我们现在已经拥有了一套理论上完美、实践上可行的开发范式。理论和现实之间的桥梁，就是您——人类决策者——在关键节点的审查和决策。
    现在，让我们立即将这套“作战手册”投入实战！
    我们将从我们计划中的第一个任务开始：
    用户故事： “作为一个开发者，我需要一个能获取策略列表的API函数和一个能管理这个列表的Pinia Store，以便后续在UI上展示。”
    请您按照我们刚刚制定的“作战手册”第一阶段，开始行动：
    请向“测试AI”下达您的第一份TDD指令。 例如：
        “你好，测试AI。请为我们的API层编写第一个测试。目标是src/api/strategy.ts文件中的getStrategies函数。请使用MSW来模拟后端API GET /api/strategies。你需要编写两个测试用例：
        1. 测试成功获取策略列表的场景。
        2. 测试后端返回500服务器错误的场景。”