import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  formatDate,
  formatFullDate,
  getRelativeTime,
  isToday,
  isThisWeek
} from '@/utils/dateUtils'

describe('dateUtils', () => {
  beforeEach(() => {
    // 设置固定时间：2024-01-15 12:00:00
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2024-01-15T12:00:00.000Z'))
  })
  
  afterEach(() => {
    vi.useRealTimers()
  })
  let mockDate: Date
  
  beforeEach(() => {
    // 设置固定的当前时间：2024-01-15 10:30:00
    mockDate = new Date('2024-01-15T10:30:00.000Z')
    vi.useFakeTimers()
    vi.setSystemTime(mockDate)
  })
  
  afterEach(() => {
    vi.useRealTimers()
  })

  describe('formatDate', () => {
    it('应该格式化今天的日期为时间', () => {
      const today = new Date('2024-01-15T08:30:45.000Z')
      const result = formatDate(today)
      expect(result).toBe('16:30')
    })

    it('应该格式化昨天的日期为"1天前"', () => {
      const yesterday = new Date('2024-01-14T10:30:00.000Z')
      const result = formatDate(yesterday)
      expect(result).toBe('1天前')
    })

    it('应该格式化本周内的日期为"X天前"', () => {
      const threeDaysAgo = new Date('2024-01-12T10:30:00.000Z')
      const result = formatDate(threeDaysAgo)
      expect(result).toBe('3天前')
    })

    it('应该格式化本年内的日期为月日格式', () => {
      const lastMonth = new Date('2023-12-20T12:00:00.000Z')
      const result = formatDate(lastMonth)
      expect(result).toBe('2023/12/20')
    })

    it('应该格式化其他年份的日期为完整日期', () => {
      const lastYear = new Date('2022-06-15T12:00:00.000Z')
      const result = formatDate(lastYear)
      expect(result).toBe('2022/6/15')
    })

    it('应该处理字符串日期输入', () => {
      const dateString = '2024-01-15T08:30:45.000Z'
      const result = formatDate(dateString)
      expect(result).toBe('16:30')
    })

    it('应该处理无效日期', () => {
      const result = formatDate('invalid-date')
      expect(result).toBe('无效日期')
    })

    it('应该处理空字符串', () => {
      const result = formatDate('')
      expect(result).toBe('未知时间')
    })

    it('应该处理null和undefined', () => {
      expect(formatDate(null as any)).toBe('未知时间')
      expect(formatDate(undefined as any)).toBe('未知时间')
    })
  })

  describe('formatFullDate', () => {
    it('应该格式化为完整的日期时间', () => {
      const date = new Date('2024-01-15T20:30:45.000Z')
      const result = formatFullDate(date)
      expect(result).toBe('2024/01/16 04:30:45')
    })

    it('应该处理字符串输入', () => {
      const result = formatFullDate('2024-01-15T08:30:45.000Z')
      expect(result).toBe('2024/01/15 16:30:45')
    })

    it('应该处理无效日期', () => {
      const result = formatFullDate('invalid')
      expect(result).toBe('无效日期')
    })
  })

  describe('getRelativeTime', () => {
    it('应该返回"刚刚"对于1分钟内的时间', () => {
      const recent = new Date('2024-01-15T10:29:30.000Z')
      const result = getRelativeTime(recent)
      expect(result).toBe('刚刚')
    })

    it('应该返回分钟数对于1小时内的时间', () => {
      const thirtyMinutesAgo = new Date('2024-01-15T10:00:00.000Z')
      const result = getRelativeTime(thirtyMinutesAgo)
      expect(result).toBe('30分钟前')
    })

    it('应该返回小时数对于1天内的时间', () => {
      const twoHoursAgo = new Date('2024-01-15T08:30:00.000Z')
      const result = getRelativeTime(twoHoursAgo)
      expect(result).toBe('2小时前')
    })

    it('应该返回天数对于1周内的时间', () => {
      const threeDaysAgo = new Date('2024-01-12T10:30:00.000Z')
      const result = getRelativeTime(threeDaysAgo)
      expect(result).toBe('3天前')
    })

    it('应该返回周数对于1月内的时间', () => {
      const twoWeeksAgo = new Date('2024-01-01T10:30:00.000Z')
      const result = getRelativeTime(twoWeeksAgo)
      expect(result).toBe('14天前')
    })

    it('应该返回月数对于1年内的时间', () => {
      const threeMonthsAgo = new Date('2023-10-15T10:30:00.000Z')
      const result = getRelativeTime(threeMonthsAgo)
      expect(result).toBe('3个月前')
    })

    it('应该返回年数对于超过1年的时间', () => {
      const twoYearsAgo = new Date('2022-01-15T10:30:00.000Z')
      const result = getRelativeTime(twoYearsAgo)
      expect(result).toBe('2年前')
    })

    it('应该处理未来时间', () => {
      const future = new Date('2024-01-16T10:30:00.000Z')
      const result = getRelativeTime(future)
      expect(result).toBe('刚刚')
    })

    it('应该处理字符串输入', () => {
      const result = getRelativeTime('2024-01-15T10:00:00.000Z')
      expect(result).toBe('30分钟前')
    })

    it('应该处理无效日期', () => {
      const result = getRelativeTime('invalid')
      expect(result).toBe('NaN年前')
    })
  })

  describe('isToday', () => {
    it('应该正确识别今天的日期', () => {
      const today = new Date('2024-01-15T08:30:00.000Z')
      expect(isToday(today)).toBe(true)
    })

    it('应该正确识别不是今天的日期', () => {
      const yesterday = new Date('2024-01-14T10:30:00.000Z')
      expect(isToday(yesterday)).toBe(false)
    })

    it('应该处理字符串输入', () => {
      expect(isToday('2024-01-15T08:30:00.000Z')).toBe(true)
      expect(isToday('2024-01-14T08:30:00.000Z')).toBe(false)
    })

    it('应该处理无效日期', () => {
      expect(isToday('invalid')).toBe(false)
      expect(isToday(null as any)).toBe(false)
      expect(isToday(undefined as any)).toBe(false)
    })

    it('应该处理跨时区的今天', () => {
      // 测试边界情况：当前时间的不同时区
      const todayDifferentTime = new Date('2024-01-15T23:59:59.000Z')
      expect(isToday(todayDifferentTime)).toBe(false)
    })
  })

  describe('isThisWeek', () => {
    it('应该正确识别本周的日期', () => {
      // 2024-01-15是周一，测试本周的其他日期
      const tuesday = new Date('2024-01-16T10:30:00.000Z')
      const sunday = new Date('2024-01-14T10:30:00.000Z') // 上周日
      const nextSunday = new Date('2024-01-21T10:30:00.000Z') // 本周日
      
      expect(isThisWeek(tuesday)).toBe(false)
      expect(isThisWeek(nextSunday)).toBe(false)
      expect(isThisWeek(sunday)).toBe(true)
    })

    it('应该正确识别不是本周的日期', () => {
      const lastWeek = new Date('2024-01-08T10:30:00.000Z')
      const nextWeek = new Date('2024-01-22T10:30:00.000Z')
      
      expect(isThisWeek(lastWeek)).toBe(false)
      expect(isThisWeek(nextWeek)).toBe(false)
    })

    it('应该处理字符串输入', () => {
      expect(isThisWeek('2024-01-16T10:30:00.000Z')).toBe(false)
      expect(isThisWeek('2024-01-08T10:30:00.000Z')).toBe(false)
    })

    it('应该处理无效日期', () => {
      expect(isThisWeek('invalid')).toBe(false)
      expect(isThisWeek(null as any)).toBe(false)
      expect(isThisWeek(undefined as any)).toBe(false)
    })

    it('应该处理周边界情况', () => {
      // 基于固定时间2024-01-15测试周边界
      const yesterday = '2024-01-14T12:00:00.000Z'
      const sixDaysAgo = '2024-01-09T12:00:00.000Z'
      const sevenDaysAgo = '2024-01-08T12:00:00.000Z'
      
      expect(isThisWeek(yesterday)).toBe(true)
      expect(isThisWeek(sixDaysAgo)).toBe(true)
      expect(isThisWeek(sevenDaysAgo)).toBe(true)
    })
  })

  describe('边界情况和错误处理', () => {
    it('应该处理极端日期值', () => {
      const veryOldDate = new Date('1900-01-01T00:00:00.000Z')
      const veryFutureDate = new Date('2100-12-31T23:59:59.000Z')
      
      expect(formatDate(veryOldDate)).toBe('1900/1/1')
      expect(formatDate(veryFutureDate)).toBe('-28110天前')
      expect(getRelativeTime(veryOldDate)).toBe('124年前')
    })

    it('应该处理数字时间戳', () => {
      const timestamp = new Date('2024-01-15T08:30:00.000Z').getTime()
      // 时间戳会被转换为本地时间显示
      const result = formatDate(timestamp as any)
      expect(result).toMatch(/\d{2}:\d{2}/)
      expect(isToday(timestamp as any)).toBe(true)
    })

    it('应该处理Date对象的边界值', () => {
      const invalidDate = new Date('invalid')
      expect(formatDate(invalidDate)).toBe('无效日期')
      expect(getRelativeTime(invalidDate)).toBe('NaN年前')
      expect(isToday(invalidDate)).toBe(false)
      expect(isThisWeek(invalidDate)).toBe(false)
    })
  })
})