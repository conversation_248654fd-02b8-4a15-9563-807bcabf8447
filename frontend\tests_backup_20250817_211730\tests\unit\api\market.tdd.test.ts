import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { setActivePinia, createPinia } from 'pinia';
import { nextTick } from 'vue';
import { getSymbols, getKlineData } from '@/api/market';
import { useAppStore } from '@/stores/app';
import { handlers } from '../mocks/handlers';
import type { SymbolsResponse, KlineDataResponse } from '@/api/types/index';

const mockSymbolsResponse: SymbolsResponse = {
  data: [
    { symbol: 'AAPL', name: 'Apple Inc.', market: 'US', industry: 'Technology', list_date: '1980-12-12' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.', market: 'US', industry: 'Technology', list_date: '2004-08-19' },
  ],
  total: 2,
  success: true,
  message: null
};

const mockKlineDataResponse: KlineDataResponse = {
  data: {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    market: 'US',
    period: '1d',
    data: [
      { date: '2023-01-01', open: 150, high: 155, low: 149, close: 154, volume: 1000000, amount: null, turnover_rate: null, change_rate: null },
      { date: '2023-01-02', open: 154, high: 158, low: 153, close: 157, volume: 1200000, amount: null, turnover_rate: null, change_rate: null },
    ],
    latest_date: '2023-01-02',
    indicators: null
  },
  success: true,
  message: null
};

// 设置MSW服务器
const server = setupServer(...handlers);

describe('Market Data API', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen();
  });
  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('getSymbols', () => {
    it('should return a list of symbols successfully', async () => {
      const response = await getSymbols();
      expect(response).toEqual(mockSymbolsResponse);
      expect(response.data).toHaveLength(2);
      expect(response.success).toBe(true);
    });

    it('should handle data source unavailable error', async () => {
        server.use(http.get('/market/symbols', () => new HttpResponse('Data source unavailable', { status: 503 })));
        await expect(getSymbols()).rejects.toThrow('Data source unavailable');
    });

    it('should manage loading state correctly when getting symbols', async () => {
      const store = useAppStore();
      const promise = getSymbols();
      await nextTick();
      expect(store.isLoading).toBe(true);
      await promise;
      expect(store.isLoading).toBe(false);
    });
  });

  describe('getKlineData', () => {
    it('should return kline data successfully', async () => {
      const response = await getKlineData('AAPL', '1d');
      expect(response).toEqual(mockKlineDataResponse);
      expect(response.data.symbol).toBe('AAPL');
      expect(response.data.data).toHaveLength(2);
      expect(response.success).toBe(true);
    });

    it('should handle invalid symbol', async () => {
      server.use(http.get('/market/kline/:symbol', ({ params }) => {
        if (params.symbol === 'INVALID') {
          return new HttpResponse('Invalid symbol', { status: 400 });
        }
        return HttpResponse.json(mockKlineDataResponse);
      }));
      await expect(getKlineData('INVALID', '1d')).rejects.toThrow('Invalid symbol');
    });

    it('should handle invalid time period parameter', async () => {
      server.use(http.get('/market/kline/:symbol', ({ request }) => {
        const url = new URL(request.url);
        const period = url.searchParams.get('period');
        if (period === 'invalid-period') {
          return new HttpResponse('Invalid period', { status: 400 });
        }
        return HttpResponse.json(mockKlineDataResponse);
      }));
      await expect(getKlineData('AAPL', 'invalid-period')).rejects.toThrow('Invalid period');
    });

    it('should manage loading state correctly when getting kline data', async () => {
      const store = useAppStore();
      const promise = getKlineData('AAPL', '1d');
      await nextTick();
      expect(store.isLoading).toBe(true);
      await promise;
      expect(store.isLoading).toBe(false);
    });
  });
});