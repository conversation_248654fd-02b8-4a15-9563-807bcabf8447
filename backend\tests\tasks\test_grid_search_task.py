import pytest
import pandas as pd
from unittest.mock import patch, MagicMock

# ------------------ 福尔摩斯最终修正版 v4 (任务测试) ------------------
# 论证：AI的“内省勘探”已证实，当替换 __repr__ 方法时，我们提供的替代函数
#       必须能够接收一个隐式的 `self` 参数，否则将在运行时抛出 TypeError。
#       我们之前的 lambda: ... 定义了一个0参数函数，因此必然失败。
# 修正：将 lambda 表达式修改为 lambda self: ... 或 lambda _: ...，
#       使其能够正确接收那个被传入的、指向模拟对象本身的参数。

from app.tasks.grid_search_task import run_grid_search

@pytest.fixture
def mock_abupy_calls():
    """一个强大的 Fixture，同时模拟 GridSearch 和我们自己的 FactorAdapter。"""
    with patch('app.tasks.grid_search_task.GridSearch.grid_search') as mock_search, \
         patch('app.tasks.grid_search_task.AbuPyFactorAdapter.adapt_factors') as mock_adapter:
        
        mock_adapter.side_effect = lambda factors: factors
        yield mock_search, mock_adapter

def test_run_grid_search_task_success(mock_abupy_calls):
    """测试网格搜索任务的成功执行流程。"""
    mock_grid_search, mock_adapter = mock_abupy_calls

    # 1. 模拟 GridSearch.grid_search 的返回值
    mock_scores = pd.Series([0.8, 0.9], index=[0, 1])
    mock_score_tuple = MagicMock()

    # 决定性的修正：lambda 必须能接收一个参数
    buy_class_mock = MagicMock()
    buy_class_repr_str = "<class 'abupy.FactorBuyBu.ABuFactorBuyBreak.AbuFactorBuyBreak'>"
    # 使用 `_` 作为参数名，表明我们接收它，但不会使用它
    buy_class_mock.__repr__ = lambda _: buy_class_repr_str

    sell_class_mock = MagicMock()
    sell_class_repr_str = "<class 'abupy.FactorSellBu.AbuFactorSellBreak.AbuFactorSellBreak'>"
    sell_class_mock.__repr__ = lambda _: sell_class_repr_str

    # 在返回值中使用我们配置好的、“会说话”的模拟对象
    mock_score_tuple.buy_factors = [{'class': buy_class_mock, 'xd': 30}]
    mock_score_tuple.sell_factors = [{'class': sell_class_mock, 'xd': 15}]
    
    mock_score_tuple_array = [MagicMock(), mock_score_tuple]
    mock_grid_search.return_value = (mock_scores, mock_score_tuple_array)

    # 2. 任务输入
    choice_symbols = ['usTSLA']
    buy_factors = [{'name': 'AbuFactorBuyBreak', 'params': {'xd': [20, 30]}}]
    sell_factors = [{'name': 'AbuFactorSellBreak', 'params': {'xd': [10, 15]}}]
    read_cash = 1000000

    # 3. 执行任务
    result = run_grid_search(choice_symbols, buy_factors, sell_factors, read_cash)

    # 4. 断言
    assert result['status'] == 'SUCCESS'
    assert 'result' in result
    res_data = result['result']
    assert res_data['best_score'] == 0.9

    assert buy_class_repr_str in res_data['best_buy_factors']
    assert sell_class_repr_str in res_data['best_sell_factors']

    mock_grid_search.assert_called_once()
    assert mock_adapter.call_count == 2

def test_run_grid_search_task_exception(mock_abupy_calls):
    """测试当 GridSearch 抛出异常时的任务行为。"""
    mock_grid_search, _ = mock_abupy_calls
    error_message = "Something went wrong"
    mock_grid_search.side_effect = Exception(error_message)

    choice_symbols = ['usTSLA']
    buy_factors = [{'name': 'AbuFactorBuyBreak', 'params': {'xd': [20]}}]
    sell_factors = [{'name': 'AbuFactorSellBreak', 'params': {'xd': [10]}}]
    read_cash = 1000000

    result = run_grid_search(choice_symbols, buy_factors, sell_factors, read_cash)

    assert result['status'] == 'FAILURE'
    assert result['message'] == error_message

def test_run_grid_search_no_results(mock_abupy_calls):
    """测试当 GridSearch 返回空结果时的任务行为。"""
    mock_grid_search, _ = mock_abupy_calls
    mock_scores = pd.Series([], dtype=float)
    mock_score_tuple_array = []
    mock_grid_search.return_value = (mock_scores, mock_score_tuple_array)

    choice_symbols = ['usTSLA']
    buy_factors = [{'name': 'AbuFactorBuyBreak', 'params': {'xd': [20]}}]
    sell_factors = [{'name': 'AbuFactorSellBreak', 'params': {'xd': [10]}}]
    read_cash = 1000000

    result = run_grid_search(choice_symbols, buy_factors, sell_factors, read_cash)

    assert result['status'] == 'SUCCESS'
    assert result['message'] == 'Grid search completed, but no valid results found.'