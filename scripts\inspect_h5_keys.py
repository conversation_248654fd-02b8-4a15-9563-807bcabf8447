import pandas as pd
import os

# 这是我们仓库文件的路径
H5_FILE_PATH = r'D:\智能投顾\量化相关\abu_modern\data\market_data.h5'

def inspect_h5_store():
    """
    一个简单的脚本，用于检查HDF5文件中存储的所有key。
    """
    print("--- HDF5 数据仓库库存盘点报告 ---")
    
    if not os.path.exists(H5_FILE_PATH):
        print(f"[错误] 仓库文件不存在: {H5_FILE_PATH}")
        return

    try:
        with pd.HDFStore(H5_FILE_PATH, mode='r') as store:
            print(f"成功打开仓库: {H5_FILE_PATH}")
            
            keys = store.keys()
            
            if not keys:
                print("\n[盘点结果] 仓库是空的，里面没有任何物资（keys）。")
            else:
                print("\n[盘点结果] 仓库中发现以下物资（keys）:")
                for key in keys:
                    # HDFStore的key通常以'/'开头，我们打印出来方便观察
                    print(f"  - {key}")
        
        print("\n--- 报告结束 ---")

    except Exception as e:
        print(f"\n[严重错误] 盘点过程中发生意外: {e}")

if __name__ == "__main__":
    inspect_h5_store()