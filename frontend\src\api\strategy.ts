import request from './request';

// 获取策略列表
export const getStrategies = () => {
  return request.get('/strategies');
};

// 根据ID获取单个策略
export const getStrategyById = (id: string) => {
  return request.get(`/strategies/${id}`);
};

// 创建策略
export const createStrategy = (strategyData: any) => {
  return request.post('/strategies', strategyData);
};

// 更新策略
export const updateStrategy = (id: string, strategyData: any) => {
  return request.put(`/strategies/${id}`, strategyData);
};

// 删除策略
export const deleteStrategy = (id: string) => {
  return request.delete(`/strategies/${id}`);
};

// 执行策略
export const executeStrategy = (id: string, executeParams: any) => {
  return request.post(`/strategies/${id}/execute`, executeParams);
};