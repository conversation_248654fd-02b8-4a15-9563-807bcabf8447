import { describe, it, expect, vi, afterEach } from 'vitest';
import { useBacktestStore } from '../../../src/stores/useBacktestStore';
import type { BacktestConfig } from '@/api/types/backtest';
import type { BacktestResult } from '@/api/types/backtest';
import { BacktestStatus } from '@/api/types/backtest';
import type { BacktestTask } from '@/api/types/backtest';
import * as backtestApi from '@/api/backtest';

// TDD专用测试文件 - useBacktestStore
// 这是简化版本，专注于核心功能的快速验证

// 契约 1: 外部依赖必须被完全模拟
vi.mock('@/api/backtest');

// 契约 2: 定义标准的模拟数据
const mockBacktestConfig: BacktestConfig = {
  strategy_id: 'test_strategy_id',
  symbol: '000001.SZ',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  capital: 100000,
  commission: 0.001,
  slippage: 0.001,
  benchmark: '000300.SH',
  parameters: {
    period: 20,
    threshold: 0.05
  }
};

const mockBacktestTask: BacktestTask = {
  id: 'test-task-123',
  strategy_id: 'test_strategy_id',
  strategy_name: 'test_strategy',
  symbol: '000001.SZ',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  status: BacktestStatus.RUNNING,
  progress: 0,
  created_at: '2023-01-01T00:00:00Z',
  config: mockBacktestConfig,
};

const mockBacktestResult: BacktestResult = {
  task_id: 'test-task-123',
  strategy_name: 'test_strategy',
  symbol: '000001.SZ',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  initial_capital: 100000,
  final_capital: 115000,
  metrics: {
    total_return: 0.15,
    annual_return: 0.12,
    max_drawdown: 0.08,
    sharpe_ratio: 1.2,
    volatility: 0.18,
    win_rate: 0.65,
    profit_loss_ratio: 1.5,
    total_trades: 10,
    winning_trades: 7,
    losing_trades: 3,
    avg_holding_period: 5.2
  },
  equity_curve: [
    { date: '2023-01-01', equity: 100000, drawdown: 0 },
    { date: '2023-06-01', equity: 105000, drawdown: 0 },
    { date: '2023-12-31', equity: 115000, drawdown: 0 }
  ],
  trades: [
    {
      id: 'trade-1',
      symbol: '000001.SZ',
      side: 'buy',
      quantity: 1000,
      price: 10.5,
      amount: 10500,
      timestamp: '2023-01-15T09:30:00Z',
      commission: 10.5
    }
  ],
  positions: [],
  generated_at: '2023-01-01T01:00:00Z'
};

// 简化的成功响应包装器
const mockSuccess = <T>(data: T) => ({ data });

describe('useBacktestStore - TDD专用测试', () => {

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态', () => {
      const store = useBacktestStore();
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestProgress).toBe(0);
      expect(store.backtestResult).toBeNull();
      expect(store.backtestHistory).toEqual([]);
      expect(store.backtestError).toBe('');
      expect(store.isLoadingResults).toBe(false);
    });
  });
  
  describe('契约 B: 启动回测流程 (startBacktest)', () => {
    it('成功启动后，状态应立即变为isBacktesting=true，并设置当前任务', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockResolvedValue({ data: mockBacktestTask, success: true });
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(true);
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
      expect(backtestApi.runBacktest).toHaveBeenCalledWith(mockBacktestConfig);
    });
    
    it('启动失败时，必须设置错误信息，且状态不能是isBacktesting', async () => {
      const store = useBacktestStore();
      const errorMessage = '策略参数无效';
      vi.mocked(backtestApi.runBacktest).mockRejectedValue(new Error(errorMessage));
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestError).toBe(errorMessage);
      expect(store.currentBacktestTask).toBeNull();
    });
  });
  
  describe('契约 D: 获取结果 (loadBacktestResults)', () => {
    it('成功获取已完成的结果后，应更新结果，并设置isBacktesting为false', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.getBacktestResults).mockResolvedValue({ data: mockBacktestResult, success: true });
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestResult).toEqual(mockBacktestResult);
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestError).toBe('');
    });
  });
  
  describe('契约 F: 停止回测 (stopCurrentBacktest)', () => {
    it('成功停止后，状态应更新，isBacktesting为false，任务被清理', async () => {
      const store = useBacktestStore();
      store.isBacktesting = true;
      store.currentBacktestTask = { id: 'test-task-123' } as any;
      vi.mocked(backtestApi.stopBacktest).mockResolvedValue({ data: mockBacktestTask, success: true });
      
      await store.stopCurrentBacktest();
      
      expect(store.isBacktesting).toBe(false);
      expect(store.currentBacktestTask).toBeNull();
      expect(store.backtestError).toBe('');
      expect(backtestApi.stopBacktest).toHaveBeenCalledWith('test-task-123');
    });
  });
  
  describe('契约 G: 状态清理', () => {
    it('clearError: 应该清除错误信息', () => {
      const store = useBacktestStore();
      store.backtestError = '测试错误';
      store.clearError();
      expect(store.backtestError).toBe('');
    });
  
    it('resetBacktestState: 应该重置所有相关状态', () => {
      const store = useBacktestStore();
      store.isBacktesting = true;
      store.backtestProgress = 50;
      store.backtestResult = mockBacktestResult;
      store.backtestError = '测试错误';
      store.currentBacktestTask = { id: 'test-task' } as any;
      
      store.resetBacktestState();
      
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestProgress).toBe(0);
      expect(store.backtestResult).toBeNull();
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toBeNull();
    });
  });
  
  describe('契约 H: 计算属性', () => {
    it('hasResult: 必须在backtestResult有值时为true，否则为false', () => {
      const store = useBacktestStore();
      expect(store.hasResult).toBe(false);
      store.backtestResult = mockBacktestResult;
      expect(store.hasResult).toBe(true);
    });
    
    it('isCompleted: 必须在结果状态为COMPLETED时为true', () => {
      const store = useBacktestStore();
      expect(store.isCompleted).toBe(false);
      store.currentBacktestTask = { ...mockBacktestTask, status: BacktestStatus.RUNNING };
      expect(store.isCompleted).toBe(false);
      store.currentBacktestTask = { ...mockBacktestTask, status: BacktestStatus.COMPLETED };
      expect(store.isCompleted).toBe(true);
    });

    it('hasActiveBacktest: 必须在isBacktesting为true时为true', () => {
      const store = useBacktestStore();
      expect(store.hasActiveBacktest).toBe(false);
      store.isBacktesting = true;
      expect(store.hasActiveBacktest).toBe(true);
      store.isBacktesting = false;
      expect(store.hasActiveBacktest).toBe(false);
    });
  });
});