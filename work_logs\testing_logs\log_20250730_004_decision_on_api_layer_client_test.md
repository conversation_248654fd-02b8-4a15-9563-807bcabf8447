决策日志：关于API层TDD测试脚本的审查与指令
决策者: ccxx
日期: 2025-07-30
审查对象: client.test.ts (由测试AI生成)

1. 审查结论
经审查，测试AI生成的API客户端基础设施测试脚本，在宏观上覆盖了主要的用户故事，Mock策略基本合理。但存在以下问题需要修正：

📋 覆盖度评估
✅ 成功响应处理 - 已覆盖
✅ 网络错误处理 - 已覆盖404、500、网络错误
✅ 请求拦截器loading状态 - 已覆盖
✅ 响应拦截器数据格式化 - 已覆盖
⚠️ 发现的技术问题
断言质量不足: 错误处理测试过于粗糙，只验证"会抛错"，未验证错误类型、状态码和错误信息
异步处理有风险: 使用vi.dynamicImportSettled()等待状态更新，可能导致时序问题
测试数据真实性存疑: 使用测试专用端点（如/api/test-success），需确认Mock数据是否匹配真实后端Schema
错误场景loading状态验证不完整: 只验证了错误后loading为false，未验证错误发生前loading是否为true
2. 行动指令
指令给"测试AI"：
请对你之前生成的client.test.ts测试脚本进行以下修改和补充，然后提交V2版本：

(1)强化错误处理断言: 修改所有错误测试用例，使其验证具体的错误类型、状态码和错误信息，而不仅仅是rejects.toThrow()
(2)修复异步处理: 将vi.dynamicImportSettled()替换为更可靠的异步状态等待方法
(3)完善loading状态测试: 在错误场景测试中，验证loading状态的完整变化周期（false -> true -> false）
(4)确认Mock数据真实性: 确保handlers中的响应数据结构与真实后端API Schema一致
优先级: 高 - 这些问题影响测试的可靠性和有效性