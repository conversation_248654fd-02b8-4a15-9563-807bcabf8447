import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StrategyEditor from '@/components/StrategyWorkshop/StrategyEditor.vue'
import { useStrategyStore } from '@/stores'
import type { Strategy } from '@/api/types'

// Mock策略工具
vi.mock('@/composables/useStrategyEditor', () => ({
  strategyUtils: {
    generateDescription: vi.fn((strategy: Strategy) => {
      if (!strategy.buy_factors?.length && !strategy.sell_factors?.length) {
        return '请添加买入和卖出因子来完善此策略'
      }
      return '自动生成的策略描述'
    })
  },
  useStrategyEditor: vi.fn(() => ({
    editingStrategy: { value: null },
    isNewStrategyFlag: { value: false },
    canSaveStrategy: { value: true },
    isSaving: { value: false },
    saveStatus: { value: null },
    saveStatusText: { value: '' },
    saveStatusClass: { value: '' },
    updateStrategy: vi.fn(),
    resetChanges: vi.fn()
  }))
}))

// Mock Element Plus组件
vi.mock('element-plus', () => ({
  ElSkeleton: { name: 'ElSkeleton', template: '<div>Loading...</div>' },
  ElTabs: { name: 'ElTabs', template: '<div><slot /></div>' },
  ElTabPane: { name: 'ElTabPane', template: '<div><slot /></div>' },
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElTag: { name: 'ElTag', template: '<span><slot /></span>' },
  ElForm: { name: 'ElForm', template: '<form><slot /></form>' },
  ElFormItem: { name: 'ElFormItem', template: '<div><slot /></div>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElSwitch: { name: 'ElSwitch', template: '<input type="checkbox" />' }
}))

describe('策略描述集成测试', () => {
  let pinia: any
  let strategyStore: any
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    strategyStore = useStrategyStore()
  })

  it('应该在创建新策略时自动填充顶部描述栏', async () => {
    // 模拟新策略
    const newStrategy: Strategy = {
      id: 'new-strategy',
      name: '新策略',
      description: '', // 空描述
      is_public: false,
      buy_factors: [],
      sell_factors: [],
      author: '测试用户',
      create_time: new Date().toISOString()
    }

    // 设置当前选中策略
    strategyStore.setCurrentSelectedStrategy(newStrategy)
    
    const wrapper = mount(StrategyEditor, {
      props: {
        strategy: newStrategy,
        loading: false
      },
      global: {
        plugins: [pinia],
        stubs: {
          StrategyHeader: {
            name: 'StrategyHeader',
            props: ['strategy'],
            template: '<div class="strategy-header"><h2>{{ strategy.name }}</h2><p class="strategy-desc">{{ strategy.description || "暂无描述" }}</p></div>'
          },
          BasicInfoForm: {
            name: 'BasicInfoForm', 
            props: ['strategy'],
            emits: ['update'],
            template: '<div class="basic-info-form">基础信息表单</div>',
            mounted() {
              // 模拟自动生成描述并发出更新事件
              if (!this.strategy.description) {
                this.$emit('update', {
                  name: this.strategy.name,
                  description: '请添加买入和卖出因子来完善此策略',
                  is_public: this.strategy.is_public
                })
              }
            }
          },
          FactorManager: {
            name: 'FactorManager',
            template: '<div>因子管理器</div>'
          }
        }
      }
    })
    
    // 等待组件初始化完成
    await wrapper.vm.$nextTick()
    await wrapper.vm.$nextTick()

    // 验证StrategyHeader显示了更新后的描述
    const strategyHeader = wrapper.findComponent({ name: 'StrategyHeader' })
    expect(strategyHeader.exists()).toBe(true)
    
    // 检查传递给StrategyHeader的strategy prop
    const headerProps = strategyHeader.props('strategy')
    expect(headerProps).toBeDefined()
    expect(headerProps.description).toBe('请添加买入和卖出因子来完善此策略')
  })

  it('应该在策略描述更新时同步更新顶部显示', async () => {
    const strategy: Strategy = {
      id: 'test-strategy',
      name: '测试策略',
      description: '原始描述',
      is_public: false,
      buy_factors: [],
      sell_factors: [],
      author: '测试用户',
      create_time: new Date().toISOString()
    }

    strategyStore.setCurrentSelectedStrategy(strategy)
    
    const wrapper = mount(StrategyEditor, {
      props: {
        strategy: strategy,
        loading: false
      },
      global: {
        plugins: [pinia],
        stubs: {
          StrategyHeader: {
            name: 'StrategyHeader',
            props: ['strategy'],
            template: '<div class="strategy-header"><p class="strategy-desc">{{ strategy.description || "暂无描述" }}</p></div>'
          },
          BasicInfoForm: {
            name: 'BasicInfoForm',
            props: ['strategy'],
            emits: ['update'],
            template: '<div class="basic-info-form"><button @click="updateDescription">更新描述</button></div>',
            methods: {
              updateDescription() {
                this.$emit('update', {
                  name: this.strategy.name,
                  description: '更新后的描述',
                  is_public: this.strategy.is_public
                })
              }
            }
          },
          FactorManager: {
            name: 'FactorManager',
            template: '<div>因子管理器</div>'
          }
        }
      }
    })
    
    await wrapper.vm.$nextTick()

    // 模拟描述更新
    const basicInfoForm = wrapper.findComponent({ name: 'BasicInfoForm' })
    const updateButton = basicInfoForm.find('button')
    await updateButton.trigger('click')
    
    await wrapper.vm.$nextTick()

    // 验证StrategyHeader接收到了更新后的策略
    const strategyHeader = wrapper.findComponent({ name: 'StrategyHeader' })
    const headerProps = strategyHeader.props('strategy')
    expect(headerProps.description).toBe('更新后的描述')
  })
})