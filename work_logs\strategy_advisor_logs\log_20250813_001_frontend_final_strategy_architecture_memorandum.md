# 战略备忘录 (Strategic Memorandum) - 军师AI

## 基本信息

| 字段 | 内容 |
|------|------|
| 备忘录ID | `SM-20250813-003` |
| 主题 | **`abu_modern` 前端策略工厂核心工作流与信息架构最** |
| 创建日期 | 2025年8月13日 |
| AI角色 | 军师AI / 战略顾问AI |
| 主要指令来源/人类决策者 | ccxx |
| 关联项目阶段/里程碑 | 在完成对`abupy`核心机制的最终勘探后，对前端"策略工场"等核心模块的UI布局、数据流和交互模式进行最终的、决定性的战略确认。 |

---

## 1. 背景：从"勘探真相"到"设计定稿"

在获得了一系列关于`abupy`底层真实机制（特别是仓位管理和卖出因子作用域）的深度勘探报告后，我们对如何构建一个既符合`abupy`能力、又具备优秀用户体验的前端界面，有了前所未有的清晰认知。本次讨论的目的，就是将这些基于"事实"的认知，转化为**最终的、不可更改的**前端设计原则和信息架构。

## 2. "策略工场"最终设计方案

### 2.1 标签页职责

#### 总体布局
- 采用左右分栏设计
- 左侧：策略管理区("我的策略")
  - 策略列表展示
  - 策略选择功能
  - 新建策略入口
- 右侧：策略编辑区
  - 顶部：当前策略简介
  - 下方：四大核心功能标签页
    1. `核心配置`：因子编排中心
    2. `高级设置`：全局性高级配置
    3. `参数优化`：参数寻优工作台
    4. `回测与方案`：策略验证与执行

### 2.2 各标签页详细设计

#### A. 核心配置标签页

**布局结构**
- 上半部分(基础信息)
  - 策略名称输入框
  - 策略描述文本域
  - 公开策略开关
- 下半部分(因子编排)
  - 可视化因子编辑器
    - 买入规则区域
    - 卖出规则区域
- 底部有一个“重置按钮”和“保存策略”按钮，左右布局且居中；
  - 重置按钮： 一个红色的、主操作按钮 ElButton，文本为 “重置策略”。
  - 保存按钮： 一个绿色的、主操作按钮 ElButton，文本为 “保存策略”。

**核心特性**
- 每个买入因子卡片配备"+ 添加专属卖出规则"按钮
- 专属卖出规则数据存储于买入因子对象的`sell_factors`数组

#### B. 高级设置标签页

**功能模块**
**仓位管理策略 (Position Sizing) **
   - 提供一个唯一的、全局的配置界面（如“动态算法” vs “固定比例”切换）。
**全局卖出规则 (Global Sell Factors) **
   - 提供一个独立的可视化因子编辑器，用于配置对所有买入因子都生效的卖出规则。

**布局结构**
   - 上下布局，上半部分为仓位管理策略，下半部分为全局卖出规则。
   - 底部有一个“重置按钮”和“保存策略”按钮，左右布局且居中；

1. 仓位管理策略 (Position Sizing) 
   - 标题： “仓位管理策略”
   - UI组件：
    - 一个**ElRadioGroup单选按钮组**，用于切换“仓位模式”。选项为：
      - “动态算法仓位”
      - “固定比例仓位”
   - 当选中“动态算法仓位”时：
     - 显示一个**ElSelect下拉框**，标签为“选择算法”。
     - 下拉框的选项，应来自后端API提供的所有可用仓位管理算法（AbuAtrPosition, AbuKellyPosition等）。
     - 根据用户选择的算法，动态渲染出该算法所需参数的配置表单。
   - 当选中“固定比例仓位”时：
     - 显示一个**ElInputNumber或ElSlider**，让用户输入一个0-100的百分比。
2. 全局卖出规则 (Global Sell Factors) 
   - 标题： “全局卖出规则”
   - UI组件：
    - 一个与“核心配置”中卖出规则区域一模一样的**“可视化因子编辑器”**。
    - 用户可以在这里，通过“+ 添加因子”按钮，为整个策略添加通用的、全局生效的卖出规则（例如，一个全局的ATR止损）。
   - 提示信息：
     - 必须在该区域的顶部，有一段清晰的提示文字：“注意：此处的卖出规则将对所有买入因子生效。”

3. 重置与保存按钮
   - 重置按钮： 一个红色的、主操作按钮 ElButton，文本为 “重置”。
   - 保存按钮： 一个绿色的、主操作按钮 ElButton，文本为 “保存”。
   - 交互： 点击重置按钮后，将当前策略配置重置为默认值；点击保存按钮后，将当前策略配置提交到后端保存。

**数据处理**
- 仓位管理的配置，在提交时由前端逻辑自动注入到所有买入因子的parameters.position中。
- 全局卖出规则的配置，作为顶级的sell_factors字段提交。

#### C. “参数优化”标签页最终设计文稿
**核心功能** 
- 对策略中的数值型参数进行自动化寻优。

**模块定位与工作流**
“参数优化”是一个典型的异步长耗时任务工作台。其核心工作流被设计为三个清晰的、非阻塞的阶段：配置 -> 监控 -> 分析。

** UI布局与组件设计**
1. 参数范围设置区 (The Lab Bench)
   - 标题： 参数范围设置
   - UI组件：
   - 自动参数扫描列表： 界面加载时，自动扫描当前策略（从Pinia Store中获取）的所有数值型参数，并以卡片化列表的形式展示。
   - 参数卡片： 每个卡片代表一个可优化的参数，包含：
    - 参数的友好名称（如“快速均线周期”）。
    - 三个ElInputNumber输入框，分别对应**“最小值”、“最大值”、“步长”**。
   - 交互： 用户可以在此区域，为任意数量的参数定义其搜索范围。
   
2. 优化与执行设置区 (The Control Panel)：
   - 标题： 优化设置
   - UI组件 (ElForm)：
    - 优化目标： 一个下拉框，让用户选择**“优化目标”（如夏普比率、“年化收益率”、“最大回撤”等核心指标。）**。
    - 搜索算法 (search_algorithm)： 一个ElSelect下拉框，目前选项为“网格搜索”。
    - 高级运行参数： （可选，可折叠）提供“最大迭代次数”、“并行进程数”等输入框。
    - 执行按钮： 一个绿色的、主操作按钮 ElButton，文本为 “开始优化”。

3. 任务监控与结果区 (The Monitor & Report)：
   - 标题： 优化任务
   - UI组件 (条件渲染)：
      - 默认/空状态： 显示引导信息，如“配置完参数后，点击‘开始优化’以启动任务”。
      - 任务进行中状态： 当用户点击“开始优化”后，此区域立即更新为一个任务状态卡片，显示：
        - 任务ID
        - 一个ElProgress进度条，实时展示优化进度（通过轮询GET /api/grid-search/status/{task_id} API更新）。
        - 一个“取消任务”按钮。
      - 任务完成状态： 当任务完成后，此区域更新为结果摘要，并提供一个主操作按钮：
        - 摘要信息：“优化完成！共测试256组参数，找到最佳组合。”
        - ElButton (主操作)： “查看详细优化结果”

4. 结果呈现 (弹出式模态框)：
    - 触发： 用户点击**“查看详细优化结果”**按钮。
    - UI组件：
      - 一个全屏或超大尺寸的ElDialog模态框，标题为“参数优化结果报告”。
    - 内容：
      - 可视化展示 (首选)： 一个**ECharts热力图**，以二维或三维的形式，直观地展示不同参数组合下的“优化目标”得分，颜色越深表示结果越好。
      - 数据表格展示：
        - 一个可排序的ElTable，完整地列出所有测试过的参数组合。
        - 表格的列包括：排名、各参数的值、目标值、年化收益、夏普比率、最大回撤等。
        - 关键交互： 表格的最后一列是“操作”列，提供一个蓝色的**“应用此参数”**按钮。点击后，会将该行的参数组合，自动回填到“核心配置”和“高级设置”标签页中。

#### D. 回测与方案标签页
**模块定位与工作流**
   - “回测与方案”是策略验证与决策的核心环节。其工作流被设计为两个主要部分：发起回测 (The Launch) 和 历史追溯 (The Archive)。
   - 核心功能 
      验证策略的历史表现，并将结果转化为可行动的方案。
**UI布局与组件设计**
   - 布局： 总体是上下布局，清晰地区分了“发起”与“历史”。
      - 上半部分 (执行回测): 一个独立的表单区域，用于配置并发起一次新的回测。
      - 下半部分 (回测历史与方案): 用于展示该策略的所有历史回测记录，并从选定的记录中生成投资方案。
   1. 执行回测配置区 (The Launchpad)
      - 标题： 执行回测
      - UI组件 (ElForm)：
         - choice_symbols (股票池): 一个可多选、可搜索、可自定义输入的ElSelect。
         - stock_picks (选股策略): 一个ElSelect下拉框，选项动态加载自“选股器”模块已保存的策略。
         - 回测周期 (n_folds vs start/end): 一个ElRadioGroup单选按钮组，用于切换并条件性地显示ElInputNumber或ElDatePicker。
         - commission_dict (手续费): 一个ElSelect下拉框，选项加载自“系统设置”中已保存的模板。
         - UmpBu (裁判系统): 一个ElSwitch开关“启用裁判系统”，打开后，显示一个ElSelect下拉框，用于选择要应用的已训练模型市场。
         - read_cash (初始资金): 一个ElInputNumber。
         - 执行按钮： 一个巨大的、蓝色的主操作按钮 ElButton，文本为 “开始回测”。
   2. 回测历史与方案区 (The Archive)
      - 标题： 回测历史
      - UI组件：
         - 一个ElTable，展示当前策略的所有历史回测记录。
         - 表格列： 回测名称（可自定义）、回测时间、核心指标（总收益、夏普等）、状态（已完成/失败）。
         - 操作列：
            - **“查看报告”**按钮。
            - “删除”按钮。
**  结果呈现 (跳转至独立页面) **
   - 触发：
      - 用户在“执行回测配置区”点击**“开始回测”**，并且API成功返回结果。
      - 用户在“回测历史区”点击某一条记录的**“查看报告”**按钮。
   - 交互：
      - 页面跳转： 使用Vue Router，将用户导航到一个全新的、独立的页面，URL为 /backtest/report/{report_id}。
   - BacktestReport.vue (独立结果页) 内容：
      - 回测摘要： 清晰地展示本次回测的所有输入参数（策略名称、回测周期、股票池等）。
      - 核心指标墙： 一排ElCard + ElStatistic，展示所有关键绩效指标。
      - 图表分析区 (ElTabs)：
         - 资金曲线 (vs 基准)。
         - 水下图 (回撤分析)。
         - 收益分布直方图。
      - 投资交易方案模块：
         - 即我们之前设计的“当前持仓建议”、“未来信号日历”、“关键参数摘要”那三个信息卡片。
      - 详细数据表格区 (ElTabs)：
         - 交易明细。
         - 每日持仓。
      - 操作区：
         - “导出PDF报告”。
         - “应用到交易驾驶舱”。

### 2.3 策略数据提交工作流

1. **统一提交入口**
   - 全局"保存策略"按钮，位于策略工厂右侧区域右上角，统一管理所有标签页数据

2. **数据处理流程**
   - 全量数据采集
   - JSON格式封装
   - API契约对接

3. **操作分离原则**
   - 策略定义保存
   - 回测任务执行

## 3. 必选项与可选项定义

### 最短核心路径（必选）
1. 策略名称填写
2. 核心配置完成
   - 至少一个买入因子
   - 至少一个卖出因子
### 默认值策略
- 仓位管理默认配置
- 系统预设安全值

### 条件解锁机制
- 核心配置优先
- 其他功能条件性开放

## 4. 风险控制(`UmpBu`)分层布局

### 功能分离
1. **模型训练**
   - 部署于系统设置
   - 全局配置性质
2. **模型应用**
   - 集成于回测配置
   - 运行时参数设定

---

## 相关参考文件
1. work_logs\explorer_logs\log_20250813_001_strategy_CRUD_API_data_contract_V2.0.md
2. work_logs\explorer_logs\log_20250813_004_abupy_position_exploration_report.md
3. work_logs\explorer_logs\log_20250813_005_abupy_backtest_input_format_report.md
