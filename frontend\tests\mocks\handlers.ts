// Mock处理器主入口
// 根据测试类型导入不同的处理器

import { setupWorker } from 'msw/browser';
import { setupServer } from 'msw/node';

// 导入不同复杂度的处理器
import { simpleHandlers } from './simple';
import { complexHandlers } from './complex';

/**
 * Mock处理器配置类型
 */
export type MockMode = 'simple' | 'complex';

/**
 * 获取指定模式的处理器
 */
export function getHandlers(mode: MockMode = 'simple') {
  switch (mode) {
    case 'simple':
      return simpleHandlers;
    case 'complex':
      return complexHandlers;
    default:
      return simpleHandlers;
  }
}

/**
 * 为浏览器环境设置Mock服务
 */
export function setupMockWorker(mode: MockMode = 'simple') {
  const handlers = getHandlers(mode);
  return setupWorker(...handlers);
}

/**
 * 为Node.js环境设置Mock服务
 */
export function setupMockServer(mode: MockMode = 'simple') {
  const handlers = getHandlers(mode);
  return setupServer(...handlers);
}

// 默认导出简单模式处理器
export { simpleHandlers as handlers };