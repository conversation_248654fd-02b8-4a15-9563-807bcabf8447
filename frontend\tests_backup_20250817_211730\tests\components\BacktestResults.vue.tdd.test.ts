import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import BacktestResults from '@/components/BacktestResults.vue'
import type { BacktestResult } from '@/types/backtest'

// Mock数据
const mockBacktestResult: BacktestResult = {
  task_id: 'test-001',
  strategy_name: '双均线策略',
  symbol: '000001.SZ',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  initial_capital: 100000,
  final_capital: 120000,
  total_return: 0.2,
  annual_return: 0.18,
  sharpe_ratio: 1.5,
  max_drawdown: 0.08,
  win_rate: 0.65,
  total_trades: 150,
  status: 'completed',
  created_at: '2024-01-01T00:00:00Z',
  completed_at: '2024-01-01T01:00:00Z',
  trades: [
    {
      id: 'trade-1',
      symbol: '000001.SZ',
      side: 'buy',
      quantity: 100,
      price: 10.5,
      timestamp: '2023-01-02T09:30:00Z'
    }
  ],
  daily_returns: [0.01, -0.005, 0.02, 0.015]
}

describe('BacktestResults.vue - TDD基础测试', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(BacktestResults, {
      props: {
        loading: false,
        result: null
      }
    })
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true)
    })

    it('应该显示加载状态', async () => {
      await wrapper.setProps({ loading: true })
      expect(wrapper.find('.loading').exists()).toBe(true)
    })

    it('没有结果时应该显示空状态', () => {
      expect(wrapper.find('.empty-state').exists()).toBe(true)
    })

    it('有结果时应该显示结果内容', async () => {
      await wrapper.setProps({ result: mockBacktestResult })
      expect(wrapper.find('.results-content').exists()).toBe(true)
    })
  })

  describe('基础数据展示测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: mockBacktestResult })
    })

    it('应该显示策略名称', () => {
      expect(wrapper.text()).toContain('双均线策略')
    })

    it('应该显示股票代码', () => {
      expect(wrapper.text()).toContain('000001.SZ')
    })

    it('应该显示回测时间范围', () => {
      expect(wrapper.text()).toContain('2023-01-01')
      expect(wrapper.text()).toContain('2023-12-31')
    })

    it('应该显示总收益率', () => {
      expect(wrapper.find('[data-testid="total-return-card"]').exists()).toBe(true)
    })

    it('应该显示夏普比率', () => {
      expect(wrapper.find('[data-testid="sharpe-ratio-card"]').exists()).toBe(true)
    })

    it('应该显示最大回撤', () => {
      expect(wrapper.find('[data-testid="max-drawdown-card"]').exists()).toBe(true)
    })
  })

  describe('收益率颜色测试', () => {
    it('正收益应该显示绿色', async () => {
      await wrapper.setProps({ result: mockBacktestResult })
      const returnCard = wrapper.find('[data-testid="total-return-card"]')
      expect(returnCard.classes()).toContain('positive')
    })

    it('负收益应该显示红色', async () => {
      const negativeResult = { ...mockBacktestResult, total_return: -0.1 }
      await wrapper.setProps({ result: negativeResult })
      const returnCard = wrapper.find('[data-testid="total-return-card"]')
      expect(returnCard.classes()).toContain('negative')
    })

    it('零收益应该显示中性颜色', async () => {
      const zeroResult = { ...mockBacktestResult, total_return: 0 }
      await wrapper.setProps({ result: zeroResult })
      const returnCard = wrapper.find('[data-testid="total-return-card"]')
      expect(returnCard.classes()).toContain('neutral')
    })
  })

  describe('基础事件处理测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: mockBacktestResult })
    })

    it('点击导出按钮应该触发导出事件', async () => {
      const exportBtn = wrapper.find('[data-testid="export-btn"]')
      await exportBtn.trigger('click')
      
      expect(wrapper.emitted('export')).toHaveLength(1)
      expect(wrapper.emitted('export')![0][0]).toEqual(mockBacktestResult)
    })

    it('点击分析按钮应该触发分析事件', async () => {
      const analysisBtn = wrapper.find('[data-testid="analysis-btn"]')
      await analysisBtn.trigger('click')
      
      expect(wrapper.emitted('viewAnalysis')).toHaveLength(1)
      expect(wrapper.emitted('viewAnalysis')![0][0]).toEqual(mockBacktestResult)
    })
  })

  describe('基础格式化函数测试', () => {
    it('formatPercentage应该正确格式化百分比', () => {
      const vm = wrapper.vm
      expect(vm.formatPercentage(0.2)).toBe('20.00%')
      expect(vm.formatPercentage(0.1234)).toBe('12.34%')
      expect(vm.formatPercentage(-0.05)).toBe('-5.00%')
    })

    it('formatNumber应该正确格式化数字', () => {
      const vm = wrapper.vm
      expect(vm.formatNumber(1.234, 2)).toBe('1.23')
      expect(vm.formatNumber(1.999, 2)).toBe('2.00')
      expect(vm.formatNumber(0, 2)).toBe('0.00')
    })

    it('formatCurrency应该正确格式化货币', () => {
      const vm = wrapper.vm
      expect(vm.formatCurrency(100000)).toBe('¥100,000.00')
      expect(vm.formatCurrency(1234.56)).toBe('¥1,234.56')
      expect(vm.formatCurrency(0)).toBe('¥0.00')
    })
  })
})