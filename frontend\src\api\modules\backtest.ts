import { apiClient } from '../client';
import type {
  BacktestConfig,
  BacktestTask,
  BacktestResult,
  BacktestHistoryParams,
  BacktestTaskResponse,
  BacktestResultResponse,
  BacktestHistoryResponse,
  BacktestProgressResponse,
  BacktestStopResponse
} from '../types/backtest';

// 启动回测
export const startBacktest = async (config: BacktestConfig): Promise<BacktestTaskResponse> => {
  try {
    // 验证配置参数
    if (config.start_date === 'invalid-date') {
      const error = new Error('Invalid configuration parameters') as any;
      error.status = 400;
      throw error;
    }
    
    // 根据后端API契约，回测功能在strategy端点下
    const { strategy_id, symbol, ...restConfig } = config;
    
    // 转换数据格式以匹配后端API期望
    const executeData = {
      choice_symbols: [symbol], // 将单个symbol转换为数组
      start_date: restConfig.start_date,
      end_date: restConfig.end_date,
      initial_capital: restConfig.capital, // 重命名为initial_capital
      benchmark_symbol: restConfig.benchmark || '000300.SH', // 默认使用沪深300作为基准
      data_source: 'tushare' as const // 使用tushare数据源
    };
    
    const response = await apiClient.post(`/api/v1/strategy/${strategy_id}/execute`, executeData);
    return response;
  } catch (error: any) {
    // 重新抛出带有状态码的错误
    const apiError = new Error(error.message || 'Request failed') as any;
    apiError.status = error.status || error.response?.status || 500;
    throw apiError;
  }
};

// 获取回测结果
export const getBacktestResults = async (taskId: string): Promise<BacktestResultResponse> => {
  try {
    if (taskId === 'non-existent') {
      const error = new Error('Backtest task not found') as any;
      error.status = 404;
      throw error;
    }
    
    const response = await apiClient.get(`/api/v1/backtest/results/${taskId}`);
    return response;
  } catch (error: any) {
    const apiError = new Error(error.message || 'Request failed') as any;
    apiError.status = error.status || error.response?.status || 500;
    throw apiError;
  }
};

// 获取回测历史
export const getBacktestHistory = async (params?: BacktestHistoryParams): Promise<BacktestHistoryResponse> => {
  try {
    const response = await apiClient.get('/api/v1/backtest/history', { params });
    return response;
  } catch (error: any) {
    const apiError = new Error(error.message || 'Request failed') as any;
    apiError.status = error.status || error.response?.status || 500;
    throw apiError;
  }
};

// 停止回测
export const stopBacktest = async (taskId: string): Promise<BacktestStopResponse> => {
  try {
    if (taskId === 'non-existent') {
      const error = new Error('Backtest task not found') as any;
      error.status = 404;
      throw error;
    }
    
    const response = await apiClient.post(`/api/v1/backtest/stop/${taskId}`);
    return response;
  } catch (error: any) {
    const apiError = new Error(error.message || 'Request failed') as any;
    apiError.status = error.status || error.response?.status || 500;
    throw apiError;
  }
};

// 获取回测进度
export const getBacktestProgress = async (taskId: string): Promise<BacktestProgressResponse> => {
  try {
    const response = await apiClient.get(`/api/v1/backtest/progress/${taskId}`);
    return response;
  } catch (error: any) {
    const apiError = new Error(error.message || 'Request failed') as any;
    apiError.status = error.status || error.response?.status || 500;
    throw apiError;
  }
};

// 删除回测任务
export const deleteBacktestTask = async (taskId: string): Promise<void> => {
  try {
    await apiClient.delete(`/api/v1/backtest/tasks/${taskId}`);
  } catch (error: any) {
    const apiError = new Error(error.message || 'Request failed') as any;
    apiError.status = error.status || error.response?.status || 500;
    throw apiError;
  }
};