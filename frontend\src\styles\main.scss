// 主样式入口文件
// 强制重新编译触发器

// 1. 变量定义（必须在最前面）
@import 'variables/_colors';
@import 'variables/_typography';
@import 'variables/_spacing';

// 2. 基础样式
@import 'base/_reset';
@import 'base/_global';

// 3. 组件样式
@import 'components/_ui-components';
@import 'components/_visual-enhancements';
@import 'components/_factor-card';
@import 'components/_card-form';

// 4. 工具类（最后导入，确保优先级）
@import 'utils/_utilities';