# 策略管理模块数据库持久化实现工作日志

## 项目概述

本次工作的目标是将策略管理模块（StrategyService）从基于内存字典的实现改造为基于SQLite数据库的持久化存储实现。使用SQLModel作为ORM框架，以便与现有的Pydantic模型和FastAPI框架无缝集成。

## 技术选型

1. **数据库**: SQLite
   - 理由：轻量级，易于设置和管理，适合作为项目起点
   - 无需额外服务器，文件即数据库，便于部署和维护

2. **ORM框架**: SQLModel
   - 理由：兼容Pydantic和FastAPI，能复用现有模型定义，减少代码冗余
   - 提供类型安全的查询API，便于开发和维护
   - 支持JSON类型字段，适合存储策略的嵌套结构（如因子列表和参数字典）

## 实现详情

### 1. 环境准备

在`requirements.txt`中添加了SQLModel依赖：

```
sqlmodel>=0.0.8
```

### 2. 数据库核心逻辑

创建了`app/core/database.py`文件，实现以下功能：

- 定义SQLite数据库引擎（engine）
- 实现`create_db_and_tables()`函数，用于应用启动时初始化数据库和表
- 实现`get_session()`函数，用于依赖注入，为API路由提供数据库会话
- 实现`get_test_engine()`和`create_test_db_and_tables()`函数，用于测试环境

关键设计决策：
- 使用环境变量配置数据库URL，便于在不同环境中切换数据库
- 为SQLite配置`check_same_thread=False`，允许在多线程环境中使用
- 为测试提供专用的内存数据库功能，确保测试的独立性和速度

### 3. 模型定义

创建了`app/models/strategy_model.py`文件，定义了`StrategyModel`类：

- 继承自`SQLModel`，并设置`table=True`，使其成为数据库表模型
- 定义了与原Pydantic模型对应的字段，并添加了数据库列属性
- 对于嵌套结构（如`buy_factors`、`sell_factors`和`parameters`），使用SQLAlchemy的JSON类型存储
- 实现了`to_schema_strategy()`和`from_schema_strategy()`方法，用于在数据库模型和API模型之间转换

关键设计决策：
- 保持字段名与原Pydantic模型一致，便于转换和理解
- 使用JSON类型存储复杂结构，避免创建多个关联表的复杂性
- 提供模型转换方法，确保数据库操作和API接口之间的数据一致性

### 4. 服务层重构

修改了`app/services/strategy_service.py`，将原基于内存字典的实现改为基于数据库的实现：

- 修改构造函数，接收数据库会话（Session）对象
- 重写所有CRUD方法，使用SQLModel通过数据库会话执行相应的查询和操作
- 添加了更详细的错误处理和异常信息

关键设计决策：
- 保持API接口不变，确保与现有代码的兼容性
- 使用SQLModel的查询API，提供类型安全的数据库操作
- 在更新操作中添加时间延迟，确保在测试中能测到时间变化
- 使用事务确保数据一致性，所有操作在提交前都可以回滚

### 5. 应用集成

修改了`main.py`，集成数据库初始化和依赖注入：

- 添加了`@app.on_event("startup")`事件处理器，在应用启动时调用`create_db_and_tables()`
- 实现了`get_db_session()`依赖函数，为API路由提供数据库会话

关键设计决策：
- 使用FastAPI的生命周期事件，确保数据库在应用启动时初始化
- 使用依赖注入系统，为API路由提供数据库会话，便于测试和维护

### 6. 测试更新

修改了`tests/services/test_strategy_service.py`，适配数据库实现：

- 使用`get_test_engine()`创建基于内存的SQLite数据库，确保测试的独立性
- 在每个测试前创建表，在每个测试后删除表，确保测试之间的隔离
- 重写测试用例，验证基于数据库的CRUD逻辑

关键设计决策：
- 使用内存数据库，提高测试速度并避免文件IO
- 保持测试用例的结构和断言不变，确保功能一致性
- 在每个测试前后重置数据库状态，避免测试之间的干扰

## 遇到的问题及解决方案

1. **JSON字段处理**
   - 问题：需要将Pydantic模型的嵌套结构（如因子列表）存储在数据库中
   - 解决方案：使用SQLAlchemy的JSON类型，并实现模型转换方法处理序列化和反序列化

2. **测试隔离**
   - 问题：测试需要独立的数据库环境，避免相互干扰和影响生产数据
   - 解决方案：使用SQLite内存数据库，并在每个测试前后重置数据库状态

3. **时间戳处理**
   - 问题：在测试中需要验证更新时间的变化
   - 解决方案：在更新操作中添加微小的时间延迟，确保时间戳有变化

4. **多线程支持**
   - 问题：SQLite默认不支持多线程访问
   - 解决方案：配置SQLite引擎的`connect_args={"check_same_thread": False}`，允许多线程访问

## 总结

本次改造成功将策略管理模块从基于内存字典的实现转换为基于SQLite数据库的持久化存储实现。使用SQLModel作为ORM框架，实现了与现有Pydantic模型和FastAPI框架的无缝集成。

主要成果：
1. 实现了数据持久化，解决了服务重启后数据丢失的问题
2. 保持了API接口的一致性，确保与现有代码的兼容性
3. 提供了更好的数据查询和过滤能力，为未来功能扩展奠定基础
4. 改进了错误处理和异常信息，提高了系统的健壮性

后续可能的改进：
1. 添加数据库迁移支持，便于未来模型变更
2. 实现更复杂的查询功能，如全文搜索和高级过滤
3. 考虑添加缓存机制，提高频繁访问数据的性能
4. 在需要时可以平滑迁移到其他关系型数据库，如PostgreSQL或MySQL
