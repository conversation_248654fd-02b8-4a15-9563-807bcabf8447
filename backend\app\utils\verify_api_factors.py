#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查真实的API响应
"""

import sys
sys.path.append('.')

try:
    from app.abupy_adapter.strategy_adapter import StrategyAdapter
    
    print('=== 获取真实API响应 ===')
    result = StrategyAdapter.get_available_abu_factors()
    
    buy_factors = result.get('buy', [])
    sell_factors = result.get('sell', [])
    
    print(f'买入因子数量: {len(buy_factors)}')
    print(f'卖出因子数量: {len(sell_factors)}')
    print(f'总因子数量: {len(buy_factors) + len(sell_factors)}')
    
    # 统计有参数的因子
    buy_with_params = 0
    sell_with_params = 0
    total_params = 0
    
    print('\n=== 买入因子详情 ===')
    for factor in buy_factors:
        params = factor.get('parameters', {})
        if params:
            buy_with_params += 1
            total_params += len(params)
            print(f'  {factor["name"]}: {len(params)} 个参数')
        else:
            print(f'  {factor["name"]}: 无参数')
    
    print('\n=== 卖出因子详情 ===')
    for factor in sell_factors:
        params = factor.get('parameters', {})
        if params:
            sell_with_params += 1
            total_params += len(params)
            print(f'  {factor["name"]}: {len(params)} 个参数')
        else:
            print(f'  {factor["name"]}: 无参数')
    
    print('\n=== 参数统计 ===')
    print(f'有参数的买入因子: {buy_with_params}/{len(buy_factors)} ({buy_with_params/len(buy_factors)*100:.1f}%)')
    print(f'有参数的卖出因子: {sell_with_params}/{len(sell_factors)} ({sell_with_params/len(sell_factors)*100:.1f}%)')
    print(f'有参数的总因子: {buy_with_params + sell_with_params}/{len(buy_factors) + len(sell_factors)} ({(buy_with_params + sell_with_params)/(len(buy_factors) + len(sell_factors))*100:.1f}%)')
    print(f'提取的参数总数: {total_params}')
    
except Exception as e:
    print(f'获取API响应时出错: {e}')
    import traceback
    traceback.print_exc()