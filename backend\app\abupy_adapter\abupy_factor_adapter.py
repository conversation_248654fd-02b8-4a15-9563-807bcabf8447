import logging

# 直接从abupy的各个模块中导入真实的因子类
# 买入因子导入
try:
    from abupy.FactorBuyBu.ABuFactorBuyBreak import ABuFactorBuyBreak
except ImportError:
    ABuFactorBuyBreak = None
    logging.warning("无法导入 ABuFactorBuyBreak")

try:
    from abupy.FactorBuyBu.ABuDoubleMa import ABuDoubleMaBuy
except ImportError:
    ABuDoubleMaBuy = None
    logging.warning("无法导入 ABuDoubleMaBuy")

try:
    from abupy.FactorBuyBu.ABuFactorBuyDemo import AbuFactorBuyDemo
except ImportError:
    AbuFactorBuyDemo = None
    logging.warning("无法导入 AbuFactorBuyDemo")

try:
    from abupy.FactorBuyBu.ABuFactorBuyDip import AbuFactorBuyDip
except ImportError:
    AbuFactorBuyDip = None
    logging.warning("无法导入 AbuFactorBuyDip")

try:
    from abupy.FactorBuyBu.ABuFactorBuyWD import AbuF<PERSON>BuyWD
except ImportError:
    AbuFactorBuyWD = None
    logging.warning("无法导入 AbuFactorBuyWD")

# 卖出因子导入
try:
    from abupy.FactorSellBu.ABuFactorSellNDay import AbuFactorSellNDay
except ImportError:
    AbuFactorSellNDay = None
    logging.warning("无法导入 AbuFactorSellNDay")

try:
    from abupy.FactorSellBu.ABuFactorSellDemo import AbuFactorSellDemo
except ImportError:
    AbuFactorSellDemo = None
    logging.warning("无法导入 AbuFactorSellDemo")

try:
    from abupy.FactorSellBu.ABuFactorSellBreak import AbuFactorSellBreak
except ImportError:
    AbuFactorSellBreak = None
    logging.warning("无法导入 AbuFactorSellBreak")

try:
    from abupy.FactorSellBu.ABuFactorSellAtrNStop import AbuFactorSellAtrNStop
except ImportError:
    AbuFactorSellAtrNStop = None
    logging.warning("无法导入 AbuFactorSellAtrNStop")

logger = logging.getLogger(__name__)

# 构建完整的因子类映射字典
FACTOR_CLASS_MAP = {}

# 添加买入因子映射（支持多种命名变体以提高鲁棒性）
if ABuFactorBuyBreak:
    FACTOR_CLASS_MAP["AbuFactorBuyBreak"] = ABuFactorBuyBreak
    FACTOR_CLASS_MAP["ABuFactorBuyBreak"] = ABuFactorBuyBreak

if ABuDoubleMaBuy:
    FACTOR_CLASS_MAP["AbuDoubleMaBuy"] = ABuDoubleMaBuy
    FACTOR_CLASS_MAP["ABuDoubleMaBuy"] = ABuDoubleMaBuy

if AbuFactorBuyDemo:
    FACTOR_CLASS_MAP["AbuFactorBuyDemo"] = AbuFactorBuyDemo
    FACTOR_CLASS_MAP["ABuFactorBuyDemo"] = AbuFactorBuyDemo

if AbuFactorBuyDip:
    FACTOR_CLASS_MAP["AbuFactorBuyDip"] = AbuFactorBuyDip
    FACTOR_CLASS_MAP["ABuFactorBuyDip"] = AbuFactorBuyDip

if AbuFactorBuyWD:
    FACTOR_CLASS_MAP["AbuFactorBuyWD"] = AbuFactorBuyWD
    FACTOR_CLASS_MAP["ABuFactorBuyWD"] = AbuFactorBuyWD

# 添加卖出因子映射
if AbuFactorSellNDay:
    FACTOR_CLASS_MAP["AbuFactorSellNDay"] = AbuFactorSellNDay
    FACTOR_CLASS_MAP["ABuFactorSellNDay"] = AbuFactorSellNDay

if AbuFactorSellDemo:
    FACTOR_CLASS_MAP["AbuFactorSellDemo"] = AbuFactorSellDemo
    FACTOR_CLASS_MAP["ABuFactorSellDemo"] = AbuFactorSellDemo

if AbuFactorSellBreak:
    FACTOR_CLASS_MAP["AbuFactorSellBreak"] = AbuFactorSellBreak
    FACTOR_CLASS_MAP["ABuFactorSellBreak"] = AbuFactorSellBreak

if AbuFactorSellAtrNStop:
    FACTOR_CLASS_MAP["AbuFactorSellAtrNStop"] = AbuFactorSellAtrNStop
    FACTOR_CLASS_MAP["ABuFactorSellAtrNStop"] = AbuFactorSellAtrNStop

class AbuPyFactorAdapter:
    """
    将前端传来的因子名称字符串，适配为abupy库中真实的因子类对象。
    使用直接导入的因子类进行映射。
    """
    # 使用全局的因子映射表
    FACTOR_CLASS_MAP = FACTOR_CLASS_MAP

    @staticmethod
    def adapt_factors(factors_config):
        """
        根据配置列表，将因子名称字符串转换为因子类字典列表。
        此方法保留了您原始代码的逻辑。
        """
        if not factors_config:
            return []

        adapted_factors = []
        for factor_config in factors_config:
            # 根据您的原始代码，这里应该是 'name' 而非 'class'
            class_name = factor_config.get('name') 
            if not class_name:
                logger.warning(f"因子配置缺少 'name' 字段: {factor_config}")
                continue

            if class_name in AbuPyFactorAdapter.FACTOR_CLASS_MAP:
                factor_class = AbuPyFactorAdapter.FACTOR_CLASS_MAP[class_name]
                # 您的原始逻辑是 'class' 和其他参数分开，这里遵循并优化
                params = factor_config.get("params", {})
                adapted_factor = {'class': factor_class, **params}
                adapted_factors.append(adapted_factor)
            else:
                # 不再抛出异常，而是记录错误，让其他有效因子可以继续处理
                logger.error(f"请求的因子 '{class_name}' 在当前abupy版本中不可用或名称错误，已被忽略。")
        
        return adapted_factors

# 打印日志，方便调试时确认哪些因子被成功加载
logger.info(f"AbuPyFactorAdapter 初始化完成，成功加载 {len(AbuPyFactorAdapter.FACTOR_CLASS_MAP)} 个因子。")