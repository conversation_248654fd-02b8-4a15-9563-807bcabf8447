import { describe, it, expect, vi, beforeEach } from 'vitest'
import { runBacktest } from '@/api/backtest'
import { apiClient } from '@/api/client'
import type { BacktestConfig } from '@/api/types/backtest'

// Mock apiClient
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn()
  }
}))

const mockApiClient = vi.mocked(apiClient)

describe('Backtest API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('runBacktest', () => {
    it('应该将前端配置正确转换为后端API格式', async () => {
      // 准备测试数据
      const frontendConfig: BacktestConfig = {
        strategy_id: 'test-strategy-123',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        capital: 100000,
        benchmark: '000300.SH'
      }

      // Mock API响应
      const mockResponse = {
        success: true,
        data: {
          id: 'task-123',
          strategy_id: 'test-strategy-123',
          status: 'pending'
        }
      }
      mockApiClient.post.mockResolvedValue(mockResponse)

      // 执行API调用
      await runBacktest(frontendConfig)

      // 验证API调用参数
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/strategy/test-strategy-123/execute',
        {
          choice_symbols: ['000001.SZ'], // 应该转换为数组
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          initial_capital: 100000,
          benchmark_symbol: '000300.SH', // 应该重命名字段
          data_source: 'local'
        }
      )
    })

    it('应该处理没有benchmark的情况', async () => {
      const frontendConfig: BacktestConfig = {
        strategy_id: 'test-strategy-123',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        capital: 100000
      }

      const mockResponse = {
        success: true,
        data: { id: 'task-123', status: 'pending' }
      }
      mockApiClient.post.mockResolvedValue(mockResponse)

      await runBacktest(frontendConfig)

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/strategy/test-strategy-123/execute',
        {
          choice_symbols: ['000001.SZ'],
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          initial_capital: 100000,
          benchmark_symbol: undefined,
          data_source: 'local'
        }
      )
    })

    it('应该正确处理API错误', async () => {
      const frontendConfig: BacktestConfig = {
        strategy_id: 'invalid-strategy',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        capital: 100000
      }

      // Mock API错误
      const mockError = {
        message: 'Strategy not found',
        response: { status: 404 }
      }
      mockApiClient.post.mockRejectedValue(mockError)

      // 验证错误处理
      await expect(runBacktest(frontendConfig)).rejects.toThrow('Strategy not found')
    })

    it('应该验证无效日期配置', async () => {
      const frontendConfig: BacktestConfig = {
        strategy_id: 'test-strategy-123',
        symbol: '000001.SZ',
        start_date: 'invalid-date',
        end_date: '2023-12-31',
        capital: 100000
      }

      await expect(runBacktest(frontendConfig)).rejects.toThrow('Invalid configuration parameters')
    })
  })
})