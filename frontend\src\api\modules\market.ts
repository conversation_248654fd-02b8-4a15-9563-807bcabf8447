// src/api/market.ts
import { apiClient } from '../client';
import type { SymbolsResponse, KlineDataResponse } from '../types';

// 获取所有可用的股票代码。
export const getSymbols = async (): Promise<SymbolsResponse> => {
  const response = await apiClient.get('/market/symbols');
  return response;
};

// 获取特定代码的K线数据。
export const getKlineData = async (symbol: string, period: string): Promise<KlineDataResponse> => {
  const response = await apiClient.get(`/market/kline/${symbol}`, { params: { period } });
  return response;
};