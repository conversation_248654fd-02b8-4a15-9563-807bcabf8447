<template>
  <div class="strategy-header">
    <div class="strategy-info">
      <h2 class="strategy-title">{{ strategy.name || '未命名策略' }}</h2>
      <p class="strategy-desc">{{ strategy.description || UI_TEXT.NO_DESCRIPTION }}</p>
    </div>
    <div class="strategy-meta">
      <el-tag :type="strategy.is_public ? 'success' : 'info'" size="small">
        {{ strategy.is_public ? UI_TEXT.PUBLIC_STRATEGY : UI_TEXT.PRIVATE_STRATEGY }}
      </el-tag>
      <span class="author-info">作者：{{ strategy.author || '当前用户' }}</span>
      <span class="time-info">{{ formatDate(strategy.create_time) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Strategy } from '@/api/types'
import { formatDate } from '@/utils/dateUtils'
import { UI_TEXT } from '@/constants/strategy'

interface Props {
  strategy: Strategy
}

defineProps<Props>()
</script>

<style scoped>
.strategy-header {
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  background: linear-gradient(135deg, #f8faff 0%, #f0f9ff 100%);
  border-radius: var(--border-radius-lg);
  border: 1px solid #e1ecff;
}

.strategy-info {
  margin-bottom: var(--space-md);
}

.strategy-title {
  margin: 0 0 var(--space-xs) 0;
  color: var(--text-color-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.3;
}

.strategy-desc {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.strategy-meta {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.author-info,
.time-info {
  color: var(--text-color-secondary);
  font-size: var(--font-size-xs);
}
</style>
