from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path, status
from typing import List, Dict, Any, Optional
from sqlmodel import Session

from backend.app.schemas.strategy import Strategy, StrategyCreate, StrategyUpdate, StrategyExecuteRequest
from backend.app.services.strategy_service import StrategyService
from backend.app.services.factor_service import FactorService
from backend.app.core.exceptions import DataNotFoundError, AdapterError, ParameterError, FactorError
from backend.app.schemas.response_schema import ErrorResponse, SuccessResponse, PaginatedResponse
from backend.app.core.database import get_session
# FactorListResponse might be specific, if FactorService returns a list of dicts/models, SuccessResponse is better
# from backend.app.schemas.strategy import FactorListResponse 

import logging

router = APIRouter()

# Dependency injection for services
def get_strategy_service(session: Session = Depends(get_session)) -> StrategyService:
    return StrategyService(session=session)

def get_factor_service() -> FactorService:
    return FactorService()

# --- CRUD Endpoints for Strategies (Refactored) ---

@router.post(
    "/", 
    response_model=SuccessResponse[Strategy], 
    status_code=status.HTTP_201_CREATED,
    summary="创建新策略",
    responses={
        400: {"model": ErrorResponse, "description": "无效的输入数据"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    }
)
async def create_strategy_endpoint(
    strategy_data: StrategyCreate,
    service: StrategyService = Depends(get_strategy_service)
):
    """
    创建一个新的量化交易策略。
    - **name**: 策略名称 (必需)
    - **description**: 策略描述 (可选)
    - **buy_factors**: 买入因子列表 (必需, 至少一个)
    - **sell_factors**: 卖出因子列表 (可选)
    - **parameters**: 策略参数 (例如: initial_capital) (必需)
    """
    logging.debug(f"创建新策略: name={strategy_data.name}")
    try:
        created_strategy = service.create_strategy(strategy_data)
        return SuccessResponse(data=created_strategy, message="策略创建成功")
    except (ParameterError, AdapterError, FactorError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logging.error(f"创建策略时发生内部错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建策略时发生内部错误")

@router.get(
    "/", 
    response_model=PaginatedResponse[Strategy], 
    summary="获取策略列表",
    responses={
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    }
)
async def get_strategies_endpoint(
    service: StrategyService = Depends(get_strategy_service),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=100, description="每页返回的最大记录数"),
    owner: Optional[str] = Query(None, description="策略所有者 (需要 StrategyService 支持)"),
    is_public: Optional[bool] = Query(None, description="是否公开策略 (需要 StrategyService 支持)")
):
    """
    获取所有已创建的量化交易策略列表，支持分页和基本筛选。
    筛选参数 (owner, is_public) 的实际效果取决于 StrategyService 的实现。
    """
    logging.debug(f"获取策略列表: skip={skip}, limit={limit}, owner={owner}, is_public={is_public}")
    try:
        # The service method should ideally handle filtering.
        # If not, filtering can be applied here to the results from get_all_strategies_paginated.
        strategies, total_count = service.get_all_strategies_paginated(
            skip=skip, 
            limit=limit, 
            owner=owner, # Pass to service if supported
            is_public=is_public # Pass to service if supported
        )
        return PaginatedResponse(data=strategies, total=total_count, skip=skip, limit=limit, message="获取策略列表成功")
    except Exception as e:
        logging.error(f"获取策略列表时发生内部错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取策略列表时发生内部错误")


@router.get(
    "/{strategy_id}", 
    response_model=SuccessResponse[Strategy], 
    summary="获取单个策略详情",
    responses={
        404: {"model": ErrorResponse, "description": "策略未找到"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    }
)
async def get_strategy_by_id_endpoint(
    strategy_id: str = Path(..., description="要获取的策略ID"),
    service: StrategyService = Depends(get_strategy_service)
):
    """
    根据策略ID获取策略的详细信息。
    """
    logging.debug(f"获取策略详情: strategy_id={strategy_id}")
    try:
        strategy = service.get_strategy_by_id(strategy_id)
        return SuccessResponse(data=strategy, message="获取策略详情成功")
    except DataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail=f"未找到ID为 {strategy_id} 的策略"
        )
    except Exception as e:
        logging.error(f"获取策略详情时发生内部错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取策略详情时发生内部错误")

@router.put(
    "/{strategy_id}", 
    response_model=SuccessResponse[Strategy], 
    summary="更新策略",
    responses={
        400: {"model": ErrorResponse, "description": "无效的输入数据"},
        404: {"model": ErrorResponse, "description": "策略未找到"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    }
)
async def update_strategy_endpoint(
    strategy_data: StrategyUpdate,
    strategy_id: str = Path(..., description="要更新的策略ID"),
    service: StrategyService = Depends(get_strategy_service)
):
    """
    根据策略ID更新现有策略的信息。允许部分更新。
    """
    logging.debug(f"更新策略: strategy_id={strategy_id}")
    try:
        updated_strategy = service.update_strategy(strategy_id, strategy_data)
        return SuccessResponse(data=updated_strategy, message="策略更新成功")
    except DataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail=f"未找到ID为 {strategy_id} 的策略"
        )
    except (ParameterError, AdapterError, FactorError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logging.error(f"更新策略时发生内部错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新策略时发生内部错误")

@router.delete(
    "/{strategy_id}", 
    response_model=SuccessResponse[Dict[str, str]], 
    summary="删除策略",
    responses={
        404: {"model": ErrorResponse, "description": "策略未找到"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    }
)
async def delete_strategy_endpoint(
    strategy_id: str = Path(..., description="要删除的策略ID"),
    service: StrategyService = Depends(get_strategy_service)
):
    """
    根据策略ID删除一个策略。
    """
    logging.debug(f"删除策略: strategy_id={strategy_id}")
    try:
        service.delete_strategy(strategy_id) 
        return SuccessResponse(data={"deleted_strategy_id": strategy_id}, message="策略已成功删除")
    except DataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail=f"未找到ID为 {strategy_id} 的策略"
        )
    except Exception as e:
        logging.error(f"删除策略时发生内部错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除策略时发生内部错误")

# --- Strategy Execution Endpoint (New) ---
@router.post(
    "/{strategy_id}/execute", 
    response_model=SuccessResponse[Dict[str, Any]], 
    summary="执行策略回测",
    responses={
        400: {"model": ErrorResponse, "description": "无效的市场数据或策略参数"},
        404: {"model": ErrorResponse, "description": "策略未找到"},
        500: {"model": ErrorResponse, "description": "策略执行时发生内部错误"}
    }
)
async def execute_strategy_endpoint(
    market_data: StrategyExecuteRequest,
    strategy_id: str = Path(..., description="要执行的策略ID"),
    service: StrategyService = Depends(get_strategy_service)
):
    """
    执行指定ID的策略进行回测。
    
    请求模型提供市场数据参数，包括:
    - choice_symbols: 股票代码列表(必需)
    - start_date/end_date: 回测开始和结束日期(必需)
    - data_source: 数据源类型，默认'tushare'、也支持'local'
    - capital: 初始资金、benchmark_symbol: 基准指数、n_folds: 交叉验证折数
    
    注意: 必须使用'choice_symbols'作为股票代码列表参数名，而非'symbols'
    """
    logging.debug(f"执行策略: strategy_id={strategy_id}, market_data keys: {list(market_data.model_dump().keys())}")
    try:
        # 将Pydantic模型转换为字典，传递给服务层
        execution_result = service.execute_strategy(strategy_id, market_data.model_dump())
        return SuccessResponse(data=execution_result, message="策略执行成功")
    except DataNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail=f"未找到ID为 {strategy_id} 的策略"
        )
    except (ParameterError, FactorError, AdapterError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logging.error(f"策略执行过程中发生未知错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"策略执行过程中发生未知错误")


# --- Factor Listing Endpoint (Adapted from existing) ---
@router.get(
    "/factors/", 
    response_model=Dict[str, List[Dict[str, Any]]],  # 改为返回字典
    summary="获取可用因子列表",
    responses={
        400: {"model": ErrorResponse, "description": "无效的因子类型"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    }
)
async def get_available_factors_endpoint(
    factor_type: Optional[str] = Query(None, description="因子类型: 'buy' 或 'sell'", enum=["buy", "sell"]),
    service: FactorService = Depends(get_factor_service)
):
    """
    获取可用因子列表。可以按 'buy' 或 'sell' 类型过滤。
    """
    logging.debug(f"获取可用因子列表: factor_type={factor_type}")
    try:
        # 获取 FactorListResponse 对象
        factor_response = service.get_available_factors(factor_type=factor_type)
        
        # 转换为字典格式
        result = {
            "buy_factors": [f.model_dump() for f in factor_response.buy_factors],
            "sell_factors": [f.model_dump() for f in factor_response.sell_factors]
        }
        
        return result  # 直接返回字典，不包装在 SuccessResponse 中
        
    except ParameterError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logging.error(f"获取可用因子列表时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取可用因子列表时发生内部错误")
