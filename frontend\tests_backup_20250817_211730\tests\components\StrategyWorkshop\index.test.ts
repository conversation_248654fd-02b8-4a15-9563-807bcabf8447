import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia, getActivePinia } from 'pinia'
import { nextTick } from 'vue'
import StrategyWorkshop from '@/components/StrategyWorkshop/index.vue'
import { useStrategyStore } from '@/stores/useStrategyStore'
import { SimpleStrategyDataFactory } from '../../factories/SimpleStrategyDataFactory'
import type { Strategy } from '@/api/types'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElPageHeader: {
    name: 'ElPageHeader',
    props: ['title', 'content'],
    template: '<div class="el-page-header"><div class="el-page-header__title">{{ title }}</div><div class="el-page-header__content">{{ content }}</div></div>'
  }
}))

// Mock 子组件
vi.mock('@/components/StrategyWorkshop/StrategyList.vue', () => ({
  default: {
    name: 'StrategyList',
    props: ['strategies', 'currentStrategy', 'loading', 'error'],
    emits: ['select-strategy', 'create-strategy'],
    template: `
      <div class="strategy-list-mock" data-testid="strategy-list">
        <div v-if="loading" data-testid="loading-indicator">Loading...</div>
        <div v-else-if="error" data-testid="error-message">{{ error }}</div>
        <div v-else data-testid="strategy-table">
          <div 
            v-for="strategy in strategies" 
            :key="strategy.id"
            data-testid="strategy-row"
            @click="$emit('select-strategy', strategy)"
          >
            <span data-testid="strategy-name">{{ strategy.name }}</span>
          </div>
        </div>
        <button 
          class="new-strategy-btn" 
          @click="$emit('create-strategy')"
        >
          新建策略
        </button>
      </div>
    `
  }
}))

vi.mock('@/components/StrategyWorkshop/StrategyEditor.vue', () => ({
  default: {
    name: 'StrategyEditor',
    props: ['strategy', 'loading'],
    emits: ['save-strategy', 'update-strategy', 'create-strategy'],
    template: `
      <div class="strategy-editor-mock" data-testid="strategy-editor">
        <div v-if="loading">Loading editor...</div>
        <div v-else-if="strategy" class="editor-content">
          <h2>{{ strategy.name }}</h2>
          <button @click="$emit('save-strategy', strategy)">保存策略</button>
        </div>
        <div v-else class="empty-state">
          <button @click="$emit('create-strategy')">新建策略</button>
        </div>
      </div>
    `,
    setup(props, { expose }) {
      // 暴露 onSaveComplete 方法供父组件调用
      const onSaveComplete = vi.fn()
      
      expose({
        onSaveComplete
      })
      
      return {
        onSaveComplete
      }
    }
  }
}))

// Mock stores
vi.mock('@/stores/useStrategyStore')

describe('StrategyWorkshop.vue - 重构后测试', () => {
  let wrapper: VueWrapper<any>
  let mockStrategyStore: any
  let mockStrategies: Strategy[]

  beforeEach(async () => {
    // 避免重复创建 Pinia 实例
    if (!getActivePinia()) {
      const pinia = createPinia()
      setActivePinia(pinia)
    }

    // 创建模拟策略数据
    mockStrategies = SimpleStrategyDataFactory.createStrategies(3)

    // 模拟策略Store
    mockStrategyStore = {
      strategies: mockStrategies,
      currentSelectedStrategy: null,
      isLoading: false,
      error: null,
      fetchStrategies: vi.fn().mockResolvedValue(undefined),
      setCurrentSelectedStrategy: vi.fn(),
      startNewStrategyCreation: vi.fn(),
      createStrategy: vi.fn().mockResolvedValue(undefined),
      updateStrategy: vi.fn().mockResolvedValue(undefined)
    }

    vi.mocked(useStrategyStore).mockReturnValue(mockStrategyStore)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染页面头部和布局', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 验证页面标题
      expect(wrapper.find('.el-page-header__title').text()).toBe('策略工场')
      expect(wrapper.find('.el-page-header__content').text()).toBe('平台的\'中央车站\'和用户的\'思想实验室\'')
      
      // 验证布局结构
      expect(wrapper.find('.workshop-layout').exists()).toBe(true)
      expect(wrapper.find('[data-testid="strategy-list"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="strategy-editor"]').exists()).toBe(true)
    })

    it('应该在挂载时获取策略列表', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 验证fetchStrategies被调用
      expect(mockStrategyStore.fetchStrategies).toHaveBeenCalledTimes(1)
    })
  })

  describe('策略选择功能', () => {
    it('应该能够选择策略', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 获取策略列表组件并触发选择事件
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      const testStrategy = mockStrategies[0]
      
      await strategyList.vm.$emit('select-strategy', testStrategy)

      // 验证setCurrentSelectedStrategy被调用
      expect(mockStrategyStore.setCurrentSelectedStrategy).toHaveBeenCalledWith(testStrategy)
    })

    it('应该能够创建新策略', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 从策略列表触发创建事件
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')

      // 验证startNewStrategyCreation被调用
      expect(mockStrategyStore.startNewStrategyCreation).toHaveBeenCalledTimes(1)
    })
  })

  describe('策略保存功能', () => {
    it('应该能够保存新策略', async () => {
      const newStrategy = {
        ...mockStrategies[0],
        id: undefined, // 新策略没有ID
        name: '新测试策略'
      }

      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 从编辑器触发保存事件
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', newStrategy)

      // 验证createStrategy被调用
      expect(mockStrategyStore.createStrategy).toHaveBeenCalledWith(newStrategy)
      // 验证策略列表被刷新
      expect(mockStrategyStore.fetchStrategies).toHaveBeenCalledTimes(2) // 1次挂载时 + 1次保存后
    })

    it('应该能够更新现有策略', async () => {
      const existingStrategy = {
        ...mockStrategies[0],
        name: '更新后的策略名称'
      }

      wrapper = mount(StrategyWorkshop)
      await nextTick()

      // 从编辑器触发保存事件
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', existingStrategy)

      // 验证updateStrategy被调用
      expect(mockStrategyStore.updateStrategy).toHaveBeenCalledWith(
        String(existingStrategy.id), 
        existingStrategy
      )
      // 验证策略列表被刷新
      expect(mockStrategyStore.fetchStrategies).toHaveBeenCalledTimes(2) // 1次挂载时 + 1次保存后
    })
  })

  describe('加载状态处理', () => {
    it('应该正确传递加载状态给子组件', async () => {
      mockStrategyStore.isLoading = true
      
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })

      // 验证加载状态被正确传递
      expect(strategyList.props('loading')).toBe(true)
      expect(strategyEditor.props('loading')).toBe(true)
    })

    it('应该正确传递错误状态给子组件', async () => {
      const errorMessage = '网络连接失败'
      mockStrategyStore.error = errorMessage
      
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      const strategyList = wrapper.findComponent({ name: 'StrategyList' })

      // 验证错误状态被正确传递
      expect(strategyList.props('error')).toBe(errorMessage)
    })
  })

  describe('策略更新功能', () => {
    it('应该能够处理策略实时更新', async () => {
      wrapper = mount(StrategyWorkshop)
      await nextTick()

      const updatedStrategy = {
        ...mockStrategies[0],
        name: '实时更新的策略名称'
      }

      // 从编辑器触发更新事件
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('update-strategy', updatedStrategy)

      // 验证setCurrentSelectedStrategy被调用以更新当前策略
      expect(mockStrategyStore.setCurrentSelectedStrategy).toHaveBeenCalledWith(updatedStrategy)
    })
  })

  describe('错误处理', () => {
    it('应该能够处理保存失败的情况', async () => {
      const saveError = new Error('保存失败')
      mockStrategyStore.createStrategy.mockRejectedValue(saveError)

      wrapper = mount(StrategyWorkshop)
      await nextTick()

      const newStrategy = {
        ...mockStrategies[0],
        id: undefined,
        name: '测试策略'
      }

      // 捕获错误
      const originalConsoleError = console.error
      console.error = vi.fn()

      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      // 触发保存事件，期望抛出错误
      let caughtError = null
      try {
        await strategyEditor.vm.$emit('save-strategy', newStrategy)
        // 验证createStrategy被调用了，但会失败
        expect(mockStrategyStore.createStrategy).toHaveBeenCalledWith(newStrategy)
      } catch (error) {
        caughtError = error
      }
      
      // 由于emit不是async操作，我们主要验证mock被正确调用
      expect(mockStrategyStore.createStrategy).toHaveBeenCalledWith(newStrategy)

      // 恢复console.error
      console.error = originalConsoleError
    })
  })
})
