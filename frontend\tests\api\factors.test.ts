import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as factorsApi from '../../src/api/modules/factors'
import { apiClient } from '../../src/api/client'
import type { Factor, FactorTestRequest, FactorTestResult } from '../../src/api/types/factors'
import { FactorsDataFactory } from '../factories/FactorsDataFactory'

// Mock API client
vi.mock('../../src/api/client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn()
  }
}))

const mockApiClient = vi.mocked(apiClient)

describe('Factors API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getFactors', () => {
    it('应该能够获取因子库列表', async () => {
      // Arrange
      const expectedFactors: Factor[] = [
        FactorsDataFactory.createFactor(),
        FactorsDataFactory.createFactor()
      ]
      
      mockApiClient.get.mockResolvedValue(expectedFactors)

      // Act
      const result = await factorsApi.getFactors()

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/strategy/factors/', { params: undefined })
      expect(result).toEqual(expectedFactors)
    })

    it('应该处理获取因子列表失败的情况', async () => {
      // Arrange
      const error = new Error('获取因子列表失败')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(factorsApi.getFactors()).rejects.toThrow('获取因子列表失败')
    })
  })

  describe('getFactor', () => {
    it('应该能够根据ID获取特定因子详情', async () => {
      // Arrange
      const factorId = 'test-factor-id'
      const expectedFactor: Factor = FactorsDataFactory.createFactor()
      
      mockApiClient.get.mockResolvedValue({ data: expectedFactor })

      // Act
      const result = await factorsApi.getFactor(factorId)

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(`/api/v1/factors/${factorId}`)
      expect(result).toEqual(expectedFactor)
    })

    it('应该处理因子不存在的情况', async () => {
      // Arrange
      const factorId = 'non-existent-factor'
      const error = new Error('因子不存在')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(factorsApi.getFactor(factorId)).rejects.toThrow('因子不存在')
    })
  })

  describe('createCustomFactor', () => {
    it('应该能够创建自定义因子', async () => {
      // Arrange
      const factorData = {
        name: '自定义因子',
        description: '这是一个自定义因子',
        formula: 'close / open',
        category: 'technical'
      }
      const expectedFactor: Factor = FactorsDataFactory.createFactor()
      
      mockApiClient.post.mockResolvedValue({ data: expectedFactor })

      // Act
      const result = await factorsApi.createCustomFactor(factorData)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith('/api/v1/factors/custom', factorData)
      expect(result).toEqual(expectedFactor)
    })

    it('应该处理创建因子失败的情况', async () => {
      // Arrange
      const factorData = {
        name: '',
        description: '',
        formula: '',
        category: ''
      }
      const error = new Error('因子名称不能为空')
      mockApiClient.post.mockRejectedValue(error)

      // Act & Assert
      await expect(factorsApi.createCustomFactor(factorData)).rejects.toThrow('因子名称不能为空')
    })
  })

  describe('testFactor', () => {
    it('应该能够测试因子计算', async () => {
      // Arrange
      const testRequest: FactorTestRequest = FactorsDataFactory.createFactorTestRequest()
      const expectedResult: FactorTestResult = FactorsDataFactory.createFactorTestResult()
      
      mockApiClient.post.mockResolvedValue({ data: expectedResult })

      // Act
      const result = await factorsApi.testFactor(testRequest)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith('/api/v1/factors/test', testRequest)
      expect(result).toEqual(expectedResult)
    })

    it('应该处理因子计算测试失败的情况', async () => {
      // Arrange
      const testRequest: FactorTestRequest = {
        factor_id: 'invalid-factor',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      }
      const error = new Error('因子计算失败')
      mockApiClient.post.mockRejectedValue(error)

      // Act & Assert
      await expect(factorsApi.testFactor(testRequest)).rejects.toThrow('因子计算失败')
    })
  })

  describe('getFactorValues', () => {
    it('应该能够获取因子值', async () => {
      // Arrange
      const calculationRequest = {
        factor_id: 'test-factor-id',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      }
      const expectedValues = {
        dates: ['2023-01-01', '2023-01-02'],
        values: [1.2, 1.3]
      }
      
      mockApiClient.post.mockResolvedValue({ data: expectedValues })

      // Act
      const result = await factorsApi.getFactorValues(calculationRequest)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/api/v1/factors/calculate',
        calculationRequest
      )
      expect(result).toEqual(expectedValues)
    })

    it('应该处理获取因子值失败的情况', async () => {
      // Arrange
      const calculationRequest = {
        factor_id: 'invalid-factor',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      }
      const error = new Error('获取因子值失败')
      mockApiClient.post.mockRejectedValue(error)

      // Act & Assert
      await expect(
        factorsApi.getFactorValues(calculationRequest)
      ).rejects.toThrow('获取因子值失败')
    })
  })
})