# backend/tests/abupy_adapter/test_executor_facade.py

import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor
from backend.app.schemas.market import KlineData, KlineItem
from backend.app.core.exceptions import AdapterError, ParameterError, FactorError
from backend.app.abupy_adapter.execution.executor_facade import StrategyExecutor

# --- Test Helpers ---

def create_mock_kline_data(rows=30, symbol='us.TSLA') -> KlineData:
    """
    [修正版] 创建一个符合 KlineData Pydantic模型的模拟K线数据对象。
    这将确保返回值的类型是正确的，而不是一个原始的DataFrame。
    """
    # 1. 创建原始数据字典列表
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=rows, freq='D'))
    data_dicts = []
    for i, date in enumerate(dates):
        item = {
            "date": date.strftime('%Y-%m-%d'),
            "open": 100 + i,
            "close": 100 + i + (i % 5 - 2), # 增加一些波动
            "high": 100 + i + 5,
            "low": 100 + i - 5,
            "volume": 10000 + i * 100
        }
        data_dicts.append(item)

    # 2. 使用字典列表创建 KlineItem 对象列表
    kline_items = [KlineItem(**d) for d in data_dicts]

    # 3. 使用 KlineItem 列表创建并返回一个 KlineData 实例
    return KlineData(
        symbol=symbol,
        name=f"{symbol} Mock Data",
        market='us' if '.' in symbol else 'cn',
        period='day',
        data=kline_items,
        latest_date=kline_items[-1].date if kline_items else None
    )

def create_mock_strategy(buy_factors=True, sell_factors=True, initial_capital=1000000) -> Strategy:
    """创建一个标准化的测试策略。"""
    buy = [BuyFactor(name="BuyBreak", class_name="FactorBuyBreak", parameters={"xd": 20})] if buy_factors else []
    sell = [SellFactor(name="SellNDay", class_name="FactorSellNDay", parameters={"sell_n": 10})] if sell_factors else []
    
    return Strategy(
        id="test-strategy",
        name="Test Strategy",
        buy_factors=buy,
        sell_factors=sell,
        parameters={"initial_capital": initial_capital} if initial_capital else {}
    )

def create_mock_market_data(capital=1000000) -> dict:
    """创建一个标准化的市场数据字典。"""
    data = {
        "choice_symbols": ["600519.SH"],
        "benchmark_symbol": "000300.SH",
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
    }
    if capital:
        data["capital"] = capital
    return data

# --- Test Class ---

@patch('backend.app.abupy_adapter.execution.executor_facade.MarketService')
class TestStrategyExecutor:

    def test_execute_strategy_success_with_trades(self, mock_market_service_class):
        """测试：成功执行并返回交易和完整的性能指标。"""
        # 1. Arrange: 准备所有依赖和mock
        # Mock MarketService
        mock_market_service_instance = mock_market_service_class.return_value
        mock_market_service_instance.get_kline_data.return_value = create_mock_kline_data()

        # Mock abupy的核心返回
        mock_orders_df = pd.DataFrame({
            'symbol': ['600519.SH'], 'buy_date': [20230104], 'buy_price': [100.0],
            'sell_date': [20230109], 'sell_price': [110.0], 'profit': [1000.0],
            'buy_cnt': [100]
        })
        mock_action_df = pd.DataFrame({'capital_balance': [1000000, 990000, 1001000]})
        
        strategy = create_mock_strategy()
        market_data = create_mock_market_data()

        # 2. Act: 在patch上下文中执行
        with patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest', return_value=(mock_orders_df, mock_action_df, None)) as mock_call_abupy:
            result = StrategyExecutor.execute_strategy(strategy, market_data)

        # 3. Assert: 验证结果
        assert result['status'] == 'success'
        mock_call_abupy.assert_called_once()
        summary = result['execution_summary']
        assert summary['total_trades'] > 0
        assert summary['symbols_with_trades'] > 0
        assert 'annualized_return' in summary  # 验证性能指标被计算
        assert len(result['results'][0]['orders']) > 0

    def test_execute_strategy_success_no_trades(self, mock_market_service_class):
        """测试：成功执行但没有产生交易。"""
        # 1. Arrange
        mock_market_service_instance = mock_market_service_class.return_value
        mock_market_service_instance.get_kline_data.return_value = create_mock_kline_data()
        
        mock_orders_df = pd.DataFrame(columns=['symbol'])
        mock_action_df = pd.DataFrame({'capital_balance': [1000000]})
        
        strategy = create_mock_strategy()
        market_data = create_mock_market_data()

        # 2. Act
        with patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest', return_value=(mock_orders_df, mock_action_df, None)) as mock_call_abupy:
            result = StrategyExecutor.execute_strategy(strategy, market_data)
            mock_call_abupy.assert_called_once()

        # 3. Assert
        assert result['status'] == 'success'
        summary = result['execution_summary']
        assert summary['total_trades'] == 0
        assert result['results'][0]['message'] == '无交易'

    def test_parameter_error_missing_symbols(self, mock_market_service_class):
        """测试：当缺少必要参数（如股票池）时，应抛出ParameterError。"""
        strategy = create_mock_strategy()
        market_data = create_mock_market_data()
        market_data.pop("choice_symbols")
        
        with pytest.raises(ParameterError):
            StrategyExecutor.execute_strategy(strategy, market_data)

    def test_parameter_error_missing_capital(self, mock_market_service_class):
        """测试：当策略和市场数据都缺少资金时，应抛出ParameterError。"""
        strategy = create_mock_strategy(initial_capital=None)
        market_data = create_mock_market_data(capital=None)

        with pytest.raises(ParameterError, match=r"缺少初始资金参数"):
            StrategyExecutor.execute_strategy(strategy, market_data)

    def test_factor_error_no_buy_factors(self, mock_market_service_class):
        """测试：当策略没有买入因子时，应抛出FactorError。"""
        strategy = create_mock_strategy(buy_factors=False)
        market_data = create_mock_market_data()
        
        with pytest.raises(FactorError):
            StrategyExecutor.execute_strategy(strategy, market_data)

    def test_adapter_error_on_abupy_exception(self, mock_market_service_class):
        """测试：当abupy核心函数抛出异常时，应被捕获并包装为AdapterError。"""
        # 1. Arrange
        mock_market_service_instance = mock_market_service_class.return_value
        mock_market_service_instance.get_kline_data.return_value = create_mock_kline_data()
        
        strategy = create_mock_strategy()
        market_data = create_mock_market_data()
        
        # 2. Act & Assert
        with patch('backend.app.abupy_adapter.execution.executor_facade.call_abupy_backtest', side_effect=ValueError("Abu内部模拟崩溃")):
            with pytest.raises(AdapterError):
                StrategyExecutor.execute_strategy(strategy, market_data)