import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import BacktestResults from '@/components/BacktestResults.vue'
import type { BacktestResult } from '@/types/backtest'

// Mock数据
const mockBacktestResult: BacktestResult = {
  task_id: 'test-001',
  strategy_name: '双均线策略',
  symbol: '000001.SZ',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  initial_capital: 100000,
  final_capital: 120000,
  total_return: 0.2,
  annual_return: 0.18,
  sharpe_ratio: 1.5,
  max_drawdown: 0.08,
  win_rate: 0.65,
  total_trades: 150,
  status: 'completed',
  created_at: '2024-01-01T00:00:00Z',
  completed_at: '2024-01-01T01:00:00Z',
  trades: [
    {
      id: 'trade-1',
      symbol: '000001.SZ',
      side: 'buy',
      quantity: 100,
      price: 10.5,
      timestamp: '2023-01-02T09:30:00Z'
    }
  ],
  daily_returns: [0.01, -0.005, 0.02, 0.015]
}

describe('BacktestResults.vue - 扩展功能测试', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(BacktestResults, {
      props: {
        loading: false,
        result: null
      }
    })
  })

  describe('详细指标展示测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: mockBacktestResult })
    })

    it('应该显示年化收益率', () => {
      expect(wrapper.find('[data-testid="annual-return-card"]').exists()).toBe(true)
    })

    it('应该显示胜率', () => {
      expect(wrapper.find('[data-testid="win-rate-card"]').exists()).toBe(true)
    })

    it('应该显示交易次数', () => {
      expect(wrapper.find('[data-testid="total-trades-card"]').exists()).toBe(true)
    })

    it('应该显示初始资金', () => {
      expect(wrapper.find('[data-testid="initial-capital-card"]').exists()).toBe(true)
    })

    it('应该显示最终资金', () => {
      expect(wrapper.find('[data-testid="final-capital-card"]').exists()).toBe(true)
    })
  })

  describe('边界情况处理测试', () => {
    it('应该处理空的交易列表', async () => {
      const emptyTradesResult = { ...mockBacktestResult, trades: [] }
      await wrapper.setProps({ result: emptyTradesResult })
      
      expect(wrapper.find('.results-content').exists()).toBe(true)
      expect(wrapper.find('[data-testid="total-trades-card"]').text()).toContain('0')
    })

    it('应该处理空的日收益数组', async () => {
      const emptyReturnsResult = { ...mockBacktestResult, daily_returns: [] }
      await wrapper.setProps({ result: emptyReturnsResult })
      
      expect(wrapper.find('.results-content').exists()).toBe(true)
    })

    it('应该处理极端的收益率值', async () => {
      const extremeResult = { 
        ...mockBacktestResult, 
        total_return: 10.0,  // 1000%收益
        max_drawdown: 0.99   // 99%回撤
      }
      await wrapper.setProps({ result: extremeResult })
      
      expect(wrapper.find('[data-testid="total-return-card"]').text()).toContain('1000.00%')
      expect(wrapper.find('[data-testid="max-drawdown-card"]').text()).toContain('99.00%')
    })

    it('应该处理非常小的数值', async () => {
      const smallValueResult = { 
        ...mockBacktestResult, 
        total_return: 0.0001,
        sharpe_ratio: 0.001
      }
      await wrapper.setProps({ result: smallValueResult })
      
      expect(wrapper.find('[data-testid="total-return-card"]').text()).toContain('0.01%')
      expect(wrapper.find('[data-testid="sharpe-ratio-card"]').text()).toContain('0.001')
    })
  })

  describe('样式和布局测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: mockBacktestResult })
    })

    it('应该有正确的容器类名', () => {
      expect(wrapper.find('.backtest-results').exists()).toBe(true)
    })

    it('指标卡片应该使用网格布局', () => {
      expect(wrapper.find('.metrics-grid').exists()).toBe(true)
    })

    it('详细信息应该使用网格布局', () => {
      expect(wrapper.find('.details-grid').exists()).toBe(true)
    })

    it('操作按钮应该正确排列', () => {
      expect(wrapper.find('.action-buttons').exists()).toBe(true)
      expect(wrapper.find('[data-testid="export-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="analysis-btn"]').exists()).toBe(true)
    })
  })

  describe('可访问性测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: mockBacktestResult })
    })

    it('应该有正确的语义结构', () => {
      expect(wrapper.find('h2').exists()).toBe(true)
    })

    it('按钮应该有正确的data-testid', () => {
      expect(wrapper.find('[data-testid="export-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="analysis-btn"]').exists()).toBe(true)
    })

    it('指标卡片应该有适当的标识', () => {
      expect(wrapper.find('[data-testid="total-return-card"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="sharpe-ratio-card"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="max-drawdown-card"]').exists()).toBe(true)
    })
  })

  describe('数据精度处理测试', () => {
    it('应该正确处理极小收益率', async () => {
      const result = { ...mockBacktestResult, total_return: 0.0001 }
      await wrapper.setProps({ result })
      expect(wrapper.find('[data-testid="total-return-card"]').text()).toContain('0.01%')
    })

    it('应该正确处理极大收益率', async () => {
      const result = { ...mockBacktestResult, total_return: 50.0 }
      await wrapper.setProps({ result })
      expect(wrapper.find('[data-testid="total-return-card"]').text()).toContain('5000.00%')
    })

    it('应该正确处理零值', async () => {
      const zeroResult = { 
        ...mockBacktestResult, 
        total_return: 0,
        sharpe_ratio: 0,
        max_drawdown: 0
      }
      await wrapper.setProps({ result: zeroResult })
      
      expect(wrapper.find('[data-testid="total-return-card"]').text()).toContain('0.00%')
      expect(wrapper.find('[data-testid="sharpe-ratio-card"]').text()).toContain('0.000')
      expect(wrapper.find('[data-testid="max-drawdown-card"]').text()).toContain('0.00%')
    })

    it('应该正确处理小数精度', async () => {
      const preciseResult = { 
        ...mockBacktestResult, 
        total_return: 0.123456789,
        sharpe_ratio: 1.987654321
      }
      await wrapper.setProps({ result: preciseResult })
      
      expect(wrapper.find('[data-testid="total-return-card"]').text()).toContain('12.35%')
      expect(wrapper.find('[data-testid="sharpe-ratio-card"]').text()).toContain('1.988')
    })
  })

  describe('用户体验增强测试', () => {
    beforeEach(async () => {
      await wrapper.setProps({ result: mockBacktestResult })
    })

    it('应该在导出时提供用户反馈', async () => {
      const exportBtn = wrapper.find('[data-testid="export-btn"]')
      await exportBtn.trigger('click')
      
      // 验证导出状态反馈（需要组件支持）
      const exportEvents = wrapper.emitted('export')
      expect(exportEvents).toHaveLength(1)
    })

    it('应该在分析按钮点击时提供反馈', async () => {
      const analysisBtn = wrapper.find('[data-testid="analysis-btn"]')
      await analysisBtn.trigger('click')
      
      const analysisEvents = wrapper.emitted('viewAnalysis')
      expect(analysisEvents).toHaveLength(1)
      expect(analysisEvents![0][0]).toEqual(mockBacktestResult)
    })
  })

  describe('本地化支持测试', () => {
    it('应该支持不同的货币格式', async () => {
      // 测试美元格式
      const usdResult = { ...mockBacktestResult, currency: 'USD' }
      await wrapper.setProps({ result: usdResult, locale: 'en-US' })
      
      // 注意：这需要组件支持locale属性
      expect(wrapper.find('[data-testid="final-capital-card"]').exists()).toBe(true)
    })

    it('应该支持不同的日期格式', async () => {
      await wrapper.setProps({ result: mockBacktestResult, locale: 'en-US' })
      
      // 验证日期格式（需要组件支持locale）
      expect(wrapper.find('[data-testid="date-range"]').exists()).toBe(true)
    })
  })
})