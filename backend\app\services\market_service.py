# -*- coding: utf-8 -*-
"""
市场数据服务层，集成tushare和abu原有数据处理逻辑
"""
import os
import sys
import re   
print(f"MARKET_SERVICE_DIAG: Loading market_service.py. sys.modules keys: {{'backend.app.abupy_adapter.compatibility_patch' in sys.modules}}, {{'backend.app.abupy_adapter.compat' in sys.modules}}")
if 'backend.app.abupy_adapter.compatibility_patch' in sys.modules:
    print(f"MARKET_SERVICE_DIAG: compatibility_patch found in sys.modules. Object: {{sys.modules['backend.app.abupy_adapter.compatibility_patch']}}")
    try:
        print(f"MARKET_SERVICE_DIAG: compatibility_patch.__name__: {{sys.modules['backend.app.abupy_adapter.compatibility_patch'].__name__}}")
    except Exception as e:
        print(f"MARKET_SERVICE_DIAG: Error accessing compatibility_patch attributes: {{e}}")

if 'backend.app.abupy_adapter.compat' in sys.modules:
    print(f"MARKET_SERVICE_DIAG: compat found in sys.modules. Object: {{sys.modules['backend.app.abupy_adapter.compat']}}")
    try:
        print(f"MARKET_SERVICE_DIAG: compat.__name__: {{sys.modules['backend.app.abupy_adapter.compat'].__name__}}")
    except Exception as e:
        print(f"MARKET_SERVICE_DIAG: Error accessing compat attributes: {{e}}")
import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
import tushare as ts
import math
import logging
from pathlib import Path

# 添加abu原始项目路径，以便导入原有模块
sys.path.append('D:/智能投顾/量化相关/abu')

# 导入兼容性模块并应用补丁
print("MARKET_SERVICE_DIAG: About to import compat module in market_service.py")
from backend.app.abupy_adapter.compat import *
print("MARKET_SERVICE_DIAG: Finished import of compat in market_service.py")

# 导入abu原有模块
from abupy import ABuSymbolPd
from abupy.MarketBu import ABuMarket
from abupy.UtilBu import ABuDateUtil
from abupy.CoreBu import ABuEnv

# 导入我们的适配器
from backend.app.abupy_adapter.symbol_adapter import SymbolAdapter

# 使用我们创建的AbuDataCache适配器类
from backend.app.abupy_adapter.data_cache_adapter import AbuDataCache, load_kline_df, dump_kline_df, save_kline_df, load_kline_df_net

# 导入自定义异常
from backend.app.core.exceptions import DataNotFoundError, ExternalAPIError, SymbolError, ValidationError

from backend.app.core.config import settings
from backend.app.schemas.market import StockBasic, KlineData, KlineItem, StockFundamental


def safe_float(value) -> Optional[float]:
    """
    安全地转换为浮点数，处理NaN和无效值
    
    Args:
        value: 要转换的值
        
    Returns:
        Optional[float]: 处理后的浮点数，如果是NaN或无效则返回None
    """
    if value is None:
        return None
    
    try:
        # 尝试转换为浮点数
        float_val = float(value)
        
        # 检查是否是NaN或无穷大
        if math.isnan(float_val) or math.isinf(float_val):
            return None
        return float_val
    except (ValueError, TypeError):
        return None


class MarketService:
    """市场数据服务"""
    
    def __init__(self):
        """初始化，设置tushare token和环境配置"""
        # 初始化tushare
        try:
            if not settings.TUSHARE_TOKEN:
                raise ValidationError(
                    message="缺失Tushare API Token",
                    error_code="MISSING_API_TOKEN"
                )
            ts.set_token(settings.TUSHARE_TOKEN)
            self.pro = ts.pro_api()
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            logging.error(f"[_format_tushare_kline_data] 格式化Tushare K线数据时出错, symbol: {settings.TUSHARE_TOKEN}, freq: {settings.TUSHARE_TOKEN}, error: {str(e)}", exc_info=True)
            raise ExternalAPIError(
                message=f"初始化Tushare API失败: {str(e)}",
                data={"token_provided": bool(settings.TUSHARE_TOKEN)}
            )
        
        # 初始化abu环境
        ABuEnv.g_data_cache_type = ABuEnv.EDataCacheType.E_DATA_CACHE_CSV

        self.a_share_index_codes = {
            "000001.SH", "399001.SZ", "000300.SH", "399005.SZ", 
            "399006.SZ", "000905.SH", "000016.SH", "000688.SH"
        }
        if settings.ENABLE_CACHE:
            # 启用abu数据缓存
            AbuDataCache.disable_cache = False
            AbuDataCache.cache_expiry_days = settings.CACHE_EXPIRE_DAYS
        else:
            AbuDataCache.disable_cache = True
    
    def get_stock_list(self, market: Optional[str] = None, 
                      industry: Optional[str] = None, 
                      name: Optional[str] = None) -> List[StockBasic]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 CN(A股)/US(美股)/HK(港股)
            industry: 行业类型
            name: 股票名称（模糊查询）
            
        Returns:
            List[StockBasic]: 股票列表
            
        Raises:
            ValidationError: 当市场类型无效时
            ExternalAPIError: 当外部API调用失败时
            DataNotFoundError: 当没有找到匹配的股票数据时
        """
        # 验证市场参数
        if market is not None and market not in ['CN', 'US', 'HK']:
            raise ValidationError(
                message=f"不支持的市场类型: {market}",
                data={"supported_markets": ['CN', 'US', 'HK']}
            )
            
        if market == 'CN' or market is None:
            try:
                # 使用tushare获取A股列表
                fields = 'ts_code,name,industry,list_date,market,is_hs,is_st'
                where_condition = ""
                
                if industry:
                    where_condition += f" AND industry='{industry}'"
                if name:
                    where_condition += f" AND name LIKE '%{name}%'"
                    
                try:
                    df = self.pro.stock_basic(exchange='', list_status='L',
                                          fields=fields)
                except Exception as e:
                    logging.error(f"Tushare API调用失败: {str(e)}")
                    raise ExternalAPIError(
                        message=f"Tushare API获取股票列表失败: {str(e)}",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                if industry or name:
                    # 过滤数据
                    if industry:
                        df = df[df['industry'] == industry]
                    if name:
                        df = df[df['name'].str.contains(name)]
                
                # 检查是否找到数据
                if df.empty:
                    raise DataNotFoundError(
                        message="未找到匹配的股票数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                # 转换为响应模型
                result = []
                for _, row in df.iterrows():
                    try:
                        # 从tushare代码(如000001.SZ)转换为abu格式(如sh000001)
                        symbol = self._convert_to_abu_symbol(row['ts_code'])
                        
                        stock = StockBasic(
                            symbol=symbol,
                            name=row['name'],
                            market='CN',
                            industry=row.get('industry'),
                            list_date=row.get('list_date'),
                            is_hs=row.get('is_hs'),
                            is_st=bool(row.get('is_st') == 1) if 'is_st' in row else None
                        )
                        result.append(stock)
                    except Exception as e:
                        # 记录错误但继续处理其他股票
                        logging.warning(f"处理股票 {row.get('ts_code', 'unknown')} 出错: {str(e)}")
                        continue
                
                if not result:
                    raise DataNotFoundError(
                        message="未找到有效的股票数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                    
                return result
            except Exception as e:
                if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError)):
                    raise
                logging.error(f"获取A股列表失败: {str(e)}")
                raise ExternalAPIError(
                    message=f"获取A股列表失败: {str(e)}",
                    data={"market": market, "industry": industry, "name": name}
                )
        
        elif market == 'US':
            try:
                # 使用abu原有逻辑获取美股列表
                us_symbols = ABuMarket.all_us_symbol()
                if not us_symbols:
                    raise DataNotFoundError(
                        message="未找到美股列表数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                    
                result = []
                # 限制返回数量，实际应用中可以分页
                count = 0
                for symbol in us_symbols:
                    if count >= 100:  # 限制返回数量
                        break
                        
                    try:
                        # 使用abu的函数获取股票名称
                        stock_name = SymbolAdapter.get_symbol_name(symbol)
                        
                        # 如果有名称过滤条件，检查是否匹配
                        if name and name.lower() not in stock_name.lower():
                            continue
                            
                        stock = StockBasic(
                            symbol=symbol,
                            name=stock_name,
                            market='US'
                        )
                        result.append(stock)
                        count += 1
                    except Exception as e:
                        logging.warning(f"处理美股 {symbol} 出错: {str(e)}")
                        continue
                
                if not result:
                    raise DataNotFoundError(
                        message="未找到匹配的美股数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                return result
                
            except Exception as e:
                if isinstance(e, DataNotFoundError):
                    raise
                logging.error(f"获取美股列表失败: {str(e)}")
                raise ExternalAPIError(
                    message=f"获取美股列表失败: {str(e)}",
                    data={"market": market, "industry": industry, "name": name}
                )
        
        elif market == 'HK':
            try:
                # 使用tushare获取港股列表
                fields = 'ts_code,name,industry,list_date,market'
                try:
                    df = self.pro.hk_basic(fields=fields)
                except Exception as e:
                    logging.error(f"Tushare API调用失败: {str(e)}")
                    raise ExternalAPIError(
                        message=f"Tushare API获取港股列表失败: {str(e)}",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                # 过滤数据
                if industry:
                    df = df[df['industry'] == industry]
                if name:
                    df = df[df['name'].str.contains(name)]
                
                # 检查是否找到数据
                if df.empty:
                    raise DataNotFoundError(
                        message="未找到匹配的港股数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                result = []
                for _, row in df.iterrows():
                    try:
                        symbol = self._convert_to_abu_symbol(row['ts_code'], market='HK')
                        
                        stock = StockBasic(
                            symbol=symbol,
                            name=row['name'],
                            market='HK',
                            industry=row.get('industry'),
                            list_date=row.get('list_date')
                        )
                        result.append(stock)
                    except Exception as e:
                        logging.warning(f"处理港股 {row.get('ts_code', 'unknown')} 出错: {str(e)}")
                        continue
                
                if not result:
                    raise DataNotFoundError(
                        message="未找到有效的港股数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                return result
                
            except Exception as e:
                if isinstance(e, (DataNotFoundError, ExternalAPIError)):
                    raise
                logging.error(f"获取港股列表失败: {str(e)}")
                raise ExternalAPIError(
                    message=f"获取港股列表失败: {str(e)}",
                    data={"market": market, "industry": industry, "name": name}
                )
        
        else:
            # 这部分代码应该永远不会执行，因为我们在函数开头已经验证了market参数
            # 但作为防御性编程，还是保留它
            raise ValidationError(
                message=f"不支持的市场类型: {market}",
                data={"supported_markets": ['CN', 'US', 'HK']}
            )
    
    def _get_kline_from_local_h5(self, symbol: str, start_date: str, end_date: str, period: str) -> KlineData:
        """从本地HDF5文件获取K线数据"""
        logging.info(f"从本地HDF5文件 '{settings.LOCAL_DATA_PATH}' 获取 '{symbol}' 的K线数据。")
        # 移除 'sh' 或 'sz' 前缀以匹配HDF5文件中的key
        symbol_key = re.sub(r'^(sh|sz)', '', symbol)
        
        try:
            with pd.HDFStore(settings.LOCAL_DATA_PATH, mode='r') as store:
                h5_key = f'/{symbol_key}'
                if h5_key not in store.keys():
                    raise DataNotFoundError(
                        message=f"在本地HDF5文件中未找到股票 '{symbol}' (key: {h5_key}) 的数据。",
                        data={"symbol": symbol, "file": settings.LOCAL_DATA_PATH}
                    )
                
                df = store[h5_key]

            # 检查必要的列是否存在
            required_cols = {'trade_date', 'open', 'high', 'low', 'close', 'vol'}
            if not required_cols.issubset(df.columns):
                raise DataNotFoundError(
                    message=f"股票 '{symbol}' 的本地数据缺少必要列。需要: {required_cols}, 实际: {df.columns.tolist()}",
                    data={"symbol": symbol}
                )

            # 按日期过滤
            df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
            start = pd.to_datetime(start_date, format='%Y%m%d')
            end = pd.to_datetime(end_date, format='%Y%m%d')
            df_filtered = df[(df['trade_date'] >= start) & (df['trade_date'] <= end)]

            if df_filtered.empty:
                raise DataNotFoundError(
                    message=f"在指定日期范围 {start_date}-{end_date} 内未找到 '{symbol}' 的本地数据。",
                    data={"symbol": symbol, "start": start_date, "end": end_date}
                )

            # 将DataFrame转换为KlineData模型
            kline_items = [
                KlineItem(
                    date=row.trade_date.strftime('%Y-%m-%d'),
                    open=safe_float(row.open),
                    high=safe_float(row.high),
                    low=safe_float(row.low),
                    close=safe_float(row.close),
                    volume=safe_float(row.vol)  # 使用 'vol' 列
                )
                for row in df_filtered.itertuples()
            ]

            market = 'SH' if symbol.startswith('sh') else 'SZ' if symbol.startswith('sz') else 'Unknown'
            latest_date = df_filtered['trade_date'].max().strftime('%Y-%m-%d')

            return KlineData(
                symbol=symbol,
                name=symbol,  # 暂时使用symbol作为name
                market=market,
                period=period,
                data=kline_items,
                latest_date=latest_date
            )

        except FileNotFoundError:
            logging.error(f"本地数据文件未找到: {settings.LOCAL_DATA_PATH}")
            raise DataNotFoundError(
                message=f"本地数据文件未找到: {settings.LOCAL_DATA_PATH}",
                data={"file_path": settings.LOCAL_DATA_PATH}
            )
        except Exception as e:
            if isinstance(e, DataNotFoundError):
                raise
            logging.error(f"从本地HDF5文件读取数据时出错: {e}", exc_info=True)
            raise ExternalAPIError(message=f"读取本地数据失败: {e}")
    
    def get_kline_data(self, symbol: str, start_date: Optional[str] = None, 
                       end_date: Optional[str] = None, 
                       period: str = "daily", data_source: str = 'tushare') -> KlineData:
        """
        获取K线数据的统一公共入口。
        根据数据源，分发到不同的内部处理方法。
        
        Args:
            symbol: 股票代码
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            period: 周期类型，daily/weekly/monthly
            data_source: 数据源，tushare/local
            
        Returns:
            KlineData: K线数据
            
        Raises:
            SymbolError: 当股票代码格式无效时
            ValidationError: 当参数无效时
            DataNotFoundError: 当没有找到K线数据时
            ExternalAPIError: 当外部API调用失败时
        """
        logging.info(f"MarketService: 正在为 {symbol} 获取K线数据，源: {data_source}，日期: {start_date}-{end_date}")
        try:
            # 验证股票代码
            if not symbol:
                raise SymbolError(
                    message="股票代码不能为空",
                    data={}
                )
                
            # 验证period参数
            valid_periods = ["daily", "weekly", "monthly"]
            if period not in valid_periods:
                raise ValidationError(
                    message=f"无效的周期类型: {period}",
                    data={"valid_periods": valid_periods}
                )
            
            # 验证日期格式
            if start_date and (len(start_date) != 8 or not start_date.isdigit()):
                raise ValidationError(
                    message=f"开始日期格式错误: {start_date}, 应为YYYYMMDD格式",
                    data={"start_date": start_date}
                )
                
            if end_date and (len(end_date) != 8 or not end_date.isdigit()):
                raise ValidationError(
                    message=f"结束日期格式错误: {end_date}, 应为YYYYMMDD格式",
                    data={"end_date": end_date}
                )
            
            # 使用Symbol适配器验证股票代码格式
            SymbolAdapter.validate_symbol(symbol)
            
            # 确定市场类型
            self._get_market_from_symbol(symbol)
            
            # 转换日期格式
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            if not start_date:
                # 默认获取1年数据
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                
            # 根据数据源分发到不同的处理方法
            if data_source == 'local':
                return self._get_kline_from_local_h5(symbol, start_date, end_date, period)
            elif data_source == 'tushare':
                # FIX: 传递 period 参数
                return self._get_kline_from_tushare(symbol, start_date, end_date, period)
            else:
                # 默认或备选方案，可以使用abu原有方式
                # 这里暂时也指向tushare，未来可以扩展
                logging.warning(f"未知的数据源 '{data_source}'，将默认使用tushare。")
                return self._get_kline_from_tushare(symbol, start_date, end_date, period)
            
        except Exception as e:
            if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError, SymbolError)):
                raise
            logging.error(f"获取K线数据失败: {str(e)}")
            raise ExternalAPIError(
                message=f"获取K线数据失败: {str(e)}",
                data={"symbol": symbol, "period": period}
            )
            
    def _is_index(self, symbol: str) -> bool:
        """
        一个简单的辅助函数，根据后缀判断是否为指数。
        可根据需要扩展更复杂的逻辑。
        """
        # 使用SymbolAdapter的is_index方法
        return SymbolAdapter.is_index(symbol)
            
    def _get_kline_from_local(self, symbol: str, start_date: str, end_date: str) -> KlineData:
        """从本地HDF5文件加载K线数据。"""
        import re # 确保导入了 re 模块
        from backend.app.abupy_adapter.symbol_adapter import SymbolAdapter # 确保导入

        LOCAL_DATA_PATH = Path(settings.LOCAL_DATA_PATH) if hasattr(settings, 'LOCAL_DATA_PATH') else Path('data/market_data.h5')
        
        if not LOCAL_DATA_PATH.exists():
            logging.error(f"本地数据文件不存在: {LOCAL_DATA_PATH}")
            raise FileNotFoundError(f"本地测试数据文件未找到: {LOCAL_DATA_PATH}")
        # 声明一个变量，以便在日志中追踪
        symbol_key = ""
        try:
            # 最终决战修复：根据侦察情报，仓库的钥匙是纯数字格式。
            # 我们从任何格式的symbol中提取出数字部分，作为最终的钥匙。
            match = re.search(r'(\d{6})', symbol)
            if not match:
                raise KeyError(f"无法从符号 '{symbol}' 中提取出6位数字代码。")
            
            # 从侦察报告得知，钥匙的格式是 '/数字'
            symbol_key = f"/{match.group(1)}"
            
            logging.info(f"根据侦察情报，为符号 '{symbol}' 生成的最终HDF5钥匙是: '{symbol_key}'")
            df = pd.read_hdf(LOCAL_DATA_PATH, key=symbol_key)

            # --- 核心修改：确保返回的数据包含abupy需要的列 ---
            # abupy需要 'date' 作为 int 类型 (YYYYMMDD) 和 'datetime' 作为索引
            if 'trade_date' in df.columns: # Tushare返回的是trade_date
                df.rename(columns={'trade_date': 'date_str'}, inplace=True)
                df['date'] = df['date_str'].astype(int)
                df['datetime'] = pd.to_datetime(df['date_str'])
                df.set_index('datetime', inplace=True)
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            mask = (df.index >= start_dt) & (df.index <= end_dt)
            filtered_df = df.loc[mask]

            logging.info(f"成功从本地加载并筛选了 {symbol} 的 {len(filtered_df)} 条数据。")
            
            # 转换为响应模型
            kline_items = []
            filtered_df = filtered_df.sort_values('date')  # 按日期排序
            
            for _, row in filtered_df.iterrows():
                item = KlineItem(
                    date=str(row['date']),
                    open=safe_float(row['open']),
                    high=safe_float(row['high']),
                    low=safe_float(row['low']),
                    close=safe_float(row['close']),
                    volume=safe_float(row['volume']) if 'volume' in row else safe_float(row['vol']),
                    amount=safe_float(row['amount']) if 'amount' in row else None,
                    turnover_rate=safe_float(row['turnover']) if 'turnover' in row else None,
                    change_rate=safe_float(row['pct_chg']) if 'pct_chg' in row else None
                )
                kline_items.append(item)
            
            # 获取股票名称
            name = SymbolAdapter.get_symbol_name(symbol)
            
            # 计算基本技术指标
            indicators = self._calculate_indicators(filtered_df)
            
            return KlineData(
                symbol=symbol,
                name=name,
                market=self._get_market_from_symbol(symbol),
                period="daily",
                data=kline_items,
                latest_date=str(filtered_df['date'].iloc[-1]) if not filtered_df.empty else end_date,
                indicators=indicators
            )
        except KeyError:
            logging.error(f"在本地数据文件 {LOCAL_DATA_PATH} 中找不到键: {symbol_key}")
            raise DataNotFoundError(
                message=f"在本地数据文件中找不到 {symbol} 的数据",
                data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
            )
        except Exception as e:
            logging.error(f"从本地文件读取数据时发生未知错误: {e}")
            raise ExternalAPIError(
                message=f"从本地文件读取数据时发生错误: {str(e)}",
                data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
            )
            
    def _get_kline_from_tushare(self, symbol: str, start_date: str, end_date: str, period: str) -> KlineData:
        """
        通过Tushare API(A股/港股)或abu(其他市场)从网络获取K线数据。
        FIX: 此方法已重构以修复语法错误并整合逻辑。
        """
        market = self._get_market_from_symbol(symbol)

        # A股和港股走Tushare通道
        if market in ['CN', 'HK']:
            try:
                # 转换为tushare格式的代码
                ts_symbol = self._convert_to_tushare_symbol(symbol)
                
                # 判断是否为指数
                is_index = self._is_index(symbol)
                
                # 注意: Tushare的 daily, weekly, monthly 接口是独立的。
                # 此处为了保持原逻辑，只实现了daily。可以根据period扩展。
                if is_index:
                    logging.info(f"调用Tushare指数接口获取 {symbol}...")
                    df = self.pro.index_daily(ts_code=ts_symbol, start_date=start_date, end_date=end_date)
                    logging.debug(f"使用Tushare index_daily获取指数 {ts_symbol} 数据")
                else:
                    logging.info(f"调用Tushare股票接口获取 {symbol}...")
                    if market == 'HK':
                        logging.debug(f"Attempting Tushare API call for HK. Symbol: {ts_symbol}, Start: {start_date}, End: {end_date}")
                        df = self.pro.hk_daily(ts_code=ts_symbol, start_date=start_date, end_date=end_date)
                    else: # A股
                        df = self.pro.daily(ts_code=ts_symbol, start_date=start_date, end_date=end_date)
                        logging.debug(f"使用Tushare daily获取股票 {ts_symbol} 数据")
                    
                if df is None or df.empty:
                    raise DataNotFoundError(
                        message=f"未找到{symbol}的K线数据",
                        data={"symbol": symbol, "ts_symbol": ts_symbol, "is_index": is_index, "start_date": start_date, "end_date": end_date}
                    )
                
                # 转换为响应模型
                kline_items = []
                df = df.sort_values('trade_date')
                
                for _, row in df.iterrows():
                    logging.debug(f"Processing Tushare row for {ts_symbol}, date {row.get('trade_date')}: {row.to_dict()}")
                    item = KlineItem(
                        date=row['trade_date'],
                        open=safe_float(row['open']),
                        high=safe_float(row['high']),
                        low=safe_float(row['low']),
                        close=safe_float(row['close']),
                        volume=safe_float(row['vol']),
                        amount=safe_float(row['amount']) if 'amount' in row else None,
                        turnover_rate=safe_float(row.get('turnover_rate')) if 'turnover_rate' in row else None,
                        change_rate=safe_float(row.get('pct_chg')) if 'pct_chg' in row else None
                    )
                    kline_items.append(item)
                
                name = SymbolAdapter.get_symbol_name(symbol)
                indicators = self._calculate_indicators(df)
                
                return KlineData(
                    symbol=symbol,
                    name=name,
                    market=market,
                    period=period,
                    data=kline_items,
                    latest_date=df['trade_date'].iloc[-1] if not df.empty else end_date,
                    indicators=indicators
                )
                    
            except Exception as e:
                if isinstance(e, DataNotFoundError):
                    raise
                logging.error(f"通过Tushare获取 {symbol} 数据时出错: {e}", exc_info=True)
                raise ExternalAPIError(
                    message=f"通过Tushare获取K线数据失败: {str(e)}",
                    data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
                )
        
        # 其他市场 (如美股) 走abu通道
        else:
            try:
                start_date_abu = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:]}"
                end_date_abu = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:]}"
                
                try:
                    kl_pd = ABuSymbolPd.make_kl_df(symbol, start=start_date_abu, end=end_date_abu)
                except Exception as e:
                    logging.error(f"ABU API调用失败: {str(e)}", exc_info=True)
                    raise ExternalAPIError(
                        message=f"ABU API获取K线数据失败: {str(e)}",
                        data={"symbol": symbol, "market": market, "period": period}
                    )
                
                if kl_pd is None or kl_pd.empty:
                    raise DataNotFoundError(
                        message=f"未找到{symbol}的K线数据",
                        data={"symbol": symbol, "market": market, "start_date": start_date, "end_date": end_date, "period": period}
                    )
                
                kline_items = []
                for _, row in kl_pd.iterrows():
                    try:
                        date_str = row.name.strftime('%Y%m%d') if isinstance(row.name, (pd.Timestamp, datetime)) else str(row.get('date', ''))
                        
                        item = KlineItem(
                            date=date_str,
                            open=safe_float(row['open']),
                            high=safe_float(row['high']),
                            low=safe_float(row['low']),
                            close=safe_float(row['close']),
                            volume=safe_float(row['volume']),
                            amount=None,
                            turnover_rate=None,
                            change_rate=None
                        )
                        kline_items.append(item)
                    except Exception as e:
                        logging.warning(f"处理K线数据行出错: {str(e)}")
                        continue
                
                if not kline_items:
                    raise DataNotFoundError(
                        message=f"未能成功转换{symbol}的K线数据",
                        data={"symbol": symbol, "market": market, "period": period}
                    )
                
                name = SymbolAdapter.get_symbol_name(symbol)
                indicators = self._calculate_indicators_from_abu(kl_pd)
                latest_date = kl_pd.index[-1].strftime('%Y%m%d') if isinstance(kl_pd.index[-1], pd.Timestamp) else str(kl_pd.index[-1])
                
                return KlineData(
                    symbol=symbol,
                    name=name,
                    market=market,
                    period=period,
                    data=kline_items,
                    latest_date=latest_date,
                    indicators=indicators
                )
                
            except Exception as e:
                if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError, SymbolError)):
                    raise
                logging.error(f"获取其他市场 K线数据失败: {str(e)}", exc_info=True)
                raise ExternalAPIError(
                    message=f"获取{market}市场K线数据失败: {str(e)}",
                    data={"symbol": symbol, "market": market, "period": period}
                )

    def _get_index_data(self, ts_symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取指数数据"""
        # 这里可以根据ts_symbol和日期范围获取指数数据
        # 可以通过tushare接口获取，也可以通过其他数据源获取
        # 这里简单返回一个空的DataFrame作为示例
        return pd.DataFrame()
    
    def get_fundamental_data(self, symbol: str, date: Optional[str] = None) -> StockFundamental:
        """
        获取基本面数据
        
        Args:
            symbol: 股票代码
            date: 日期，格式YYYYMMDD
            
        Returns:
            StockFundamental: 基本面数据
        """
        # 确定市场类型
        market = self._get_market_from_symbol(symbol)
        
        # 如果未指定日期，使用最新日期
        if not date:
            date = datetime.now().strftime('%Y%m%d')
        
        # 获取股票名称
        name = SymbolAdapter.get_symbol_name(symbol)
        
        if market == 'CN':
            # 使用tushare获取A股基本面数据
            ts_symbol = self._convert_to_tushare_symbol(symbol)
            
            # 获取基本指标
            try:
                df_basic = self.pro.daily_basic(ts_code=ts_symbol, trade_date=date)
                if df_basic.empty:
                    # 如果指定日期没有数据，获取最近的交易日数据
                    df_basic = self.pro.daily_basic(ts_code=ts_symbol)
                    if not df_basic.empty:
                        df_basic = df_basic.iloc[0:1]  # 取最新一条
                        date = df_basic['trade_date'].iloc[0]
            except Exception as e:
                # 处理可能的API错误
                print(f"获取基本指标出错: {str(e)}")
                df_basic = pd.DataFrame()
            
            # 获取财务指标
            try:
                # 财务指标通常按季度发布，使用年份和季度查询
                year = date[:4]
                month = int(date[4:6])
                quarter = (month - 1) // 3 + 1
                end_date_for_fina = f"{year}{quarter*3:02d}31" # 获取该季度末的数据
                
                df_finance = self.pro.fina_indicator(ts_code=ts_symbol, end_date=end_date_for_fina)
                if df_finance.empty:
                    # 如果指定期间没有数据，获取最近的财报数据
                    df_finance = self.pro.fina_indicator(ts_code=ts_symbol)
                    if not df_finance.empty:
                        df_finance = df_finance.iloc[0:1]  # 取最新一条
            except Exception as e:
                print(f"获取财务指标出错: {str(e)}")
                df_finance = pd.DataFrame()
            
            # 构建响应数据
            fundamental = StockFundamental(
                symbol=symbol,
                name=name,
                market=market,
                date=date
            )
            
            # 填充基本指标
            if not df_basic.empty:
                fundamental.pe = float(df_basic['pe'].iloc[0]) if 'pe' in df_basic and not pd.isna(df_basic['pe'].iloc[0]) else None
                fundamental.pe_ttm = float(df_basic['pe_ttm'].iloc[0]) if 'pe_ttm' in df_basic and not pd.isna(df_basic['pe_ttm'].iloc[0]) else None
                fundamental.pb = float(df_basic['pb'].iloc[0]) if 'pb' in df_basic and not pd.isna(df_basic['pb'].iloc[0]) else None
                fundamental.ps = float(df_basic['ps'].iloc[0]) if 'ps' in df_basic and not pd.isna(df_basic['ps'].iloc[0]) else None
                fundamental.ps_ttm = float(df_basic['ps_ttm'].iloc[0]) if 'ps_ttm' in df_basic and not pd.isna(df_basic['ps_ttm'].iloc[0]) else None
                fundamental.dv_ratio = float(df_basic['dv_ratio'].iloc[0]) if 'dv_ratio' in df_basic and not pd.isna(df_basic['dv_ratio'].iloc[0]) else None
                fundamental.dv_ttm = float(df_basic['dv_ttm'].iloc[0]) if 'dv_ttm' in df_basic and not pd.isna(df_basic['dv_ttm'].iloc[0]) else None
                fundamental.total_mv = float(df_basic['total_mv'].iloc[0]) if 'total_mv' in df_basic and not pd.isna(df_basic['total_mv'].iloc[0]) else None
                fundamental.circ_mv = float(df_basic['circ_mv'].iloc[0]) if 'circ_mv' in df_basic and not pd.isna(df_basic['circ_mv'].iloc[0]) else None
            
            # 填充额外的财务指标
            extra_data = {}
            if not df_finance.empty:
                indicators = ['roe', 'roa', 'grossprofit_margin', 'netprofit_margin', 'debt_to_assets']
                for indicator in indicators:
                    if indicator in df_finance and not pd.isna(df_finance[indicator].iloc[0]):
                        extra_data[indicator] = float(df_finance[indicator].iloc[0])
            
            if extra_data:
                fundamental.extra_data = extra_data
            
            return fundamental
        
        else:
            # 对于其他市场，可以使用abu原有接口或其他数据源
            # 这里简化处理，返回基本信息
            return StockFundamental(
                symbol=symbol,
                name=name,
                market=market,
                date=date,
                extra_data={"message": f"目前仅支持A股市场的详细基本面数据，{market}市场基本面数据将在后续版本中支持"}
            )
    
    def _convert_to_abu_symbol(self, ts_code: str, market: str = 'CN') -> str:
        """将tushare代码转换为abu格式代码"""
        if market == 'CN':
            code, exchange = ts_code.split('.')
            if exchange == 'SZ':
                return f"sz{code}"
            elif exchange == 'SH':
                return f"sh{code}"
            else:
                return ts_code
        elif market == 'HK':
            code = ts_code.split('.')[0]
            return f"hk{code}"
        else:
            # 其他市场简单返回
            return ts_code
    
    def _convert_to_tushare_symbol(self, abu_symbol: str) -> str:
        """将abu格式代码转换为tushare格式代码
        
        支持的输入格式：
        - 标准格式：'sh600000', 'sz000001', 'hk00700'
        - 纯数字格式：'600000', '000001', '00700'
        - 后缀格式：'600000.SH', '00700.HK'
        
        Returns:
            转换后的tushare格式代码，如'600000.SH', '00700.HK'
        """
        # 如果已经是tushare格式，直接返回
        if '.' in abu_symbol:
            # 确保后缀是大写
            code, market = abu_symbol.split('.')
            return f"{code}.{market.upper()}"
            
        # 处理标准格式
        if abu_symbol.startswith('sh'):
            return f"{abu_symbol[2:]}.SH"
        elif abu_symbol.startswith('sz'):
            return f"{abu_symbol[2:]}.SZ"
        elif abu_symbol.startswith('hk'): # Restore hk stock handling
            return f"{abu_symbol[2:]}.HK"
    
        # 处理纯数字代码
        if abu_symbol.isdigit():
            # 优先处理已知的常见A股指数
            if abu_symbol == '000001': # 上证指数
                return "000001.SH"
            elif abu_symbol == '399001': # 深证成指
                return "399001.SZ"
            elif abu_symbol == '000300': # 沪深300
                return "000300.SH"
            elif abu_symbol == '399005': # 中小板指 (深)
                return "399005.SZ"
            elif abu_symbol == '399006': # 创业板指 (深)
                return "399006.SZ"
            elif abu_symbol == '000905': # 中证500 (沪)
                return "000905.SH"
            elif abu_symbol == '000016': # 上证50 (沪)
                return "000016.SH"
            elif abu_symbol == '000688': # 科创50
                return "000688.SH"

            # 根据数字长度和开头判断市场 (适用于股票)
            if len(abu_symbol) == 6:
                # 6位数字，A股股票
                # 沪市主板6, 科创板通常以688或689开头, ETF 5xxxx, 可转债 11xxxx
                if abu_symbol.startswith('6') or \
                   (abu_symbol.startswith('5') and len(abu_symbol) == 6) or \
                   (abu_symbol.startswith('11') and len(abu_symbol) == 6) or \
                   abu_symbol.startswith('9'): # 沪市B股以9开头
                    return f"{abu_symbol}.SH"
                # 深市主板0, 创业板3, ETF 15xxxx, 可转债 12xxxx
                elif abu_symbol.startswith('0') or abu_symbol.startswith('3') or \
                     ((abu_symbol.startswith('15') or abu_symbol.startswith('16')) and len(abu_symbol) == 6) or \
                     (abu_symbol.startswith('12') and len(abu_symbol) == 6) or \
                     abu_symbol.startswith('2'): # 深市B股以2开头
                    return f"{abu_symbol}.SZ"
                else:
                    # 对于其他未明确规则的6位数字代码，记录警告并尝试默认为上海
                    logging.warning(f"无法根据首位数字明确判断市场，纯数字代码 {abu_symbol} 默认为.SH")
                    return f"{abu_symbol}.SH"
            elif len(abu_symbol) == 5 or \
                 (len(abu_symbol) == 4 and abu_symbol.startswith('00')) or \
                 (len(abu_symbol) <= 5 and not abu_symbol.startswith('00')): # 港股代码通常是5位纯数字，例如00700，但也有4位如0005汇丰，或不足5位但非00开头的旧代码
                return f"{abu_symbol.zfill(5)}.HK" # 港股代码补齐到5位，如 5 -> 00005.HK

        # 如果无法判断，尝试使用适配器标准化
        try:
            normalized_symbol, market = SymbolAdapter.normalize_symbol(abu_symbol)
            return self._convert_to_tushare_symbol(normalized_symbol)
        except Exception as e:
            logging.warning(f"无法将{abu_symbol}转换为tushare格式: {str(e)}")
            # 如果所有尝试都失败，返回原始代码
            return abu_symbol
    
    def _get_market_from_symbol(self, symbol: str) -> str:
        """从代码判断市场类型"""
        # 首先处理带市场前缀的代码
        if symbol.startswith('sh') or symbol.startswith('sz'):
            return 'CN'
        elif symbol.startswith('us'):
            return 'US'
        elif symbol.startswith('hk'):
            return 'HK'
        
        # 处理带后缀的代码，如'0700.HK'
        if '.' in symbol:
            code, market_suffix = symbol.split('.')
            if market_suffix.upper() == 'HK':
                return 'HK'
            elif market_suffix.upper() in ['SH', 'SZ']:
                return 'CN'
            # 可以添加其他市场后缀的判断
        
        # 处理纯数字代码，根据长度判断
        if symbol.isdigit():
            if len(symbol) == 6:
                # 6位纯数字，视为A股
                return 'CN'
            elif len(symbol) <= 5:
                # 5位或以下纯数字，视为港股
                return 'HK'
        
        # 尝试使用SymbolAdapter来获取市场类型
        try:
            _, market = SymbolAdapter.normalize_symbol(symbol)
            return market
        except Exception:
            pass
            
        # 默认返回A股
        logging.warning(f"无法明确判断符号 {symbol} 的市场，默认为 'CN'")
        return 'CN'
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, List[float]]:
        """计算基本技术指标"""
        if df.empty:
            return {}
        
        indicators = {}
        
        # 计算MA
        for period in [5, 10, 20, 60]:
            ma_key = f'ma{period}'
            if len(df) >= period:
                ma_series = df['close'].rolling(window=period).mean()
                # 处理NaN值
                ma_values = [safe_float(x) for x in ma_series.tolist()]
                indicators[ma_key] = ma_values
        
        # 这里可以添加更多指标计算，如MACD、RSI等
        # 也可以复用abu原有的指标计算逻辑
        
        return indicators
    
    def _calculate_indicators_from_abu(self, kl_pd: pd.DataFrame) -> Dict[str, List[float]]:
        """从abu K线数据计算技术指标"""
        # 这个方法可以复用abu原有的技术指标计算逻辑
        indicators = {}
        
        # 计算MA
        for period in [5, 10, 20, 60]:
            if len(kl_pd) >= period:
                ma_key = f'ma{period}'
                ma_series = kl_pd['close'].rolling(window=period).mean()
                # 处理NaN值
                ma_values = [safe_float(x) for x in ma_series.tolist()]
                indicators[ma_key] = ma_values
        
        # 可以添加更多abu原有的指标计算
        
        return indicators