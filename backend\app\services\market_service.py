# -*- coding: utf-8 -*-
"""
市场数据服务层，重定向到market/facade.py

此文件仅作为重定向用途，保持向后兼容性。
所有实际功能已移至backend.app.services.market包中的各个模块：
- facade.py: 市场数据服务门面，整合各个数据提供者的功能
- kline_provider.py: K线数据提供者
- fundamental_provider.py: 基本面数据提供者
- symbol_provider.py: 股票代码和列表数据提供者

请直接使用：from backend.app.services.market import MarketService
或：from backend.app.services.market.facade import MarketService
"""

# 从facade.py导入MarketService类
from backend.app.services.market.facade import MarketService

# 导出MarketService类
__all__ = ['MarketService']
