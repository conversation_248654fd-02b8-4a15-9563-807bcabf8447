import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { nextTick } from 'vue';
import BacktestHistory from '@/components/BacktestHistory.vue';
import { useBacktestStore } from '@/stores/useBacktestStore';
import { BacktestHistoryItem, BacktestStatus } from '@/types/backtest';
import { ElTable, ElTableColumn, ElButton, ElTag, ElPagination, ElInput, ElSelect, ElOption, ElDatePicker, ElCard } from 'element-plus';

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElTable: {
    name: 'ElTable',
    template: '<table class="el-table"><slot /></table>',
    props: ['data', 'stripe', 'border', 'loading'],
    emits: ['selection-change', 'sort-change']
  },
  ElTableColumn: {
    name: 'ElTableColumn',
    template: '<td><slot /></td>',
    props: ['prop', 'label', 'width', 'sortable', 'formatter', 'type']
  },
  ElButton: {
    name: 'ElButton',
    template: '<button :disabled="disabled" @click="$emit(\'click\')" :loading="loading"><slot /></button>',
    props: ['disabled', 'loading', 'type', 'size', 'icon'],
    emits: ['click']
  },
  ElTag: {
    name: 'ElTag',
    template: '<span class="el-tag" :class="`el-tag--${type}`"><slot /></span>',
    props: ['type', 'size', 'effect']
  },
  ElPagination: {
    name: 'ElPagination',
    template: '<div class="el-pagination"><slot /></div>',
    props: ['currentPage', 'pageSize', 'total', 'layout', 'pageSizes'],
    emits: ['current-change', 'size-change']
  },
  ElInput: {
    name: 'ElInput',
    template: '<input class="el-input" :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" :placeholder="placeholder" />',
    props: ['modelValue', 'placeholder', 'clearable'],
    emits: ['update:modelValue', 'clear']
  },
  ElSelect: {
    name: 'ElSelect',
    template: '<select class="el-select" :value="modelValue" @change="$emit(\'update:modelValue\', $event.target.value)"><slot /></select>',
    props: ['modelValue', 'placeholder', 'clearable'],
    emits: ['update:modelValue', 'change']
  },
  ElOption: {
    name: 'ElOption',
    template: '<option :value="value"><slot /></option>',
    props: ['value', 'label']
  },
  ElDatePicker: {
    name: 'ElDatePicker',
    template: '<input class="el-date-picker" :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" />',
    props: ['modelValue', 'type', 'placeholder', 'format'],
    emits: ['update:modelValue']
  },
  ElCard: {
    name: 'ElCard',
    template: '<div class="el-card"><div class="el-card__header"><slot name="header" /></div><div class="el-card__body"><slot /></div></div>',
    props: ['shadow']
  }
}));

// 模拟数据
const mockHistoryItems: BacktestHistoryItem[] = [
  {
    task_id: 'task-001',
    strategy_name: '双均线策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    status: BacktestStatus.COMPLETED,
    created_at: '2023-01-01T10:00:00Z',
    completed_at: '2023-01-01T10:30:00Z',
    total_return: 0.15,
    sharpe_ratio: 1.2,
    max_drawdown: 0.08
  },
  {
    task_id: 'task-002',
    strategy_name: 'RSI策略',
    symbol: '000002.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    status: BacktestStatus.RUNNING,
    created_at: '2023-01-02T09:00:00Z',
    completed_at: null,
    total_return: null,
    sharpe_ratio: null,
    max_drawdown: null
  },
  {
    task_id: 'task-003',
    strategy_name: 'MACD策略',
    symbol: '000003.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    status: BacktestStatus.FAILED,
    created_at: '2023-01-03T11:00:00Z',
    completed_at: '2023-01-03T11:05:00Z',
    total_return: null,
    sharpe_ratio: null,
    max_drawdown: null
  }
];

describe('BacktestHistory', () => {
  let wrapper: VueWrapper<any>;
  let store: ReturnType<typeof useBacktestStore>;
  
  beforeEach(() => {
    // 创建新的 Pinia 实例
    setActivePinia(createPinia());
    store = useBacktestStore();
    
    // 挂载组件
    wrapper = mount(BacktestHistory, {
      global: {
        plugins: [createPinia()],
        components: {
          ElTable,
          ElTableColumn,
          ElButton,
          ElTag,
          ElPagination,
          ElInput,
          ElSelect,
          ElOption,
          ElDatePicker,
          ElCard
        }
      }
    });
  });
  
  afterEach(() => {
    wrapper.unmount();
    vi.clearAllMocks();
  });
  
  describe('组件渲染', () => {
    it('应该正确渲染历史记录组件', () => {
      expect(wrapper.find('[data-testid="history-container"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="history-table"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="history-pagination"]').exists()).toBe(true);
    });
    
    it('应该显示搜索和筛选控件', () => {
      expect(wrapper.find('[data-testid="search-input"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="status-filter"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="date-range-picker"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="strategy-filter"]').exists()).toBe(true);
    });
    
    it('应该显示操作按钮', () => {
      expect(wrapper.find('[data-testid="refresh-button"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="clear-filters-button"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="batch-delete-button"]').exists()).toBe(true);
    });
  });
  
  describe('数据加载', () => {
    it('应该在组件挂载时加载历史记录', async () => {
      const getHistorySpy = vi.spyOn(store, 'getBacktestHistory').mockResolvedValue();
      
      wrapper = mount(BacktestHistory, {
        global: {
          plugins: [createPinia()]
        }
      });
      
      expect(getHistorySpy).toHaveBeenCalled();
    });
    
    it('应该显示加载状态', async () => {
      store.isLoading = true;
      await nextTick();
      
      const table = wrapper.find('[data-testid="history-table"]');
      expect(table.attributes('loading')).toBe('true');
    });
    
    it('应该显示历史记录数据', async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
      
      const rows = wrapper.findAll('[data-testid^="history-row-"]');
      expect(rows.length).toBe(mockHistoryItems.length);
    });
  });
  
  describe('状态显示', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该正确显示完成状态', () => {
      const completedRow = wrapper.find('[data-testid="history-row-task-001"]');
      const statusTag = completedRow.find('[data-testid="status-tag"]');
      
      expect(statusTag.text()).toContain('已完成');
      expect(statusTag.classes()).toContain('el-tag--success');
    });
    
    it('应该正确显示运行中状态', () => {
      const runningRow = wrapper.find('[data-testid="history-row-task-002"]');
      const statusTag = runningRow.find('[data-testid="status-tag"]');
      
      expect(statusTag.text()).toContain('运行中');
      expect(statusTag.classes()).toContain('el-tag--warning');
    });
    
    it('应该正确显示失败状态', () => {
      const failedRow = wrapper.find('[data-testid="history-row-task-003"]');
      const statusTag = failedRow.find('[data-testid="status-tag"]');
      
      expect(statusTag.text()).toContain('失败');
      expect(statusTag.classes()).toContain('el-tag--danger');
    });
  });
  
  describe('搜索功能', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持按策略名称搜索', async () => {
      const searchInput = wrapper.find('[data-testid="search-input"]');
      
      await searchInput.setValue('双均线');
      await nextTick();
      
      const visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(1);
      expect(visibleRows[0].text()).toContain('双均线策略');
    });
    
    it('应该支持按标的代码搜索', async () => {
      const searchInput = wrapper.find('[data-testid="search-input"]');
      
      await searchInput.setValue('000001');
      await nextTick();
      
      const visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(1);
      expect(visibleRows[0].text()).toContain('000001.SZ');
    });
    
    it('应该支持清空搜索', async () => {
      const searchInput = wrapper.find('[data-testid="search-input"]');
      
      await searchInput.setValue('双均线');
      await nextTick();
      
      let visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(1);
      
      await searchInput.setValue('');
      await nextTick();
      
      visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(mockHistoryItems.length);
    });
  });
  
  describe('状态筛选', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持按状态筛选', async () => {
      const statusFilter = wrapper.find('[data-testid="status-filter"]');
      
      await statusFilter.setValue(BacktestStatus.COMPLETED);
      await nextTick();
      
      const visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(1);
      expect(visibleRows[0].text()).toContain('已完成');
    });
    
    it('应该支持显示所有状态', async () => {
      const statusFilter = wrapper.find('[data-testid="status-filter"]');
      
      await statusFilter.setValue('');
      await nextTick();
      
      const visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(mockHistoryItems.length);
    });
  });
  
  describe('日期范围筛选', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持按日期范围筛选', async () => {
      const dateRangePicker = wrapper.find('[data-testid="date-range-picker"]');
      
      await dateRangePicker.setValue(['2023-01-01', '2023-01-01']);
      await nextTick();
      
      const visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(1);
    });
    
    it('应该支持清空日期筛选', async () => {
      const dateRangePicker = wrapper.find('[data-testid="date-range-picker"]');
      
      await dateRangePicker.setValue(['2023-01-01', '2023-01-01']);
      await nextTick();
      
      let visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(1);
      
      await dateRangePicker.setValue(null);
      await nextTick();
      
      visibleRows = wrapper.findAll('[data-testid^="history-row-"]:not(.hidden)');
      expect(visibleRows.length).toBe(mockHistoryItems.length);
    });
  });
  
  describe('排序功能', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持按创建时间排序', async () => {
      const table = wrapper.find('[data-testid="history-table"]');
      
      await table.vm.$emit('sort-change', {
        column: { property: 'created_at' },
        prop: 'created_at',
        order: 'descending'
      });
      await nextTick();
      
      const rows = wrapper.findAll('[data-testid^="history-row-"]');
      expect(rows[0].text()).toContain('task-003');
    });
    
    it('应该支持按收益率排序', async () => {
      const table = wrapper.find('[data-testid="history-table"]');
      
      await table.vm.$emit('sort-change', {
        column: { property: 'total_return' },
        prop: 'total_return',
        order: 'descending'
      });
      await nextTick();
      
      const rows = wrapper.findAll('[data-testid^="history-row-"]');
      expect(rows[0].text()).toContain('task-001');
    });
  });
  
  describe('分页功能', () => {
    beforeEach(async () => {
      // 创建更多数据以测试分页
      const moreItems = Array.from({ length: 25 }, (_, i) => ({
        ...mockHistoryItems[0],
        task_id: `task-${String(i + 4).padStart(3, '0')}`,
        strategy_name: `策略${i + 4}`
      }));
      
      store.backtestHistory = [...mockHistoryItems, ...moreItems];
      await nextTick();
    });
    
    it('应该正确显示分页信息', () => {
      const pagination = wrapper.find('[data-testid="history-pagination"]');
      expect(pagination.exists()).toBe(true);
      expect(pagination.attributes('total')).toBe('28');
    });
    
    it('应该支持切换页码', async () => {
      const pagination = wrapper.find('[data-testid="history-pagination"]');
      
      await pagination.vm.$emit('current-change', 2);
      await nextTick();
      
      expect(wrapper.vm.currentPage).toBe(2);
    });
    
    it('应该支持修改每页显示数量', async () => {
      const pagination = wrapper.find('[data-testid="history-pagination"]');
      
      await pagination.vm.$emit('size-change', 20);
      await nextTick();
      
      expect(wrapper.vm.pageSize).toBe(20);
    });
  });
  
  describe('批量操作', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持选择记录', async () => {
      const table = wrapper.find('[data-testid="history-table"]');
      
      await table.vm.$emit('selection-change', [mockHistoryItems[0], mockHistoryItems[1]]);
      await nextTick();
      
      expect(wrapper.vm.selectedItems).toHaveLength(2);
      expect(wrapper.find('[data-testid="batch-delete-button"]').attributes('disabled')).toBeUndefined();
    });
    
    it('应该支持批量删除', async () => {
      const table = wrapper.find('[data-testid="history-table"]');
      const deleteHistorySpy = vi.spyOn(store, 'deleteBacktestHistory').mockResolvedValue();
      
      await table.vm.$emit('selection-change', [mockHistoryItems[0]]);
      await nextTick();
      
      const batchDeleteButton = wrapper.find('[data-testid="batch-delete-button"]');
      await batchDeleteButton.trigger('click');
      
      expect(deleteHistorySpy).toHaveBeenCalledWith(['task-001']);
    });
    
    it('应该在没有选择时禁用批量删除按钮', () => {
      const batchDeleteButton = wrapper.find('[data-testid="batch-delete-button"]');
      expect(batchDeleteButton.attributes('disabled')).toBeDefined();
    });
  });
  
  describe('行操作', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持查看详情', async () => {
      const viewButton = wrapper.find('[data-testid="view-button-task-001"]');
      
      await viewButton.trigger('click');
      
      expect(wrapper.emitted('view-details')).toBeTruthy();
      expect(wrapper.emitted('view-details')![0][0]).toBe('task-001');
    });
    
    it('应该支持重新运行', async () => {
      const rerunButton = wrapper.find('[data-testid="rerun-button-task-001"]');
      
      await rerunButton.trigger('click');
      
      expect(wrapper.emitted('rerun')).toBeTruthy();
      expect(wrapper.emitted('rerun')![0][0]).toEqual(mockHistoryItems[0]);
    });
    
    it('应该支持删除单个记录', async () => {
      const deleteButton = wrapper.find('[data-testid="delete-button-task-001"]');
      const deleteHistorySpy = vi.spyOn(store, 'deleteBacktestHistory').mockResolvedValue();
      
      await deleteButton.trigger('click');
      
      expect(deleteHistorySpy).toHaveBeenCalledWith(['task-001']);
    });
    
    it('应该支持停止运行中的任务', async () => {
      const stopButton = wrapper.find('[data-testid="stop-button-task-002"]');
      const stopBacktestSpy = vi.spyOn(store, 'stopBacktest').mockResolvedValue();
      
      await stopButton.trigger('click');
      
      expect(stopBacktestSpy).toHaveBeenCalledWith('task-002');
    });
    
    it('应该根据状态显示不同的操作按钮', () => {
      // 完成状态：查看、重新运行、删除
      expect(wrapper.find('[data-testid="view-button-task-001"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="rerun-button-task-001"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="delete-button-task-001"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="stop-button-task-001"]').exists()).toBe(false);
      
      // 运行中状态：查看、停止
      expect(wrapper.find('[data-testid="view-button-task-002"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="stop-button-task-002"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="rerun-button-task-002"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="delete-button-task-002"]').exists()).toBe(false);
      
      // 失败状态：查看、重新运行、删除
      expect(wrapper.find('[data-testid="view-button-task-003"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="rerun-button-task-003"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="delete-button-task-003"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="stop-button-task-003"]').exists()).toBe(false);
    });
  });
  
  describe('刷新功能', () => {
    it('应该支持手动刷新', async () => {
      const getHistorySpy = vi.spyOn(store, 'getBacktestHistory').mockResolvedValue();
      const refreshButton = wrapper.find('[data-testid="refresh-button"]');
      
      await refreshButton.trigger('click');
      
      expect(getHistorySpy).toHaveBeenCalled();
    });
    
    it('应该在刷新时显示加载状态', async () => {
      store.isLoading = true;
      await nextTick();
      
      const refreshButton = wrapper.find('[data-testid="refresh-button"]');
      expect(refreshButton.attributes('loading')).toBe('true');
    });
  });
  
  describe('清空筛选', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该支持清空所有筛选条件', async () => {
      // 设置筛选条件
      const searchInput = wrapper.find('[data-testid="search-input"]');
      const statusFilter = wrapper.find('[data-testid="status-filter"]');
      const dateRangePicker = wrapper.find('[data-testid="date-range-picker"]');
      
      await searchInput.setValue('双均线');
      await statusFilter.setValue(BacktestStatus.COMPLETED);
      await dateRangePicker.setValue(['2023-01-01', '2023-01-31']);
      await nextTick();
      
      // 清空筛选
      const clearButton = wrapper.find('[data-testid="clear-filters-button"]');
      await clearButton.trigger('click');
      await nextTick();
      
      expect(wrapper.vm.searchKeyword).toBe('');
      expect(wrapper.vm.statusFilter).toBe('');
      expect(wrapper.vm.dateRange).toBeNull();
    });
  });
  
  describe('空状态', () => {
    it('应该在没有历史记录时显示空状态', async () => {
      store.backtestHistory = [];
      await nextTick();
      
      expect(wrapper.find('[data-testid="empty-state"]').exists()).toBe(true);
      expect(wrapper.text()).toContain('暂无回测历史记录');
    });
    
    it('应该在筛选后没有结果时显示空状态', async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
      
      const searchInput = wrapper.find('[data-testid="search-input"]');
      await searchInput.setValue('不存在的策略');
      await nextTick();
      
      expect(wrapper.find('[data-testid="no-results"]').exists()).toBe(true);
      expect(wrapper.text()).toContain('没有找到匹配的记录');
    });
  });
  
  describe('错误处理', () => {
    it('应该显示错误信息', async () => {
      store.error = '获取历史记录失败';
      await nextTick();
      
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true);
      expect(wrapper.text()).toContain('获取历史记录失败');
    });
    
    it('应该提供重试按钮', async () => {
      store.error = '网络错误';
      await nextTick();
      
      const retryButton = wrapper.find('[data-testid="retry-button"]');
      expect(retryButton.exists()).toBe(true);
      
      const getHistorySpy = vi.spyOn(store, 'getBacktestHistory').mockResolvedValue();
      
      await retryButton.trigger('click');
      
      expect(getHistorySpy).toHaveBeenCalled();
    });
  });
  
  describe('响应式设计', () => {
    it('应该在移动端调整表格布局', async () => {
      // 模拟移动端屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      });
      
      window.dispatchEvent(new Event('resize'));
      await nextTick();
      
      expect(wrapper.find('[data-testid="history-table"]').classes()).toContain('mobile-table');
    });
    
    it('应该在小屏幕上隐藏部分列', async () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480
      });
      
      window.dispatchEvent(new Event('resize'));
      await nextTick();
      
      expect(wrapper.find('[data-testid="sharpe-column"]').isVisible()).toBe(false);
      expect(wrapper.find('[data-testid="drawdown-column"]').isVisible()).toBe(false);
    });
  });
  
  describe('可访问性', () => {
    beforeEach(async () => {
      store.backtestHistory = mockHistoryItems;
      await nextTick();
    });
    
    it('应该有正确的ARIA标签', () => {
      const table = wrapper.find('[data-testid="history-table"]');
      expect(table.attributes('aria-label')).toBe('回测历史记录表格');
      
      const searchInput = wrapper.find('[data-testid="search-input"]');
      expect(searchInput.attributes('aria-label')).toBe('搜索回测记录');
    });
    
    it('应该支持键盘导航', async () => {
      const viewButton = wrapper.find('[data-testid="view-button-task-001"]');
      
      await viewButton.trigger('keydown', { key: 'Enter' });
      
      expect(wrapper.emitted('view-details')).toBeTruthy();
    });
    
    it('应该有正确的表格标题', () => {
      expect(wrapper.text()).toContain('策略名称');
      expect(wrapper.text()).toContain('标的代码');
      expect(wrapper.text()).toContain('状态');
      expect(wrapper.text()).toContain('创建时间');
      expect(wrapper.text()).toContain('总收益率');
      expect(wrapper.text()).toContain('夏普比率');
      expect(wrapper.text()).toContain('最大回撤');
      expect(wrapper.text()).toContain('操作');
    });
  });
});