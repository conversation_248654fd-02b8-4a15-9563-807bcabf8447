# Umpire System V2 Implementation Log

## 1. Overview

This log documents the implementation of the Umpire System (UmpBu) V2. The primary goal was to refactor the existing umpire logic to align with the new technical specification, which mandates a strict separation between the model **Training** flow and the prediction **Execution** flow.

This refactoring enhances modularity, simplifies the backtesting execution call, and establishes a clearer, more robust process for managing umpire models.

## 2. Phase 1: Training Flow Implementation (Train API)

The first phase focused on creating a dedicated API for training and saving umpire models.

### 2.1. Umpire Training API Endpoint

- **File Created**: `backend/app/api/endpoints/umpire.py`
- **Endpoint**: `POST /api/umpires/train`
- **Functionality**:
  1.  Receives backtesting parameters required for generating training data.
  2.  Executes a preliminary backtest **without** any umpires to produce a high-quality `orders_pd` DataFrame.
  3.  Utilizes `AbuUmpMainBase.ump_main_clf_dump` and `AbuUmpEdgeBase.ump_edge_clf_dump` to train the main and edge umpire models, respectively, using the generated `orders_pd`.
  4.  Saves the trained models to disk for later use in the execution flow.

### 2.2. API Routing

- **File Modified**: `backend/app/api/router.py`
- **Change**: Imported the new `umpire` router and included it in the main `api_router`. This makes the `/api/umpires/train` endpoint accessible.

## 3. Phase 2: Prediction Flow Refactoring (Execute API)

The second phase involved refactoring the strategy execution logic to use the pre-trained umpire models.

### 3.1. Schema Update

- **File Modified**: `backend/app/schemas/strategy.py`
- **Change**: Added the `umpire_market_name: Optional[str]` field to the `StrategyCreate` and `StrategyUpdate` Pydantic models. This allows users to specify which trained umpire model set to use for a backtest.

### 3.2. Umpire Adapter for Prediction

- **File Modified**: `backend/app/abupy_adapter/execution/umpire_adapter.py`
- **Functionality**:
  - Implemented `setup_umpire_for_prediction(market_name, rules)`: This function activates the global umpire switch, loads the specified pre-trained models from disk, and registers them with the `ABuUmpManager`.
  - Implemented `teardown_umpire()`: This function deactivates the global umpire switch and clears the registered umpires from the manager, ensuring a clean state after each backtest.

### 3.3. Integration into Strategy Executor

- **File Modified**: `backend/app/abupy_adapter/execution/executor_facade.py`
- **Refactoring Details**:
  1.  Replaced the import of the old `create_umpire_managers` with the new `setup_umpire_for_prediction` and `teardown_umpire`.
  2.  In the `execute_strategy` method, a `try...finally` block was introduced:
      - The `try` block now begins with a call to `setup_umpire_for_prediction` if umpire parameters are provided.
      - The `finally` block ensures `teardown_umpire` is always called, guaranteeing resource cleanup even if the backtest fails.
  3.  The old `_configure_umpire_system` method was completely removed.
  4.  The call to `call_abupy_backtest` was simplified, removing the `umpire_instances` parameter.

### 3.4. AbuPy Caller Refactoring

- **File Modified**: `backend/app/abupy_adapter/execution/abupy_caller.py`
- **Change**: Removed the `umpire_instances` parameter from the `call_abupy_backtest` function signature and all associated logic for manually configuring the `ABuUmpManager`. The function is now solely responsible for invoking the core backtest engine.

## 4. Conclusion

The Umpire System V2 implementation is complete. The logic for training and execution is now cleanly separated, adhering to the new design. The prediction flow is more robust, using a `try...finally` structure to manage the global state of the umpire system, and the core calling mechanism has been simplified. This concludes the refactoring task.