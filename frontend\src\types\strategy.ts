/**
 * 策略相关的类型定义
 * 提供完整的类型安全支持
 */

import type { Strategy } from '@/api/types'

// 策略编辑状态
export interface StrategyEditState {
  editingStrategy: Strategy | null
  originalStrategy: Strategy | null
  isDirty: boolean
  isSaving: boolean
  errors: ValidationError[]
}

// 验证错误
export interface ValidationError {
  field: string
  message: string
  code?: string
}

// 策略表单数据
export interface StrategyFormData {
  name: string
  description: string
  is_public: boolean
}

// 因子类型
export type FactorType = 'buy' | 'sell'

// 因子参数
export interface FactorParameter {
  name: string
  type: 'string' | 'number' | 'boolean'
  label?: string
  description?: string
  default?: any
  required?: boolean
  min?: number
  max?: number
  options?: Array<{ label: string; value: any }>
}

// 因子配置
export interface FactorConfig {
  class_name: string
  parameters: Record<string, any>
  description?: string
  factor_type?: FactorType
}

// 策略操作类型
export type StrategyAction = 
  | 'create'
  | 'update'
  | 'delete'
  | 'duplicate'
  | 'export'
  | 'import'

// 策略状态
export type StrategyStatus = 
  | 'draft'      // 草稿
  | 'active'     // 活跃
  | 'archived'   // 已归档
  | 'error'      // 错误状态

// 策略元数据
export interface StrategyMetadata {
  created_at: string
  updated_at: string
  version: string
  tags: string[]
  performance?: {
    total_return: number
    sharpe_ratio: number
    max_drawdown: number
    win_rate: number
  }
}

// 扩展的策略类型
export interface ExtendedStrategy extends Strategy {
  status?: StrategyStatus
  metadata?: StrategyMetadata
  permissions?: {
    can_edit: boolean
    can_delete: boolean
    can_share: boolean
  }
}

// 策略列表过滤器
export interface StrategyFilter {
  status?: StrategyStatus[]
  author?: string[]
  tags?: string[]
  is_public?: boolean
  created_after?: string
  created_before?: string
  search?: string
}

// 策略排序选项
export interface StrategySortOption {
  field: 'name' | 'created_at' | 'updated_at' | 'performance.total_return'
  order: 'asc' | 'desc'
}

// 策略列表查询参数
export interface StrategyListQuery {
  page?: number
  limit?: number
  filter?: StrategyFilter
  sort?: StrategySortOption
}

// 策略操作结果
export interface StrategyOperationResult {
  success: boolean
  data?: Strategy
  error?: string
  warnings?: string[]
}

// 策略验证结果
export interface StrategyValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
}

// 策略导入/导出格式
export interface StrategyExportData {
  strategy: Strategy
  version: string
  exported_at: string
  metadata?: Record<string, any>
}

// 策略比较结果
export interface StrategyComparison {
  differences: Array<{
    field: string
    oldValue: any
    newValue: any
    type: 'added' | 'removed' | 'modified'
  }>
  summary: {
    total_changes: number
    critical_changes: number
  }
}

// 策略模板
export interface StrategyTemplate {
  id: string
  name: string
  description: string
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  template_data: Partial<Strategy>
  usage_count: number
  rating: number
}

// 策略工作区配置
export interface StrategyWorkspaceConfig {
  auto_save: boolean
  auto_save_interval: number // 秒
  show_advanced_settings: boolean
  default_factor_type: FactorType
  grid_layout: boolean
  theme: 'light' | 'dark'
}

// 策略编辑器事件
export interface StrategyEditorEvents {
  'strategy-created': (strategy: Strategy) => void
  'strategy-updated': (strategy: Strategy) => void
  'strategy-deleted': (strategyId: string) => void
  'factor-added': (factor: FactorConfig, type: FactorType) => void
  'factor-updated': (factor: FactorConfig, type: FactorType, index: number) => void
  'factor-deleted': (type: FactorType, index: number) => void
  'validation-error': (errors: ValidationError[]) => void
  'save-state-changed': (isDirty: boolean) => void
}

// 工具函数类型定义
export type StrategyValidator = (strategy: Strategy) => StrategyValidationResult
export type FactorParameterNormalizer = (parameters: any) => FactorParameter[]
export type StrategyTransformer = (strategy: Strategy) => Strategy

// 常用的类型守卫函数
export function isValidStrategy(obj: any): obj is Strategy {
  return obj && 
         typeof obj.name === 'string' && 
         Array.isArray(obj.buy_factors) &&
         Array.isArray(obj.sell_factors)
}

export function isNewStrategy(strategy: Strategy | null | undefined): boolean {
  return !strategy?.id || 
         strategy.id === '' ||
         strategy.id === 'new' ||
         String(strategy.id).startsWith('temp-') ||
         String(strategy.id).startsWith('draft-')
}

export function isValidFactorConfig(obj: any): obj is FactorConfig {
  return obj && 
         typeof obj.class_name === 'string' &&
         typeof obj.parameters === 'object'
}

// 策略状态工具函数
export function getStrategyDisplayStatus(strategy: Strategy): string {
  if (isNewStrategy(strategy)) {
    return '草稿'
  }
  
  if (strategy.is_public) {
    return '公开'
  }
  
  return '私有'
}

export function canEditStrategy(strategy: Strategy, currentUserId?: string): boolean {
  if (isNewStrategy(strategy)) {
    return true
  }
  
  if (currentUserId && strategy.author === currentUserId) {
    return true
  }
  
  // 这里可以添加更多权限检查逻辑
  return false
}