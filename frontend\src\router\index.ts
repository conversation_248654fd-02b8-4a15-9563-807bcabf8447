import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import DefaultLayout from '../layouts/DefaultLayout.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
  },
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
      },
      {
        path: 'workshop',
        name: 'StrategyWorkshop',
        component: () => import('../views/StrategyWorkshop.vue'),
      },
      {
        path: 'market',
        name: 'MarketCenter',
        component: () => import('../views/MarketCenter.vue'),
      },
      {
        path: 'screener',
        name: 'StockScreener',
        component: () => import('../views/StockScreener.vue'),
      },
      {
        path: 'cockpit',
        name: 'TradingCockpit',
        component: () => import('../views/TradingCockpit.vue'),
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('../views/Settings.vue'),
      },
      {
        path: 'backtest/:reportId',
        name: 'BacktestReport',
        component: () => import('../views/BacktestReport.vue'),
        props: true,
      },

    ],
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;