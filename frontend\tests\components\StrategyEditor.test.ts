import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StrategyEditor from '../../src/components/StrategyWorkshop/StrategyEditor.vue'
import type { Strategy } from '../../src/api/types/strategy'

// Mock stores
const mockUseStrategyStore = {
  currentStrategy: {
    id: '1',
    name: 'Test Strategy',
    description: 'Test Description',
    factors: [],
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  },
  isLoading: false,
  error: '',
  updateExistingStrategy: vi.fn(),
  createNewStrategy: vi.fn(),
  setCurrentStrategy: vi.fn()
}

const mockUseFactorsStore = {
  factors: [],
  isLoading: false,
  fetchFactors: vi.fn()
}

const mockUseBacktestStore = {
  backtestConfig: {
    initialCapital: 100000,
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    benchmark: 'hs300'
  },
  setBacktestConfig: vi.fn(),
  resetBacktestState: vi.fn()
}

vi.mock('../../src/stores', () => ({
  useStrategyStore: () => mockUseStrategyStore,
  useFactorsStore: () => mockUseFactorsStore,
  useBacktestStore: () => mockUseBacktestStore
}))

// Mock Element Plus components
vi.mock('element-plus', () => ({
  ElTabs: { name: 'ElTabs', template: '<div><slot /></div>' },
  ElTabPane: { name: 'ElTabPane', template: '<div><slot /></div>' },
  ElForm: { name: 'ElForm', template: '<div><slot /></div>' },
  ElFormItem: { name: 'ElFormItem', template: '<div><slot /></div>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElCard: { name: 'ElCard', template: '<div><slot /></div>' },
  ElSkeleton: { name: 'ElSkeleton', template: '<div>Loading...</div>' },
  ElSelect: { name: 'ElSelect', template: '<select><slot /></select>' },
  ElOption: { name: 'ElOption', template: '<option><slot /></option>' },
  ElSwitch: { name: 'ElSwitch', template: '<input type="checkbox" />' },
  ElInputNumber: { name: 'ElInputNumber', template: '<input type="number" />' },
  ElDatePicker: { name: 'ElDatePicker', template: '<input type="date" />' },
  ElRow: { name: 'ElRow', template: '<div class="el-row"><slot /></div>' },
  ElCol: { name: 'ElCol', template: '<div class="el-col"><slot /></div>' },
  ElCheckbox: { name: 'ElCheckbox', template: '<input type="checkbox" />' },
  ElRadio: { name: 'ElRadio', template: '<input type="radio" />' },
  ElRadioGroup: { name: 'ElRadioGroup', template: '<div><slot /></div>' },
  ElSlider: { name: 'ElSlider', template: '<input type="range" />' },
  ElTextarea: { name: 'ElTextarea', template: '<textarea></textarea>' },
  ElTable: { name: 'ElTable', template: '<table><slot /></table>' },
  ElTableColumn: { name: 'ElTableColumn', template: '<td><slot /></td>' },
  ElDivider: { name: 'ElDivider', template: '<hr />' },
  ElTooltip: { name: 'ElTooltip', template: '<div><slot /></div>' },
  ElPopover: { name: 'ElPopover', template: '<div><slot /></div>' },
  ElDialog: { name: 'ElDialog', template: '<div><slot /></div>' },
  ElDrawer: { name: 'ElDrawer', template: '<div><slot /></div>' },
  ElTag: { name: 'ElTag', template: '<span><slot /></span>' },
  ElBadge: { name: 'ElBadge', template: '<div><slot /></div>' },
  ElAlert: { name: 'ElAlert', template: '<div><slot /></div>' },
  ElProgress: { name: 'ElProgress', template: '<div></div>' },
  ElLoading: { name: 'ElLoading', template: '<div>Loading...</div>' },
  ElEmpty: { name: 'ElEmpty', template: '<div>No data</div>' },
  ElSpin: { name: 'ElSpin', template: '<div><slot /></div>' },
  ElResult: { name: 'ElResult', template: '<div><slot /></div>' },
  ElIcon: { name: 'ElIcon', template: '<i><slot /></i>' },
  ElImage: { name: 'ElImage', template: '<img />' },
  ElAvatar: { name: 'ElAvatar', template: '<div><slot /></div>' },
  ElLoadingDirective: {},
  ElInfiniteScroll: {},
  ElPopconfirm: { name: 'ElPopconfirm', template: '<div><slot /></div>' },
  ElDropdown: { name: 'ElDropdown', template: '<div><slot /></div>' },
  ElDropdownMenu: { name: 'ElDropdownMenu', template: '<div><slot /></div>' },
  ElDropdownItem: { name: 'ElDropdownItem', template: '<div><slot /></div>' }
}))

describe('StrategyEditor', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    mockUseStrategyStore.currentStrategy = null
    mockUseStrategyStore.isLoading = false
    mockUseStrategyStore.error = ''
  })

  const createWrapper = (props = {}) => {
    return mount(StrategyEditor, {
      props: {
        ...props
      },
      global: {
        stubs: {
          ElTabs: true,
          ElTabPane: true,
          ElForm: true,
          ElFormItem: true,
          ElInput: true,
          ElButton: true,
          ElCard: true,
          ElSkeleton: true,
          StrategyHeader: true,
          BasicInfoForm: true,
          FactorManager: true
        }
      }
    })
  }

  describe('渲染', () => {
    it('应该在加载状态下显示骨架屏', () => {
      mockUseStrategyStore.isLoading = true
      const wrapper = createWrapper()

      expect(wrapper.vm).toBeDefined()
      expect(mockUseStrategyStore.isLoading).toBe(true)
    })

    it('应该在非加载状态下显示编辑器内容', () => {
      mockUseStrategyStore.isLoading = false
      const wrapper = createWrapper()

      expect(wrapper.vm).toBeDefined()
      expect(wrapper.findComponent({ name: 'ElTabs' }).exists()).toBe(true)
    })

    it('应该显示策略头部信息', () => {
      mockUseStrategyStore.currentStrategy = {
        id: 'test-strategy',
        name: '测试策略',
        description: '这是一个测试策略',
        factors: [],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
      
      const wrapper = createWrapper()
      
      expect(wrapper.findComponent({ name: 'StrategyHeader' }).exists()).toBe(true)
      expect(wrapper.vm).toBeDefined()
    })
  })

  describe('策略信息编辑', () => {
    it('应该能够编辑策略名称', async () => {
      mockUseStrategyStore.currentStrategy = {
        id: 'test-strategy',
        name: '测试策略',
        description: '描述',
        factors: [],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
      
      const wrapper = createWrapper()
      
      const nameInput = wrapper.find('[data-testid="strategy-name-input"]')
      if (nameInput.exists()) {
        await nameInput.setValue('新策略名称')
        expect((nameInput.element as HTMLInputElement).value).toBe('新策略名称')
      }
    })

    it('应该能够编辑策略描述', async () => {
      mockUseStrategyStore.currentStrategy = {
        id: 'test-strategy',
        name: '测试策略',
        description: '原始描述',
        factors: [],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
      
      const wrapper = createWrapper()
      
      const descInput = wrapper.find('[data-testid="strategy-description-input"]')
      if (descInput.exists()) {
        await descInput.setValue('新的策略描述')
        expect((descInput.element as HTMLInputElement).value).toBe('新的策略描述')
      }
    })
  })

  describe('因子管理', () => {
    it('应该显示因子管理选项卡', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.vm).toBeDefined()
      expect(wrapper.exists()).toBe(true)
    })

    it('应该能够添加因子', async () => {
      const wrapper = createWrapper()
      
      const addFactorBtn = wrapper.find('[data-testid="add-factor-btn"]')
      if (addFactorBtn.exists()) {
        await addFactorBtn.trigger('click')
        
        // 验证是否触发了添加因子的逻辑
        expect(wrapper.emitted('factor-added')).toBeTruthy()
      }
    })

    it('应该能够移除因子', async () => {
      mockUseStrategyStore.currentStrategy = {
        id: 'test-strategy',
        name: '测试策略',
        description: '描述',
        factors: [
          { id: 'factor1', name: '因子1', type: 'technical' },
          { id: 'factor2', name: '因子2', type: 'fundamental' }
        ],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
      
      const wrapper = createWrapper()
      
      const removeFactorBtn = wrapper.find('[data-testid="remove-factor-btn"]')
      if (removeFactorBtn.exists()) {
        await removeFactorBtn.trigger('click')
        
        expect(wrapper.emitted('factor-removed')).toBeTruthy()
      }
    })
  })

  describe('保存功能', () => {
    it('应该能够保存新策略', async () => {
      mockUseStrategyStore.currentStrategy = null
      
      const wrapper = createWrapper()
      
      const saveBtn = wrapper.find('[data-testid="save-strategy-btn"]')
      if (saveBtn.exists()) {
        await saveBtn.trigger('click')
        
        expect(mockUseStrategyStore.createNewStrategy).toHaveBeenCalled()
      }
    })

    it('应该能够更新现有策略', async () => {
      mockUseStrategyStore.currentStrategy = {
        id: 'existing-strategy',
        name: '现有策略',
        description: '描述',
        factors: [],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }
      
      const wrapper = createWrapper()
      
      const saveBtn = wrapper.find('[data-testid="save-strategy-btn"]')
      if (saveBtn.exists()) {
        await saveBtn.trigger('click')
        
        expect(mockUseStrategyStore.updateExistingStrategy).toHaveBeenCalledWith(
          'existing-strategy',
          expect.any(Object)
        )
      }
    })

    it('应该在保存成功后发出saved事件', async () => {
      mockUseStrategyStore.createNewStrategy.mockResolvedValue({
        id: 'new-strategy',
        name: '新策略'
      })
      
      const wrapper = createWrapper()
      
      const saveBtn = wrapper.find('[data-testid="save-strategy-btn"]')
      if (saveBtn.exists()) {
        await saveBtn.trigger('click')
        
        // 等待异步操作完成
        await wrapper.vm.$nextTick()
        
        expect(wrapper.emitted('saved')).toBeTruthy()
      }
    })
  })

  describe('表单验证', () => {
    it('应该验证策略名称不能为空', async () => {
      const wrapper = createWrapper()
      
      const nameInput = wrapper.find('[data-testid="strategy-name-input"]')
      if (nameInput.exists()) {
        await nameInput.setValue('')
        
        const saveBtn = wrapper.find('[data-testid="save-strategy-btn"]')
        if (saveBtn.exists()) {
          await saveBtn.trigger('click')
          
          // 验证是否显示了验证错误
          const errorMsg = wrapper.find('.error-message, .el-form-item__error')
          if (errorMsg.exists()) {
            expect(errorMsg.text()).toContain('策略名称不能为空')
          }
        }
      }
    })

    it('应该验证策略名称长度', async () => {
      const wrapper = createWrapper()
      
      const nameInput = wrapper.find('[data-testid="strategy-name-input"]')
      if (nameInput.exists()) {
        // 输入过长的名称
        const longName = 'a'.repeat(101)
        await nameInput.setValue(longName)
        
        const saveBtn = wrapper.find('[data-testid="save-strategy-btn"]')
        if (saveBtn.exists()) {
          await saveBtn.trigger('click')
          
          const errorMsg = wrapper.find('.error-message, .el-form-item__error')
          if (errorMsg.exists()) {
            expect(errorMsg.text()).toContain('策略名称长度不能超过100个字符')
          }
        }
      }
    })
  })

  describe('错误处理', () => {
    it('应该显示错误信息', async () => {
      mockUseStrategyStore.error = '保存策略失败'
      
      const wrapper = createWrapper()
      
      const errorElement = wrapper.find('.error-message, .alert-error, [data-testid="error-message"]')
      if (errorElement.exists()) {
        expect(errorElement.text()).toContain('保存策略失败')
      }
    })

    it('应该在保存失败时显示错误提示', async () => {
      mockUseStrategyStore.createNewStrategy.mockRejectedValue(new Error('网络错误'))
      
      const wrapper = createWrapper()
      
      const saveBtn = wrapper.find('[data-testid="save-strategy-btn"]')
      if (saveBtn.exists()) {
        await saveBtn.trigger('click')
        
        // 等待异步操作完成
        await wrapper.vm.$nextTick()
        
        expect(wrapper.emitted('error')).toBeTruthy()
      }
    })
  })

  describe('选项卡切换', () => {
    it('应该能够在不同选项卡之间切换', async () => {
      const wrapper = createWrapper()
      
      const factorTab = wrapper.find('[data-testid="factor-tab"]')
      const settingsTab = wrapper.find('[data-testid="settings-tab"]')
      
      if (factorTab.exists()) {
        await factorTab.trigger('click')
        expect(wrapper.emitted('tab-change')).toBeTruthy()
      }
      
      if (settingsTab.exists()) {
        await settingsTab.trigger('click')
        expect(wrapper.emitted('tab-change')).toBeTruthy()
      }
    })
  })

  describe('响应式行为', () => {
    it('应该响应策略数据的变化', async () => {
      const wrapper = createWrapper()
      
      // 模拟策略数据变化
      mockUseStrategyStore.currentStrategy = {
        id: 'updated-strategy',
        name: '更新后的策略',
        description: '更新后的描述',
        factors: [],
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z'
      }
      
      await wrapper.vm.$nextTick()
      
      // 由于组件被 stub，我们检查组件是否仍然存在
      expect(wrapper.vm).toBeDefined()
      expect(wrapper.findComponent({ name: 'StrategyHeader' }).exists()).toBe(true)
    })
  })
})