# backend/app/services/umpire_service.py

import logging
from typing import Dict, Any

from fastapi import Depends
from sqlmodel import Session

from backend.app.core.database import get_session
from backend.app.core.exceptions import AdapterError, ParameterError
from backend.app.abupy_adapter.execution.umpire_adapter import train_umpire_models

# 修正部分：开始
# 导入正确的类名 AbuDataCache，并使用 'as' 关键字创建别名 AbuDataCacheAdapter
# 这样就不需要修改文件内其他地方的代码了。
from backend.app.abupy_adapter.data_cache_adapter import AbuDataCache as AbuDataCacheAdapter
# 修正部分：结束


class UmpireService:
    """裁判系统服务，负责处理与裁判相关的业务逻辑，特别是模型训练。"""

    def __init__(self, session: Session):
        self.session = session

    def train_models(self, train_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据给定的市场数据和裁判规则，执行裁判模型的训练流程。

        Args:
            train_data: 包含 'market_info' 和 'umpire_rules' 的字典。

        Returns:
            一个表示训练成功或失败的字典。
        """
        try:
            market_info = train_data.get('market_info')
            umpire_rules = train_data.get('umpire_rules')

            market_name = train_data.get('market_name')

            if not market_info or not umpire_rules or not market_name:
                raise ParameterError(message="'market_info', 'umpire_rules', and 'market_name' are required.")

            # 1. 准备K线数据
            # 我们需要一个kl_pd_manager实例，它由AbuDataCacheAdapter提供
            # 这里我们假设 market_info 包含了所有需要的参数
            # 由于我们使用了别名，这里的 AbuDataCacheAdapter.get_kl_pd_manager_for_training 调用无需修改
            kl_pd_manager = AbuDataCacheAdapter.get_kl_pd_manager_for_training(**market_info)

            # 2. 调用适配器进行训练
            train_umpire_models(rules=umpire_rules, kl_pd_manager=kl_pd_manager, market_name=market_name)

            return {"status": "success", "message": "Umpire models trained successfully."}

        except (ParameterError, AdapterError) as e:
            logging.error(f"裁判模型训练期间的业务逻辑错误: {e}", exc_info=True)
            raise e
        except Exception as e:
            logging.error(f"训练裁判模型时发生未预期的错误: {e}", exc_info=True)
            raise AdapterError(message=f"An unexpected error occurred during umpire model training: {str(e)}",
                               error_code="UNEXPECTED_TRAINING_ERROR")


def get_umpire_service(session: Session = Depends(get_session)) -> UmpireService:
    """FastAPI 依赖注入函数，用于获取 UmpireService 实例"""
    return UmpireService(session=session)