战略备忘录 (Strategic Memorandum) - 军师AI
备忘录ID： SM-20250804-006
主题： 前端开发战略转折：从“水平分层”到“垂直突破”
创建日期： 2025-08-04
AI角色： 军师AI / 战略顾问AI
主要指令来源/人类决策者： ccxx
关联项目阶段/里程碑： 在前端TDD开发实践初期，对开发策略进行重大调整，以应对“过度工程化”风险，确保项目聚焦于快速交付用户价值。
1. 背景：理想的理论框架与严峻的实践困境
在启动前端核心功能开发之前，我们采纳了一个理论上非常先进和完美的**“TDD三阶段进化策略”**。该策略旨在通过构建.tdd, .extended, .advanced三个层级的测试金字塔，来确保代码质量的极致。
然而，在初步实践中，决策者ccxx敏锐地察觉到了一个严峻的实践困境：
进度缓慢： 大量时间被投入到为所有模块规划和编写多层次的测试脚本中。
功能停滞： 迟迟无法产出任何一个用户可感知的、端到端的功能闭环。
过度工程化风险： 项目面临着为了追求“完美的测试体系”而牺牲“交付可用功能”这一根本目标的巨大风险。我们陷入了“水平分层”构建的陷阱，试图在盖第一层楼之前，就把所有楼层的消防通道图纸都画好。
2. 战略决策：从“完美主义”到“敏捷务实”的根本性转变
在意识到“水平分层”策略的不可持续性后，经决策者ccxx提议并与军师AI深入研讨，我们在此做出以下根本性的、将指导未来所有前端开发的战略转折决策：
我们正式废弃“水平分层”的开发模式，全面转向“垂直突破 (Vertical Slice)”的敏捷开发模式。
3. “垂直突破”新战略的核心原则
“垂直突破”战略的核心，是聚焦于交付端到端的用户价值。我们将不再横向地、一次性地完成所有模块的某一“层”（如API层或Store层），而是纵向地、一次只选择一个核心的用户故事，并集中所有火力，完成实现这个故事所需的所有层次的工作。
新战略下的工作流：
选择一个垂直切片：
我们选择一个对用户有独立价值的、最小的功能闭环。例如：“完成回测系统的完整前后端交互与UI展示”。
聚焦于此切片，纵向开发：
只为这个切片涉及的模块编写测试和代码。
API层： 只做backtest.ts。
Store层： 只做useBacktestStore.ts。
UI组件层： 只做回测相关的组件（如BacktestRunner.vue, BacktestReport.vue）。
其他所有模块（如strategy, market等）的深入开发将暂时搁置。
简化测试策略，加速迭代：
在“垂直突破”的初次冲刺中，我们暂时搁置“.extended”和“.advanced”测试的编写。
我们只编写核心的**“.tdd”单元测试**，以最快的速度完成“红-绿-重构”循环，确保核心功能的正确性。
这是一种**“先求生存，再求发展”**的务实策略。
完成后再加固与扩展：
当我们成功地完成了第一个“垂直切片”（例如，用户可以在UI上看到回测报告了），我们获得了一个可工作的、有价值的产品增量。
然后，我们再回头，为这个已经稳定下来的模块，去补充更复杂的.extended和.advanced测试，进行“加固”。
同时，我们再选择下一个“垂直切片”（例如，“完整的策略CRUD功能”）开始新的突破。
4. 结论与意义
这次战略转折，是我们项目管理思想的一次重大成熟。
它让我们摆脱了“完美主义”的陷阱，回归到了敏捷开发“快速交付价值”的核心。
它将为项目团队带来正向反馈，用“看得见的成果”取代“看不见的底层建设”，极大地提升开发士气。
它降低了项目风险，通过在最小的功能集上验证我们整个端到端的技术栈和协作流程。
这份备忘录将作为我们前端开发的“新灯塔”，指引我们以一种更敏捷、更务实、更聚焦于价值的方式前进。我们不再追求一次性构建一座“完美的宫殿”，而是选择先快速地搭建起一座“坚固、可用的小屋”，然后再逐步将其扩建成我们梦想中的宫殿。