import { describe, it, expect, beforeEach } from 'vitest';
import {
  ApiSchemas,
  ContractValidator,
  type StrategyExecuteRequest,
  type StrategyExecuteResponse
} from './api-schemas';
import { BacktestDataFactory } from '../factories/BacktestDataFactory';
import { BacktestTestUtils } from '../utils/BacktestTestUtils';
import {
  setupTestScenario,
  resetMockData,
  getContractValidationReport,
  clearContractValidationRecords
} from '../mocks/backtestHandlers';

describe('API契约验证', () => {
  beforeEach(() => {
    resetMockData();
    clearContractValidationRecords();
    ContractValidator.clearValidationErrors();
  });

  describe('策略执行请求契约验证', () => {
    it('应该验证有效的策略执行请求', () => {
      const validRequest = BacktestDataFactory.createStrategyExecuteRequest('normal');
      
      const validation = BacktestTestUtils.validateStrategyExecuteRequest(validRequest);
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('应该拒绝无效的策略执行请求', () => {
      const invalidRequest = {
        choice_symbols: [], // 空数组应该被拒绝
        start_date: 'invalid-date', // 无效日期格式
        end_date: '20231231',
        capital: -1000, // 负数资金
        benchmark_symbol: '',
        data_source: 'invalid_source',
        n_folds: 0
      };
      
      const validation = BacktestTestUtils.validateApiRequest(
        invalidRequest,
        'StrategyExecuteRequest'
      );
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    it('应该验证边界情况的策略执行请求', () => {
      const edgeCaseRequest = BacktestDataFactory.createStrategyExecuteRequest('edge_case');
      
      const validation = BacktestTestUtils.validateStrategyExecuteRequest(edgeCaseRequest);
      
      expect(validation.isValid).toBe(true);
      expect(validation.warnings.length).toBeGreaterThan(0); // 应该有警告
    });

    it('应该验证多股票策略执行请求', () => {
      const multiSymbolRequest = BacktestDataFactory.createStrategyExecuteRequest('multi_symbol');
      
      const validation = BacktestTestUtils.validateStrategyExecuteRequest(multiSymbolRequest);
      
      expect(validation.isValid).toBe(true);
      expect(multiSymbolRequest.choice_symbols.length).toBeGreaterThan(1);
    });
  });

  describe('策略执行响应契约验证', () => {
    it('应该验证有效的策略执行响应', () => {
      const request = BacktestDataFactory.createStrategyExecuteRequest('normal');
      const validResponse = BacktestDataFactory.createStrategyExecuteResponse('profitable', request);
      
      const validation = BacktestTestUtils.validateStrategyExecuteResponse(validResponse);
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validResponse.success).toBe(true);
    });

    it('应该验证不同场景的策略执行响应', () => {
      const scenarios: Array<'profitable' | 'loss' | 'mixed' | 'no_trades'> = [
        'profitable', 'loss', 'mixed', 'no_trades'
      ];
      
      scenarios.forEach(scenario => {
        const request = BacktestDataFactory.createStrategyExecuteRequest('normal');
        const response = BacktestDataFactory.createStrategyExecuteResponse(scenario, request);
        
        const validation = BacktestTestUtils.validateStrategyExecuteResponse(response);
        
        expect(validation.isValid).toBe(true);
        expect(response.data.execution_summary).toBeDefined();
      });
    });

    it('应该拒绝无效的策略执行响应', () => {
      const invalidResponse = {
        success: true,
        message: '策略执行成功',
        data: {
          status: 'invalid_status', // 无效状态
          message: '策略执行完成',
          results: [
            {
              symbol: '', // 空股票代码
              orders_count: -1, // 负数订单数
              message: '交易完成',
              orders: []
            }
          ],
          execution_summary: {
            total_symbols: -1, // 负数
            symbols_with_trades: 2,
            total_trades: 1,
            initial_capital: 1000000,
            final_capital: 1100000,
            cumulative_return: 0.1,
            annualized_return: 0.12,
            max_drawdown: 0.05,
            sharpe_ratio: 1.5,
            win_rate: 1.5, // 超过1的胜率
            profit_loss_ratio: 2.0
          },
          parameters_used: {
            initial_capital: 1000000,
            symbols: ['000001.SZ'],
            start_date: '2023-01-01',
            end_date: '2023-12-31'
          }
        }
      };
      
      const validation = BacktestTestUtils.validateApiResponse(
        invalidResponse,
        'StrategyExecuteResponse'
      );
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('错误响应契约验证', () => {
    it('应该验证有效的API错误响应', () => {
      const errorResponse = {
        success: false,
        message: '请求参数验证失败',
        error_code: 'VALIDATION_ERROR',
        details: '股票代码不能为空'
      };
      
      const validation = BacktestTestUtils.validateApiResponse(
        errorResponse,
        'ErrorResponse'
      );
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('应该拒绝无效的API错误响应', () => {
      const invalidErrorResponse = {
        success: false,
        message: '', // 空错误消息
        error_code: '', // 空错误代码
        details: null // null详情
      };
      
      const validation = BacktestTestUtils.validateApiResponse(
        invalidErrorResponse,
        'ErrorResponse'
      );
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('契约验证工具测试', () => {
    it('应该正确记录契约不匹配', () => {
      const invalidData = { invalid: 'data' };
      
      ContractValidator.validateRequest(
        invalidData,
        ApiSchemas.StrategyExecuteRequest,
        'TestAPI'
      );
      
      const mismatches = ContractValidator.getContractMismatches();
      expect(mismatches.length).toBeGreaterThan(0);
      
      const errors = ContractValidator.getValidationErrors();
      expect(errors.length).toBeGreaterThan(0);
    });

    it('应该生成详细的验证报告', () => {
      // 创建一些验证错误
      ContractValidator.validateRequest(
        { invalid: 'data1' },
        ApiSchemas.StrategyExecuteRequest,
        'TestAPI1'
      );
      
      ContractValidator.validateResponse(
        { invalid: 'data2' },
        ApiSchemas.StrategyExecuteResponse,
        'TestAPI2'
      );
      
      const report = ContractValidator.generateValidationReport();
      
      expect(report.totalErrors).toBeGreaterThan(0);
      expect(report.totalMismatches).toBeGreaterThan(0);
      expect(report.errors.length).toBeGreaterThan(0);
      expect(report.mismatches.length).toBeGreaterThan(0);
    });

    it('应该支持批量验证', () => {
      const testItems = [
        {
          data: BacktestDataFactory.createStrategyExecuteRequest('normal'),
          schema: ApiSchemas.StrategyExecuteRequest,
          name: 'valid_request'
        },
        {
          data: { invalid: 'data' },
          schema: ApiSchemas.StrategyExecuteRequest,
          name: 'invalid_request'
        }
      ];
      
      const batchResult = ContractValidator.batchValidate(testItems);
      
      expect(batchResult.totalItems).toBe(2);
      expect(batchResult.validItems).toBe(1);
      expect(batchResult.invalidItems).toBe(1);
      expect(batchResult.results).toHaveLength(2);
    });

    it('应该支持类型安全验证', () => {
      const validRequest = BacktestDataFactory.createStrategyExecuteRequest('normal');
      const invalidRequest = { invalid: 'data' };
      
      expect(
        ContractValidator.validateTypeSafety(validRequest, ApiSchemas.StrategyExecuteRequest)
      ).toBe(true);
      
      expect(
        ContractValidator.validateTypeSafety(invalidRequest, ApiSchemas.StrategyExecuteRequest)
      ).toBe(false);
    });
  });

  describe('契约验证测试套件', () => {
    it('应该创建并执行完整的契约验证测试套件', () => {
      const validRequests = [
        BacktestDataFactory.createStrategyExecuteRequest('normal'),
        BacktestDataFactory.createStrategyExecuteRequest('edge_case'),
        BacktestDataFactory.createStrategyExecuteRequest('multi_symbol')
      ];
      
      const invalidRequests = [
        { choice_symbols: [] },
        { start_date: 'invalid' },
        { capital: -1000 }
      ];
      
      const validResponses = [
        BacktestDataFactory.createStrategyExecuteResponse('profitable'),
        BacktestDataFactory.createStrategyExecuteResponse('loss'),
        BacktestDataFactory.createStrategyExecuteResponse('mixed')
      ];
      
      const invalidResponses = [
        { success: 'not_boolean' },
        { data: null },
        { invalid: 'response' }
      ];
      
      const testSuite = BacktestTestUtils.createContractValidationSuite({
        validRequests,
        invalidRequests,
        validResponses,
        invalidResponses
      });
      
      // 测试有效请求
      expect(() => testSuite.testValidRequests()).not.toThrow();
      
      // 测试无效请求
      expect(() => testSuite.testInvalidRequests()).not.toThrow();
      
      // 测试有效响应
      try {
        testSuite.testValidResponses();
      } catch (error) {
        console.log('Valid responses test failed:', error.message);
        // 调试第一个响应
        const firstResponse = validResponses[0];
        console.log('First response:', JSON.stringify(firstResponse, null, 2));
        const validation = BacktestTestUtils.validateStrategyExecuteResponse(firstResponse);
        console.log('Validation result:', validation);
        throw error;
      }
      
      // 测试无效响应
      expect(() => testSuite.testInvalidResponses()).not.toThrow();
    });
  });

  describe('数据一致性验证', () => {
    it('应该验证请求和响应之间的数据一致性', () => {
      const request = BacktestDataFactory.createStrategyExecuteRequest('normal', {
        choice_symbols: ['000001.SZ', '000002.SZ'],
        capital: 2000000
      });
      
      const response = BacktestDataFactory.createStrategyExecuteResponse('profitable', request);
      
      // 验证响应中的参数与请求一致
      expect(response.data.parameters_used.initial_capital).toBe(request.capital);
      expect(response.data.parameters_used.symbols).toEqual(request.choice_symbols);
      expect(response.data.execution_summary.total_symbols).toBe(request.choice_symbols.length);
    });

    it('应该验证订单数据的内部一致性', () => {
      const response = BacktestDataFactory.createStrategyExecuteResponse('profitable');
      
      response.data.results.forEach(result => {
        expect(result.orders_count).toBe(result.orders.length);
        
        result.orders.forEach(order => {
          expect(order.symbol).toBe(result.symbol);
          expect(order.buy_date).toBeLessThan(order.sell_date);
          expect(order.buy_price).toBeGreaterThan(0);
          expect(order.sell_price).toBeGreaterThan(0);
        });
      });
    });

    it('应该验证执行摘要的数值合理性', () => {
      const response = BacktestDataFactory.createStrategyExecuteResponse('profitable');
      const summary = response.data.execution_summary;
      
      expect(summary.total_symbols).toBeGreaterThanOrEqual(0);
      expect(summary.symbols_with_trades).toBeLessThanOrEqual(summary.total_symbols);
      expect(summary.total_trades).toBeGreaterThanOrEqual(0);
      expect(summary.initial_capital).toBeGreaterThan(0);
      expect(summary.win_rate).toBeGreaterThanOrEqual(0);
      expect(summary.win_rate).toBeLessThanOrEqual(1);
      expect(summary.profit_loss_ratio).toBeGreaterThan(0);
    });
  });
});