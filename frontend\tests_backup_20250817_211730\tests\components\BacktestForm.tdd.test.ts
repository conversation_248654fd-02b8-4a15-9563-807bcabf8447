import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import BacktestForm from '@/components/BacktestForm.vue'
import type { BacktestConfig } from '@/api/types/backtest'

/**
 * BacktestForm TDD 核心测试套件
 * 
 * 这是TDD红阶段的核心测试用例，包含最基础的功能验证：
 * 1. 基础渲染测试 - 确保组件能正确渲染必要元素
 * 2. 用户交互测试 - 验证表单提交和重置功能
 * 3. Props测试 - 验证loading状态的正确处理
 * 
 * 预期开发时间：每个测试用例 15-30分钟
 */
describe('BacktestForm.vue - TDD核心测试', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    wrapper = mount(BacktestForm)
  })

  afterEach(() => {
    wrapper.unmount()
  })

  // ===== 基础渲染测试 =====
  describe('基础渲染测试', () => {
    it('should render all required form fields', () => {
      // 验证所有必需的表单字段都能正确渲染
      expect(wrapper.find('[data-testid="strategy-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="symbol-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="start-date-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="end-date-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="capital-input"]').exists()).toBe(true)
    })

    it('should render action buttons', () => {
      // 验证操作按钮能正确渲染
      expect(wrapper.find('[data-testid="reset-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="submit-btn"]').exists()).toBe(true)
    })
  })

  // ===== 用户交互测试 =====
  describe('用户交互测试', () => {
    it('should emit a "submit" event with form data when submitted', async () => {
      // 准备测试数据
      const mockConfig: BacktestConfig = {
        strategy_name: '测试策略',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        initial_capital: 100000
      }

      // 填写表单数据
      await wrapper.find('[data-testid="strategy-input"]').setValue(mockConfig.strategy_name)
      await wrapper.find('[data-testid="symbol-input"]').setValue(mockConfig.symbol)
      await wrapper.find('[data-testid="start-date-input"]').setValue(mockConfig.start_date)
      await wrapper.find('[data-testid="end-date-input"]').setValue(mockConfig.end_date)
      await wrapper.find('[data-testid="capital-input"]').setValue(mockConfig.initial_capital.toString())
      await nextTick()

      // 提交表单
      await wrapper.find('form').trigger('submit.prevent')

      // 验证submit事件被正确触发，并携带正确的数据
      const submitEvents = wrapper.emitted('submit')
      expect(submitEvents).toHaveLength(1)
      expect(submitEvents![0][0]).toEqual(mockConfig)
    })

    it('should emit a "reset" event when the reset button is clicked', async () => {
      // 先填写一些数据
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      
      // 点击重置按钮
      await wrapper.find('[data-testid="reset-btn"]').trigger('click')
      await nextTick()

      // 验证表单被清空
      expect(wrapper.find('[data-testid="strategy-input"]').element.value).toBe('')
      expect(wrapper.find('[data-testid="symbol-input"]').element.value).toBe('')
      
      // 验证reset事件被正确触发
      const resetEvents = wrapper.emitted('reset')
      expect(resetEvents).toHaveLength(1)
    })
  })

  // ===== Props测试 =====
  describe('Props测试', () => {
    it('should disable the submit button and show loading text when loading prop is true', async () => {
      // 设置loading状态为true
      await wrapper.setProps({ loading: true })
      
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      
      // 验证按钮被禁用并显示加载文本
      expect(submitBtn.text()).toBe('运行中...')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('should enable the submit button and show normal text when loading prop is false', async () => {
      // 设置loading状态为false
      await wrapper.setProps({ loading: false })
      
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      
      // 验证按钮显示正常文本（注意：可能仍然因为表单验证而被禁用）
      expect(submitBtn.text()).toBe('开始回测')
    })
  })

  // ===== 表单验证基础测试 =====
  describe('表单验证基础测试', () => {
    it('should disable submit button when form is empty', () => {
      // 验证空表单时提交按钮被禁用
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeDefined()
    })

    it('should enable submit button when all required fields are filled', async () => {
      // 填写所有必需字段
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-01-01')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="capital-input"]').setValue('100000')
      await nextTick()

      // 验证提交按钮变为可用
      const submitBtn = wrapper.find('[data-testid="submit-btn"]')
      expect(submitBtn.attributes('disabled')).toBeUndefined()
    })
  })
})