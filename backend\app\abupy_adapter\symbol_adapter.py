# -*- coding: utf-8 -*-
"""
abu Symbol模块适配器，用于连接FastAPI与原abu框架
"""
import re
import pandas as pd
import threading
import logging
from typing import List, Optional, Dict, Any, Union, Tuple

# Forward declaration for EMarketTargetType in case of import failure
EMarketTargetType = Any

# 导入abu原有模块
try:
    import abupy
    from abupy import ABuSymbolPd
    from abupy.MarketBu import ABuMarket, ABuSymbol
    from abupy.CoreBu import ABuEnv
    from abupy.CoreBu.ABuEnv import EMarketTargetType
    abu_import_success = True
    logging.info("Abu模块导入成功")
except ImportError as e:
    logging.warning(f"Abu模块导入失败: {e}")
    abu_import_success = False

# 导入自定义异常
from ..core.exceptions import SymbolError, DataNotFoundError


class SymbolAdapter:
    """abu Symbol适配器"""
    
    # 用于标识常见的市场前缀
    MARKET_PREFIXES = {
        'sh': 'CN',
        'sz': 'CN',
        'us': 'US',
        'hk': 'HK',
        'cn': 'CN'
    }
    
    # 常见指数代码
    CN_INDEX_CODES = {
        'sh000001': '上证指数',
        'sh000300': '沪深300',
        'sz399001': '深证成指',
        'sz399006': '创业板指'
    }
    
    US_INDEX_CODES = {
        'us.dji': '道琼斯工业平均指数',
        'us.ixic': '纳斯达克综合指数',
        'us.inx': '标普500'
    }
    
    HK_INDEX_CODES = {
        'hkhsi': '恒生指数',
        'hkhscei': '国企指数',
        'hkhscci': '红筹指数'
    }
    
    # 缓存锁，确保线程安全
    _cache_lock = threading.RLock()
    
    # 股票名称缓存
    _symbol_name_cache = {}
    
    @staticmethod
    def get_symbol_obj(symbol_str: str):
        """
        根据字符串代码获取abu Symbol对象
        
        Args:
            symbol_str: 代码字符串，如'sh000001'
            
        Returns:
            abu Symbol对象
            
        Raises:
            SymbolError: 当代码格式无效或无法识别市场类型时
        """
        if not abu_import_success:
            raise SymbolError(
                message="无法导入abu模块，请确保abupy已正确安装",
                data={"symbol": symbol_str}
            )
            
        try:
            # 尝试使用原始abu代码转换功能
            return ABuSymbol.code_to_symbol(symbol_str)
        except (ValueError, TypeError) as e:
            # 如果原始方法失败，尝试我们的备用方法
            try:
                market = SymbolAdapter.get_market_type(symbol_str)
                return ABuEnv.get_symbol_obj(symbol_str, market)
            except Exception as inner_e:
                raise SymbolError(
                    message=f"无法识别股票代码格式: {symbol_str}, 错误: {str(e)}",
                    data={"symbol": symbol_str, "original_error": str(e)}
                )
    
    @staticmethod
    def get_market_type(symbol_str: str) -> EMarketTargetType:
        """
        根据代码获取市场类型
        
        Args:
            symbol_str: 代码字符串，如'sh000001'
            
        Returns:
            EMarketTargetType: abu市场类型枚举
            
        Raises:
            SymbolError: 当无法识别市场类型时
        """
        if not symbol_str:
            raise SymbolError(message="股票代码不能为空")
            
        # 规范化代码为小写
        symbol_lower = symbol_str.lower()
        
        # 检查常见前缀
        if symbol_lower.startswith('sh') or symbol_lower.startswith('sz'):
            return EMarketTargetType.E_MARKET_TARGET_CN
        elif symbol_lower.startswith('us'):
            return EMarketTargetType.E_MARKET_TARGET_US
        elif symbol_lower.startswith('hk'):
            return EMarketTargetType.E_MARKET_TARGET_HK
            
        # 检查数字代码，根据长度判断
        if symbol_str.isdigit():
            if len(symbol_str) == 6:
                # 6位数字默认为A股
                return EMarketTargetType.E_MARKET_TARGET_CN
            elif len(symbol_str) == 5:
                # 5位数字默认为港股
                return EMarketTargetType.E_MARKET_TARGET_HK
                
        # 尝试使用原始abu的方法进行更全面的判断
        try:
            symbol_obj = ABuSymbol.code_to_symbol(symbol_str, rs=False)
            if symbol_obj:
                return symbol_obj.market
        except:
            pass
            
        # 默认返回A股，但标记为可能不准确
        return EMarketTargetType.E_MARKET_TARGET_CN
    
    @staticmethod
    def get_kline_data(symbol_str: str, start: Optional[str] = None, 
                      end: Optional[str] = None) -> pd.DataFrame:
        """
        获取K线数据，包装abu原有接口
        
        Args:
            symbol_str: 代码字符串
            start: 开始日期，格式'YYYY-MM-DD'
            end: 结束日期，格式'YYYY-MM-DD'
            
        Returns:
            pd.DataFrame: K线数据DataFrame
            
        Raises:
            DataNotFoundError: 当无法获取K线数据时
            SymbolError: 当代码格式无效时
        """
        if not abu_import_success:
            raise SymbolError(
                message="无法导入abu模块，请确保abupy已正确安装",
                data={"symbol": symbol_str}
            )
        try:
            # 首先验证代码格式
            SymbolAdapter.validate_symbol(symbol_str)
            
            # 使用abu原有接口获取K线数据
            df = ABuSymbolPd.make_kl_df(symbol_str, start=start, end=end)
            
            if df is None or df.empty:
                raise DataNotFoundError(
                    message=f"未找到{symbol_str}的K线数据",
                    data={"symbol": symbol_str, "start": start, "end": end}
                )
                
            return df
        except SymbolError:
            # 重新抛出SymbolError异常
            raise
        except DataNotFoundError:
            # 重新抛出DataNotFoundError异常
            raise
        except Exception as e:
            # 其他异常转换为DataNotFoundError
            raise DataNotFoundError(
                message=f"获取K线数据失败: {str(e)}",
                data={"symbol": symbol_str, "start": start, "end": end}
            )
    
    @staticmethod
    def get_symbol_name(symbol_str: str) -> str:
        """
        获取股票名称
        
        Args:
            symbol_str: 代码字符串
            
        Returns:
            str: 股票名称
        """
        # 检查缓存中是否已有该股票名称
        with SymbolAdapter._cache_lock:
            if symbol_str in SymbolAdapter._symbol_name_cache:
                return SymbolAdapter._symbol_name_cache[symbol_str]
        
        # 检查是否为已知指数
        for index_dict in [SymbolAdapter.CN_INDEX_CODES, SymbolAdapter.US_INDEX_CODES, SymbolAdapter.HK_INDEX_CODES]:
            if symbol_str.lower() in index_dict:
                name = index_dict[symbol_str.lower()]
                # 缓存结果
                with SymbolAdapter._cache_lock:
                    SymbolAdapter._symbol_name_cache[symbol_str] = name
                return name
        
        # 尝试使用tushare或其他数据源获取名称
        try:
            import tushare as ts
            # from backend.app.core.config import settings # Moved down
            
            # 确保有有效的Tushare Token
            # Import settings just before it's used
            from backend.app.core.config import settings
            if settings.TUSHARE_TOKEN:
                ts.set_token(settings.TUSHARE_TOKEN)
                pro = ts.pro_api()
                
                # 先试图将股票代码转换为tushare格式
                try:
                    # 根据代码的格式进行转换
                    if symbol_str.startswith('sh') or symbol_str.startswith('sz'):
                        # 标准A股格式
                        ts_code = f"{symbol_str[2:]}.{symbol_str[:2].upper()}"
                    elif symbol_str.startswith('hk'):
                        # 标准港股格式
                        ts_code = f"{symbol_str[2:]}.HK"
                    elif '.' in symbol_str:
                        # 已经是tushare格式
                        code, suffix = symbol_str.split('.')
                        ts_code = f"{code}.{suffix.upper()}"
                    elif symbol_str.isdigit():
                        # 纯数字代码
                        if len(symbol_str) == 6:
                            # A股
                            if symbol_str.startswith('6'):
                                ts_code = f"{symbol_str}.SH"
                            else:
                                ts_code = f"{symbol_str}.SZ"
                        elif len(symbol_str) in [4, 5]:
                            # 港股
                            ts_code = f"{symbol_str}.HK"
                        else:
                            # 不支持的数字长度
                            raise ValueError(f"无法转换为tushare格式: {symbol_str}")
                    else:
                        # 不支持的格式
                        raise ValueError(f"无法转换为tushare格式: {symbol_str}")
                    
                    # 使用tushare的基础信息接口获取股票名称
                    if ts_code.endswith('.SH') or ts_code.endswith('.SZ'):
                        # 获取A股名称
                        df = pro.stock_basic(ts_code=ts_code, fields='ts_code,name')
                        if not df.empty:
                            name = df['name'].iloc[0]
                            # 缓存结果
                            with SymbolAdapter._cache_lock:
                                SymbolAdapter._symbol_name_cache[symbol_str] = name
                            return name
                    elif ts_code.endswith('.HK'):
                        # 获取港股名称
                        df = pro.hk_basic(ts_code=ts_code, fields='ts_code,name')
                        if not df.empty:
                            name = df['name'].iloc[0]
                            # 缓存结果
                            with SymbolAdapter._cache_lock:
                                SymbolAdapter._symbol_name_cache[symbol_str] = name
                            return name
                except Exception as e:
                    # 如果转换或获取失败，记录错误并继续使用默认规则
                    import logging
                    logging.warning(f"使用tushare获取{symbol_str}的名称失败: {str(e)}")
        except Exception as e:
            # tushare不可用或其他异常，记录错误并继续使用默认规则
            import logging
            logging.warning(f"初始化tushare失败: {str(e)}")
        
        
        # 如果外部数据源不可用，则使用本地规则
        try:
            # 规范化代码
            symbol_lower = symbol_str.lower()
            
            # 处理A股
            if symbol_lower.startswith('sh'):
                code = symbol_lower[2:]
                if code.startswith('60'):
                    name = f"{code}(沪A)"
                elif code.startswith('688'):
                    name = f"{code}(科创板)"
                elif code.startswith('000001'):
                    name = "上证指数"
                else:
                    name = f"{code}(上证)"
            elif symbol_lower.startswith('sz'):
                code = symbol_lower[2:]
                if code.startswith('00'):
                    name = f"{code}(深A)"
                elif code.startswith('30'):
                    name = f"{code}(创业板)"
                elif code.startswith('399001'):
                    name = "深证成指"
                else:
                    name = f"{code}(深证)"
            # 处理美股
            elif symbol_lower.startswith('us'):
                name = f"{symbol_str[2:].upper()}(美股)"
            # 处理港股
            elif symbol_lower.startswith('hk'):
                name = f"{symbol_lower[2:]}(港股)"
            # 处理纯数字代码
            elif symbol_str.isdigit():
                if len(symbol_str) == 6:
                    # 根据首位判断市场
                    if symbol_str.startswith('6'):
                        name = f"sh{symbol_str}(沪A)"
                    elif symbol_str.startswith('0') or symbol_str.startswith('3'):
                        name = f"sz{symbol_str}(深A)"
                    else:
                        name = symbol_str
                elif len(symbol_str) == 5:
                    name = f"hk{symbol_str}(港股)"
                else:
                    name = symbol_str
            else:
                name = symbol_str
                
            # 缓存结果
            with SymbolAdapter._cache_lock:
                SymbolAdapter._symbol_name_cache[symbol_str] = name
                
            return name
        except Exception as e:
            # 如果发生异常，返回原始代码
            return symbol_str
    
    @staticmethod
    def get_market_symbols(market_type: str) -> List[str]:
        """
        获取指定市场的所有代码
        
        Args:
            market_type: 市场类型，'CN'/'US'/'HK'
            
        Returns:
            List[str]: 代码列表
            
        Raises:
            DataNotFoundError: 当无法获取代码列表时
            ValidationError: 当市场类型无效时
        """
        try:
            if market_type == 'CN':
                return ABuMarket.all_cn_symbol()
            elif market_type == 'US':
                return ABuMarket.all_us_symbol()
            elif market_type == 'HK':
                return ABuMarket.all_hk_symbol()
            else:
                from backend.app.core.exceptions import ValidationError
                raise ValidationError(
                    message=f"不支持的市场类型: {market_type}",
                    data={"supported_markets": ['CN', 'US', 'HK']}
                )
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise DataNotFoundError(
                message=f"获取{market_type}市场代码列表失败: {str(e)}",
                data={"market": market_type}
            )
    
    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """
        验证股票代码格式是否有效。重构后的逻辑更清晰，能正确处理前缀和后缀带点的情况。

        Args:
            symbol: 股票代码

        Returns:
            bool: 格式是否有效

        Raises:
            SymbolError: 当代码格式无效时
        """
        if not symbol:
            raise SymbolError(message="股票代码不能为空")

        # 优先处理带'.'的格式，因为其意图最明确
        if '.' in symbol:
            parts = symbol.split('.')
            if len(parts) != 2:
                raise SymbolError(f"股票代码格式错误，包含多个点: {symbol}")

            part1, part2 = parts[0], parts[1]

            # 场景一: market_prefix.code 格式, e.g., 'us.TSLA'
            if part1.lower() in ['us', 'sh', 'sz', 'hk']:
                prefix = part1.lower()
                code = part2
                if prefix == 'us':
                    if not re.match(r'^[A-Za-z0-9\.-]+$', code) or not code:
                        raise SymbolError(f"美股代码部分格式错误: {symbol}")
                    return True
                elif prefix in ['sh', 'sz']:
                    if not (code.isdigit() and len(code) == 6):
                        raise SymbolError(f"A股代码部分必须为6位数字: {symbol}")
                    return True
                elif prefix == 'hk':
                    if not (code.isdigit() and len(code) in [4, 5]):
                        raise SymbolError(f"港股代码部分必须为4或5位数字: {symbol}")
                    return True

            # 场景二: code.market_suffix 格式, e.g., '600036.SH'
            else:
                code = part1
                suffix = part2.upper()
                if suffix in ['SH', 'SZ']:
                    if not (code.isdigit() and len(code) == 6):
                        raise SymbolError(f"A股代码必须为6位数字: {symbol}")
                    return True
                elif suffix == 'HK':
                    if not (code.isdigit() and len(code) in [4, 5]):
                        raise SymbolError(f"港股代码必须为4或5位数字: {symbol}")
                    return True
                else:
                    raise SymbolError(f"不支持的市场后缀: {suffix}", data={"supported_suffixes": ["SH", "SZ", "HK"]})
            
            # 如果带点，但两种格式都不匹配，则为错误
            raise SymbolError(f"无法识别的带点格式: {symbol}")

        # --- 以下处理不带'.'的格式 ---

        # 场景三: market_prefix + code 格式, e.g., 'sh600036'
        if symbol.lower().startswith(('sh', 'sz')):
            code = symbol[2:]
            if not (code.isdigit() and len(code) == 6):
                raise SymbolError(f"A股代码必须为sh/sz前缀+6位数字: {symbol}")
            return True
        
        if symbol.lower().startswith('hk'):
            code = symbol[2:]
            if code.lower() in ['hsi', 'hscei', 'hscci']: # 指数代码
                return True
            if not (code.isdigit() and len(code) in [4, 5]):
                raise SymbolError(f"港股代码必须为hk前缀+4/5位数字: {symbol}")
            return True

        if symbol.lower().startswith('us'):
            code = symbol[2:]
            if not re.match(r'^[A-Za-z0-9\.-]+$', code) or not code:
                raise SymbolError(f"美股代码格式错误: {symbol}")
            return True

        # 场景四: 纯数字代码, e.g., '600036'
        if symbol.isdigit():
            if len(symbol) == 6:  # A股
                return True
            if len(symbol) in [4, 5]:  # 港股
                return True
            raise SymbolError(f"纯数字代码长度必须为4, 5或6位: {symbol}")

        # 如果所有规则都不匹配，则格式无效
        raise SymbolError(
            message=f"无法识别的股票代码格式: {symbol}",
            data={"symbol": symbol}
        )
    @staticmethod
    def is_index(symbol: str) -> bool:
        """
        判断一个符号是否为指数
        
        Args:
            symbol: 股票代码
            
        Returns:
            bool: 是否为指数
        """
        # 规范化代码为小写
        symbol_lower = symbol.lower()
        
        # 检查是否在已知指数代码中
        if symbol_lower in SymbolAdapter.CN_INDEX_CODES or \
           symbol_lower in SymbolAdapter.US_INDEX_CODES or \
           symbol_lower in SymbolAdapter.HK_INDEX_CODES:
            return True
            
        # 检查是否为常见指数代码格式
        # A股指数通常以000或399开头
        if symbol_lower.startswith('sh000') or symbol_lower.startswith('sz399'):
            return True
            
        # 处理后缀格式，如'000300.SH', '399001.SZ'
        if '.' in symbol:
            code, market_suffix = symbol.split('.')
            if market_suffix.upper() in ['SH', 'SZ'] and (code.startswith('000') or code.startswith('399')):
                return True
                
        # 处理纯数字格式
        if symbol.isdigit() and (symbol.startswith('000') or symbol.startswith('399')):
            return True
            
        # 其他情况视为非指数
        return False
        
    @staticmethod
    def normalize_symbol(symbol: str) -> Tuple[str, str]:
        """
        标准化股票代码，并返回市场类型
        
        Args:
            symbol: 原始股票代码。支持多种格式：
                    A股: 'sh600000', '600000'
                    港股: 'hk0700', '0700', '0700.HK'
                    美股: 'usAAPL', 'AAPL'
            
        Returns:
            Tuple[str, str]: (标准化后的代码, 市场类型)
            
        Raises:
            SymbolError: 当代码格式无效或无法标准化时
        """
        # 验证输入
        if not symbol:
            raise SymbolError(message="股票代码不能为空")
        
        # 处理后缀格式，如'0700.HK'
        if '.' in symbol:
            code, market_suffix = symbol.split('.')
            if market_suffix.upper() == 'HK':
                # 帮港股即使没有前缀也返回成功
                normalized_symbol = f"hk{code}"
                return normalized_symbol, 'HK'
            # 其他格式的后缀也可以在这里处理
        
        # 已经是标准格式的代码
        if symbol.startswith(('sh', 'sz', 'us', 'hk')):
            market = SymbolAdapter.MARKET_PREFIXES.get(symbol[:2].lower(), 'CN')
            return symbol, market
            
        # 纯数字代码转换
        if symbol.isdigit():
            if len(symbol) == 6:
                # 6位纯数字，根据首位判断沪深
                if symbol.startswith('6'):
                    return f"sh{symbol}", 'CN'
                elif symbol.startswith('0') or symbol.startswith('3'):
                    return f"sz{symbol}", 'CN'
                else:
                    # 其他情况默认为上海
                    return f"sh{symbol}", 'CN'
            elif len(symbol) == 5 or len(symbol) == 4:
                # 5位或4位纯数字，视为港股
                return f"hk{symbol}", 'HK'
                
        # 常见指数代码
        for index_dict in [SymbolAdapter.CN_INDEX_CODES, SymbolAdapter.US_INDEX_CODES, SymbolAdapter.HK_INDEX_CODES]:
            if symbol.lower() in index_dict:
                return symbol.lower(), SymbolAdapter.get_market_type(symbol)._value_
        
        # 尝试使用abu原始方法标准化
        try:
            symbol_obj = ABuSymbol.code_to_symbol(symbol)
            if symbol_obj:
                # 从symbol对象构建标准化代码
                market_map = {
                    EMarketTargetType.E_MARKET_TARGET_CN: 'CN',
                    EMarketTargetType.E_MARKET_TARGET_US: 'US',
                    EMarketTargetType.E_MARKET_TARGET_HK: 'HK'
                }
                return symbol_obj.value, market_map.get(symbol_obj.market, 'CN')
        except Exception as e:
            # 如果原始方法失败，尝试其他处理方式
            pass
            
        # 如果所有方法都失败，抛出异常
        raise SymbolError(
            message=f"无法标准化股票代码: {symbol}",
            data={"symbol": symbol, "supported_formats": [
                "A股: sh/sz + 6位数字, 纯6位数字", 
                "港股: hk + 4/5位数字, 纯4/5位数字, 数字.HK",
                "美股: us + 股票代码"
            ]}
        )
