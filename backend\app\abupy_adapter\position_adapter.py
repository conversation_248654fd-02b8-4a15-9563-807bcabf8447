from typing import Dict, Any, Type, List
from abupy import AbuPositionBase, AbuAtrPosition

# 映射字符串到类
POSITION_CLASSES: Dict[str, Type[AbuPositionBase]] = {
    "AbuAtrPosition": AbuAtrPosition,
    # 未来可以添加其他仓位管理类
}

# 这是一个硬编码的列表，未来可以从配置文件或数据库中读取
SUPPORTED_POSITIONS: List[Dict[str, Any]] = [
    {
        "name": "ATR仓位管理 (AbuAtrPosition)",
        "class_name": "AbuAtrPosition",
        "description": "基于平均真实波幅(ATR)动态调整仓位大小，市场波动大时仓位小，波动小时仓位大。",
        "params": [
            {
                "name": "atr_base_price",
                "type": "float",
                "default": 15.0,
                "label": "ATR价格基数",
                "description": "用于计算标准ATR的价格基准。"
            },
            {
                "name": "std_atr_threshold",
                "type": "float",
                "default": 0.5,
                "label": "标准ATR阈值",
                "description": "限制仓位大小，避免在价格波动极小的情况下分配过大仓位。"
            },
            {
                "name": "atr_pos_base",
                "type": "float",
                "default": 0.1,
                "label": "基础仓位比例",
                "description": "在标准ATR为1时分配的仓位比例 (例如0.1代表10%)"
            }
        ]
    }
    # 未来可以添加更多支持的仓位管理策略
]

def create_position_manager(position_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据提供的配置字典创建abupy仓位管理器的配置。

    :param position_config: 包含 'class_name' 和参数的字典。
                          例如: {'class_name': 'AbuAtrPosition', 'atr_base_price': 20}
    :return: 一个适用于abupy买入因子的position字典。
             例如: {'class': AbuAtrPosition, 'atr_base_price': 20}
    """
    if not position_config or 'class_name' not in position_config:
        return None

    class_name = position_config.get('class_name')
    position_class = POSITION_CLASSES.get(class_name)

    if not position_class:
        raise ValueError(f"未知的仓位管理类: {class_name}")

    # 准备传递给买入因子的参数字典
    # 第一个元素是类本身，而不是实例
    params_for_factor = {'class': position_class}
    
    # 复制除了 'class_name' 之外的所有参数
    params_for_factor.update({k: v for k, v in position_config.items() if k != 'class_name'})

    return params_for_factor