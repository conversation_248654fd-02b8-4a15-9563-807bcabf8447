import { describe, it, expect, beforeEach, vi } from 'vitest'
import { create<PERSON><PERSON>, setActiveP<PERSON> } from 'pinia'
import { ElMessage } from 'element-plus'
import { useStrategyStore } from '@/stores/modules/useStrategyStore'
import { useBacktestStore } from '@/stores/modules/useBacktestStore'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElContainer: { name: 'ElContainer', template: '<div><slot /></div>' },
  ElHeader: { name: 'ElHeader', template: '<div><slot /></div>' },
  ElMain: { name: '<PERSON>Main', template: '<div><slot /></div>' },
  ElAside: { name: 'ElAside', template: '<div><slot /></div>' },
  ElMenu: { name: 'ElMenu', template: '<nav><slot /></nav>' },
  ElMenuItem: { name: 'ElMenuItem', template: '<a><slot /></a>' },
  ElPageHeader: { name: '<PERSON><PERSON>ageHeader', template: '<div><slot /></div>' },
  ElRow: { name: 'ElRow', template: '<div><slot /></div>' },
  ElCol: { name: 'ElCol', template: '<div><slot /></div>' },
  ElCard: { name: 'ElCard', template: '<div><slot /></div>' },
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElTextarea: { name: 'ElTextarea', template: '<textarea></textarea>' },
  ElInputNumber: { name: 'ElInputNumber', template: '<input type="number" />' },
  ElDatePicker: { name: 'ElDatePicker', template: '<input type="date" />' },
  ElSelect: { name: 'ElSelect', template: '<select><slot /></select>' },
  ElOption: { name: 'ElOption', template: '<option><slot /></option>' },
  ElForm: { name: 'ElForm', template: '<form><slot /></form>' },
  ElFormItem: { name: 'ElFormItem', template: '<div><slot /></div>' },
  ElTable: { name: 'ElTable', template: '<table><slot /></table>' },
  ElTableColumn: { name: 'ElTableColumn', template: '<td><slot /></td>' },
  ElProgress: { name: 'ElProgress', template: '<div><slot /></div>' },
  ElLoading: { directive: {} },
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock 路由组件
vi.mock('@/views/Dashboard.vue', () => ({
  default: {
    name: 'Dashboard',
    template: '<div data-testid="dashboard">仪表板</div>'
  }
}))

vi.mock('@/views/StrategyWorkshop.vue', () => ({
  default: {
    name: 'StrategyWorkshop',
    template: '<div data-testid="strategy-workshop">策略工厂</div>',
    methods: {
      createNewStrategy() {
        return this.$emit('create-strategy')
      },
      saveStrategy(strategy: any) {
        return this.$emit('save-strategy', strategy)
      }
    }
  }
}))

vi.mock('@/views/BacktestView.vue', () => ({
  default: {
    name: 'BacktestView',
    template: '<div data-testid="backtest-view">回测中心</div>',
    methods: {
      startBacktest(params: any) {
        return this.$emit('run-backtest', params)
      }
    }
  }
}))

// Mock API 模块
vi.mock('@/api/strategy', () => ({
  strategyApi: {
    getStrategies: vi.fn(),
    getStrategyById: vi.fn(),
    createStrategy: vi.fn(),
    updateStrategy: vi.fn(),
    deleteStrategy: vi.fn(),
    executeStrategy: vi.fn()
  }
}))

vi.mock('@/api/backtest', () => ({
  backtestApi: {
    startBacktest: vi.fn(),
    getBacktestHistory: vi.fn(),
    loadBacktestResults: vi.fn(),
    deleteBacktest: vi.fn()
  }
}))

vi.mock('@/api/factors', () => ({
  factorsApi: {
    getFactors: vi.fn(),
    getFactorById: vi.fn()
  }
}))

vi.mock('@/api/market', () => ({
  marketApi: {
    getSymbols: vi.fn(),
    getKlineData: vi.fn()
  }
}))

describe('完整用户工作流程端到端测试', () => {
  let strategyStore: any
  let backtestStore: any

  beforeEach(async () => {
    // 设置 Pinia
    const pinia = createPinia()
    setActivePinia(pinia)
    
    // 获取 store 实例
    strategyStore = useStrategyStore()
    backtestStore = useBacktestStore()
    
    // 重置所有 mock
    vi.clearAllMocks()
    
    // Mock store 方法
    vi.spyOn(strategyStore, 'fetchStrategies').mockResolvedValue()
    vi.spyOn(strategyStore, 'createNewStrategy').mockResolvedValue({ success: true })
    vi.spyOn(strategyStore, 'updateExistingStrategy').mockResolvedValue({ success: true })
    vi.spyOn(strategyStore, 'executeStrategy').mockResolvedValue({ success: true })
    vi.spyOn(backtestStore, 'startBacktest').mockResolvedValue({ success: true, task_id: 'test-task-1' })
    vi.spyOn(backtestStore, 'fetchBacktestHistory').mockResolvedValue()
    vi.spyOn(backtestStore, 'loadBacktestResults').mockResolvedValue()
  })

  describe('完整量化投资工作流程', () => {
    it('应该能够完成从策略创建到回测分析的完整流程', async () => {
      // 1. Store 初始化验证
      expect(strategyStore).toBeDefined()
      expect(backtestStore).toBeDefined()
      
      // 3. 创建新策略
      const newStrategy = {
        id: 'e2e-test-strategy-1',
        name: '端到端测试策略',
        description: '这是一个完整的端到端测试策略',
        author: 'test-user',
        content: `def initialize(context):
    # 初始化策略
    context.stocks = ['000001.SZ', '000002.SZ']
    
def handle_data(context, data):
    # 策略逻辑
    for stock in context.stocks:
        if data[stock].price > data[stock].ma20:
            order_target_percent(stock, 0.5)
        else:
            order_target_percent(stock, 0)`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        tags: ['测试', '双均线'],
        version: '1.0.0'
      }
      
      // 模拟策略创建
      await strategyStore.createNewStrategy(newStrategy)
      expect(strategyStore.createNewStrategy).toHaveBeenCalledWith(newStrategy)
      
      // 4. 策略保存成功反馈
      expect(strategyStore.error).toBeNull()
      
      // 5. 切换到回测流程
      
      // 6. 设置回测参数
      const backtestParams = {
        strategy_id: newStrategy.id,
        initial_capital: 1000000, // 100万初始资金
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        benchmark: '000300.SH', // 沪深300基准
        commission: 0.0003, // 万三手续费
        slippage: 0.001,
        symbols: ['000001.SZ', '000002.SZ']
      }
      
      // 7. 执行回测
      await backtestStore.startBacktest(backtestParams)
      expect(backtestStore.startBacktest).toHaveBeenCalledWith(backtestParams)
      
      // 8. 回测成功反馈
      expect(backtestStore.backtestError).toBe('')
      
      // 9. 获取回测结果
      const taskId = 'test-task-1'
      await backtestStore.loadBacktestResults(taskId)
      expect(backtestStore.loadBacktestResults).toHaveBeenCalledWith(taskId)
      
      // 10. 验证完整流程状态
      expect(strategyStore.strategies).toBeDefined()
      expect(backtestStore.backtestHistory).toBeDefined()
    })

    it('应该能够处理策略修改和重新回测的流程', async () => {
      // 1. 创建初始策略
      const originalStrategy = {
        id: 'strategy-to-modify',
        name: '原始策略',
        description: '原始策略描述',
        author: 'test-user',
        content: 'def initialize(context): pass',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        tags: ['测试'],
        version: '1.0.0'
      }
      await strategyStore.createNewStrategy(originalStrategy)
      
      // 2. 运行初始回测
      const initialBacktest = {
        strategy_id: originalStrategy.id,
        initial_capital: 500000,
        start_date: '2023-01-01',
        end_date: '2023-06-30',
        benchmark: '000300.SH',
        commission: 0.0003,
        slippage: 0.001,
        symbols: ['000001.SZ']
      }
      await backtestStore.startBacktest(initialBacktest)
      
      // 3. 修改策略
      const modifiedStrategy = { ...originalStrategy }
      modifiedStrategy.name = '修改后的策略'
      modifiedStrategy.content = modifiedStrategy.content + '\n# 添加新的逻辑'
      
      await strategyStore.updateExistingStrategy(modifiedStrategy.id, modifiedStrategy)
      expect(strategyStore.updateExistingStrategy).toHaveBeenCalledWith(modifiedStrategy.id, modifiedStrategy)
      
      // 4. 重新运行回测
      const newBacktest = {
        strategy_id: modifiedStrategy.id,
        initial_capital: 500000,
        start_date: '2024-01-01',
        end_date: '2024-06-30',
        benchmark: '000300.SH',
        commission: 0.0003,
        slippage: 0.001,
        symbols: ['000001.SZ']
      }
      
      await backtestStore.startBacktest(newBacktest)
      expect(backtestStore.startBacktest).toHaveBeenCalledWith(newBacktest)
      
      // 5. 比较回测结果
      await backtestStore.fetchBacktestHistory(modifiedStrategy.id)
      expect(backtestStore.fetchBacktestHistory).toHaveBeenCalledWith(modifiedStrategy.id)
    })
  })

  describe('多策略组合管理流程', () => {
    it('应该能够创建和管理多个策略的组合', async () => {
      // 1. 创建多个策略
      const strategies = [
        {
          id: 'trend-strategy',
          name: '趋势跟踪策略',
          description: '基于趋势跟踪的策略',
          author: 'test-user',
          content: 'def initialize(context): pass',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true,
          tags: ['趋势'],
          version: '1.0.0'
        },
        {
          id: 'mean-reversion-strategy',
          name: '均值回归策略',
          description: '基于均值回归的策略',
          author: 'test-user',
          content: 'def initialize(context): pass',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true,
          tags: ['均值回归'],
          version: '1.0.0'
        },
        {
          id: 'momentum-strategy',
          name: '动量策略',
          description: '基于动量的策略',
          author: 'test-user',
          content: 'def initialize(context): pass',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true,
          tags: ['动量'],
          version: '1.0.0'
        }
      ]
      
      // 2. 依次创建策略
      for (const strategy of strategies) {
        await strategyStore.createNewStrategy(strategy)
      }
      
      expect(strategyStore.createNewStrategy).toHaveBeenCalledTimes(3)
      
      // 3. 为每个策略运行回测
      const backtestResults = []
      for (const strategy of strategies) {
        const backtestParams = {
          strategy_id: strategy.id,
          initial_capital: 1000000,
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          benchmark: '000300.SH',
          commission: 0.0003,
          slippage: 0.001,
          symbols: ['000001.SZ']
        }
        
        const result = await backtestStore.startBacktest(backtestParams)
        backtestResults.push(result)
      }
      
      expect(backtestStore.startBacktest).toHaveBeenCalledTimes(3)
      
      // 4. 获取所有回测历史
      for (const strategy of strategies) {
        await backtestStore.fetchBacktestHistory(strategy.id)
      }
      
      expect(backtestStore.fetchBacktestHistory).toHaveBeenCalledTimes(3)
    })
  })

  describe('错误处理和用户体验', () => {
    it('应该在整个流程中提供一致的错误处理', async () => {
      // 1. 策略创建失败 - 直接设置错误状态
      strategyStore.error = '策略创建失败'
      expect(strategyStore.error).toBe('策略创建失败')
      expect(strategyStore.error).toBeDefined()
      
      // 2. 回测执行失败 - 直接设置错误状态
      backtestStore.backtestError = '回测执行失败'
      expect(backtestStore.backtestError).toBe('回测执行失败')
      expect(backtestStore.backtestError).toBeDefined()
      
      // 3. 网络连接失败 - 直接设置错误状态
      strategyStore.error = '网络连接失败'
      expect(strategyStore.error).toBe('网络连接失败')
      expect(strategyStore.error).toBeDefined()
    })

    it('应该提供适当的加载状态反馈', async () => {
      // 1. 策略加载状态
      expect(strategyStore.isLoading).toBeDefined()
      
      // 2. 回测加载状态
      expect(backtestStore.isLoadingResults).toBeDefined()
      
      // 3. 设置加载状态
      strategyStore.isLoading = true
      expect(strategyStore.isLoading).toBe(true)
      
      backtestStore.isLoadingResults = true
      expect(backtestStore.isLoadingResults).toBe(true)
      
      // 4. 重置加载状态
      strategyStore.isLoading = false
      backtestStore.isLoadingResults = false
      
      expect(strategyStore.isLoading).toBe(false)
      expect(backtestStore.isLoadingResults).toBe(false)
    })
  })

  describe('数据一致性和状态同步', () => {
    it('应该在不同模块间保持数据一致性', async () => {
      // 1. 在策略模块创建策略
      const strategy = {
        id: 'consistency-test-strategy',
        name: '一致性测试策略',
        description: '用于测试数据一致性的策略',
        author: 'test-user',
        content: 'def initialize(context): pass',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        tags: ['测试'],
        version: '1.0.0'
      }
      await strategyStore.createNewStrategy(strategy)
      
      // 2. 验证策略数据可用
      await strategyStore.fetchStrategies()
      expect(strategyStore.strategies).toBeDefined()
      
      // 3. 运行回测
      const backtestParams = {
        strategy_id: strategy.id,
        initial_capital: 1000000,
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        benchmark: '000300.SH',
        commission: 0.0003,
        slippage: 0.001,
        symbols: ['000001.SZ']
      }
      
      await backtestStore.startBacktest(backtestParams)
      
      // 4. 验证回测历史可见
      await backtestStore.fetchBacktestHistory(strategy.id)
      expect(backtestStore.backtestHistory).toBeDefined()
    })

    it('应该正确处理并发操作', async () => {
      // 1. 同时创建多个策略
      const strategies = [
        {
          id: 'concurrent-strategy-1',
          name: '并发策略1',
          description: '并发测试策略1',
          author: 'test-user',
          content: 'def initialize(context): pass',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true,
          tags: ['并发'],
          version: '1.0.0'
        },
        {
          id: 'concurrent-strategy-2',
          name: '并发策略2',
          description: '并发测试策略2',
          author: 'test-user',
          content: 'def initialize(context): pass',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true,
          tags: ['并发'],
          version: '1.0.0'
        }
      ]
      
      const createPromises = strategies.map(strategy => 
        strategyStore.createNewStrategy(strategy)
      )
      
      await Promise.all(createPromises)
      
      expect(strategyStore.createNewStrategy).toHaveBeenCalledTimes(2)
      
      // 2. 同时运行多个回测
      const backtestPromises = strategies.map(strategy => {
        const params = {
          strategy_id: strategy.id,
          initial_capital: 1000000,
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          benchmark: '000300.SH',
          commission: 0.0003,
          slippage: 0.001,
          symbols: ['000001.SZ']
        }
        return backtestStore.startBacktest(params)
      })
      
      await Promise.all(backtestPromises)
      
      expect(backtestStore.startBacktest).toHaveBeenCalledTimes(2)
    })
  })

  describe('性能和资源管理', () => {
    it('应该有效管理内存和资源使用', async () => {
      // 1. 创建大量策略
      const manyStrategies = Array.from({ length: 10 }, (_, index) => ({
        id: `performance-strategy-${index}`,
        name: `性能测试策略${index}`,
        description: `性能测试策略${index}的描述`,
        author: 'test-user',
        content: 'def initialize(context): pass',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        tags: ['性能测试'],
        version: '1.0.0'
      }))
      
      // 2. 批量创建
      for (const strategy of manyStrategies) {
        await strategyStore.createNewStrategy(strategy)
      }
      
      expect(strategyStore.createNewStrategy).toHaveBeenCalledTimes(10)
      
      // 3. 清理资源
      strategyStore.clearCurrentSelectedStrategy()
      backtestStore.resetBacktestState()
      
      // 4. 验证清理效果
      expect(strategyStore.currentSelectedStrategy).toBeNull()
      expect(backtestStore.backtestResult).toBeNull()
    })

    it('应该处理大数据量的回测结果', async () => {
      // 1. 模拟大数据量回测
      const largeBacktestResult = {
        task_id: 'large-data-task',
        status: 'completed',
        performance_metrics: {
          total_return: 0.25,
          annual_return: 0.15,
          sharpe_ratio: 1.2,
          max_drawdown: -0.08
        },
        trades: Array.from({ length: 1000 }, (_, i) => ({
          id: `trade-${i}`,
          symbol: '000001.SZ',
          side: i % 2 === 0 ? 'buy' : 'sell',
          quantity: 100,
          price: 10 + Math.random() * 5,
          timestamp: new Date(2023, 0, 1 + i).toISOString()
        })),
        portfolio_value: Array.from({ length: 365 }, (_, i) => ({
          date: new Date(2023, 0, 1 + i).toISOString(),
          value: 1000000 + Math.random() * 200000
        }))
      }
      
      vi.spyOn(backtestStore, 'loadBacktestResults').mockResolvedValue(largeBacktestResult)
      
      // 2. 获取大数据量结果
      await backtestStore.loadBacktestResults('large-data-task')
      
      expect(backtestStore.loadBacktestResults).toHaveBeenCalledWith('large-data-task')
      
      // 3. 验证数据处理
      expect(largeBacktestResult.trades).toHaveLength(1000)
      expect(largeBacktestResult.portfolio_value).toHaveLength(365)
    })
  })
})