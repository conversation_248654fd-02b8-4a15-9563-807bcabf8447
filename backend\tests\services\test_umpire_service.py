# backend/tests/services/test_umpire_service.py

import pytest
from unittest.mock import Mock, patch

from backend.app.services.umpire_service import UmpireService
from backend.app.core.exceptions import ParameterError, AdapterError

@pytest.fixture
def umpire_service():
    """提供一个 UmpireService 实例，其中 session 是一个 Mock 对象。"""
    mock_session = Mock()
    return UmpireService(session=mock_session)

# 修正: 将 patch 目标从 ...AbuDataCache 改回 ...AbuDataCacheAdapter
@patch('backend.app.services.umpire_service.train_umpire_models')
@patch('backend.app.services.umpire_service.AbuDataCacheAdapter.get_kl_pd_manager_for_training')
def test_train_models_success(mock_get_kl_manager, mock_train_umpire, umpire_service):
    """测试 train_models 在成功路径下的行为。"""
    mock_kl_manager = Mock()
    mock_get_kl_manager.return_value = mock_kl_manager
    train_data = {
        'market_name': 'cn',
        'market_info': {'start': '2020-01-01', 'end': '2021-01-01', 'benchmark': 'SH', 'choice_symbols': ['usAAPL']},
        'umpire_rules': [{'class_name': 'AbuUmpMainDeg', 'params': {}}]
    }
    result = umpire_service.train_models(train_data)
    mock_get_kl_manager.assert_called_once_with(**train_data['market_info'])
    mock_train_umpire.assert_called_once_with(
        rules=train_data['umpire_rules'], 
        kl_pd_manager=mock_kl_manager,
        market_name='cn'
    )
    assert result == {"status": "success", "message": "Umpire models trained successfully."}

def test_train_models_missing_market_info(umpire_service):
    """测试当缺少 market_info 时是否引发 ParameterError。"""
    train_data = {
        'market_name': 'cn',
        'umpire_rules': [{'class_name': 'AbuUmpMainDeg'}]
    }
    with pytest.raises(ParameterError, match="'market_info', 'umpire_rules', and 'market_name' are required."):
        umpire_service.train_models(train_data)

# 修正: 将 patch 目标从 ...AbuDataCache 改回 ...AbuDataCacheAdapter
@patch('backend.app.services.umpire_service.AbuDataCacheAdapter.get_kl_pd_manager_for_training')
def test_train_models_adapter_error(mock_get_kl_manager, umpire_service):
    """测试当底层适配器抛出 AdapterError 时，服务是否正确传递异常。"""
    mock_get_kl_manager.side_effect = AdapterError(message="Failed to get data")
    train_data = {
        'market_name': 'us',
        'market_info': {'start': '2020-01-01'},
        'umpire_rules': [{'class_name': 'AbuUmpMainDeg'}]
    }
    with pytest.raises(AdapterError, match="Failed to get data"):
        umpire_service.train_models(train_data)

# 修正: 将 patch 目标从 ...AbuDataCache 改回 ...AbuDataCacheAdapter
@patch('backend.app.services.umpire_service.train_umpire_models')
@patch('backend.app.services.umpire_service.AbuDataCacheAdapter.get_kl_pd_manager_for_training')
def test_train_models_unexpected_error(mock_get_kl_manager, mock_train_umpire, umpire_service):
    """测试当发生未预期的异常时，是否被包装成 AdapterError。"""
    mock_get_kl_manager.side_effect = ValueError("Something went wrong")
    train_data = {
        'market_name': 'hk',
        'market_info': {'start': '2020-01-01'},
        'umpire_rules': [{'class_name': 'AbuUmpMainDeg'}]
    }
    with pytest.raises(AdapterError, match="An unexpected error occurred during umpire model training: Something went wrong"):
        umpire_service.train_models(train_data)