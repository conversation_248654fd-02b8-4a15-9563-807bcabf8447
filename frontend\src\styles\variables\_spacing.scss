// 间距和尺寸变量定义

// 基础间距单位
$spacing-unit: 8px;

// 间距变量 (SCSS)
$spacing-xs: $spacing-unit * 0.5;  // 4px
$spacing-sm: $spacing-unit;        // 8px
$spacing-md: $spacing-unit * 2;    // 16px
$spacing-lg: $spacing-unit * 3;    // 24px
$spacing-xl: $spacing-unit * 4;    // 32px
$spacing-xxl: $spacing-unit * 6;   // 48px
$spacing-xxxl: $spacing-unit * 8;  // 64px

// CSS自定义属性 (符合UI规范)
:root {
  // 标准间距令牌
  --space-xxs: 2px;  // 0.25x - 用于极小的间距
  --space-xs: 4px;   // 0.5x - 用于元素内部极小的间距
  --space-sm: 8px;   // 1x - 用于小组件之间，或组件内部
  --space-md: 16px;  // 2x - 最常用，用于卡片内边距、表单项之间
  --space-workshop-gap: 16px;  // 策略工作区专用间距（已修复，可自由调整）
  --space-config-cards-gap: 10px;  // 策略配置卡片之间的间距（基础信息与因子配置）
  --space-lg: 32px;  // 3x - 用于大模块之间，或页面的主要分区
  --space-xl: 32px;  // 4x - 用于页面的顶部和底部外边距
  
  // 布局相关
  --sidebar-width: 168px;
  --sidebar-width-collapsed: 64px;
  --header-height: 60px;
  
  // 卡片内边距
  --card-header-padding: 16px 20px;
  --card-body-padding: 20px;
  
  // 边框半径
  --border-radius-sm: 2px;
  --border-radius-base: 4px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-round: 50%;
  
  // 边框宽度
  --border-width-thin: 1px;
  --border-width-base: 2px;
  --border-width-thick: 4px;
  
  // 组件高度
  --height-sm: 24px;
  --height-base: 32px;
  --height-lg: 40px;
  --height-xl: 48px;
  
  // 阴影层级
  --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  --shadow-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
  
  // 过渡动画
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  // Z-index层级
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  // 容器宽度 (SCSS变量)
$container-sm: 576px;
$container-md: 768px;
$container-lg: 992px;
$container-xl: 1200px;
$container-xxl: 1400px;

// 容器宽度
  --container-sm: 576px;
  --container-md: 768px;
  --container-lg: 992px;
  --container-xl: 1200px;
  --container-xxl: 1400px;
}

// 边框半径
$border-radius-sm: 2px;
$border-radius-base: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-round: 50%;

// 边框宽度
$border-width-thin: 1px;
$border-width-base: 2px;
$border-width-thick: 4px;

// 组件高度
$height-sm: 24px;
$height-base: 32px;
$height-lg: 40px;
$height-xl: 48px;

// 容器宽度
$container-sm: 576px;
$container-md: 768px;
$container-lg: 992px;
$container-xl: 1200px;
$container-xxl: 1400px;

// Z-index层级 (SCSS变量)
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;