// src/mocks/handlers.ts
import { http, HttpResponse } from 'msw';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../src/api/types';

const mockSymbols = {
  data: [
    { symbol: 'AAPL', name: 'Apple Inc.', market: 'US', industry: 'Technology', list_date: '1980-12-12' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.', market: 'US', industry: 'Technology', list_date: '2004-08-19' },
  ],
  total: 2,
  success: true,
  message: null
};

const mockKlineData = {
  data: {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    market: 'US',
    period: '1d',
    data: [
      { date: '2023-01-01', open: 150, high: 155, low: 149, close: 154, volume: 1000000, amount: null, turnover_rate: null, change_rate: null },
      { date: '2023-01-02', open: 154, high: 158, low: 153, close: 157, volume: 1200000, amount: null, turnover_rate: null, change_rate: null },
    ],
    latest_date: '2023-01-02',
    indicators: null
  },
  success: true,
  message: null
};

const mockStrategies: Strategy[] = [
  { 
    id: '1', 
    name: 'Test Strategy 1', 
    description: 'Description 1', 
    author: 'Author 1', 
    content: 'content 1',
    create_time: '2024-01-01T10:00:00Z',
    update_time: '2024-01-01T10:00:00Z',
    owner: 'Author 1',
    is_public: false,
    buy_factors: [{ class_name: 'AbuFactorBuyBreak', parameters: {} }],
    sell_factors: [{ class_name: 'AbuFactorSellNDay', parameters: { sell_n: 5 } }],
    position_strategy: { class_name: 'AbuPositionBase', parameters: {} },
    parameters: { capital: 100000, commission: 0.001 },
    tags: ['test', 'strategy'],
    umpire_rules: []
  },
  { 
    id: '2', 
    name: 'Test Strategy 2', 
    description: 'Description 2', 
    author: 'Author 2', 
    content: 'content 2',
    create_time: '2024-01-02T10:00:00Z',
    update_time: '2024-01-02T10:00:00Z',
    owner: 'Author 2',
    is_public: true,
    buy_factors: [{ class_name: 'AbuFactorBuyWD', parameters: {} }],
    sell_factors: [{ class_name: 'AbuFactorSellXD', parameters: { sell_x: 10 } }],
    position_strategy: { class_name: 'AbuPositionBase', parameters: {} },
    parameters: { capital: 200000, commission: 0.002 },
    tags: ['test', 'public'],
    umpire_rules: []
  },
];

export const handlers = [
  // Market API handlers
  http.get('/market/symbols', ({ request }) => {
    const url = new URL(request.url);
    if (url.searchParams.get('error') === 'unavailable') {
      return new HttpResponse('Data source unavailable', { status: 503 });
    }
    return HttpResponse.json({
      data: mockSymbols.data,
      total: mockSymbols.total,
      success: mockSymbols.success,
      message: mockSymbols.message
    });
  }),

  http.get('/market/kline/:symbol', ({ params, request }) => {
    const { symbol } = params;
    const url = new URL(request.url);
    const period = url.searchParams.get('period');

    if (symbol === 'INVALID') {
      return new HttpResponse('Invalid symbol', { status: 400 });
    }
    if (period === 'invalid-period') {
      return new HttpResponse('Invalid period', { status: 400 });
    }
    return HttpResponse.json({
      data: mockKlineData.data,
      success: mockKlineData.success,
      message: mockKlineData.message
    });
  }),

  // Strategy API handlers
  http.get('/api/strategies', async ({ request }) => {
    // 添加小延迟以确保loading状态能被测试捕获
    await new Promise(resolve => setTimeout(resolve, 10));
    const url = new URL(request.url);
    if (url.searchParams.get('error') === '500') {
      return new HttpResponse('Internal server error', { status: 500 });
    }
    if (url.searchParams.get('error') === '403') {
      return new HttpResponse('Access denied: insufficient permissions', { status: 403 });
    }
    return HttpResponse.json({ success: true, total: mockStrategies.length, data: mockStrategies });
  }),

  http.get('/api/strategies/:id', async ({ params }) => {
    // 添加小延迟以确保loading状态能被测试捕获
    await new Promise(resolve => setTimeout(resolve, 10));
    const { id } = params;
    if (id === 'invalid-id') {
      return new HttpResponse('Invalid strategy ID format', { status: 400 });
    }
    if (id === 'forbidden') {
      return new HttpResponse('Access denied: insufficient permissions', { status: 403 });
    }
    const strategy = mockStrategies.find(s => s.id === id);
    if (strategy) {
      return HttpResponse.json({ success: true, data: strategy });
    }
    return new HttpResponse('Strategy not found', { status: 404 });
  }),

  http.post('/api/strategies', async ({ request }) => {
    // 添加小延迟以确保loading状态能被测试捕获
    await new Promise(resolve => setTimeout(resolve, 10));
    const newStrategy = await request.json() as CreateStrategyRequest;
    if (!newStrategy.name) {
      return new HttpResponse('Name is required', { status: 400 });
    }
    if (newStrategy.name === 'Existing Strategy') {
      return new HttpResponse('Strategy with this name already exists', { status: 409 });
    }
    if (newStrategy.name === 'Forbidden Strategy') {
      return new HttpResponse('Access denied: insufficient permissions', { status: 403 });
    }
    const created: Strategy = { 
      id: '3', 
      ...newStrategy, 
      author: 'New Author', 
      content: 'New Content',
      create_time: new Date().toISOString(),
      update_time: new Date().toISOString(),
      owner: 'New Author',
      is_public: newStrategy.is_public || false,
      buy_factors: [],
      sell_factors: [],
      position_strategy: undefined,
      parameters: {},
      tags: [],
      umpire_rules: []
    };
    return HttpResponse.json({ success: true, data: created }, { status: 201 });
  }),

  http.put('/api/strategies/:id', async ({ params, request }) => {
    // 添加小延迟以确保loading状态能被测试捕获
    await new Promise(resolve => setTimeout(resolve, 10));
    const { id } = params;
    const updatedData = await request.json() as UpdateStrategyRequest;
    if (id === 'not-found') {
      return new HttpResponse('Strategy not found', { status: 404 });
    }
    if (id === 'conflict') {
      return new HttpResponse('Update conflict: strategy was modified by another user', { status: 409 });
    }
    if (id === 'forbidden') {
      return new HttpResponse('Access denied: insufficient permissions', { status: 403 });
    }
    const updated: Strategy = { 
      id: id as string, 
      name: updatedData.name ?? 'Test Strategy 1', 
      description: updatedData.description ?? 'Description 1', 
      author: 'Updated Author', 
      content: 'Updated Content',
      create_time: '2024-01-01T10:00:00Z',
      update_time: new Date().toISOString(),
      owner: 'Updated Author',
      is_public: false,
      buy_factors: [{ class_name: 'AbuFactorBuyBreak', parameters: {} }],
      sell_factors: [{ class_name: 'AbuFactorSellNDay', parameters: { sell_n: 5 } }],
      position_strategy: { class_name: 'AbuPositionBase', parameters: {} },
      parameters: { capital: 100000, commission: 0.001 },
      tags: ['test', 'updated'],
      umpire_rules: []
    };
    return HttpResponse.json({ success: true, data: updated });
  }),

  http.delete('/api/strategies/:id', async ({ params }) => {
    // 添加小延迟以确保loading状态能被测试捕获
    await new Promise(resolve => setTimeout(resolve, 10));
    const { id } = params;
    if (id === 'not-found') {
      return new HttpResponse('Strategy not found', { status: 404 });
    }
    if (id === 'in-use') {
      return new HttpResponse('Cannot delete strategy: strategy is currently in use', { status: 400 });
    }
    if (id === 'forbidden') {
      return new HttpResponse('Access denied: insufficient permissions', { status: 403 });
    }
    return new HttpResponse(null, { status: 204 });
  }),

  // Client test handlers
  http.get('/api/test-success', async () => {
    // 添加小延迟以确保loading状态能被测试捕获
    await new Promise(resolve => setTimeout(resolve, 10));
    return HttpResponse.json({ message: 'Success' });
  }),
  http.get('/api/test-notfound', () => {
    return new HttpResponse(null, { status: 404 });
  }),
  http.get('/api/test-servererror', () => {
    return new HttpResponse(null, { status: 500 });
  }),
  http.get('/api/network-error', () => {
    return HttpResponse.error();
  }),

  // Backtest API handlers
  http.post('/api/backtest/run', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const config = await request.json();
    
    // Invalid configuration parameters
    if (!config || typeof config !== 'object' || !('strategy_id' in config) || !('symbol' in config) || config.start_date === 'invalid-date') {
      return new HttpResponse('Invalid configuration parameters', { status: 400 });
    }
    
    // Strategy not found
    if (config.strategy_id === 'non-existent-strategy') {
      return new HttpResponse('Strategy not found', { status: 404 });
    }
    
    // Permission denied
    if (config.strategy_id === 'unauthorized-strategy') {
      return new HttpResponse('Permission denied', { status: 403 });
    }
    
    // Concurrent limit exceeded
    if (config.strategy_id === 'limit-exceeded-strategy') {
      return new HttpResponse('Concurrent backtest limit exceeded', { status: 429 });
    }
    
    // Invalid date range
    if (config.start_date && config.end_date && config.start_date > config.end_date) {
      return new HttpResponse('Invalid date range', { status: 400 });
    }
    
    // Invalid capital or commission
    if (config.capital < 0 || config.commission > 1) {
      return new HttpResponse('Invalid capital or commission', { status: 400 });
    }
    
    // Insufficient historical data
    if (config.symbol === 'NEW_SYMBOL') {
      return new HttpResponse('Insufficient historical data', { status: 400 });
    }
    
    const mockTask = {
      id: 'backtest-' + Date.now(),
      strategy_id: config.strategy_id,
      strategy_name: 'Test Strategy',
      symbol: config.symbol,
      start_date: config.start_date,
      end_date: config.end_date,
      status: 'pending',
      progress: 0,
      created_at: new Date().toISOString(),
      config: config
    };
    
    return HttpResponse.json({ success: true, data: mockTask }, { status: 201 });
  }),

  http.get('/api/backtest/results/:id', async ({ params }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const { id } = params;
    
    // Backtest task not found
    if (id === 'non-existent') {
      return new HttpResponse('Backtest task not found', { status: 404 });
    }
    
    // Backtest still running
    if (id === 'running-backtest') {
      return HttpResponse.json({
        success: false,
        message: 'Backtest is still running',
        data: { progress: 75, status: 'running' }
      }, { status: 202 });
    }
    
    // Permission denied for private backtest
    if (id === 'private-backtest-id') {
      return new HttpResponse('Permission denied', { status: 403 });
    }
    
    // Corrupted backtest data
    if (id === 'corrupted-data-id') {
      return new HttpResponse('Corrupted backtest data', { status: 500 });
    }
    
    // Special test case with complete data structure
    if (id === 'test-backtest-id') {
      return HttpResponse.json({ 
        success: true, 
        data: {
          id: 'test-backtest-id',
          strategy_id: 'strategy-1',
          status: 'completed',
          total_return: 0.20,
          annual_return: 0.20,
          max_drawdown: -0.05,
          sharpe_ratio: 1.5,
          win_rate: 0.65,
          profit_loss_ratio: 1.8,
          total_trades: 50,
          winning_trades: 32,
          losing_trades: 18,
          trades: [],
          positions: [],
          equity_curve: [],
          created_at: '2024-01-01T10:00:00Z',
          completed_at: '2024-01-01T12:00:00Z'
        }
      });
    }
    
    const mockResult = {
      task_id: id,
      strategy_name: 'Test Strategy',
      symbol: 'AAPL',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      initial_capital: 100000,
      final_capital: 120000,
      metrics: {
        total_return: 0.20,
        annual_return: 0.20,
        max_drawdown: -0.05,
        sharpe_ratio: 1.5,
        win_rate: 0.65,
        profit_loss_ratio: 1.8,
        total_trades: 50,
        winning_trades: 32,
        losing_trades: 18,
        avg_holding_period: 15,
        volatility: 0.15
      },
      trades: [],
      positions: [],
      equity_curve: [],
      generated_at: new Date().toISOString()
    };
    
    return HttpResponse.json({ success: true, data: mockResult });
  }),

  http.get('/api/backtest/history', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('page_size') || '20');
    const strategyId = url.searchParams.get('strategy_id');
    const status = url.searchParams.get('status');
    const startDate = url.searchParams.get('start_date');
    const endDate = url.searchParams.get('end_date');
    
    // Validate pagination parameters
    if (page < 1 || pageSize <= 0) {
      return new HttpResponse('Invalid pagination parameters', { status: 400 });
    }
    
    // Validate page size limit
    if (pageSize > 100) {
      return new HttpResponse('Page size exceeds maximum limit', { status: 400 });
    }
    
    // Validate date range
    if (startDate && endDate && startDate > endDate) {
      return new HttpResponse('Invalid date range', { status: 400 });
    }
    
    let mockHistory = [
      {
        id: 'backtest-1',
        strategy_id: 'strategy-1',
        strategy_name: 'Test Strategy 1',
        symbol: 'AAPL',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        status: 'completed',
        progress: 100,
        created_at: '2024-01-01T10:00:00Z',
        completed_at: '2024-01-01T11:30:00Z',
        config: {}
      },
      {
        id: 'backtest-2',
        strategy_id: 'strategy-2',
        strategy_name: 'Test Strategy 2',
        symbol: 'GOOGL',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        status: 'running',
        progress: 50,
        created_at: '2024-01-01T12:00:00Z',
        started_at: '2024-01-01T12:01:00Z',
        config: {}
      }
    ];
    
    // 应用筛选
    if (strategyId) {
      mockHistory = mockHistory.filter(task => task.strategy_id === strategyId);
    }
    if (status) {
      mockHistory = mockHistory.filter(task => task.status === status);
    }
    
    // Handle empty results for high page numbers
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedItems = page === 999 ? [] : mockHistory.slice(startIndex, endIndex);
    
    return HttpResponse.json({
      success: true,
      data: paginatedItems,
      total: mockHistory.length,
      page: page,
      page_size: pageSize
    });
  }),

  http.post('/api/backtest/stop/:id', async ({ params }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const { id } = params;
    
    // Cannot stop completed backtest
    if (id === 'completed-backtest') {
      return new HttpResponse('Cannot stop completed backtest', { status: 400 });
    }
    
    // Backtest task not found
    if (id === 'non-existent') {
      return new HttpResponse('Backtest task not found', { status: 404 });
    }
    
    // Permission denied for stopping others' backtest
    if (id === 'others-backtest-id') {
      return new HttpResponse('Permission denied', { status: 403 });
    }
    
    // Concurrent stop requests
    if (id === 'stopping-backtest') {
      return new HttpResponse('Backtest is already being stopped', { status: 409 });
    }
    
    const stoppedTask = {
      id: id,
      strategy_id: 'strategy-1',
      strategy_name: 'Test Strategy',
      symbol: 'AAPL',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      status: 'stopped',
      progress: 75,
      created_at: '2024-01-01T10:00:00Z',
      started_at: '2024-01-01T10:01:00Z',
      stopped_at: new Date().toISOString(),
      config: {}
    };
    
    return HttpResponse.json({ success: true, data: stoppedTask });
  }),

  // Factors API handlers
  http.get('/api/factors', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('page_size') || '20');
    const category = url.searchParams.get('category');
    const search = url.searchParams.get('search');
    const sortBy = url.searchParams.get('sort_by') || 'name';
    const sortOrder = url.searchParams.get('sort_order') || 'asc';
    
    let mockFactors = [
      {
        id: 'factor-1',
        name: 'RSI',
        description: '相对强弱指数，用于衡量价格变动的速度和变化',
        category: 'technical',
        data_type: 'numeric',
        formula: 'RSI = 100 - (100 / (1 + RS))',
        parameters: [{
          name: 'period',
          type: 'number',
          default_value: 14,
          description: '计算周期',
          required: true,
          min_value: 1,
          max_value: 100
        }],
        dependencies: ['close_price'],
        frequency: 'daily',
        lookback_period: 14,
        is_builtin: true,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-01T10:00:00Z',
        usage_count: 150,
        rating: 4.5
      },
      {
        id: 'factor-2',
        name: 'MACD',
        description: '移动平均收敛散度指标',
        category: 'technical',
        data_type: 'numeric',
        formula: 'MACD = EMA12 - EMA26',
        parameters: [],
        dependencies: ['close_price'],
        frequency: 'daily',
        lookback_period: 26,
        is_builtin: true,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-01T10:00:00Z',
        usage_count: 120,
        rating: 4.2
      },
      {
        id: 'factor-3',
        name: 'PE_Ratio',
        description: '市盈率',
        category: 'fundamental',
        data_type: 'numeric',
        formula: 'PE = Price / EPS',
        parameters: [],
        dependencies: ['price', 'earnings_per_share'],
        frequency: 'daily',
        is_builtin: true,
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-01T10:00:00Z',
        usage_count: 200,
        rating: 4.8
      }
    ];
    
    // 应用筛选
    if (category) {
      mockFactors = mockFactors.filter(factor => factor.category === category);
    }
    if (search) {
      mockFactors = mockFactors.filter(factor => 
        factor.name.toLowerCase().includes(search.toLowerCase()) ||
        factor.description.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // 应用排序
    mockFactors.sort((a, b) => {
      let aVal = a[sortBy as keyof typeof a];
      let bVal = b[sortBy as keyof typeof b];
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase();
        bVal = (bVal as string).toLowerCase();
      }
      
      if (sortOrder === 'desc') {
        // 处理aVal为undefined的情况
        if (aVal === undefined) return 1;
        if (bVal === undefined) return -1;
        return aVal < bVal ? 1 : -1;
      }
      // 处理aVal为undefined的情况
      if (aVal === undefined) return 1;
      if (bVal === undefined) return -1;
      return aVal > bVal ? 1 : -1;
    });
    
    return HttpResponse.json({
      success: true,
      data: mockFactors,
      total: mockFactors.length,
      page: page,
      page_size: pageSize
    });
  }),

  http.get('/api/factors/:id', async ({ params }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const { id } = params;
    
    if (id === 'non-existent') {
      return new HttpResponse('Factor not found', { status: 404 });
    }
    
    const mockFactor = {
      id: id,
      name: 'RSI',
      description: '相对强弱指数，用于衡量价格变动的速度和变化',
      category: 'technical',
      data_type: 'numeric',
      formula: 'RSI = 100 - (100 / (1 + RS))',
      parameters: [{
        name: 'period',
        type: 'number',
        default_value: 14,
        description: '计算周期',
        required: true,
        min_value: 1,
        max_value: 100
      }],
      dependencies: ['close_price'],
      frequency: 'daily',
      lookback_period: 14,
      is_builtin: true,
      created_at: '2024-01-01T10:00:00Z',
      updated_at: '2024-01-01T10:00:00Z',
      usage_count: 150,
      rating: 4.5
    };
    
    return HttpResponse.json({ success: true, data: mockFactor });
  }),

  http.post('/api/factors/custom', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const definition = await request.json();
    
    if (!definition || typeof definition !== 'object' || !('name' in definition)) {
      return new HttpResponse('Factor name is required', { status: 400 });
    }
    
    if (definition.name === 'RSI') {
      return new HttpResponse('Factor name already exists', { status: 409 });
    }
    
    if (definition.formula && definition.formula.includes('invalid')) {
      return new HttpResponse('Invalid factor definition syntax', { status: 400 });
    }
    
    const createdFactor = {
      id: 'custom-factor-' + Date.now(),
      ...definition,
      is_builtin: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      usage_count: 0,
      rating: 0
    };
    
    return HttpResponse.json({ success: true, data: createdFactor }, { status: 201 });
  }),

  http.post('/api/factors/test', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 10));
    const testRequest = await request.json();
    
    if (!testRequest || typeof testRequest !== 'object' || !('test_data' in testRequest) || testRequest.test_data?.start_date === 'invalid-date') {
      return new HttpResponse('Invalid data format', { status: 400 });
    }
    
    if (testRequest.definition?.formula?.includes('time.sleep')) {
      return new HttpResponse('Factor calculation timeout', { status: 408 });
    }
    
    if (testRequest.definition?.dependencies?.includes('non_existent_data')) {
      return new HttpResponse('Missing required dependencies', { status: 400 });
    }
    
    const mockTestResult = {
      success: true,
      values: [
        { date: '2023-01-01', symbol: 'AAPL', value: 65.5 },
        { date: '2023-01-02', symbol: 'AAPL', value: 67.2 },
        { date: '2023-01-03', symbol: 'AAPL', value: 63.8 }
      ],
      statistics: {
        count: 3,
        mean: 65.5,
        std: 1.7,
        min: 63.8,
        max: 67.2,
        null_count: 0,
        null_rate: 0
      },
      execution_time: 150,
      warnings: []
    };
    
    return HttpResponse.json({ success: true, data: mockTestResult });
  })
];