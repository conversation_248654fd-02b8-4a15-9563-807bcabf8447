# -*- coding: utf-8 -*-
"""
配置文件
"""
import os
from pathlib import Path
from pydantic_settings import BaseSettings
from pydantic import field_validator
from typing import List
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 获取当前文件的绝对路径
current_file = Path(__file__)
# 获取项目根目录路径 (backend/app/core -> backend/app -> backend -> abu_modern)
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent


class Settings(BaseSettings):
    """应用配置"""
    PROJECT_NAME: str = "ABU量化投资系统"
    PROJECT_DESCRIPTION: str = "基于abu量化框架的现代化改造版本"
    API_V1_STR: str = "/api/v1"
    
    # 项目根目录路径
    PROJECT_ROOT_PATH: str = str(BASE_DIR)
    
    # Tushare配置
    TUSHARE_TOKEN: str = os.getenv("TUSHARE_TOKEN", "")  # 从环境变量中读取TUSHARE_TOKEN
    
    # abupy 本地数据存储目录
    ABUPY_DATA_DIR: str = os.getenv("ABUPY_DATA_DIR", str(BASE_DIR / "test_data"))
    
    # 数据库配置
    DATABASE_URL: str = f"sqlite:///{str(BASE_DIR / 'data' / 'abu_modern.db')}"
    
    # 缓存配置
    ENABLE_CACHE: bool = True
    CACHE_EXPIRE_DAYS: int = 1

    # 本地数据源配置
    LOCAL_DATA_PATH: str = str(BASE_DIR / "data" / "market_data.h5")
    
    # CORS配置
    CORS_ORIGINS: List[str] = ["*"]  # 在生产环境中应该限制来源
    
    model_config = {
        # .env文件位于backend目录下
        "env_file": os.path.join(BASE_DIR, "backend", ".env"),
        "env_file_encoding": "utf-8",
        "extra": "ignore",
    }


settings = Settings()
