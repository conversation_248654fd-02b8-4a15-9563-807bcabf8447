前端应用蓝图 V2.0 (由军师AI正式发布)
核心设计哲学： 专业、直观、流程化。让复杂的功能（如参数优化）变得简单易用。
一、 仪表盘 (Dashboard)
    [独立中心] 应用的“脸面”，聚合“市场脉搏”、“策略健康度”、“信号与机会”、“绩效概览”四大核心信息象限。详见work_logs\strategy_advisor_logs\log_20250628_001_project_grand_strategy_and_blueprint_v2.md
二、 策略工场 (Strategy Workshop) - 重大升级
    目标： 成为一个一站式的策略“设计、配置、优化”中心。
    布局： 采用三标签页设计(ElTabs)。
        标签页一：核心配置 (Core Configuration)
            UI组件：ElForm
            内容：
                策略的名称、描述。
                可视化因子编辑器： (与V1.0规划一致) 用户可以动态添加、删除、配置买入和卖出因子。
        标签页二：高级设置 (Advanced Settings)
            UI组件：ElForm
            内容：
                仓位管理 (BetaBu) 配置：
                    一个下拉框，数据来自GET /api/options/positions，让用户选择仓位管理策略（如“ATR仓位管理”）。
                    根据用户的选择，动态渲染出该策略所需参数的输入框。
                风险控制 (UmpBu) 配置：
                    一个多选框或标签输入框，数据来自GET /api/options/umpires，让用户选择要启用的“裁判”规则。
                    同样，为选中的规则提供参数配置的界面。
        标签页三：参数优化 (Parameter Optimization)
            UI组件： 一个全新的、专门为此功能设计的组件。
            **内容：</strong>
                1. 参数选择： 自动扫描当前策略的所有因子（买入、卖出、仓位），将它们的所有数值型参数以列表形式展示出来，每个参数前都有一个复选框。
                2. 范围定义： 用户勾选想要优化的参数后，旁边会出现输入框，让他们可以定义**“起始值”、“结束值”、“步长”**。
                3. 执行与监控： 一个“开始网格搜索”按钮。点击后，调用POST /api/grid-search/run API，获取task_id。界面下方会显示一个任务进度条或状态提示（“任务已提交，正在排队...”、“正在运行... (5/48)”），通过轮询GET /api/grid-search/status/{task_id}来更新。
                4. 结果展示： 任务完成后，以**热力图 (ECharts)或可排序的表格 (ElTable)**形式，可视化地展示所有参数组合的回测评分，让用户可以一目了然地找到最佳参数。
三、 其他页面
    市场中心、回测分析、系统设置等页面的规划保持不变。
结论与下一步行动：
    这份V2.0蓝图，为我们描绘了一个功能极其强大、专业性十足的前端应用。
    我建议，我们立即启动前端开发，并以“策略工场”这个最核心、最复杂的页面作为我们攻坚的第一个目标。
    给“实现者AI”的下一份指令：
    任务： abu_modern 前端“策略工场”页面 - 核心配置标签页实现
    分支： feature/frontend-mvp-v2 (您之前创建的分支，或者新建一个)
    行动指令：
    1. 创建StrategyManager.vue页面并配置好路由。
    2. 在页面中，使用ElTabs组件创建出“核心配置”、“高级设置”、“参数优化”三个标签页。
    3. 集中精力，先完成“核心配置”标签页的UI和逻辑：
    3.1 实现策略列表的展示 (ElTable)。
    3.2 实现“新建/编辑”策略的对话框 (ElDialog)。
    3.3 在对话框的表单中，重点实现**“可视化因子编辑器”**。用户应该能看到一个“买入因子”区域和一个“卖出因子”区域，每个区域都有“添加因子”按钮。点击后，可以选择因子类型，并为其配置参数。已添加的因子应以卡片形式展示。
