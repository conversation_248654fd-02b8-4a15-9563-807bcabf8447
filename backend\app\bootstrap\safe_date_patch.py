def install(raise_on_yyyymmdd=True, stack_on_print=True):
    import builtins, traceback, numpy as np, pandas as pd, inspect

    _orig_print = builtins.print
    _orig_print("[SAFE_DATE_PATCH] install enter")

    # 1) 更稳健的内置类型修复：用字面量取“真类型”，避免调用 type()
    try:
        real_str = ("").__class__           # <class 'str'>
        real_bytes = (b"").__class__        # <class 'bytes'>
        real_tuple = (()).__class__         # <class 'tuple'>
        real_type = (real_str).__class__    # <class 'type'>
        real_object = int.__mro__[-1]       # <class 'object'>

        wanted = {"str": real_str, "bytes": real_bytes, "tuple": real_tuple,
                  "type": real_type, "object": real_object}

        for name, real in wanted.items():
            cur = getattr(builtins, name, None)
            if cur is not real and inspect.isclass(real):
                _orig_print(f"[SAFE_DATE_PATCH] restoring builtins.{name}: {cur} -> {real}")
                setattr(builtins, name, real)

        _orig_print("[SAFE_DATE_PATCH] DIAG builtins: "
                    f"str={builtins.str} ({type(builtins.str)}), "
                    f"bytes={builtins.bytes} ({type(builtins.bytes)}), "
                    f"tuple={builtins.tuple} ({type(builtins.tuple)}), "
                    f"object={builtins.object} ({type(builtins.object)}), "
                    f"type={builtins.type} ({type(builtins.type)})")
        _orig_print(f"[SAFE_DATE_PATCH] DIAG identity str_is_real={builtins.str is real_str}")
    except Exception as e:
        _orig_print(f"[SAFE_DATE_PATCH] builtins heal soft-failed: {e}")

    RAISE = raise_on_yyyymmdd
    STACK_PRINT = stack_on_print

    def _log_stack(prefix):
        try:
            _orig_print(prefix)
            _orig_print("".join(traceback.format_stack(limit=20)))
        except Exception:
            pass
        
    # 2) 明确不补丁 numpy.datetime64，避免 pandas 中的 issubclass 断言崩溃
    _orig_print("[SAFE_DATE_PATCH] np.datetime64 patch disabled (by design)")
    _orig_print(f"[SAFE_DATE_PATCH] DIAG numpy.datetime64={np.datetime64} ({type(np.datetime64)})")

    # 3) pandas.to_datetime 补丁（不动 pd.Timestamp）
    if not getattr(pd, "_safe_to_datetime_patched", False):
        _orig_to_datetime = pd.to_datetime
        def _safe_to_datetime(arg, *a, **k):
            try:
                if ("format" not in k) and ("unit" not in k):
                    # 仅拦截标量
                    if not hasattr(arg, "__iter__") or isinstance(arg, (builtins.str, bytes)):
                        sarg = None
                        if isinstance(arg, (builtins.str, bytes)) and str(arg).isdigit() and len(str(arg)) == 8:
                            sarg = str(arg)
                        else:
                            i = int(arg)
                            if 19000101 <= i <= 20991231:
                                sarg = str(i)
                        if sarg is not None:
                            if RAISE:
                                _log_stack(f"[SAFE_DATE_PATCH] pd.to_datetime got YYYYMMDD: {arg!r}")
                                raise RuntimeError(f"pd.to_datetime received YYYYMMDD: {arg!r}")
                            return _orig_to_datetime(sarg, format="%Y%m%d")
            except Exception:
                pass
            return _orig_to_datetime(arg, *a, **k)
        pd.to_datetime = _safe_to_datetime
        pd._safe_to_datetime_patched = True
        _orig_print("[SAFE_DATE_PATCH] pd.to_datetime patched")

    # 4) print 钩子：用原始 print 打栈，避免递归
    if STACK_PRINT and not getattr(builtins, "_print_with_stack_patched", False):
        KEYS = ("Checking AbuFactorBuyBreak", "1970-01-01 00:00:00.020", "DATE_DEBUG")
        def _print_with_stack(*args, **kwargs):
            s = " ".join(str(a) for a in args)
            if any(k in s for k in KEYS):
                _orig_print(*args, **kwargs)
                _log_stack("[SAFE_DATE_PATCH] print hit key")
            else:
                _orig_print(*args, **kwargs)
        builtins.print = _print_with_stack
        builtins._print_with_stack_patched = True
        _orig_print("[SAFE_DATE_PATCH] builtins.print patched")