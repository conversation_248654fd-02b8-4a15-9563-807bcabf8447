一、回测与方案标签页 - TDD红阶段提示词 (结构性测试需求包)
任务： abu_modern “策略工场-回测与方案”标签页 - 功能TDD测试用例编写
核心参考文档 (必读)：
log_20250813_001_frontend_final_strategy_architecture_memorandum.md (特别是关于回测与方案标签页的UI/UX设计部分)
log_20250813_005_abupy_backtest_input_format_report.md (回测API的输入参数格式)
第一部分：高层级用户故事
    - 用户故事1 (发起回测):
        作为一个策略研究员，
        我希望能够在“回测与方案”标签页中，详细配置本次回测的所有运行时参数（如股票池、周期、资金、风控模型等），并发起一次回测，
        以便我能验证我设计的策略在特定历史条件下的表现。
    - 用户故事2 (追溯历史回测记录):
        作为一个策略研究员，
        我希望能够看到当前策略的所有历史回测记录，并能点击查看任何一次的详细报告，
        以便我能对比不同参数下的策略表现，并追溯我的研究过程。
第二部分：视觉验收标准
    我们的目标是实现设计文档中“回测与方案”标签页的UI。请注意以下关键点：
    - 整体布局：
        - 页面必须清晰地分为上下两个部分：上方的**“执行回测”配置区** (data-testid="backtest-launcher") 和下方的**“回测历史”区** (data-testid="backtest-history")。
    - “执行回测”配置区：
        - 必须包含以下所有表单控件，并绑定到正确的状态：
            - 股票池 (choice_symbols): 一个data-testid="choice-symbols-select"的ElSelect组件。
            - 回测周期 (start_date, end_date): 两个data-testid="date-picker"的ElDatePicker组件。
            - 初始资金 (read_cash): 一个data-testid="initial-capital-input"的ElInputNumber组件。
            - 裁判系统 (UmpBu)： 一个data-testid="umpire-switch"的ElSwitch开关，和一个在开关打开时才可见的data-testid="umpire-market-select"的 ElSelect下拉框。
            - 执行按钮： 一个data-testid="start-backtest-btn"的主操作按钮。
    - “回测历史”区：
        - 必须包含一个data-testid="history-table"的ElTable组件。
        - 表格的每一行，必须包含一个data-testid="view-report-btn"的“查看报告”按钮。
第三部分：交互与逻辑测试点
    请为以上功能编写一套完整的集成测试用例 (StrategyWorkshop.backtest.integration.test.ts)，必须覆盖以下所有场景：
    场景一：发起新回测
        - 测试1.1 (按钮状态)：
            Given: 用户已在“核心配置”中设置好有效的买入/卖出因子。
            When: 用户切换到“回测与方案”标签页。
            Then: “开始回测”按钮 (start-backtest-btn) 应该是可用的 (enabled)。
        - 测试1.2 (API调用)：
            Given: 用户已在表单中填写了所有必需的回测参数（股票池、周期、资金）。
            When: 用户点击“开始回测”按钮。
            Then:
            useStrategyStore的executeStrategy action必须被调用。
            传递给executeStrategy的strategyId和params对象，必须与当前选中的策略ID和用户在表单中输入的数据完全一致。
            (Mock API) 模拟后端成功返回回测结果。
            Vue Router的push方法必须被调用，并且跳转的目标URL应该符合格式 /backtest/report/{report_id}。
    场景二：回测历史
        - 测试2.1 (数据加载)：
            Given: 一个策略被选中。
            When: “回测与方案”标签页被挂载或变为可见。
            Then: 一个用于获取该策略回测历史的action（例如fetchBacktestHistory）必须被调用。
        - 测试2.2 (列表渲染)：
            Given: (Mock Store) Store中的回测历史列表包含2条记录。
            When: 组件渲染完成。
            Then: ElTable (history-table) 中必须渲染出2行数据。
        - 测试2.3 (查看报告跳转)：
            Given: 回测历史列表已渲染。
            When: 用户点击第一条记录的“查看报告”按钮 (view-report-btn)。
            Then: Vue Router的push方法必须被调用，并且跳转的目标URL必须包含第一条记录的ID。
    场景三：边界与错误处理
        - 测试3.1 (核心配置未完成)：
            Given: 用户新建一个策略，但没有配置任何因子。
            When: 用户切换到“回测与方案”标签页。
            Then: “开始回测”按钮 (start-backtest-btn) 应该是禁用的 (disabled)，并可以有一个提示信息。

二、回测与方案标签页 - TDD绿阶段提示词

你的身份是abu_modern实现者AI。

我们目前正处于TDD流程中“从红到绿”转换的关键阶段。

【已完成】“红”阶段：已成功编写并通过12个高质量的测试用例，形成了完整的“测试剧本”。

【即将开始】“绿”阶段：你的核心任务是修改`StrategyWorkshop.vue`文件，使其通过所有已编写的测试用例。

**下一步行动：** 执行以下指令，让“演员”上台表演。

**给abu_modern实现者AI的明确指令：**

**任务：** 实现abu_modern“策略工-场-回测与方案”标签页功能 (TDD绿阶段)

**核心输入 (你的“剧本”)：**

1.  `tests/integration/StrategyWorkshop.backtest.integration.test.ts` (这是你必须通过的考验)

2.  `log_20250813_001_frontend_final_strategy_architecture_memorandum.md` (这是你的视觉和交互参考)

**行动指令：**

**1. UI实现 (搭建舞台)：**

*   **定位文件：** `frontend/src/views/StrategyWorkshop.vue`。

*   **定位/创建ElTabPane：** 在`ElTabs`中，找到或创建“回测与方案”的`ElTabPane`。

*   **构建HTML模板：** 在该`ElTabPane`内部，严格按照设计文档和测试脚本中的`data-testid`，搭建“执行回测”和“回测历史”两个区域的完整HTML模板。这包括使用所有必要的Element Plus组件，如`ElForm`, `ElSelect`, `ElDatePicker`, `ElTable`等。

**2. 逻辑实现 (连接线路)：**

*   在`<script setup>`中，实现所有必要的逻辑以通过测试。这包括：

*   **状态绑定：** 将表单控件的值与一个本地的`ref`或`reactive`对象进行双向绑定。

*   **条件渲染/禁用：** 实现一个计算属性（`computed`），根据“核心配置”中是否存在买卖因子，来决定“开始回测”按钮的`disabled`状态。

*   **事件处理：**

*   在组件挂载时，调用Store的`fetchBacktestHistory` action。

*   为“开始回测”按钮绑定`@click`事件处理器，该处理器将调用Store的`executeStrategy` action。

*   为“查看报告”按钮绑定`@click`事件处理器，该处理器将调用Vue Router的`push`方法。

**最终目标：**

*   运行`npx vitest run tests/integration/StrategyWorkshop.backtest.integration.test.ts`，直到所有12个测试用例全部通过。

三、回测与方案标签页 - TDD绿阶段视觉重构提示词 (结构性需求包)

任务： abu_modern “策略工场-回测与方案”标签页 - TDD绿阶段视觉重构

核心参考文档 (必读)：
log_20250813_001_frontend_final_strategy_architecture_memorandum.md (特别是关于回测与方案标签页的UI/UX设计部分)
log_20250813_005_abupy_backtest_input_format_report.md (回测API的输入参数格式)
第一部分：高层级用户故事
    - 用户故事1 (发起回测):
        作为一个策略研究员，
        我希望能够在“回测与方案”标签页中，详细配置本次回测的所有运行时参数（如股票池、周期、资金、风控模型等），并发起一次回测，
        以便我能验证我设计的策略在特定历史条件下的表现。
    - 用户故事2 (追溯历史回测记录):
        作为一个策略研究员，
        我希望能够看到当前策略的所有历史回测记录，并能点击查看任何一次的详细报告，
        以便我能对比不同参数下的策略表现，并追溯我的研究过程。
第二部分：视觉验收标准
    - 目的： 提供无可辩驳的、像素级的实现目标。
    - 内容：
        - 高保真设计图 (作为附件)： “附件中的图1、图2、图3，是我们这次任务唯一的、最终的视觉参考标准。”
        - 关键组件与布局注解：
            “布局要求：
            必须使用ElCard、ElRow和ElCol，严格复现设计图中的两栏式表单布局和上下分组。
            所有标题（‘执行回测’、‘回测历史’）和字段标签（‘股票池’、‘回测周期’）的文本和样式，必须与设计图一致。”
    - DOM结构契约 (data-testid)：
        1. 执行回测配置区 (The Launchpad)
            - 标题： 执行回测
            - UI组件 (ElForm)：
                - choice_symbols (股票池): 一个可多选、可搜索、可自定义输入的ElSelect。
                - stock_picks (选股策略): 一个ElSelect下拉框，选项动态加载自“选股器”模块已保存的策略。
                - 回测周期 (n_folds vs start/end): 一个ElRadioGroup单选按钮组，用于切换并条件性地显示ElInputNumber或ElDatePicker。
                - commission_dict (手续费): 一个ElSelect下拉框，选项加载自“系统设置”中已保存的模板。
                - UmpBu (裁判系统): 一个ElSwitch开关“启用裁判系统”，打开后，显示一个ElSelect下拉框，用于选择要应用的已训练模型市场。
                - read_cash (初始资金): 一个ElInputNumber。
                - 执行按钮： 一个巨大的、蓝色的主操作按钮 ElButton，文本为 “开始回测”。

        2. 回测历史与方案区 (The Archive)
            - 标题： 回测历史
            - UI组件：
                - 一个ElTable，展示当前策略的所有历史回测记录。
                - 表格列： 回测名称（可自定义）、回测时间、核心指标（总收益、夏普等）、状态（已完成/失败）。
                - 操作列：
                    - **“查看报告”**按钮。
                    - “删除”按钮。

第三部分：交互与逻辑实现
   - 触发：
      - 用户在“执行回测配置区”点击**“开始回测”**，并且API成功返回结果。
      - 用户在“回测历史区”点击某一条记录的**“查看报告”**按钮。
   - 交互：
      - 页面跳转： 使用Vue Router，将用户导航到一个全新的、独立的页面，URL为 /backtest/report/{report_id}。
   - 数据绑定要求：
     - 逻辑要求：
        表单中的所有输入控件，都必须与一个本地的、响应式的ref或reactive对象进行双向绑定 (v-model)。
        ‘开始回测’按钮的disabled状态，必须由一个计算属性（computed）来控制，该属性依赖于‘核心配置’中是否存在买卖因子。
      - API集成要求：
        集成要求：
        在浏览器开发环境中，必须能够与真实的后端API进行交互。
        请使用浏览器开发者工具的网络(Network)标签页，手动验证所有相关API（获取历史、发起回测）的调用是成功的，并且请求和响应的数据格式正确。

