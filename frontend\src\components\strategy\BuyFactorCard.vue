<template>
  <div class="buy-factor-card">
    <el-card class="factor-item-card is-hover-shadow">
      <div class="factor-card-header">
        <span class="factor-title" data-testid="buy-factor-name">{{ getFriendlyFactorName(buyFactor) }}</span>
        <div class="factor-actions">
          <el-button 
            size="small" 
            type="primary" 
            plain
            @click="handleEditFactor"
          >
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="info" 
            plain
            data-testid="exclusive-sell-rule-btn"
            @click="handleOpenExclusiveSellRulesDialog"
          >
            专属卖出规则
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            plain
            data-testid="remove-buy-factor"
            @click="handleDeleteFactor"
          >
            删除
          </el-button>
        </div>
      </div>
      <div class="factor-params">
        <div v-if="hasParameters(buyFactor)" class="param-list">
          <div 
            v-for="[key, value] in Object.entries(getFactorParameters(buyFactor))" 
            :key="key"
            class="param-item"
          >
            <span class="param-name">{{ key }}:</span>
            <span class="param-value">{{ formatParameterValue(value) }}</span>
          </div>
        </div>
        <div v-else class="no-params">无参数</div>
      </div>
      
      <!-- 专属卖出规则显示区域 -->
      <div 
        v-if="hasExclusiveSellRules" 
        class="exclusive-rules-section"
        data-testid="exclusive-rules-display"
      >
        <div class="exclusive-rules-header">
          <span class="exclusive-rules-label">专属卖出规则:</span>
        </div>
        <div class="exclusive-rules-list">
          <el-tag
            v-for="sellRule in buyFactor.sell_factors"
            :key="sellRule.id"
            type="info"
            size="small"
            closable
            @close="handleRemoveExclusiveSellRule(sellRule.id)"
          >
            {{ sellRule.name }}
          </el-tag>
        </div>
      </div>
    </el-card>
    
    <!-- 专属卖出规则对话框 -->
    <ExclusiveSellRulesDialog
      v-model:visible="isExclusiveSellRulesDialogVisible"
      :buy-factor="buyFactor"
      :title="`为'${buyFactor.name}'配置专属卖出规则`"
      @update:exclusive-sell-rules="handleExclusiveSellRulesUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  getFriendlyFactorName, 
  getFactorParameters, 
  formatParameterValue,
  hasParameters 
} from '@/utils/factorUtils'
import type { BuyFactor } from '@/types/factor'
import ExclusiveSellRulesDialog from './ExclusiveSellRulesDialog.vue'

interface Props {
  buyFactor: BuyFactor
}

interface Emits {
  (e: 'edit'): void
  (e: 'delete'): void
  (e: 'update:exclusive-sell-rules', buyFactorId: string, sellFactors: any[]): void
  (e: 'remove-exclusive-sell-rule', sellFactorId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 对话框状态
const isExclusiveSellRulesDialogVisible = ref(false)

// 计算属性
const hasExclusiveSellRules = computed(() => {
  const sellFactors = props.buyFactor.sell_factors
  return sellFactors && 
         Array.isArray(sellFactors) && 
         sellFactors.length > 0
})

// 事件处理
const handleEditFactor = () => {
  emit('edit')
}

const handleDeleteFactor = () => {
  emit('delete')
}

const handleOpenExclusiveSellRulesDialog = () => {
  isExclusiveSellRulesDialogVisible.value = true
}

const handleExclusiveSellRulesUpdate = (buyFactorId: string, sellFactors: any[]) => {
  emit('update:exclusive-sell-rules', buyFactorId, sellFactors)
}

const handleRemoveExclusiveSellRule = (sellFactorId: string) => {
  emit('remove-exclusive-sell-rule', sellFactorId)
}
</script>

<style scoped>
@import '@/styles/components/_factor-card.scss';

.buy-factor-card {
  width: 100%;
}

.factor-card-header {
  margin-bottom: var(--space-xs);
}

.exclusive-rules-section {
  margin-top: var(--space-md);
  padding-top: var(--space-xs);
  border-top: 1px solid var(--border-color-light);
}

.exclusive-rules-header {
  margin-bottom: var(--space-xs);
}

.exclusive-rules-label {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.exclusive-rules-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}
</style>
