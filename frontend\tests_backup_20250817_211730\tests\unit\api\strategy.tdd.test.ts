import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { getStrategies, getStrategy, createStrategy, updateStrategy, deleteStrategy } from '../../../src/api/strategy';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../../src/api/types';

/**
 * 策略API测试套件 - TDD版本
 * 
 * 本测试文件专注于核心功能的TDD测试，包括：
 * - 基本的CRUD操作
 * - 核心错误处理机制
 * - 基础的响应数据验证
 */

// 简化的Mock数据 - TDD专用
const mockStrategy: Strategy = {
  id: '1',
  name: 'TDD Test Strategy',
  description: 'Simple test strategy',
  author: 'Test Author',
  content: 'test content',
  create_time: '2024-01-01T10:00:00Z',
  update_time: '2024-01-01T10:00:00Z',
  owner: 'Test Author',
  is_public: false,
  buy_factors: [],
  sell_factors: [],
  position_strategy: { class_name: 'AbuPositionBase', parameters: {} },
  parameters: { capital: 100000 },
  tags: [],
  umpire_rules: []
};

// 简化的MSW handlers - TDD专用
const handlers = [
  // getStrategies - 基础成功场景
  http.get('/api/strategies', () => {
    return HttpResponse.json({
      success: true,
      data: [mockStrategy]
    });
  }),
  
  // getStrategy - 基础成功场景
  http.get('/api/strategies/:id', ({ params }) => {
    const { id } = params;
    if (id === '1') {
      return HttpResponse.json({
        success: true,
        data: mockStrategy
      });
    }
    return HttpResponse.json(
      { success: false, message: 'Strategy not found' },
      { status: 404 }
    );
  }),
  
  // createStrategy - 基础成功场景
  http.post('/api/strategies', () => {
    return HttpResponse.json({
      success: true,
      data: { ...mockStrategy, id: '2' }
    });
  }),
  
  // updateStrategy - 基础成功场景
  http.put('/api/strategies/:id', ({ params }) => {
    const { id } = params;
    return HttpResponse.json({
      success: true,
      data: { ...mockStrategy, id, name: 'Updated Strategy' }
    });
  }),
  
  // deleteStrategy - 基础成功场景
  http.delete('/api/strategies/:id', () => {
    return HttpResponse.json({
      success: true,
      data: null
    });
  })
];

const server = setupServer(...handlers);

describe('Strategy API - TDD', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('getStrategies', () => {
    it('should return list of strategies successfully', async () => {
      const response = await getStrategies();
      expect(response).toEqual([mockStrategy]);
      expect(response.length).toBe(1);
    });

    it('should handle server error (500)', async () => {
      server.use(
        http.get('/api/strategies', () => {
          return HttpResponse.json(
            { success: false, message: 'Internal server error' },
            { status: 500 }
          );
        })
      );

      await expect(getStrategies()).rejects.toThrow('Internal server error');
    });
  });

  describe('getStrategy', () => {
    it('should return single strategy successfully', async () => {
      const response = await getStrategy('1');
      expect(response).toEqual(mockStrategy);
    });

    it('should handle strategy not found (404)', async () => {
      await expect(getStrategy('999')).rejects.toThrow('Strategy not found');
    });
  });

  describe('createStrategy', () => {
    it('should create strategy successfully', async () => {
      const newStrategy: CreateStrategyRequest = {
        name: 'New Strategy',
        description: 'New Description',
        content: 'New Content',
        is_public: false,
        buy_factors: [],
        sell_factors: [],
        parameters: {}
      };

      const response = await createStrategy(newStrategy);
      expect(response).toHaveProperty('id', '2');
      expect(response.name).toBe('TDD Test Strategy');
    });

    it('should handle validation error (400)', async () => {
      server.use(
        http.post('/api/strategies', () => {
          return HttpResponse.json(
            { success: false, message: 'Name is required' },
            { status: 400 }
          );
        })
      );

      const invalidStrategy: CreateStrategyRequest = {
        name: '',
        description: '',
        content: '',
        is_public: false,
        buy_factors: [],
        sell_factors: [],
        parameters: {}
      };

      await expect(createStrategy(invalidStrategy)).rejects.toThrow('Name is required');
    });
  });

  describe('updateStrategy', () => {
    it('should update strategy successfully', async () => {
      const updates: UpdateStrategyRequest = { name: 'Updated Name' };
      const response = await updateStrategy('1', updates);
      expect(response.name).toBe('Updated Strategy');
    });

    it('should handle strategy not found (404)', async () => {
      server.use(
        http.put('/api/strategies/:id', () => {
          return HttpResponse.json(
            { success: false, message: 'Strategy not found' },
            { status: 404 }
          );
        })
      );

      const updates: UpdateStrategyRequest = { name: 'Updated Name' };
      await expect(updateStrategy('999', updates)).rejects.toThrow('Strategy not found');
    });
  });

  describe('deleteStrategy', () => {
    it('should delete strategy successfully', async () => {
      await expect(deleteStrategy('1')).resolves.toBeUndefined();
    });

    it('should handle strategy not found (404)', async () => {
      server.use(
        http.delete('/api/strategies/:id', () => {
          return HttpResponse.json(
            { success: false, message: 'Strategy not found' },
            { status: 404 }
          );
        })
      );

      await expect(deleteStrategy('999')).rejects.toThrow('Strategy not found');
    });
  });
});