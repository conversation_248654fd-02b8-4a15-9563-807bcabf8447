### 裁判系统训练流实现

#### 1. `umpire_adapter.py` 文件修改

- **目标**：在现有的裁判适配器中增加模型训练的功能。
- **具体操作**：
    - 添加了 `train_umpire_models` 函数，该函数接收裁判规则和K线数据管理器作为输入。
    - 实现了遍历规则、实例化裁判类并调用其 `fit` 方法进行训练的逻辑。
    - 添加了 `import pandas as pd` 以解决依赖问题。
- **结果**：`umpire_adapter.py` 现在同时支持预测时的裁判设置和独立的模型训练功能。

#### 2. `umpire_service.py` 文件创建

- **目标**：创建一个新的服务层，用于封装裁判模型训练的业务逻辑。
- **具体操作**：
    - 创建了 `backend/app/services/umpire_service.py` 文件。
    - 定义了 `UmpireService` 类，其中包含 `train_models` 方法。
    - `train_models` 方法负责解析输入数据，通过 `AbuDataCacheAdapter` 获取K线数据，并调用 `train_umpire_models` 执行训练。
    - 实现了完整的错误处理和依赖注入机制。
- **结果**：成功将训练逻辑封装在服务层中，实现了与API层的解耦。

#### 3. `umpire.py` API端点重构

- **目标**：重构现有的裁判API端点，使其与新创建的 `UmpireService` 集成。
- **具体操作**：
    - 完全重写了 `backend/app/api/endpoints/umpire.py` 文件的内容。
    - 移除了直接调用 `abupy` 底层函数的旧逻辑。
    - 创建了一个新的 `/train` POST 端点，该端点接收训练数据，并通过依赖注入调用 `UmpireService.train_models` 方法。
- **结果**：API层现在变得更加清晰，只负责路由和数据校验，业务逻辑完全由服务层处理。

#### 4. `router.py` 文件修改

- **目标**：将新的裁判API端点注册到主应用路由器中。
- **具体操作**：
    - 在 `backend/app/api/router.py` 中导入了 `umpire` 路由。
    - 使用 `api_router.include_router` 将其包含进来，并设置了 `/umpire` 前缀和 `Umpire` 标签。
    - 修复了之前错误的重复添加和命名不一致的问题。
- **结果**：裁判训练API已成功集成到应用中，可以通过 `/api/v1/umpire/train` 进行访问。

#### 5. `test_umpire_service.py` 文件创建

- **目标**：为新的 `UmpireService` 编写单元测试，确保其逻辑的正确性。
- **具体操作**：
    - 在 `backend/tests/services/` 目录下创建了 `test_umpire_service.py` 文件。
    - 使用 `pytest` 和 `unittest.mock` 编写了多个测试用例。
    - 测试覆盖了成功路径、参数错误、适配器异常和未预期异常等多种场景。
- **结果**：为 `UmpireService` 提供了可靠的测试保障，确保了代码质量。