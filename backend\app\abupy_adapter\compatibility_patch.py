# -*- coding: utf-8 -*-
"""
Python版本兼容性补丁
用于解决Python 3.12与abu项目的兼容性问题
"""
import sys
print(f"COMPAT_PATCH_DIAG: Loading compatibility_patch.py. sys.modules keys: {{'backend.app.services.market_service' in sys.modules}}, {{'backend.app.abupy_adapter.compat' in sys.modules}}")
if 'backend.app.services.market_service' in sys.modules:
    print(f"COMPAT_PATCH_DIAG: market_service found in sys.modules. Object: {{sys.modules['backend.app.services.market_service']}}")
    try:
        print(f"COMPAT_PATCH_DIAG: market_service.__name__: {{sys.modules['backend.app.services.market_service'].__name__}}")
    except Exception as e:
        print(f"COMPAT_PATCH_DIAG: Error accessing market_service attributes: {{e}}")

if 'backend.app.abupy_adapter.compat' in sys.modules:
    print(f"COMPAT_PATCH_DIAG: compat found in sys.modules. Object: {{sys.modules['backend.app.abupy_adapter.compat']}}")
    try:
        print(f"COMPAT_PATCH_DIAG: compat.__name__: {{sys.modules['backend.app.abupy_adapter.compat'].__name__}}")
    except Exception as e:
        print(f"COMPAT_PATCH_DIAG: Error accessing compat attributes: {{e}}")
import logging
import os

print("COMPAT_PATCH_DIAG: Finished top-level imports in compatibility_patch.py")

def apply_patches():
    """应用所有兼容性补丁"""
    # 为collections.Iterable应用补丁
    patch_collections_iterable()
    
    # 模拟IPython模块
    patch_ipython_module()
    
    # 对scipy模块应用补丁
    patch_scipy_module()
    
    # 使用独立的模拟模块文件
    from .mock_modules import install_mock_modules
    install_mock_modules()
    logging.info("已应用 mock_modules (如 ipywidgets) 模拟补丁。")
    
    # 其他潜在的补丁可以在这里添加

def patch_collections_iterable():
    """
    在collections模块中添加已移动到abc子模块的类型引用
    解决Python 3.12中各种类型被移动到collections.abc的问题
    """
    try:
        import collections
        from collections import abc
        
        # 需要补丁的类型列表
        abc_types = [
            'Iterable',
            'Mapping',
            'MutableMapping',
            'Sequence',
            'MutableSequence',
            'Set',
            'MutableSet',
            'Container',
            'Sized',
            'Callable'
        ]
        
        patched_count = 0
        
        # 将abc中的类型添加到collections模块
        for type_name in abc_types:
            if not hasattr(collections, type_name) and hasattr(abc, type_name):
                setattr(collections, type_name, getattr(abc, type_name))
                logging.debug(f"为 collections.{type_name} 创建别名指向 collections.abc.{type_name}")
                patched_count += 1
                
        if patched_count > 0:
            logging.info(f"已应用 collections 模块兼容性补丁，为 {patched_count} 个 abc 类型（如 Iterable, Mapping 等）在 collections 命名空间下创建了别名。")
    except Exception as e:
        logging.error(f"应用collections兼容性补丁失败: {str(e)}")

def patch_ipython_module():
    """
    模拟IPython模块，为测试提供必要的空实现
    """
    try:
        # 检查IPython模块是否已经存在
        try:
            import IPython
            logging.info("IPython模块已存在，无需补丁")
            return
        except ImportError:
            pass
        
        # 创建一个模拟IPython模块
        class MockDisplayModule:
            @staticmethod
            def clear_output(wait=False):
                """Mock implementation of IPython.display.clear_output"""
                pass
                
            @staticmethod
            def display(*args, **kwargs):
                """Mock implementation of IPython.display.display"""
                pass
                
            @staticmethod
            def HTML(html):
                """Mock implementation of IPython.display.HTML"""
                return html
            
            @staticmethod
            def Javascript(js):
                """Mock implementation of IPython.display.Javascript"""
                return js
            
            @staticmethod
            def Image(filename=None, data=None, url=None, filename_or_data=None, format=None, embed=None, width=None, height=None, retina=False, unconfined=False, metadata=None):
                """Mock implementation of IPython.display.Image"""
                return "[Mock Image]"
        
        # 创建模拟IPython模块
        class MockIPythonModule:
            display = MockDisplayModule
            
            class core:
                class display:
                    @staticmethod
                    def display(*args, **kwargs):
                        return MockDisplayModule.display(*args, **kwargs)
        
        # 将模拟模块添加到sys.modules
        sys.modules['IPython'] = MockIPythonModule
        sys.modules['IPython.display'] = MockDisplayModule
        sys.modules['IPython.core'] = MockIPythonModule.core
        sys.modules['IPython.core.display'] = MockIPythonModule.core.display
        
        logging.info("已应用 IPython 模拟补丁。如果 IPython 未安装，将使用模拟版本以保证兼容性。")
    except Exception as e:
        logging.error(f"模拟IPython模块失败: {str(e)}")

def patch_scipy_module():
    """
    对scipy模块应用补丁，处理兼容性问题
    """
    try:
        # 先尝试导入scipy模块
        import scipy
        
        # 处理interp函数的兼容性
        if not hasattr(scipy, 'interp'):
            try:
                # 在新版本scipy中interp在interpolate子模块中
                from scipy.interpolate import interp1d
                
                # 创建兼容版本的interp函数
                def compat_interp(x, xp, fp, left=None, right=None, period=None):
                    """兼容版本的interp函数"""
                    import numpy as np
                    
                    # 将输入转换为numpy数组
                    x = np.asarray(x)
                    xp = np.asarray(xp)
                    fp = np.asarray(fp)
                    
                    # 处理边界值
                    if left is None:
                        left = fp[0]
                    if right is None:
                        right = fp[-1]
                    
                    # 创建插值函数
                    f = interp1d(xp, fp, bounds_error=False, fill_value=(left, right))
                    return f(x)
                
                # 将兼容函数添加到scipy模块
                scipy.interp = compat_interp
                logging.info("已应用 SciPy 模块兼容性补丁：为 scipy.interp 创建了基于 scipy.interpolate.interp1d 的兼容版本。")
            except ImportError as e:
                logging.error(f"无法导入scipy.interpolate.interp1d: {str(e)}")
    except ImportError as e:
        logging.error(f"无法导入scipy模块: {str(e)}")
    except Exception as e:
        logging.error(f"应用scipy补丁失败: {str(e)}")
