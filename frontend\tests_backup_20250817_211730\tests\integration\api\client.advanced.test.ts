// Client API Advanced 测试 - 高级集成测试
// 测试复杂业务场景、系统集成和端到端流程

import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { setActivePinia, createPinia } from 'pinia';
import { apiClient } from '../../../src/api/client';
import { useAppStore } from '../../../src/stores/app';
import { SimpleClientDataFactory } from '../../factories/SimpleClientDataFactory';

// 创建MSW服务器
const server = setupServer();

describe('Client API Advanced Tests', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('复杂业务流程测试', () => {
    it('应该支持完整的CRUD操作流程', async () => {
      let resourceId: number;
      
      // 1. 创建资源
      server.use(
        http.post('/api/resources', async ({ request }) => {
          const body = await request.json();
          resourceId = 1;
          return HttpResponse.json({ id: resourceId, ...body });
        })
      );

      const createData = { name: 'Test Resource', description: 'Test Description' };
      const created = await apiClient.post('/api/resources', createData);
      expect(created.id).toBe(1);
      expect(created.name).toBe(createData.name);

      // 2. 读取资源
      server.use(
        http.get('/api/resources/1', () => {
          return HttpResponse.json({ id: 1, ...createData });
        })
      );

      const retrieved = await apiClient.get('/api/resources/1');
      expect(retrieved.id).toBe(1);
      expect(retrieved.name).toBe(createData.name);

      // 3. 更新资源
      const updateData = { name: 'Updated Resource', description: 'Updated Description' };
      server.use(
        http.put('/api/resources/1', async ({ request }) => {
          const body = await request.json();
          return HttpResponse.json({ id: 1, ...body });
        })
      );

      const updated = await apiClient.put('/api/resources/1', updateData);
      expect(updated.name).toBe(updateData.name);

      // 4. 删除资源
      server.use(
        http.delete('/api/resources/1', () => {
          return HttpResponse.json({ message: 'Resource deleted' });
        })
      );

      const deleted = await apiClient.delete('/api/resources/1');
      expect(deleted.message).toBe('Resource deleted');
    });

    it('应该处理复杂的数据关联操作', async () => {
      // 模拟获取用户及其关联数据的复杂流程
      const userId = 1;
      
      // 1. 获取用户基本信息
      server.use(
        http.get(`/api/users/${userId}`, () => {
          return HttpResponse.json({
            id: userId,
            name: 'Test User',
            email: '<EMAIL>'
          });
        })
      );

      const user = await apiClient.get(`/api/users/${userId}`);
      expect(user.id).toBe(userId);

      // 2. 获取用户的订单
      server.use(
        http.get(`/api/users/${userId}/orders`, () => {
          return HttpResponse.json([
            { id: 1, userId, amount: 100, status: 'completed' },
            { id: 2, userId, amount: 200, status: 'pending' }
          ]);
        })
      );

      const orders = await apiClient.get(`/api/users/${userId}/orders`);
      expect(orders).toHaveLength(2);
      expect(orders[0].userId).toBe(userId);

      // 3. 获取用户的偏好设置
      server.use(
        http.get(`/api/users/${userId}/preferences`, () => {
          return HttpResponse.json({
            theme: 'dark',
            language: 'zh-CN',
            notifications: true
          });
        })
      );

      const preferences = await apiClient.get(`/api/users/${userId}/preferences`);
      expect(preferences.theme).toBe('dark');

      // 4. 并发获取所有关联数据
      const [userInfo, userOrders, userPrefs] = await Promise.all([
        apiClient.get(`/api/users/${userId}`),
        apiClient.get(`/api/users/${userId}/orders`),
        apiClient.get(`/api/users/${userId}/preferences`)
      ]);

      expect(userInfo.id).toBe(userId);
      expect(userOrders).toHaveLength(2);
      expect(userPrefs.theme).toBe('dark');
    });
  });

  describe('错误恢复和重试机制测试', () => {
    it('应该处理间歇性网络故障', async () => {
      let attemptCount = 0;
      
      server.use(
        http.get('/api/unreliable', () => {
          attemptCount++;
          
          // 前两次请求失败，第三次成功
          if (attemptCount < 3) {
            return HttpResponse.json(
              { error: 'Service temporarily unavailable' },
              { status: 503 }
            );
          }
          
          return HttpResponse.json({ message: 'Success', attempt: attemptCount });
        })
      );

      // 模拟重试逻辑
      let result;
      let lastError;
      const maxRetries = 3;
      
      for (let i = 0; i < maxRetries; i++) {
        try {
          result = await apiClient.get('/api/unreliable');
          break;
        } catch (error) {
          lastError = error;
          if (i < maxRetries - 1) {
            await new Promise(resolve => setTimeout(resolve, 100 * (i + 1))); // 递增延迟
          }
        }
      }

      expect(result?.message).toBe('Success');
      expect(result?.attempt).toBe(3);
      expect(attemptCount).toBe(3);
    });

    it('应该处理部分API失败的批量操作', async () => {
      const items = [1, 2, 3, 4, 5];
      
      // 设置第3个请求失败
      items.forEach((item, index) => {
        if (index === 2) { // 第3个项目
          server.use(
            http.get(`/api/items/${item}`, () => {
              return HttpResponse.json(
                { error: 'Item not found' },
                { status: 404 }
              );
            })
          );
        } else {
          server.use(
            http.get(`/api/items/${item}`, () => {
              return HttpResponse.json({ id: item, name: `Item ${item}` });
            })
          );
        }
      });

      // 批量请求，处理部分失败
      const results = await Promise.allSettled(
        items.map(item => apiClient.get(`/api/items/${item}`))
      );

      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');

      expect(successful).toHaveLength(4);
      expect(failed).toHaveLength(1);
    });
  });

  describe('性能和并发测试', () => {
    it('应该处理高并发请求', async () => {
      const concurrentRequests = 50;
      
      // 设置多个端点
      Array.from({ length: concurrentRequests }, (_, index) => {
        server.use(
          http.get(`/api/concurrent/${index}`, () => {
            return HttpResponse.json({
              id: index,
              timestamp: Date.now(),
              message: `Response ${index}`
            });
          })
        );
      });

      const startTime = Date.now();
      
      // 并发发送请求
      const promises = Array.from({ length: concurrentRequests }, (_, index) => 
        apiClient.get(`/api/concurrent/${index}`)
      );
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      expect(results).toHaveLength(concurrentRequests);
      expect(endTime - startTime).toBeLessThan(10000); // 10秒内完成
      
      // 验证所有响应都正确
      results.forEach((result, index) => {
        expect(result.id).toBe(index);
        expect(result.message).toBe(`Response ${index}`);
      });
    });

    it('应该正确处理请求队列和限流', async () => {
      let activeRequests = 0;
      let maxConcurrent = 0;
      
      server.use(
        http.get('/api/throttled', () => {
          activeRequests++;
          maxConcurrent = Math.max(maxConcurrent, activeRequests);
          
          return new Promise((resolve) => {
            setTimeout(() => {
              activeRequests--;
              resolve(HttpResponse.json({ 
                message: 'Throttled response',
                activeRequests,
                maxConcurrent
              }));
            }, 100);
          });
        })
      );

      // 快速发送多个请求
      const promises = Array.from({ length: 10 }, () => 
        apiClient.get('/api/throttled')
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      // 验证并发控制是否生效（这里只是示例，实际限流逻辑可能在更高层实现）
      expect(maxConcurrent).toBeGreaterThan(0);
    });
  });

  describe('数据一致性和状态管理测试', () => {
    it('应该在复杂操作中保持状态一致性', async () => {
      const appStore = useAppStore();
      
      // 模拟复杂的多步骤操作
      server.use(
        http.post('/api/complex-operation/step1', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json({ step: 1, status: 'completed' }));
            }, 500);
          });
        }),
        http.post('/api/complex-operation/step2', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json({ step: 2, status: 'completed' }));
            }, 300);
          });
        }),
        http.post('/api/complex-operation/step3', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json({ step: 3, status: 'completed' }));
            }, 200);
          });
        })
      );

      expect(appStore.isLoading).toBe(false);

      // 执行多步骤操作
      const step1Promise = apiClient.post('/api/complex-operation/step1');
      await vi.dynamicImportSettled();
      expect(appStore.isLoading).toBe(true);

      const step1Result = await step1Promise;
      expect(step1Result.step).toBe(1);

      const step2Result = await apiClient.post('/api/complex-operation/step2');
      expect(step2Result.step).toBe(2);

      const step3Result = await apiClient.post('/api/complex-operation/step3');
      expect(step3Result.step).toBe(3);

      expect(appStore.isLoading).toBe(false);
    });

    it('应该处理长时间运行的操作', async () => {
      const appStore = useAppStore();
      
      server.use(
        http.post('/api/long-running', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json({ 
                message: 'Long operation completed',
                duration: 3000
              }));
            }, 3000);
          });
        })
      );

      expect(appStore.isLoading).toBe(false);

      const startTime = Date.now();
      const promise = apiClient.post('/api/long-running');
      
      await vi.dynamicImportSettled();
      expect(appStore.isLoading).toBe(true);

      const result = await promise;
      const endTime = Date.now();
      
      expect(result.message).toBe('Long operation completed');
      expect(endTime - startTime).toBeGreaterThanOrEqual(2900); // 至少3秒
      expect(appStore.isLoading).toBe(false);
    });
  });

  describe('缓存和优化测试', () => {
    it('应该支持条件请求和缓存控制', async () => {
      let requestCount = 0;
      const etag = '"123456789"';
      
      server.use(
        http.get('/api/cached-resource', ({ request }) => {
          requestCount++;
          const ifNoneMatch = request.headers.get('If-None-Match');
          
          if (ifNoneMatch === etag) {
            return new Response(null, { 
              status: 304,
              headers: { 'ETag': etag }
            });
          }
          
          return HttpResponse.json(
            { data: 'Cached content', version: 1 },
            { headers: { 'ETag': etag, 'Cache-Control': 'max-age=3600' } }
          );
        })
      );

      // 第一次请求
      const result1 = await apiClient.get('/api/cached-resource');
      expect(result1.data).toBe('Cached content');
      expect(requestCount).toBe(1);

      // 第二次请求（带ETag）
      const result2 = await apiClient.get('/api/cached-resource', {
        headers: { 'If-None-Match': etag }
      });
      expect(requestCount).toBe(2);
      // 304响应通常返回空内容
    });
  });
});