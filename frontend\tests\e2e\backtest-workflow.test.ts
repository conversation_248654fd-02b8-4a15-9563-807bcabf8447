import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { ElMessage } from 'element-plus'
import BacktestView from '@/views/BacktestView.vue'
import { useBacktestStore } from '@/stores/modules/useBacktestStore'
import { useStrategyStore } from '@/stores/modules/useStrategyStore'
import { SimpleBacktestDataFactory } from '../factories/SimpleBacktestDataFactory'
import { SimpleStrategyDataFactory } from '../factories/SimpleStrategyDataFactory'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElContainer: { name: 'ElContainer', template: '<div><slot /></div>' },
  ElHeader: { name: 'ElHeader', template: '<div><slot /></div>' },
  ElMain: { name: '<PERSON><PERSON>ain', template: '<div><slot /></div>' },
  ElAside: { name: 'ElAside', template: '<div><slot /></div>' },
  ElPageHeader: { name: 'ElPageHeader', template: '<div><slot /></div>' },
  ElRow: { name: 'ElRow', template: '<div><slot /></div>' },
  ElCol: { name: 'ElCol', template: '<div><slot /></div>' },
  ElCard: { name: 'ElCard', template: '<div><slot /></div>' },
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElInputNumber: { name: 'ElInputNumber', template: '<input type="number" />' },
  ElDatePicker: { name: 'ElDatePicker', template: '<input type="date" />' },
  ElSelect: { name: 'ElSelect', template: '<select><slot /></select>' },
  ElOption: { name: 'ElOption', template: '<option><slot /></option>' },
  ElForm: { name: 'ElForm', template: '<form><slot /></form>' },
  ElFormItem: { name: 'ElFormItem', template: '<div><slot /></div>' },
  ElTable: { name: 'ElTable', template: '<table><slot /></table>' },
  ElTableColumn: { name: 'ElTableColumn', template: '<td><slot /></td>' },
  ElProgress: { name: 'ElProgress', template: '<div><slot /></div>' },
  ElLoading: { directive: {} },
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock API 模块
vi.mock('@/api/backtest', () => ({
  backtestApi: {
    startBacktest: vi.fn(),
    getBacktestHistory: vi.fn(),
    loadBacktestResults: vi.fn(),
    deleteBacktest: vi.fn()
  }
}))

vi.mock('@/api/strategy', () => ({
  strategyApi: {
    getStrategies: vi.fn(),
    getStrategyById: vi.fn()
  }
}))

describe('回测工作流程端到端测试', () => {
  let wrapper: any
  let backtestStore: any
  let strategyStore: any

  beforeEach(() => {
    // 设置 Pinia
    const pinia = createPinia()
    setActivePinia(pinia)
    
    // 获取 store 实例
    backtestStore = useBacktestStore()
    strategyStore = useStrategyStore()
    
    // 重置所有 mock
    vi.clearAllMocks()
    
    // Mock store 方法
    vi.spyOn(backtestStore, 'startBacktest').mockResolvedValue({ success: true, task_id: 'test-task-1' })
    vi.spyOn(backtestStore, 'fetchBacktestHistory').mockResolvedValue()
    vi.spyOn(backtestStore, 'loadBacktestResults').mockResolvedValue({ success: true })
    vi.spyOn(backtestStore, 'deleteBacktest').mockResolvedValue({ success: true })
    vi.spyOn(strategyStore, 'fetchStrategies').mockResolvedValue()
    
    // 挂载组件
    wrapper = mount(BacktestView, {
      global: {
        plugins: [pinia],
        stubs: {
          BacktestForm: {
            name: 'BacktestForm',
            template: '<div data-testid="backtest-form"><slot /></div>',
            emits: ['submit-backtest'],
            methods: {
              async submitBacktest(params: any) {
                try {
                  await backtestStore.startBacktest(params)
                  ElMessage.success('回测启动成功')
                  this.$emit('submit-backtest', params)
                } catch (error) {
                  ElMessage.error(error instanceof Error ? error.message : '回测启动失败')
                  throw error
                }
              }
            }
          },
          BacktestResults: {
            name: 'BacktestResults',
            template: '<div data-testid="backtest-results"><slot /></div>',
            props: ['results', 'isLoading'],
            emits: ['delete-result'],
            methods: {
              async deleteResult(taskId: string) {
                await backtestStore.deleteBacktest(taskId)
                this.$emit('delete-result', taskId)
              }
            }
          }
        }
      }
    })
  })

  describe('完整回测执行工作流程', () => {
    it('应该能够完成从参数设置到结果查看的完整回测流程', async () => {
      // 1. 初始化 - 获取策略列表和历史记录
      expect(strategyStore.fetchStrategies).toHaveBeenCalled()
      expect(backtestStore.fetchBacktestHistory).toHaveBeenCalled()
      
      // 2. 设置回测参数
      const backtestParams = SimpleBacktestDataFactory.createSimpleConfig()
      backtestParams.strategy_id = 'test-strategy-1'
      backtestParams.capital = 100000
      backtestParams.start_date = '2023-01-01'
      backtestParams.end_date = '2023-12-31'
      backtestParams.benchmark = '000300.SH'
      
      // 3. 提交回测任务
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.submitBacktest(backtestParams)
      
      // 验证回测提交
      expect(backtestStore.startBacktest).toHaveBeenCalledWith(backtestParams)
      
      // 4. 验证成功消息
      expect(ElMessage.success).toHaveBeenCalled()
      
      // 5. 获取回测结果
      const taskId = 'test-task-1'
      await backtestStore.loadBacktestResults(taskId)
      
      // 验证结果获取
      expect(backtestStore.loadBacktestResults).toHaveBeenCalledWith(taskId)
    })

    it('应该能够处理回测参数验证', async () => {
      // 1. 无效参数测试
      const invalidParams = {
        strategy_id: '',
        initial_capital: -1000,
        start_date: '',
        end_date: ''
      }
      
      // Mock 参数验证失败
      vi.spyOn(backtestStore, 'startBacktest').mockRejectedValue(new Error('参数验证失败'))
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      
      try {
        await backtestForm.vm.submitBacktest(invalidParams)
      } catch (error) {
        // 预期的错误
      }
      
      // 验证错误处理
      expect(ElMessage.error).toHaveBeenCalled()
    })
  })

  describe('回测历史管理工作流程', () => {
    it('应该能够查看和管理回测历史记录', async () => {
      // 1. 获取历史记录
      await backtestStore.fetchBacktestHistory('test-strategy-1')
      
      expect(backtestStore.fetchBacktestHistory).toHaveBeenCalledWith('test-strategy-1')
      
      // 2. 模拟历史记录数据
      const historyData = [
        SimpleBacktestDataFactory.createSimpleResult('task-1'),
        SimpleBacktestDataFactory.createSimpleResult('task-2')
      ]
      
      backtestStore.backtestHistory = historyData
      
      // 3. 验证历史记录显示
      expect(backtestStore.backtestHistory).toHaveLength(2)
      
      // 4. 删除历史记录
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      const taskIdToDelete = historyData[0].task_id
      
      await backtestResults.vm.deleteResult(taskIdToDelete)
      
      // 验证删除调用
      expect(backtestStore.deleteBacktest).toHaveBeenCalledWith(taskIdToDelete)
    })

    it('应该能够处理历史记录获取失败', async () => {
      // Mock 获取失败
      vi.spyOn(backtestStore, 'fetchBacktestHistory').mockRejectedValue(new Error('获取历史失败'))
      
      try {
        await backtestStore.fetchBacktestHistory('test-strategy-1')
      } catch (error) {
        // 预期的错误
      }
      
      // 验证错误状态
      expect(backtestStore.backtestError).toBeDefined()
    })
  })

  describe('回测结果分析工作流程', () => {
    it('应该能够查看详细的回测结果', async () => {
      // 1. 模拟回测完成
      const taskId = 'completed-task-1'
      const backtestResult = SimpleBacktestDataFactory.createSimpleResult(taskId)
      backtestResult.task_id = taskId
      backtestResult.status = 'completed'
      
      // 2. 获取详细结果
      vi.spyOn(backtestStore, 'loadBacktestResults').mockResolvedValue(backtestResult)
      
      await backtestStore.loadBacktestResults(taskId)
      
      expect(backtestStore.loadBacktestResults).toHaveBeenCalledWith(taskId)
      
      // 3. 验证结果数据结构
      expect(backtestResult).toHaveProperty('metrics')
      expect(backtestResult).toHaveProperty('trades')
      expect(backtestResult).toHaveProperty('equity_curve')
    })

    it('应该能够处理回测进行中的状态', async () => {
      // 1. 模拟回测进行中
      const taskId = 'running-task-1'
      const runningResult = {
        task_id: taskId,
        status: 'running',
        progress: 50
      }
      
      vi.spyOn(backtestStore, 'loadBacktestResults').mockResolvedValue(runningResult)
      
      await backtestStore.loadBacktestResults(taskId)
      
      // 验证进度状态
      expect(runningResult.status).toBe('running')
      expect(runningResult.progress).toBe(50)
    })
  })

  describe('多策略回测比较工作流程', () => {
    it('应该能够同时运行多个策略的回测', async () => {
      // 1. 准备多个策略
      const strategies = [
        SimpleStrategyDataFactory.createStrategy(),
        SimpleStrategyDataFactory.createStrategy()
      ]
      strategies[0].id = 'strategy-1'
      strategies[1].id = 'strategy-2'
      
      // 2. 为每个策略创建回测参数
      const backtestParams1 = SimpleBacktestDataFactory.createSimpleConfig()
      backtestParams1.strategy_id = 'strategy-1'
      
      const backtestParams2 = SimpleBacktestDataFactory.createSimpleConfig()
      backtestParams2.strategy_id = 'strategy-2'
      
      // 3. 提交多个回测任务
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      
      await backtestForm.vm.submitBacktest(backtestParams1)
      await backtestForm.vm.submitBacktest(backtestParams2)
      
      // 验证多次调用
      expect(backtestStore.startBacktest).toHaveBeenCalledTimes(2)
      expect(backtestStore.startBacktest).toHaveBeenCalledWith(backtestParams1)
      expect(backtestStore.startBacktest).toHaveBeenCalledWith(backtestParams2)
    })
  })

  describe('错误处理和恢复', () => {
    it('应该能够处理回测执行失败', async () => {
      // Mock 回测失败
      vi.spyOn(backtestStore, 'startBacktest').mockRejectedValue(new Error('回测执行失败'))
      
      const backtestParams = SimpleBacktestDataFactory.createSimpleConfig()
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      
      try {
        await backtestForm.vm.submitBacktest(backtestParams)
      } catch (error) {
        // 预期的错误
      }
      
      // 验证错误处理
      expect(ElMessage.error).toHaveBeenCalled()
      expect(backtestStore.backtestError).toBeDefined()
    })

    it('应该能够处理网络连接问题', async () => {
      // Mock 网络错误
      vi.spyOn(backtestStore, 'fetchBacktestHistory').mockRejectedValue(new Error('网络连接失败'))
      
      try {
        await backtestStore.fetchBacktestHistory('test-strategy-1')
      } catch (error) {
        // 预期的错误
      }
      
      // 验证错误状态
      expect(backtestStore.backtestError).toBeDefined()
    })

    it('应该能够重试失败的操作', async () => {
      // 第一次失败，第二次成功
      vi.spyOn(backtestStore, 'startBacktest')
        .mockRejectedValueOnce(new Error('临时失败'))
        .mockResolvedValueOnce({ success: true, task_id: 'retry-task-1' })
      
      const backtestParams = SimpleBacktestDataFactory.createSimpleConfig()
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      
      // 第一次尝试
      try {
        await backtestForm.vm.submitBacktest(backtestParams)
      } catch (error) {
        // 预期的错误
      }
      expect(ElMessage.error).toHaveBeenCalled()
      
      // 重试
      await backtestForm.vm.submitBacktest(backtestParams)
      expect(ElMessage.success).toHaveBeenCalled()
    })
  })

  describe('性能和资源管理', () => {
    it('应该正确管理长时间运行的回测任务', async () => {
      // 模拟长时间运行的任务
      const longRunningTask = {
        task_id: 'long-task-1',
        status: 'running',
        progress: 25,
        estimated_time_remaining: 300 // 5分钟
      }
      
      vi.spyOn(backtestStore, 'loadBacktestResults').mockResolvedValue(longRunningTask)
      
      await backtestStore.loadBacktestResults('long-task-1')
      
      // 验证任务状态
      expect(longRunningTask.status).toBe('running')
      expect(longRunningTask.progress).toBe(25)
    })

    it('应该能够取消正在运行的回测任务', async () => {
      // Mock 取消任务
      vi.spyOn(backtestStore, 'deleteBacktest').mockResolvedValue({ success: true })
      
      const taskId = 'running-task-to-cancel'
      await backtestStore.deleteBacktest(taskId)
      
      expect(backtestStore.deleteBacktest).toHaveBeenCalledWith(taskId)
    })
  })
})