"""性能监控模块

提供系统性能监控、指标收集和性能分析功能。
"""

import time
import psutil
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from functools import wraps
from contextlib import contextmanager
from collections import defaultdict, deque

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class ExecutionStats:
    """执行统计信息"""
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    last_execution: Optional[datetime] = None

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.metrics_history: deque = deque(maxlen=max_history_size)
        self.execution_stats: Dict[str, ExecutionStats] = defaultdict(ExecutionStats)
        self.active_timers: Dict[str, float] = {}
        self.system_metrics_enabled = True
        
    def record_metric(self, name: str, value: float, unit: str = "", tags: Dict[str, str] = None):
        """记录性能指标"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            tags=tags or {}
        )
        self.metrics_history.append(metric)
        
        # 记录到日志
        logging.info(f"Performance Metric | {name}: {value} {unit}")
    
    def start_timer(self, operation_name: str) -> str:
        """开始计时"""
        timer_id = f"{operation_name}_{int(time.time() * 1000)}"
        self.active_timers[timer_id] = time.time()
        return timer_id
    
    def end_timer(self, timer_id: str, operation_name: str = None) -> float:
        """结束计时并记录"""
        if timer_id not in self.active_timers:
            logging.warning(f"Timer {timer_id} not found")
            return 0.0
        
        start_time = self.active_timers.pop(timer_id)
        duration = time.time() - start_time
        
        if operation_name:
            self.record_execution(operation_name, duration, True)
            self.record_metric(f"{operation_name}_duration", duration, "seconds")
        
        return duration
    
    def record_execution(self, operation_name: str, duration: float, success: bool):
        """记录操作执行统计"""
        stats = self.execution_stats[operation_name]
        stats.total_executions += 1
        stats.last_execution = datetime.now()
        
        if success:
            stats.successful_executions += 1
        else:
            stats.failed_executions += 1
        
        # 更新持续时间统计
        if duration > 0:
            stats.min_duration = min(stats.min_duration, duration)
            stats.max_duration = max(stats.max_duration, duration)
            
            # 计算平均持续时间
            total_duration = stats.average_duration * (stats.total_executions - 1) + duration
            stats.average_duration = total_duration / stats.total_executions
    
    def get_system_metrics(self) -> Dict[str, float]:
        """获取系统性能指标"""
        if not self.system_metrics_enabled:
            return {}
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / 1024 / 1024
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 进程信息
            process = psutil.Process()
            process_memory_mb = process.memory_info().rss / 1024 / 1024
            process_cpu_percent = process.cpu_percent()
            
            metrics = {
                'system_cpu_percent': cpu_percent,
                'system_memory_percent': memory_percent,
                'system_memory_used_mb': memory_used_mb,
                'system_disk_percent': disk_percent,
                'process_memory_mb': process_memory_mb,
                'process_cpu_percent': process_cpu_percent
            }
            
            # 记录系统指标
            for name, value in metrics.items():
                unit = 'percent' if 'percent' in name else 'mb' if 'mb' in name else ''
                self.record_metric(name, value, unit, {'type': 'system'})
            
            return metrics
            
        except Exception as e:
            logging.error(f"Failed to collect system metrics: {e}")
            return {}
    
    def get_execution_summary(self, operation_name: str = None) -> Dict[str, Any]:
        """获取执行统计摘要"""
        if operation_name:
            if operation_name not in self.execution_stats:
                return {}
            
            stats = self.execution_stats[operation_name]
            return {
                'operation': operation_name,
                'total_executions': stats.total_executions,
                'successful_executions': stats.successful_executions,
                'failed_executions': stats.failed_executions,
                'success_rate': stats.successful_executions / stats.total_executions if stats.total_executions > 0 else 0,
                'average_duration': stats.average_duration,
                'min_duration': stats.min_duration if stats.min_duration != float('inf') else 0,
                'max_duration': stats.max_duration,
                'last_execution': stats.last_execution.isoformat() if stats.last_execution else None
            }
        else:
            # 返回所有操作的摘要
            return {
                operation: self.get_execution_summary(operation)
                for operation in self.execution_stats.keys()
            }
    
    def get_recent_metrics(self, minutes: int = 10) -> List[PerformanceMetric]:
        """获取最近的性能指标"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [
            metric for metric in self.metrics_history
            if metric.timestamp >= cutoff_time
        ]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        system_metrics = self.get_system_metrics()
        execution_summary = self.get_execution_summary()
        recent_metrics = self.get_recent_metrics()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_metrics': system_metrics,
            'execution_summary': execution_summary,
            'recent_metrics_count': len(recent_metrics),
            'total_metrics_recorded': len(self.metrics_history),
            'active_timers': len(self.active_timers)
        }
    
    def clear_history(self):
        """清除历史数据"""
        self.metrics_history.clear()
        self.execution_stats.clear()
        self.active_timers.clear()
        logging.info("Performance monitor history cleared")

# 全局性能监控实例
performance_monitor = PerformanceMonitor()

# 装饰器函数
def monitor_performance(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            timer_id = performance_monitor.start_timer(op_name)
            
            try:
                result = func(*args, **kwargs)
                performance_monitor.end_timer(timer_id, op_name)
                return result
            except Exception as e:
                duration = performance_monitor.end_timer(timer_id)
                performance_monitor.record_execution(op_name, duration, False)
                raise
        
        return wrapper
    return decorator

@contextmanager
def performance_context(operation_name: str):
    """性能监控上下文管理器"""
    timer_id = performance_monitor.start_timer(operation_name)
    try:
        yield
        performance_monitor.end_timer(timer_id, operation_name)
    except Exception as e:
        duration = performance_monitor.end_timer(timer_id)
        performance_monitor.record_execution(operation_name, duration, False)
        raise

# 便捷函数
def record_metric(name: str, value: float, unit: str = "", tags: Dict[str, str] = None):
    """记录性能指标的便捷函数"""
    performance_monitor.record_metric(name, value, unit, tags)

def get_performance_report() -> Dict[str, Any]:
    """获取性能报告的便捷函数"""
    return performance_monitor.get_performance_report()

def monitor_strategy_execution(strategy_name: str):
    """策略执行监控装饰器"""
    return monitor_performance(f"strategy_execution_{strategy_name}")

def monitor_market_data_fetch(symbol: str):
    """市场数据获取监控装饰器"""
    return monitor_performance(f"market_data_fetch_{symbol}")