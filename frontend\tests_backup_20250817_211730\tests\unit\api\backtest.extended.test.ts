import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { runBacktest, getBacktestResults, getBacktestHistory, stopBacktest } from '../../../src/api/backtest';
import type { BacktestConfig, BacktestResult, BacktestTask } from '../../../src/api/types/backtest';
import { BacktestStatus } from '../../../src/api/types/backtest';

const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 回测API测试套件 - Extended TDD版本
 * 
 * 本测试文件在TDD版本基础上扩展了更多测试用例，包括：
 * - 基本的HTTP请求处理
 * - 核心错误处理机制
 * - 基础的响应数据验证
 * - 参数验证测试
 * - 简单的业务逻辑测试
 */

// Mock数据
const mockBacktestConfig: BacktestConfig = {
  strategy_id: 'strategy-1',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  capital: 100000,
  commission: 0.001,
  slippage: 0.001,
  benchmark: 'SPY'
};

const mockBacktestResult: BacktestResult = {
  task_id: 'backtest-1',
  strategy_name: 'Test Strategy',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  initial_capital: 100000,
  final_capital: 120000,
  metrics: {
    total_return: 0.20,
    annual_return: 0.20,
    max_drawdown: -0.05,
    sharpe_ratio: 1.5,
    win_rate: 0.65,
    profit_loss_ratio: 1.8,
    total_trades: 50,
    winning_trades: 32,
    losing_trades: 18,
    avg_holding_period: 15,
    volatility: 0.15
  },
  trades: [],
  positions: [],
  equity_curve: [],
  generated_at: '2024-01-01T12:00:00Z'
};

const mockBacktestHistory: BacktestTask[] = [
  {
    id: 'backtest-1',
    strategy_id: 'strategy-1',
    strategy_name: 'Test Strategy',
    symbol: 'AAPL',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    status: 'running',
    created_at: '2024-01-01T10:00:00Z',
    config: mockBacktestConfig
  },
  {
    id: 'backtest-2',
    strategy_id: 'strategy-2',
    strategy_name: 'Test Strategy 2',
    symbol: 'AAPL',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    status: 'completed',
    created_at: '2024-01-01T11:30:00Z',
    config: mockBacktestConfig
  }
];

// 扩展的MSW handlers
const handlers = [
  // runBacktest handlers
  http.post('/api/backtest/run', ({ request }) => {
    return HttpResponse.json({
      success: true,
      data: {
        id: 'backtest-1',
        status: 'pending',
        strategy_id: mockBacktestConfig.strategy_id,
        symbol: mockBacktestConfig.symbol
      }
    });
  }),
  
  // getBacktestResults handlers
  http.get('/api/backtest/results/:id', ({ params }) => {
    const { id } = params;
    if (id === 'backtest-1') {
      return HttpResponse.json({
        success: true,
        data: mockBacktestResult
      });
    }
    if (id === 'running-backtest') {
      return HttpResponse.json(
        { success: false, message: 'Backtest is still running' },
        { status: 202 }
      );
    }
    return HttpResponse.json(
      { success: false, message: 'Backtest task not found' },
      { status: 404 }
    );
  }),
  
  // getBacktestHistory handlers
  http.get('/api/backtest/history', ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('page_size') || '10');
    const strategyId = url.searchParams.get('strategy_id');
    const status = url.searchParams.get('status');
    
    let filteredHistory = mockBacktestHistory;
    
    if (strategyId) {
      filteredHistory = filteredHistory.filter(task => task.strategy_id === strategyId);
    }
    
    if (status) {
      filteredHistory = filteredHistory.filter(task => task.status === status);
    }
    
    return HttpResponse.json({
      success: true,
      data: filteredHistory,
      total: filteredHistory.length,
      page: page,
      page_size: pageSize
    });
  }),
  
  // stopBacktest handlers
  http.post('/api/backtest/stop/:id', ({ params }) => {
    const { id } = params;
    if (id === 'non-existent') {
      return HttpResponse.json(
        { success: false, message: 'Backtest task not found' },
        { status: 404 }
      );
    }
    if (id === 'completed-backtest') {
      return HttpResponse.json(
        { success: false, message: 'Cannot stop completed backtest' },
        { status: 400 }
      );
    }
    return HttpResponse.json({
      success: true,
      data: {
        id,
        status: BacktestStatus.STOPPED,
        stopped_at: new Date().toISOString()
      }
    });
  })
];

const server = setupServer(...handlers);

describe('Backtest API - Extended TDD', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('runBacktest', () => {
    it('should successfully start backtest task', async () => {
      const response = await runBacktest(mockBacktestConfig);
      expect(response.success).toBe(true);
      expect(response.data.id).toBeDefined();
      expect(response.data.status).toBe('pending');
      expect(response.data.strategy_id).toBe(mockBacktestConfig.strategy_id);
      expect(response.data.symbol).toBe(mockBacktestConfig.symbol);
    });

    it('should handle invalid configuration parameters error (400)', async () => {
      server.use(
        http.post('/api/backtest/run', () => {
          return HttpResponse.json(
            { success: false, message: 'Invalid configuration parameters' },
            { status: 400 }
          );
        })
      );

      const invalidConfig = {
        ...mockBacktestConfig,
        start_date: 'invalid-date'
      };

      await expect(runBacktest(invalidConfig)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid configuration parameters')
      });
    });

    it('should validate date range parameters - Extended TDD', async () => {
      server.use(
        http.post('/api/backtest/run', () => {
          return HttpResponse.json(
            { success: false, message: 'Invalid date range' },
            { status: 400 }
          );
        })
      );

      const invalidDateRangeConfig = {
        ...mockBacktestConfig,
        start_date: '2023-12-31',
        end_date: '2023-01-01' // end_date before start_date
      };

      await expect(runBacktest(invalidDateRangeConfig)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid date range')
      });
    });

    it('should validate capital and commission parameters - Extended TDD', async () => {
      server.use(
        http.post('/api/backtest/run', () => {
          return HttpResponse.json(
            { success: false, message: 'Invalid capital or commission' },
            { status: 400 }
          );
        })
      );

      const invalidCapitalConfig = {
        ...mockBacktestConfig,
        capital: -1000, // negative capital
        commission: 1.5 // commission > 100%
      };

      await expect(runBacktest(invalidCapitalConfig)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid capital or commission')
      });
    });
  });

  describe('getBacktestResults', () => {
    it('should successfully get complete backtest report', async () => {
      const response = await getBacktestResults('backtest-1');
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          task_id: 'backtest-1',
          metrics: expect.any(Object),
          trades: expect.any(Array),
          equity_curve: expect.any(Array)
        })
      });
    });

    it('should handle backtest task not found error (404)', async () => {
      await expect(getBacktestResults('non-existent')).rejects.toMatchObject({
        status: 404,
        message: expect.stringContaining('Backtest task not found')
      });
    });

    it('should handle backtest still running case and return progress info - Extended TDD', async () => {
      await expect(getBacktestResults('running-backtest')).rejects.toMatchObject({
        status: 202,
        message: expect.stringContaining('Backtest is still running')
      });
    });
  });

  describe('getBacktestHistory', () => {
    it('should successfully get backtest history list', async () => {
      const response = await getBacktestHistory();
      expect(response).toMatchObject({
        success: true,
        data: expect.any(Array),
        total: expect.any(Number),
        page: expect.any(Number),
        page_size: expect.any(Number)
      });
      expect(response.total).toBeGreaterThan(0);
    });

    it('should support pagination parameters', async () => {
      const response = await getBacktestHistory({ page: 2, page_size: 10 });
      expect(response).toMatchObject({
        success: true,
        page: 2,
        page_size: 10,
        data: expect.any(Array)
      });
    });

    it('should support filtering by strategy - Extended TDD', async () => {
      const response = await getBacktestHistory({ strategy_id: 'strategy-1' });
      expect(response).toMatchObject({
        success: true,
        data: expect.any(Array)
      });
      expect(response.data.every(task => task.strategy_id === 'strategy-1')).toBe(true);
    });

    it('should support filtering by status - Extended TDD', async () => {
      const response = await getBacktestHistory({ status: BacktestStatus.COMPLETED });
      expect(response).toMatchObject({
        success: true,
        data: expect.any(Array)
      });
      expect(response.data.every(task => task.status === BacktestStatus.COMPLETED)).toBe(true);
    });
  });

  describe('stopBacktest', () => {
    it('should successfully stop running backtest', async () => {
      const response = await stopBacktest('running-backtest');
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          status: BacktestStatus.STOPPED
        })
      });
    });

    it('should handle backtest task not found error (404)', async () => {
      await expect(stopBacktest('non-existent')).rejects.toMatchObject({
        status: 404,
        message: expect.stringContaining('Backtest task not found')
      });
    });

    it('should handle cannot stop completed backtest case - Extended TDD', async () => {
      await expect(stopBacktest('completed-backtest')).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Cannot stop completed backtest')
      });
    });

    it('should validate backtest status transition - Extended TDD', async () => {
      const response = await stopBacktest('running-backtest');
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: 'running-backtest',
          status: BacktestStatus.STOPPED,
          stopped_at: expect.any(String)
        })
      });
    });
  });
});