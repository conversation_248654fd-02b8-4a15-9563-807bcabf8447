// 字体变量定义

// 字体族
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
$font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

// 行高
$line-height-base: 1.5;
$line-height-sm: 1.2;
$line-height-lg: 1.8;

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 标题字体大小
$h1-font-size: $font-size-xxxl;
$h2-font-size: $font-size-xxl;
$h3-font-size: $font-size-xl;
$h4-font-size: $font-size-lg;
$h5-font-size: $font-size-base;
$h6-font-size: $font-size-sm;

// CSS自定义属性 (符合UI规范)
:root {
  // 字体族
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  --font-size-xxxl: 32px;
  
  // 行高
  --line-height-base: 1.5;
  --line-height-sm: 1.2;
  --line-height-lg: 1.8;
  
  // 字重
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  // 标题字体大小
  --h1-font-size: 32px;
  --h2-font-size: 24px;
  --h3-font-size: 20px;
  --h4-font-size: 18px;
  --h5-font-size: 16px;
  --h6-font-size: 14px;
}