import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useStrategyStore } from '../../../src/stores/useStrategyStore';
import * as strategyApi from '@/api/strategy';

// TDD专用测试文件 - useStrategyStore
// 专注于核心CRUD操作的快速验证

// 契约 1: 外部依赖必须被完全模拟
vi.mock('@/api/strategy');

// 契约 2: 定义标准的模拟数据
const mockStrategy = {
  id: '1',
  name: '测试策略',
  description: '测试描述',
  content: '策略内容',
  is_public: true
};

const mockStrategies = [mockStrategy, { ...mockStrategy, id: '2', name: '策略2' }];

describe('useStrategyStore - TDD专用测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态 - TDD', () => {
      const store = useStrategyStore();
      expect(store.strategies).toEqual([]);
      expect(store.currentStrategy).toBeNull();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('fetchStrategies', () => {
    it('should fetch strategies and update state on success', async () => {
      const store = useStrategyStore();
      const mockStrategiesList = [
        {
          id: 'strategy-001',
          name: '双均线策略',
          description: '基于短期和长期移动平均线的交叉信号进行买卖决策'
        },
        {
          id: 'strategy-002', 
          name: 'RSI策略',
          description: '基于RSI指标的超买超卖策略'
        }
      ];
      
      // Mock API调用成功
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategiesList);
      
      // 验证初始状态
      expect(store.strategies).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
      
      // 调用fetchStrategies
      await store.fetchStrategies();
      
      // 验证最终状态
      expect(store.strategies).toEqual(mockStrategiesList);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should set error state on failure', async () => {
      const store = useStrategyStore();
      const errorMessage = '网络连接失败';
      
      // Mock API调用失败
      vi.mocked(strategyApi.getStrategies).mockRejectedValue(new Error(errorMessage));
      
      // 验证初始状态
      expect(store.strategies).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
      
      // 调用fetchStrategies
      await store.fetchStrategies();
      
      // 验证失败后的状态
      expect(store.strategies).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBe(errorMessage);
    });
  });

  describe('契约 B: 获取策略列表 (fetchStrategies) - 原有测试', () => {
    it('成功获取策略列表后，应更新strategies状态 - TDD', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      await store.fetchStrategies();
      
      expect(store.strategies).toEqual(mockStrategies);
      expect(store.isLoading).toBe(false);
    });

    it('获取失败时，应保持strategies为空数组并清除loading - TDD', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategies).mockRejectedValue(new Error('网络错误'));
      
      await store.fetchStrategies();
      
      expect(store.strategies).toEqual([]);
      expect(store.isLoading).toBe(false);
    });
  });

  describe('契约 C: 获取单个策略 (fetchStrategyById)', () => {
    it('成功获取策略详情后，应更新currentStrategy状态 - TDD', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategy).mockResolvedValue({ data: mockStrategy });
      
      await store.fetchStrategyById('1');
      
      expect(store.currentStrategy).toEqual(mockStrategy);
      expect(store.isLoading).toBe(false);
    });

    it('获取失败时，应保持currentStrategy不变并清除loading - TDD', async () => {
      const store = useStrategyStore();
      vi.mocked(strategyApi.getStrategy).mockRejectedValue(new Error('策略不存在'));
      
      await store.fetchStrategyById('999');
      
      expect(store.currentStrategy).toBeNull();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('契约 D: 创建策略 (createNewStrategy)', () => {
    it('成功创建策略后，应重新获取策略列表 - TDD', async () => {
      const store = useStrategyStore();
      const newStrategyData = { name: '新策略', description: '新描述' };
      
      vi.mocked(strategyApi.createStrategy).mockResolvedValue({ data: mockStrategy });
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      await store.createNewStrategy(newStrategyData);
      
      expect(strategyApi.createStrategy).toHaveBeenCalledWith(newStrategyData);
      expect(strategyApi.getStrategies).toHaveBeenCalled();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('契约 E: 更新策略 (updateExistingStrategy)', () => {
    it('成功更新策略后，应重新获取策略列表 - TDD', async () => {
      const store = useStrategyStore();
      const updateData = { name: '更新的策略名' };
      
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue({ data: mockStrategy });
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      await store.updateExistingStrategy('1', updateData);
      
      expect(strategyApi.updateStrategy).toHaveBeenCalledWith('1', updateData);
      expect(strategyApi.getStrategies).toHaveBeenCalled();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('契约 F: 删除策略 (deleteExistingStrategy)', () => {
    it('成功删除策略后，应重新获取策略列表 - TDD', async () => {
      const store = useStrategyStore();
      
      vi.mocked(strategyApi.deleteStrategy).mockResolvedValue({ data: null });
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      await store.deleteExistingStrategy('1');
      
      expect(strategyApi.deleteStrategy).toHaveBeenCalledWith('1');
      expect(strategyApi.getStrategies).toHaveBeenCalled();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('契约 G: 新建策略流程 (startNewStrategyCreation) - 新增测试', () => {
    it('调用 startNewStrategyCreation 时，应设置 currentSelectedStrategy 为默认空策略对象且 id 为 null - TDD', () => {
      const store = useStrategyStore();
      
      // 先设置一个已选中的策略
      store.setCurrentSelectedStrategy(mockStrategy);
      expect(store.currentSelectedStrategy).toEqual(mockStrategy);
      
      // 调用开始新建策略的 action
      store.startNewStrategyCreation();
      
      // 验证 currentSelectedStrategy 被设置为新的空策略对象
      expect(store.currentSelectedStrategy).toEqual({
        id: null,
        name: '',
        description: '',
        content: '',
        is_public: false,
        buy_factors: [],
        sell_factors: [],
        author: '',
        create_time: null,
        update_time: null
      });
      
      // 特别验证 id 必须为 null，这代表这是一个新策略
      expect(store.currentSelectedStrategy.id).toBeNull();
    });
    
    it('调用 startNewStrategyCreation 时，左侧列表的选中状态应被清除 - TDD', () => {
      const store = useStrategyStore();
      
      // 先设置策略列表和选中状态
      store.strategies = mockStrategies;
      store.setCurrentSelectedStrategy(mockStrategies[0]);
      expect(store.currentSelectedStrategy.id).toBe('1');
      
      // 调用开始新建策略的 action
      store.startNewStrategyCreation();
      
      // 验证选中状态被清除（通过 id 为 null 来判断）
      expect(store.currentSelectedStrategy.id).toBeNull();
      
      // 验证策略列表本身不受影响
      expect(store.strategies).toEqual(mockStrategies);
    });
  });

  describe('契约 H: 创建策略 API 调用测试 (createStrategy) - 新增测试', () => {
    it('调用 createStrategy 时，应调用 POST /api/v1/strategy API - TDD', async () => {
      const store = useStrategyStore();
      const newStrategyData = {
        name: '新建策略',
        description: '新建策略描述',
        content: '',
        is_public: false,
        buy_factors: [],
        sell_factors: [],
        parameters: {}
      };
      
      // Mock API调用成功
      vi.mocked(strategyApi.createStrategy).mockResolvedValue({
        id: 'new-strategy-id',
        ...newStrategyData,
        author: 'test_user',
        create_time: '2025-01-01T00:00:00Z'
      } as any);
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      // 调用 createStrategy action（注意：实际方法名是 createNewStrategy）
      await store.createNewStrategy(newStrategyData);
      
      // 验证 POST API 被正确调用
      expect(strategyApi.createStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.createStrategy).toHaveBeenCalledWith(newStrategyData);
      
      // 验证创建成功后重新获取策略列表
      expect(strategyApi.getStrategies).toHaveBeenCalledTimes(1);
      
      // 验证状态更新
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('createStrategy API 调用失败时，应正确设置错误状态 - TDD', async () => {
      const store = useStrategyStore();
      const newStrategyData = {
        name: '失败的策略',
        description: '这个策略会创建失败',
        content: '',
        is_public: false
      };
      const errorMessage = '策略名称已存在';
      
      // Mock API调用失败
      vi.mocked(strategyApi.createStrategy).mockRejectedValue(new Error(errorMessage));
      
      // 调用 createStrategy action
      await store.createNewStrategy(newStrategyData);
      
      // 验证 POST API 被调用
      expect(strategyApi.createStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.createStrategy).toHaveBeenCalledWith(newStrategyData);
      
      // 验证错误状态设置
      expect(store.error).toBe(errorMessage);
      expect(store.isLoading).toBe(false);
      
      // 验证失败时不会调用 getStrategies
      expect(strategyApi.getStrategies).not.toHaveBeenCalled();
    });
  });

  describe('契约 I: 更新策略 API 调用测试 (updateStrategy) - 新增测试', () => {
    it('调用 updateStrategy 时，应调用 PUT /api/v1/strategy/{id} API - TDD', async () => {
      const store = useStrategyStore();
      const strategyId = 'strategy-123';
      const updateData = {
        name: '更新后的策略名称',
        description: '更新后的描述',
        is_public: true
      };
      
      // Mock API调用成功
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue({
        id: strategyId,
        ...updateData,
        author: 'test_user'
      } as any);
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      // 调用 updateStrategy action（注意：实际方法名是 updateExistingStrategy）
      await store.updateExistingStrategy(strategyId, updateData);
      
      // 验证 PUT API 被正确调用
      expect(strategyApi.updateStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.updateStrategy).toHaveBeenCalledWith(strategyId, updateData);
      
      // 验证更新成功后重新获取策略列表
      expect(strategyApi.getStrategies).toHaveBeenCalledTimes(1);
      
      // 验证状态更新
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('updateStrategy API 调用失败时，应正确设置错误状态 - TDD', async () => {
      const store = useStrategyStore();
      const strategyId = 'non-existent-strategy';
      const updateData = {
        name: '无法更新的策略'
      };
      const errorMessage = '策略不存在或无权限修改';
      
      // Mock API调用失败
      vi.mocked(strategyApi.updateStrategy).mockRejectedValue(new Error(errorMessage));
      
      // 调用 updateStrategy action
      await store.updateExistingStrategy(strategyId, updateData);
      
      // 验证 PUT API 被调用
      expect(strategyApi.updateStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.updateStrategy).toHaveBeenCalledWith(strategyId, updateData);
      
      // 验证错误状态设置
      expect(store.error).toBe(errorMessage);
      expect(store.isLoading).toBe(false);
      
      // 验证失败时不会调用 getStrategies
      expect(strategyApi.getStrategies).not.toHaveBeenCalled();
    });

    it('updateStrategy 应正确处理空的更新数据 - TDD', async () => {
      const store = useStrategyStore();
      const strategyId = 'strategy-456';
      const emptyUpdateData = {};
      
      // Mock API调用成功
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue({
        id: strategyId,
        name: '原策略名称'
      } as any);
      vi.mocked(strategyApi.getStrategies).mockResolvedValue(mockStrategies);
      
      // 调用 updateStrategy action with empty data
      await store.updateExistingStrategy(strategyId, emptyUpdateData);
      
      // 验证 PUT API 被调用，即使数据为空
      expect(strategyApi.updateStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.updateStrategy).toHaveBeenCalledWith(strategyId, emptyUpdateData);
      
      // 验证成功状态
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });
  });

  describe('契约 J: 直接 API 调用方法测试 (createStrategy & updateStrategy) - 新增测试', () => {
    it('应该存在 createStrategy action，直接调用 POST /api/v1/strategy API - TDD', async () => {
      const store = useStrategyStore();
      const newStrategyData = {
        name: '直接创建的策略',
        description: '通过 createStrategy 方法创建',
        content: '',
        is_public: false,
        parameters: {}
      };
      
      // Mock API调用成功
      vi.mocked(strategyApi.createStrategy).mockResolvedValue({
        id: 'direct-create-id',
        ...newStrategyData,
        author: 'test_user'
      } as any);
      
      // 验证 createStrategy 方法存在并可调用
      expect(typeof store.createStrategy).toBe('function');
      
      // 调用 createStrategy action
      const result = await store.createStrategy(newStrategyData);
      
      // 验证 POST API 被正确调用
      expect(strategyApi.createStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.createStrategy).toHaveBeenCalledWith(newStrategyData);
      
      // 验证返回结果
      expect(result).toBeDefined();
      expect(result.id).toBe('direct-create-id');
    });

    it('应该存在 updateStrategy action，直接调用 PUT /api/v1/strategy/{id} API - TDD', async () => {
      const store = useStrategyStore();
      const strategyId = 'strategy-to-update';
      const updateData = {
        name: '直接更新的策略',
        description: '通过 updateStrategy 方法更新'
      };
      
      // Mock API调用成功
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue({
        id: strategyId,
        ...updateData,
        author: 'test_user'
      } as any);
      
      // 验证 updateStrategy 方法存在并可调用
      expect(typeof store.updateStrategy).toBe('function');
      
      // 调用 updateStrategy action
      const result = await store.updateStrategy(strategyId, updateData);
      
      // 验证 PUT API 被正确调用
      expect(strategyApi.updateStrategy).toHaveBeenCalledTimes(1);
      expect(strategyApi.updateStrategy).toHaveBeenCalledWith(strategyId, updateData);
      
      // 验证返回结果
      expect(result).toBeDefined();
      expect(result.id).toBe(strategyId);
    });

    it('createStrategy 应该不会自动刷新策略列表 - TDD', async () => {
      const store = useStrategyStore();
      const newStrategyData = {
        name: '不刷新列表的策略',
        description: '直接 API 调用不应自动刷新'
      };
      
      // Mock API调用成功
      vi.mocked(strategyApi.createStrategy).mockResolvedValue({
        id: 'no-refresh-id',
        ...newStrategyData
      } as any);
      
      // 调用 createStrategy action
      await store.createStrategy(newStrategyData);
      
      // 验证 createStrategy API 被调用
      expect(strategyApi.createStrategy).toHaveBeenCalledTimes(1);
      
      // 验证 getStrategies 不会被自动调用（与 createNewStrategy 的区别）
      expect(strategyApi.getStrategies).not.toHaveBeenCalled();
    });

    it('updateStrategy 应该不会自动刷新策略列表 - TDD', async () => {
      const store = useStrategyStore();
      const strategyId = 'no-refresh-update';
      const updateData = {
        name: '不刷新列表的更新'
      };
      
      // Mock API调用成功
      vi.mocked(strategyApi.updateStrategy).mockResolvedValue({
        id: strategyId,
        ...updateData
      } as any);
      
      // 调用 updateStrategy action
      await store.updateStrategy(strategyId, updateData);
      
      // 验证 updateStrategy API 被调用
      expect(strategyApi.updateStrategy).toHaveBeenCalledTimes(1);
      
      // 验证 getStrategies 不会被自动调用（与 updateExistingStrategy 的区别）
      expect(strategyApi.getStrategies).not.toHaveBeenCalled();
    });
  });
});