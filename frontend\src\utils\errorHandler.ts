/**
 * 统一错误处理工具
 * 提供类型安全的错误处理和用户友好的错误消息
 */

import { ElMessage, ElMessageBox } from 'element-plus'
import { ERROR_MESSAGES } from '@/constants/strategy'

// 错误类型定义
export interface AppError {
  code: string
  message: string
  details?: any
  context?: string
}

// 预定义的错误类型
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  AUTH_ERROR: 'AUTH_ERROR'
} as const

export type ErrorType = typeof ERROR_TYPES[keyof typeof ERROR_TYPES]

/**
 * 错误处理器类
 */
export class ErrorHandler {
  /**
   * 显示错误消息
   */
  static showError(error: AppError | Error | string, options?: {
    showClose?: boolean
    duration?: number
    type?: 'error' | 'warning'
  }) {
    const { showClose = true, duration = 3000, type = 'error' } = options || {}
    
    let message: string
    
    if (typeof error === 'string') {
      message = error
    } else if (error instanceof Error) {
      message = error.message
    } else {
      message = error.message
    }
    
    console.error('[ErrorHandler]', message)
    ElMessage({
      message,
      type,
      duration,
      showClose
    })
  }

  /**
   * 显示成功消息
   */
  static showSuccess(message: string, options?: {
    showClose?: boolean
    duration?: number
  }) {
    const { showClose = true, duration = 3000 } = options || {}
    
    console.log('[ErrorHandler]', message)
    ElMessage({
      message,
      type: 'success',
      duration,
      showClose
    })
  }

  /**
   * 显示警告消息
   */
  static showWarning(message: string, options?: {
    showClose?: boolean
    duration?: number
  }) {
    console.warn('[ErrorHandler]', message)
    ElMessage({
      message,
      type: 'warning',
      duration: options?.duration ?? 3000,
      showClose: options?.showClose ?? true
    })
  }

  /**
   * 显示确认对话框
   */
  static async confirm(message: string, title?: string): Promise<boolean> {
    try {
      await ElMessageBox.confirm(
        message,
        title || '确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      return true
    } catch {
      return false
    }
  }

  /**
   * 处理API错误
   */
  static handleApiError(error: any, context?: string): AppError {
    console.error('[API Error]', error)
    
    let appError: AppError
    
    // 处理 null 或 undefined 错误
    if (!error) {
      appError = {
        code: ERROR_TYPES.UNKNOWN_ERROR,
        message: '未知错误',
        context
      }
    }
    // 字符串错误
    else if (typeof error === 'string') {
      appError = {
        code: ERROR_TYPES.UNKNOWN_ERROR,
        message: error,
        context
      }
    }
    // 对象错误（可能包含 code 和 message）
    else if (typeof error === 'object' && error !== null && error.code && error.message) {
      let message = error.message
      
      // 根据错误代码映射特定消息
      if (error.code === 'AUTH_ERROR') {
        message = '认证失败，请重新登录'
      } else if (error.code === 'UNKNOWN_ERROR') {
        message = '系统错误，请稍后重试'
      } else if (error.code === 'NETWORK_ERROR') {
        message = '网络连接失败，请检查网络设置'
      }
      
      appError = {
        code: error.code || ERROR_TYPES.UNKNOWN_ERROR,
        message: message || '未知错误',
        details: error,
        context
      }
    }
    // 网络错误
    else if (!window.navigator.onLine) {
      appError = {
        code: ERROR_TYPES.NETWORK_ERROR,
        message: ERROR_MESSAGES.NETWORK_ERROR,
        context
      }
    }
    // HTTP状态码错误
    else if (error.response) {
      const status = error.response.status
      
      switch (status) {
        case 400:
          appError = {
            code: ERROR_TYPES.VALIDATION_ERROR,
            message: error.response.data?.message || '请求参数错误',
            details: error.response.data,
            context
          }
          break
          
        case 401:
        case 403:
          appError = {
            code: ERROR_TYPES.PERMISSION_ERROR,
            message: '权限不足或登录已过期',
            context
          }
          break
          
        case 404:
          appError = {
            code: ERROR_TYPES.NOT_FOUND_ERROR,
            message: '请求的资源不存在',
            context
          }
          break
          
        case 500:
        case 502:
        case 503:
          appError = {
            code: ERROR_TYPES.SERVER_ERROR,
            message: '服务器内部错误，请稍后重试',
            context
          }
          break
          
        default:
          appError = {
            code: ERROR_TYPES.UNKNOWN_ERROR,
            message: error.response.data?.message || '未知错误',
            details: error.response.data,
            context
          }
      }
    }
    // 请求错误（网络问题等）
    else if (error.request) {
      appError = {
        code: ERROR_TYPES.NETWORK_ERROR,
        message: ERROR_MESSAGES.NETWORK_ERROR,
        context
      }
    }
    // 其他错误
    else {
      appError = {
        code: ERROR_TYPES.UNKNOWN_ERROR,
        message: error.message || '未知错误',
        context
      }
    }
    
    this.showError(appError)
    return appError
  }

  /**
   * 处理业务逻辑错误
   */
  static handleBusinessError(error: any, customMessage?: string): AppError {
    console.error(`[Business Error]`, error)
    
    let appError: AppError
    if (error instanceof Error) {
      appError = {
        code: ERROR_TYPES.VALIDATION_ERROR,
        message: customMessage || error.message || '操作失败'
      }
    } else {
      appError = {
        code: ERROR_TYPES.UNKNOWN_ERROR,
        message: customMessage || (typeof error === 'string' ? error : '操作失败')
      }
    }
    
    this.showError(appError)
    return appError
  }

  /**
   * 安全的异步操作执行器
   */
  static async safeExecute<T>(
    operation: (...args: any[]) => Promise<T> | T,
    ...args: any[]
  ): Promise<T | null> {
    try {
      const result = await operation(...args)
      return result
    } catch (error) {
      console.error('[Safe Execute]', error)
      const appError = this.handleApiError(error)
      this.showError(appError)
      return null
    }
  }

  /**
   * 断言函数
   */
  static assert(condition: any, message: string = '断言失败'): asserts condition {
    if (!condition) {
      const error = new Error(message)
      console.error('Assertion failed:', message)
      throw error
    }
  }

  /**
   * 错误处理装饰器
   */
  static withErrorHandling<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    context?: string
  ): T {
    return (async (...args: any[]) => {
      try {
        return await fn(...args)
      } catch (error) {
        const appError = ErrorHandler.handleApiError(error, context)
        ErrorHandler.showError(appError)
        return undefined
      }
    }) as T
  }

  /**
   * 开发环境调试工具
   */
  static dev = {
    log: (message: string, data?: any) => {
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        console.log(`[DEV] ${message}`, data)
      }
    },
    
    warn: (message: string, data?: any) => {
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        console.warn(`[DEV] ${message}`, data)
      }
    },
    
    error: (message: string, error?: any) => {
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        console.error(`[DEV] ${message}`, error)
      }
    }
  }
}

/**
 * 便捷的错误处理函数
 */
export const handleError = ErrorHandler.handleApiError
export const showError = ErrorHandler.showError
export const showSuccess = ErrorHandler.showSuccess
export const showWarning = ErrorHandler.showWarning
export const confirm = ErrorHandler.confirm
export const safeExecute = ErrorHandler.safeExecute

/**
 * 错误边界装饰器（用于组合函数）
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: string
): T {
  return (async (...args: any[]) => {
    return await ErrorHandler.safeExecute(
      () => fn(...args),
      { context, showError: true }
    )
  }) as T
}

/**
 * 类型安全的断言函数
 */
export function assert(condition: any, message: string): asserts condition {
  if (!condition) {
    const error = new Error(message)
    console.error('Assertion failed:', message)
    throw error
  }
}

/**
 * 开发环境调试工具
 */
export const dev = {
  log: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      console.log(`[DEV] ${message}`, data)
    }
  },
  
  warn: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      console.warn(`[DEV] ${message}`, data)
    }
  },
  
  error: (message: string, error?: any) => {
    if (import.meta.env.DEV) {
      console.error(`[DEV] ${message}`, error)
    }
  }
}
