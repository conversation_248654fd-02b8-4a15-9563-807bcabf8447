# -*- coding: utf-8 -*-
"""
市场数据服务通用工具函数
"""
import math
from typing import Optional

def safe_float(value) -> Optional[float]:
    """
    安全地转换为浮点数，处理NaN和无效值
    
    Args:
        value: 要转换的值
        
    Returns:
        Optional[float]: 处理后的浮点数，如果是NaN或无效则返回None
    """
    if value is None:
        return None
    
    try:
        # 尝试转换为浮点数
        float_val = float(value)
        
        # 检查是否是NaN或无穷大
        if math.isnan(float_val) or math.isinf(float_val):
            return None
        return float_val
    except (ValueError, TypeError):
        return None
