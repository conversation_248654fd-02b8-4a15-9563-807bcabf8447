"""配置管理模块

提供统一的配置管理、环境变量处理和配置验证功能。
"""

import os
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30
    pool_recycle: int = 3600

@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

@dataclass
class AbuPyConfig:
    """AbuPy相关配置"""
    data_cache_dir: str = "./data_cache"
    enable_cache: bool = True
    cache_expire_days: int = 7
    max_symbols_per_request: int = 100
    request_timeout: int = 30

@dataclass
class PerformanceConfig:
    """性能相关配置"""
    max_concurrent_strategies: int = 5
    strategy_timeout: int = 300  # 5分钟
    memory_limit_mb: int = 2048
    enable_profiling: bool = False

@dataclass
class SecurityConfig:
    """安全相关配置"""
    secret_key: str = ""
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    allowed_hosts: List[str] = field(default_factory=lambda: ["*"])
    cors_origins: List[str] = field(default_factory=lambda: ["*"])

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, env: Environment = None):
        self.env = env or Environment(os.getenv("ENVIRONMENT", "development"))
        self._config_cache: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 数据库配置
        self.database = DatabaseConfig(
            url=self._get_env("DATABASE_URL", "sqlite:///./abu_modern.db"),
            echo=self._get_env_bool("DB_ECHO", False),
            pool_size=self._get_env_int("DB_POOL_SIZE", 5),
            max_overflow=self._get_env_int("DB_MAX_OVERFLOW", 10)
        )
        
        # 日志配置
        self.logging = LoggingConfig(
            level=self._get_env("LOG_LEVEL", "INFO"),
            file_path=self._get_env("LOG_FILE_PATH"),
            max_file_size=self._get_env_int("LOG_MAX_FILE_SIZE", 10 * 1024 * 1024),
            backup_count=self._get_env_int("LOG_BACKUP_COUNT", 5)
        )
        
        # AbuPy配置
        self.abupy = AbuPyConfig(
            data_cache_dir=self._get_env("ABUPY_CACHE_DIR", "./data_cache"),
            enable_cache=self._get_env_bool("ABUPY_ENABLE_CACHE", True),
            cache_expire_days=self._get_env_int("ABUPY_CACHE_EXPIRE_DAYS", 7),
            max_symbols_per_request=self._get_env_int("ABUPY_MAX_SYMBOLS", 100)
        )
        
        # 性能配置
        self.performance = PerformanceConfig(
            max_concurrent_strategies=self._get_env_int("MAX_CONCURRENT_STRATEGIES", 5),
            strategy_timeout=self._get_env_int("STRATEGY_TIMEOUT", 300),
            memory_limit_mb=self._get_env_int("MEMORY_LIMIT_MB", 2048),
            enable_profiling=self._get_env_bool("ENABLE_PROFILING", False)
        )
        
        # 安全配置
        self.security = SecurityConfig(
            secret_key=self._get_env("SECRET_KEY", "your-secret-key-here"),
            algorithm=self._get_env("ALGORITHM", "HS256"),
            access_token_expire_minutes=self._get_env_int("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
        )
    
    def _get_env(self, key: str, default: str = None) -> str:
        """获取环境变量"""
        return os.getenv(key, default)
    
    def _get_env_bool(self, key: str, default: bool = False) -> bool:
        """获取布尔类型环境变量"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def _get_env_int(self, key: str, default: int = 0) -> int:
        """获取整数类型环境变量"""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    def _get_env_list(self, key: str, default: List[str] = None, separator: str = ",") -> List[str]:
        """获取列表类型环境变量"""
        value = os.getenv(key)
        if not value:
            return default or []
        return [item.strip() for item in value.split(separator)]
    
    def get_config(self, section: str, key: str = None) -> Any:
        """获取配置值"""
        section_config = getattr(self, section, None)
        if not section_config:
            return None
        
        if key:
            return getattr(section_config, key, None)
        return section_config
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 验证数据库配置
        if not self.database.url:
            errors.append("数据库URL未配置")
        
        # 验证安全配置
        if self.env == Environment.PRODUCTION:
            if self.security.secret_key == "your-secret-key-here":
                errors.append("生产环境必须设置安全的SECRET_KEY")
        
        # 验证目录权限
        cache_dir = Path(self.abupy.data_cache_dir)
        try:
            cache_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            errors.append(f"无法创建缓存目录: {cache_dir}")
        
        return errors
    
    def setup_logging(self):
        """设置日志配置"""
        log_config = {
            'level': getattr(logging, self.logging.level.upper()),
            'format': self.logging.format
        }
        
        if self.logging.file_path:
            from logging.handlers import RotatingFileHandler
            handler = RotatingFileHandler(
                self.logging.file_path,
                maxBytes=self.logging.max_file_size,
                backupCount=self.logging.backup_count
            )
            handler.setFormatter(logging.Formatter(self.logging.format))
            
            root_logger = logging.getLogger()
            root_logger.setLevel(log_config['level'])
            root_logger.addHandler(handler)
        else:
            logging.basicConfig(**log_config)
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "environment": self.env.value,
            "python_version": os.sys.version,
            "platform": os.name,
            "working_directory": os.getcwd(),
            "config_validation_errors": self.validate_config()
        }

# 全局配置实例
config_manager = ConfigManager()

# 便捷访问函数
def get_database_config() -> DatabaseConfig:
    """获取数据库配置"""
    return config_manager.database

def get_abupy_config() -> AbuPyConfig:
    """获取AbuPy配置"""
    return config_manager.abupy

def get_performance_config() -> PerformanceConfig:
    """获取性能配置"""
    return config_manager.performance

def is_development() -> bool:
    """是否为开发环境"""
    return config_manager.env == Environment.DEVELOPMENT

def is_production() -> bool:
    """是否为生产环境"""
    return config_manager.env == Environment.PRODUCTION