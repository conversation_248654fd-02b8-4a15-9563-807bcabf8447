// ===== UI组件样式增强 =====

// ===== 按钮样式增强 =====
.el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  // 主要按钮
  &.el-button--primary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--text-color-inverse);
    
    &:hover {
      background-color: var(--color-primary-light);
      border-color: var(--color-primary-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }
    
    &:active {
      background-color: var(--color-primary-dark);
      border-color: var(--color-primary-dark);
      transform: translateY(0);
    }
  }
  
  // 成功按钮
  &.el-button--success {
    background-color: var(--color-success);
    border-color: var(--color-success);
    
    &:hover {
      background-color: var(--color-success-light);
      border-color: var(--color-success-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
    }
  }
  
  // 警告按钮
  &.el-button--warning {
    background-color: var(--color-warning);
    border-color: var(--color-warning);
    
    &:hover {
      background-color: var(--color-warning-light);
      border-color: var(--color-warning-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
    }
  }
  
  // 危险按钮
  &.el-button--danger {
    background-color: var(--color-danger);
    border-color: var(--color-danger);
    color: var(--text-color-inverse); /* 确保文本颜色为白色 */
    
    &:hover {
      background-color: var(--color-danger-light);
      border-color: var(--color-danger-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
    }
    
    /* Plain样式的危险按钮 */
    &.is-plain {
      background-color: transparent;
      color: var(--color-danger); /* 文本颜色为红色 */
      border-color: var(--color-danger);
      
      &:hover {
        background-color: var(--color-danger);
        color: var(--text-color-inverse); /* 悬停时文本变白色 */
        border-color: var(--color-danger);
      }
    }
  }
  
  // 默认按钮
  &.el-button--default {
    background-color: var(--bg-color-card);
    border-color: var(--border-color-base);
    color: var(--text-color-primary);
    
    &:hover {
      background-color: var(--bg-color-hover);
      border-color: var(--color-primary);
      color: var(--color-primary);
      transform: translateY(-1px);
    }
  }
}

// ===== 标签样式增强 =====
.el-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
  
  // 成功标签（运行中）
  &.el-tag--success {
    background-color: var(--bg-color-profit);
    color: var(--color-profit-dark);
    border: 1px solid var(--color-profit-light);
  }
  
  // 危险标签（已停止）
  &.el-tag--danger {
    background-color: var(--bg-color-loss);
    color: var(--color-loss-dark);
    border: 1px solid var(--color-loss-light);
  }
  
  // 警告标签
  &.el-tag--warning {
    background-color: #fff7e6;
    color: var(--color-warning-dark);
    border: 1px solid var(--color-warning-light);
  }
  
  // 信息标签
  &.el-tag--info {
    background-color: var(--bg-color-active);
    color: var(--color-info-dark);
    border: 1px solid var(--color-info-light);
  }
  
  // 默认标签
  &.el-tag--default {
    background-color: var(--bg-color-container);
    color: var(--text-color-secondary);
    border: 1px solid var(--border-color-light);
  }
}

// ===== 输入框样式增强 =====
.el-input {
  .el-input__wrapper {
    background-color: var(--bg-color-card);
    border: 1px solid var(--border-color-base);
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--color-primary-light);
    }
    
    &.is-focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }
  
  .el-input__inner {
    color: var(--text-color-primary);
    
    &::placeholder {
      color: var(--text-color-placeholder);
    }
  }
}

// ===== 选择器样式增强 =====
.el-select {
  .el-select__wrapper {
    background-color: var(--bg-color-card);
    border: 1px solid var(--border-color-base);
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--color-primary-light);
    }
    
    &.is-focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }
}

// ===== 表格样式增强 =====
.el-table {
  background-color: var(--bg-color-card);
  
  .el-table__header {
    background-color: var(--bg-color-container);
    
    th {
      background-color: var(--bg-color-container);
      color: var(--text-color-primary);
      font-weight: 600;
      border-bottom: 1px solid var(--border-color-split);
    }
  }
  
  .el-table__body {
    tr {
      background-color: var(--bg-color-card);
      
      &:hover {
        background-color: var(--bg-color-hover);
      }
      
      td {
        border-bottom: 1px solid var(--border-color-split);
        color: var(--text-color-primary);
      }
    }
  }
  
  // 盈利行
  .profit-row {
    background-color: var(--bg-color-profit-light);
    
    &:hover {
      background-color: var(--bg-color-profit);
    }
  }
  
  // 亏损行
  .loss-row {
    background-color: var(--bg-color-loss-light);
    
    &:hover {
      background-color: var(--bg-color-loss);
    }
  }
}

// ===== 菜单样式增强 =====
.el-menu {
  background-color: var(--bg-color-card);
  border-right: 1px solid var(--border-color-split);
  
  .el-menu-item {
    color: var(--text-color-secondary);
    transition: all 0.3s ease;
    
    &:hover {
      background-color: var(--bg-color-hover);
      color: var(--color-primary);
    }
    
    &.is-active {
      background-color: var(--bg-color-active);
      color: var(--color-primary);
      border-right: 3px solid var(--color-primary);
    }
  }
  
  .el-sub-menu__title {
    color: var(--text-color-secondary);
    
    &:hover {
      background-color: var(--bg-color-hover);
      color: var(--color-primary);
    }
  }
}

// ===== 对话框样式增强 =====
.el-dialog {
  background-color: var(--bg-color-card);
  border-radius: 8px;
  box-shadow: var(--shadow-elevated);
  
  .el-dialog__header {
    background-color: var(--bg-color-card);
    border-bottom: 1px solid var(--border-color-split);
    border-radius: 8px 8px 0 0;
    
    .el-dialog__title {
      color: var(--text-color-primary);
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    background-color: var(--bg-color-card);
    color: var(--text-color-primary);
  }
  
  .el-dialog__footer {
    background-color: var(--bg-color-card);
    border-top: 1px solid var(--border-color-split);
    border-radius: 0 0 8px 8px;
  }
}