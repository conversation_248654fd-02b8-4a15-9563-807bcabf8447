// src/api/types/backtest.ts

// 回测配置接口
export interface BacktestConfig {
  strategy_id: string;
  symbol: string;
  start_date: string;
  end_date: string;
  capital: number;
  commission?: number;
  slippage?: number;
  benchmark?: string;
  parameters?: Record<string, any>;
}

// 回测状态枚举
export const BacktestStatus = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  STOPPED: 'stopped'
} as const;

export type BacktestStatus = typeof BacktestStatus[keyof typeof BacktestStatus];

// 回测任务信息
export interface BacktestTask {
  id: string;
  strategy_id: string;
  strategy_name: string;
  symbol: string;
  start_date: string;
  end_date: string;
  status: BacktestStatus;
  progress?: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  config: BacktestConfig;
}

// 回测结果统计
export interface BacktestMetrics {
  total_return: number;
  annual_return: number;
  max_drawdown: number;
  sharpe_ratio: number;
  win_rate: number;
  profit_loss_ratio: number;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  avg_holding_period: number;
  volatility: number;
  beta?: number;
  alpha?: number;
  information_ratio?: number;
}

// 交易记录
export interface Trade {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  amount: number;
  commission: number;
  timestamp: string;
  signal_type?: string;
  profit_loss?: number;
}

// 持仓记录
export interface Position {
  symbol: string;
  quantity: number;
  avg_cost: number;
  market_value: number;
  unrealized_pnl: number;
  weight: number;
}

// 资金曲线数据点
export interface EquityPoint {
  date: string;
  equity: number;
  benchmark?: number;
  drawdown: number;
}

// 完整回测结果
export interface BacktestResult {
  task_id: string;
  strategy_name: string;
  symbol: string;
  start_date: string;
  end_date: string;
  initial_capital: number;
  final_capital: number;
  metrics: BacktestMetrics;
  trades: Trade[];
  positions: Position[];
  equity_curve: EquityPoint[];
  generated_at: string;
}

// 回测历史查询参数
export interface BacktestHistoryParams {
  page?: number;
  page_size?: number;
  strategy_id?: string;
  symbol?: string;
  status?: BacktestStatus;
  start_date?: string;
  end_date?: string;
}

// API响应类型
export interface BacktestTaskResponse {
  success: boolean;
  data: BacktestTask;
  message?: string;
}

export interface BacktestResultResponse {
  success: boolean;
  data: BacktestResult;
  message?: string;
}

export interface BacktestHistoryResponse {
  success: boolean;
  data: BacktestTask[];
  total: number;
  page: number;
  page_size: number;
  message?: string;
}

export interface BacktestProgressResponse {
  success: boolean;
  data: {
    task_id: string;
    status: BacktestStatus;
    progress: number;
    current_date?: string;
    message?: string;
  };
  message?: string;
}

export interface BacktestStopResponse {
  success: boolean;
  data: BacktestTask;
  message?: string;
}