"""
数据库配置和核心逻辑模块

提供SQLite数据库引擎的创建和数据库表的初始化功能。
"""

import os
from typing import Optional

from sqlmodel import Session, SQLModel, create_engine


# 数据库URL，可通过环境变量配置，默认使用SQLite
DEFAULT_DB_URL = "sqlite:///./abu_strategy.db"
DB_URL = os.environ.get("DATABASE_URL", DEFAULT_DB_URL)

# 创建数据库引擎
# echo=True会输出所有SQL语句，方便调试，生产环境应设为False

# 生产环境风险说明:
# 1. SQLite本身设计为单用户文件数据库，在高并发生产环境中有性能和可靠性限制
# 2. check_same_thread=False用于允许FastAPI中多个并发请求共享同一数据库连接，但这会导致:
#    - 可能的数据竞争和一致性问题，特别是在多进程部署模式下
#    - 在事务管理和并发写操作下的数据完整性风险
#    - 不在事务的范围内的读/写操作可能会看到不一致的数据状态
# 3. 如需部署到生产环境，建议替换为:
#    - PostgreSQL或MySQL等支持全功能事务和并发的关系型数据库
#    - 移除check_same_thread参数，并使用连接池来管理数据库连接

engine = create_engine(
    DB_URL, 
    echo=False, 
    connect_args={"check_same_thread": False}  # 允许在多线程环境中使用SQLite，仅用于开发和测试
)


def create_db_and_tables():
    """
    创建数据库表
    在应用启动时调用此函数来确保所有必要的表已经创建
    """
    # 导入所有模型到SQLModel的元数据
    # 这行导入会触发模型注册，请勿删除
    from backend.app.models import strategy_model  # noqa
    
    # 创建所有表
    SQLModel.metadata.create_all(engine)


def get_session() -> Session:
    """
    获取数据库会话
    用作依赖注入，为API路由提供数据库会话对象

    Returns:
        Session: 数据库会话对象
    """
    with Session(engine) as session:
        yield session


def get_test_engine(db_url: Optional[str] = None):
    """
    获取用于测试的数据库引擎
    默认使用内存数据库，确保测试的独立性

    Args:
        db_url: 可选的测试数据库URL

    Returns:
        SQLAlchemy引擎对象
    """
    test_db_url = db_url or "sqlite:///:memory:"
    test_engine = create_engine(
        test_db_url, 
        echo=False,
        connect_args={"check_same_thread": False}
    )
    return test_engine


def create_test_db_and_tables(test_engine):
    """
    为测试创建数据库表

    Args:
        test_engine: 测试数据库引擎
    """
    # 导入所有模型到SQLModel的元数据
    from backend.app.models import strategy_model  # noqa
    
    # 创建所有表
    SQLModel.metadata.create_all(test_engine)
