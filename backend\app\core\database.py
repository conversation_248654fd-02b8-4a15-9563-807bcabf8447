"""数据库配置和核心逻辑模块

提供SQLite数据库引擎的创建和数据库表的初始化功能。
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.pool import StaticPool
from sqlmodel import Session, SQLModel

from backend.app.core.config import settings

# 只有当使用SQLite数据库时才需要创建目录
if settings.DATABASE_URL.startswith('sqlite:///') and not settings.DATABASE_URL.startswith('sqlite:///:memory:'):
    # 从DATABASE_URL中提取SQLite数据库文件路径
    db_path = settings.DATABASE_URL.replace('sqlite:///', '')
    # 创建数据库目录（如果不存在）
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

# 创建数据库引擎
engine_kwargs = {
    "echo": getattr(settings, 'DB_ECHO', False)  # 如果DB_ECHO不存在，默认为False
}

# 只有SQLite数据库需要这些特殊设置
if settings.DATABASE_URL.startswith('sqlite://'):
    engine_kwargs.update({
        "connect_args": {"check_same_thread": False},
        "poolclass": StaticPool
    })

engine = create_engine(settings.DATABASE_URL, **engine_kwargs)

# 创建会话工厂 - 使用 SQLModel 的 Session
def SessionLocal():
    return Session(engine)


def get_db():
    """获取数据库会话（已废弃，请使用get_session）
    
    用于FastAPI的依赖注入，提供数据库会话对象
    
    Yields:
        Session: 数据库会话对象
    """
    return get_session()


def get_session():
    """获取数据库会话
    
    用于FastAPI的依赖注入，提供数据库会话对象
    
    Yields:
        Session: 数据库会话对象
    """
    with Session(engine) as session:
        yield session


def create_db_and_tables():
    """创建数据库和表
    
    创建所有定义的表，用于应用启动时初始化数据库
    """
    # 导入所有模型以确保它们被注册到SQLModel中
    from backend.app.models import strategy_model
    
    # 创建所有表
    SQLModel.metadata.create_all(bind=engine)


# 保留init_db函数以兼容旧代码
def init_db():
    """初始化数据库（已废弃，请使用create_db_and_tables）
    
    创建所有定义的表
    """
    create_db_and_tables()


def get_test_engine():
    """获取测试用的数据库引擎
    
    创建一个基于内存的SQLite数据库引擎，用于测试
    
    Returns:
        Engine: SQLAlchemy引擎对象
    """
    from sqlalchemy import create_engine
    from sqlalchemy.pool import StaticPool
    
    # 使用内存数据库
    test_db_url = "sqlite:///:memory:"
    
    # 创建引擎
    test_engine = create_engine(
        test_db_url,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False
    )
    
    return test_engine


def create_test_db_and_tables(test_engine):
    """创建测试用的数据库表
    
    在测试引擎上创建所有定义的表
    
    Args:
        test_engine (Engine): 测试用的SQLAlchemy引擎对象
    """
    # 导入所有模型以确保它们被注册
    from backend.app.models import Strategy
    from sqlmodel import SQLModel
    
    # 创建所有表
    SQLModel.metadata.create_all(bind=test_engine)
