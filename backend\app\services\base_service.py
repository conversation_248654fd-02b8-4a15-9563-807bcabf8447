"""基础服务类模块

提供所有服务类的基础功能，包括统一的异常处理、日志记录等。
"""

from typing import Any, Dict, Optional
from sqlalchemy.orm import Session
from backend.app.core.exceptions import DataNotFoundError
from backend.app.constants.messages import StrategyMessages, SystemMessages
from backend.app.core.logging_config import setup_service_logger


class BaseService:
    """基础服务类
    
    提供所有服务类的通用功能，包括：
    - 统一的异常处理
    - 标准化的日志记录
    - 通用的验证方法
    """
    
    def __init__(self, session: Optional[Session] = None):
        """初始化基础服务
        
        Args:
            session: 数据库会话（可选）
        """
        self.session = session
        self.logger = setup_service_logger(self.__class__.__name__.lower().replace('service', ''))
    
    def _handle_not_found(self, entity_id: str, entity_type: str = "资源") -> None:
        """处理资源未找到的情况
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型描述
            
        Raises:
            DataNotFoundError: 资源未找到异常
        """
        message = f"未找到ID为 {entity_id} 的{entity_type}"
        self.logger.warning(f"资源未找到: {message}")
        raise DataNotFoundError(message=message, error_code=f"{entity_type.upper()}_NOT_FOUND")
    
    def _handle_strategy_not_found(self, strategy_id: str) -> None:
        """处理策略未找到的情况
        
        Args:
            strategy_id: 策略ID
            
        Raises:
            DataNotFoundError: 策略未找到异常
        """
        message = StrategyMessages.strategy_not_found(strategy_id)
        self.logger.warning(f"策略未找到: {message}")
        raise DataNotFoundError(message=message, error_code="STRATEGY_NOT_FOUND")
    
    def _log_operation_start(self, operation: str, **kwargs) -> None:
        """记录操作开始日志
        
        Args:
            operation: 操作名称
            **kwargs: 操作参数
        """
        params_str = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
        self.logger.debug(f"开始执行{operation}: {params_str}")
    
    def _log_operation_success(self, operation: str, result: Any = None) -> None:
        """记录操作成功日志
        
        Args:
            operation: 操作名称
            result: 操作结果（可选）
        """
        if result is not None:
            self.logger.info(f"{operation}成功完成，结果: {type(result).__name__}")
        else:
            self.logger.info(f"{operation}成功完成")
    
    def _log_operation_error(self, operation: str, error: Exception) -> None:
        """记录操作错误日志
        
        Args:
            operation: 操作名称
            error: 异常对象
        """
        self.logger.error(f"{operation}失败: {str(error)}", exc_info=True)
    
    def _validate_required_params(self, **params) -> None:
        """验证必需参数
        
        Args:
            **params: 参数字典，值为None的参数将被视为缺失
            
        Raises:
            ValueError: 当有必需参数缺失时
        """
        missing_params = [name for name, value in params.items() if value is None]
        if missing_params:
            raise ValueError(f"缺少必需参数: {', '.join(missing_params)}")