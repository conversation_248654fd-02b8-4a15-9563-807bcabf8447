// 简化数据工厂 - Backtest
// 用于TDD快速测试数据生成

import type {
  BacktestConfig,
  BacktestResult,
  BacktestTask,
  BacktestMetrics,
  Trade,
  Position,
  EquityPoint,
  BacktestStatus
} from '../../src/api/types/backtest';
import { BacktestStatus as Status } from '../../src/api/types/backtest';
import type {
  StrategyExecuteRequest,
  StrategyExecuteResponse
} from '../contracts/api-schemas';

/**
 * 简化的回测数据工厂类
 * 专为TDD快速循环和Extended测试设计
 * 提供固定、可预测的数据，无复杂业务逻辑
 */
export class SimpleBacktestDataFactory {
  /**
   * 创建简单的回测任务
   */
  static createSimpleTask(config: BacktestConfig): BacktestTask {
    return {
      id: `task-${Date.now()}`,
      strategy_id: config.strategy_id,
      strategy_name: `策略-${config.strategy_id}`,
      symbol: config.symbol,
      start_date: config.start_date,
      end_date: config.end_date,
      status: Status.PENDING,  // 修改：使用 Status 而不是 BacktestStatus
      progress: 0,
      created_at: new Date().toISOString(),
      config
    };
  }

  /**
   * 创建简单的回测结果
   */
  static createSimpleResult(taskId: string, scenario: 'success' | 'failure' = 'success'): BacktestResult {
    if (scenario === 'failure') {
      return {
        task_id: taskId,
        strategy_name: '测试策略',
        symbol: '000001',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        initial_capital: 100000,
        final_capital: 0,
        total_return: -1.0,
        metrics: this.createSimpleMetrics('failure'),
        trades: [],
        positions: [],
        equity_curve: [],
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString()
      };
    }

    return {
      task_id: taskId,
      strategy_name: '测试策略',
      symbol: '000001',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      initial_capital: 100000,
      final_capital: 120000,
      total_return: 0.2,
      metrics: this.createSimpleMetrics('success'),
      trades: this.createSimpleTrades(),
      positions: this.createSimplePositions(),
      equity_curve: this.createSimpleEquityCurve(),
      created_at: new Date().toISOString(),
      completed_at: new Date().toISOString()
    };
  }

  /**
   * 创建简单的策略执行响应
   */
  static createSimpleStrategyResponse(request: StrategyExecuteRequest): StrategyExecuteResponse {
    return {
      success: true,
      data: {
        task_id: `task-${Date.now()}`,
        status: 'pending',
        message: '回测任务已创建'
      }
    };
  }

  /**
   * 创建简单的回测指标
   */
  private static createSimpleMetrics(scenario: 'success' | 'failure'): BacktestMetrics {
    if (scenario === 'failure') {
      return {
        total_trades: 0,
        winning_trades: 0,
        losing_trades: 0,
        win_rate: 0,
        total_return: -1.0,
        annual_return: -1.0,
        max_drawdown: -1.0,
        sharpe_ratio: -10,
        profit_loss_ratio: 0,
        avg_holding_period: 0,
        volatility: 0,
        beta: 0,
        alpha: 0,
        information_ratio: 0
      };
    }

    return {
      total_trades: 10,
      winning_trades: 6,
      losing_trades: 4,
      win_rate: 0.6,
      total_return: 0.2,
      annual_return: 0.2,
      max_drawdown: -0.05,
      sharpe_ratio: 1.5,
      profit_loss_ratio: 1.2,
      avg_holding_period: 5,
      volatility: 0.15,
      beta: 1.0,
      alpha: 0.05,
      information_ratio: 0.8
    };
  }

  /**
   * 创建简单的交易记录
   */
  private static createSimpleTrades(): Trade[] {
    return [
      {
        id: 'trade-1',
        symbol: '000001',
        side: 'buy',
        quantity: 100,
        price: 10.0,
        timestamp: '2023-01-15T09:30:00Z',
        commission: 5.0,
        pnl: 0
      },
      {
        id: 'trade-2',
        symbol: '000001',
        side: 'sell',
        quantity: 100,
        price: 12.0,
        timestamp: '2023-01-20T15:00:00Z',
        commission: 5.0,
        pnl: 190.0
      }
    ];
  }

  /**
   * 创建简单的持仓记录
   */
  private static createSimplePositions(): Position[] {
    return [
      {
        symbol: '000001',
        quantity: 0,
        avg_price: 11.0,
        market_value: 0,
        unrealized_pnl: 0,
        realized_pnl: 190.0
      }
    ];
  }

  /**
   * 创建简单的权益曲线
   */
  private static createSimpleEquityCurve(): EquityPoint[] {
    return [
      {
        date: '2023-01-01',
        equity: 100000,
        drawdown: 0,
        benchmark: 100000
      },
      {
        date: '2023-06-30',
        equity: 110000,
        drawdown: 0,
        benchmark: 105000
      },
      {
        date: '2023-12-31',
        equity: 120000,
        drawdown: 0,
        benchmark: 108000
      }
    ];
  }

  /**
   * 创建简单的回测配置
   */
  static createSimpleConfig(overrides: Partial<BacktestConfig> = {}): BacktestConfig {
    return {
      strategy_id: 'simple-strategy',
      symbol: '000001',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      capital: 100000,
      commission: 0.001,
      slippage: 0.001,
      ...overrides
    };
  }

  /**
   * 创建简单的历史任务列表
   */
  static createSimpleHistory(count: number = 5): BacktestTask[] {
    const tasks: BacktestTask[] = [];
    
    for (let i = 0; i < count; i++) {
      const config = this.createSimpleConfig({
        strategy_id: `strategy-${i + 1}`
      });
      
      const task = this.createSimpleTask(config);
      task.id = `task-${String(i + 1).padStart(3, '0')}`;
      task.status = i % 3 === 0 ? Status.COMPLETED : 
                   i % 3 === 1 ? Status.RUNNING : Status.FAILED;
      task.progress = task.status === Status.COMPLETED ? 100 :
                     task.status === Status.RUNNING ? 50 : 0;
      
      if (task.status === Status.COMPLETED || task.status === Status.FAILED) {
        task.completed_at = new Date().toISOString();
      }
      
      if (task.status === Status.FAILED) {
        task.error_message = '模拟错误：数据加载失败';
      }
      
      tasks.push(task);
    }
    
    return tasks;
  }
}