import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StrategyWorkshop from '@/views/StrategyWorkshop.vue'
import { useStrategyStore } from '@/stores/modules/useStrategyStore'
import { useFactorsStore } from '@/stores/modules/useFactorsStore'

// 模拟策略Store
vi.mock('@/stores/modules/useStrategyStore')
vi.mock('@/stores/modules/useFactorsStore')

describe('StrategyWorkshop.vue', () => {
  let wrapper: VueWrapper<any>
  let mockStrategyStore: any
  let mockFactorsStore: any

  beforeEach(() => {
    // 重置所有模拟
    vi.clearAllMocks()

    // 设置Pinia
    const pinia = createPinia()
    setActivePinia(pinia)

    // 模拟策略Store
    mockStrategyStore = {
      strategies: [],
      currentSelectedStrategy: null,
      isLoading: false,
      error: null,
      fetchStrategies: vi.fn(),
      setCurrentSelectedStrategy: vi.fn(),
      startNewStrategyCreation: vi.fn(),
      createStrategy: vi.fn(),
      updateStrategy: vi.fn(),
      deleteStrategy: vi.fn()
    }
    
    // 模拟因子Store
    mockFactorsStore = {
      factors: [],
      fetchFactors: vi.fn()
    }
    
    // 正确设置模拟返回值
    vi.mocked(useStrategyStore).mockReturnValue(mockStrategyStore)
    vi.mocked(useFactorsStore).mockReturnValue(mockFactorsStore)

    wrapper = mount(StrategyWorkshop, {
      global: {
        plugins: [pinia],
        stubs: {
          'StrategyList': true,
          'StrategyEditor': true,
          'ElPageHeader': true
        }
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染策略工场组件', () => {
      expect(wrapper.find('.strategy-workshop').exists()).toBe(true)
    })

    it('应该包含策略列表组件', () => {
      expect(wrapper.findComponent({ name: 'StrategyList' }).exists()).toBe(true)
    })

    it('应该包含策略编辑器组件', () => {
      expect(wrapper.findComponent({ name: 'StrategyEditor' }).exists()).toBe(true)
    })

    it('应该正确挂载组件', () => {
      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('Store集成测试', () => {
    it('应该正确调用策略Store', () => {
      expect(useStrategyStore).toHaveBeenCalled()
    })

    it('应该在挂载时获取策略列表', () => {
      expect(mockStrategyStore.fetchStrategies).toHaveBeenCalled()
    })
  })
})