<template>
  <div class="factor-manager">
    <!-- 买入因子规则 -->
    <div class="rule-section">
      <h4>{{ UI_TEXT.BUY_RULES }}</h4>
      <div class="factors-list">
        <BuyFactorCard 
          v-for="(factor, index) in buyFactors" 
          :key="`buy-${index}`"
          :buy-factor="factor"
          @edit="handleEditFactor('buy', index, factor)"
          @delete="handleDeleteFactor('buy', index)"
          @update:exclusive-sell-rules="handleUpdateExclusiveSellRules"
          @remove-exclusive-sell-rule="handleRemoveExclusiveSellRule"
        />
      </div>
      <div class="list-actions">
        <el-button 
          type="primary" 
          size="default" 
          class="add-factor-btn"
          data-testid="add-buy-factor"
          @click="handleOpenDialog('buy')"
        >
          + {{ UI_TEXT.ADD_FACTOR }}
        </el-button>
      </div>
    </div>
    
    <!-- 卖出因子规则 -->
    <div class="rule-section">
      <h4>{{ UI_TEXT.SELL_RULES }}</h4>
      <div class="factors-list">
        <SellFactorCard 
          v-for="(factor, index) in sellFactors" 
          :key="`sell-${index}`"
          :sell-factor="factor"
          @edit="handleEditFactor('sell', index, factor)"
          @delete="handleDeleteFactor('sell', index)"
        />
      </div>
      <div class="list-actions">
        <el-button 
          type="primary" 
          size="default" 
          class="add-factor-btn"
          data-testid="add-sell-factor"
          @click="handleOpenDialog('sell')"
        >
          + {{ UI_TEXT.ADD_FACTOR }}
        </el-button>
      </div>
    </div>
    
    <!-- 因子选择对话框 -->
    <StrategyFormDialog
      v-model="isDialogVisible"
      :current-factor-type="currentFactorType"
      :editing-type="editingType"
      :editing-index="editingIndex"
      :strategy-data="strategy"
      @save="handleDialogSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Strategy } from '@/api/types'
import type { BuyFactor, SellFactor } from '@/types/factor'
import { useFactorSelection } from '@/composables/useFactorSelection'
import { UI_TEXT } from '@/constants/strategy'
import { 
  getFriendlyFactorName, 
  getFactorParameters, 
  formatParameterValue,
  hasParameters 
} from '@/utils/factorUtils'
import StrategyFormDialog from '@/components/forms/StrategyFormDialog.vue'
import BuyFactorCard from '@/components/strategy/BuyFactorCard.vue'
import SellFactorCard from '@/components/strategy/SellFactorCard.vue'

interface Props {
  strategy: Strategy
}

interface Emits {
  (e: 'update', updates: Partial<Strategy>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用因子选择组合函数
const {
  isDialogVisible,
  currentFactorType,
  editingType,
  editingIndex,
  openDialog,
  editFactor,
  deleteFactor,
  saveFactor
} = useFactorSelection()

// 计算属性
const buyFactors = computed(() => props.strategy?.buy_factors || [])
const sellFactors = computed(() => props.strategy?.sell_factors || [])

// 事件处理
const handleOpenDialog = (factorType: 'buy' | 'sell') => {
  openDialog(factorType)
}

const handleEditFactor = (type: 'buy' | 'sell', index: number, factor: BuyFactor | SellFactor) => {
  editFactor(type, index, factor)
}

const handleDeleteFactor = (type: 'buy' | 'sell', index: number) => {
  const factorUpdates = deleteFactor(props.strategy, type, index)
  // 合并更新到完整策略对象
  const updatedStrategy = { ...props.strategy, ...factorUpdates }
  emit('update', updatedStrategy)
}

const handleDialogSave = (factorPayload: BuyFactor | SellFactor) => {
  // 判断是新增还是编辑模式
  const isEditing = editingType.value && editingIndex.value !== null
  const factorType = currentFactorType.value || 'buy'
  
  let updatedStrategy: Strategy
  
  if (isEditing) {
    // 编辑模式：更新现有因子
    const factorUpdates = saveFactor(props.strategy, factorType, factorPayload, editingIndex.value)
    updatedStrategy = { ...props.strategy, ...factorUpdates }
  } else {
    // 新增模式：添加新因子
    const factorUpdates = saveFactor(props.strategy, factorType, factorPayload)
    updatedStrategy = { ...props.strategy, ...factorUpdates }
  }
  
  // 发出完整的策略更新
  emit('update', updatedStrategy)
  isDialogVisible.value = false
}

// 处理专属卖出规则更新
const handleUpdateExclusiveSellRules = (buyFactorId: string, sellFactors: SellFactor[]) => {
  // 找到对应的买入因子并更新其专属卖出规则
  const updatedBuyFactors = props.strategy.buy_factors?.map(factor => {
    if (factor.id === buyFactorId) {
      return {
        ...factor,
        sell_factors: sellFactors
      }
    }
    return factor
  }) || []
  
  const updatedStrategy = {
    ...props.strategy,
    buy_factors: updatedBuyFactors
  }
  
  emit('update', updatedStrategy)
}

// 处理移除专属卖出规则
const handleRemoveExclusiveSellRule = (sellFactorId: string) => {
  // 遍历所有买入因子，移除指定的专属卖出规则
  const updatedBuyFactors = props.strategy.buy_factors?.map(factor => {
    if (Array.isArray(factor.sell_factors)) {
      return {
        ...factor,
        sell_factors: factor.sell_factors.filter(sellFactor => sellFactor.id !== sellFactorId)
      }
    }
    return factor
  }) || []
  
  const updatedStrategy = {
    ...props.strategy,
    buy_factors: updatedBuyFactors
  }
  
  emit('update', updatedStrategy)
}
</script>

<style scoped>
.factor-manager {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.rule-section {
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  background: var(--bg-color-secondary);
}

.rule-section h4 {
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.factors-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

/* 卖出因子相关样式已迁移到 SellFactorCard.vue */

.list-actions {
  margin-top: var(--space-xs);
}

.add-factor-btn {
  font-size: var(--font-size-sm);
}
</style>
