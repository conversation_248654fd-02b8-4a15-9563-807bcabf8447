import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { getFactors, getFactor, createCustomFactor, testFactor } from '../../../src/api/factors';
import type { Factor, CustomFactorDefinition, FactorTestRequest } from '../../../src/api/types/factors';

/**
 * 因子API测试套件 - TDD版本
 * 
 * 本测试文件专注于核心功能的TDD测试，包括：
 * - 基本的因子CRUD操作
 * - 核心错误处理机制
 * - 基础的响应数据验证
 */

// 简化的Mock数据 - TDD专用
const mockFactor: Factor = {
  id: 'factor-1',
  name: 'RSI',
  description: '相对强弱指数',
  category: 'technical',
  data_type: 'numeric',
  formula: 'RSI = 100 - (100 / (1 + RS))',
  parameters: [],
  dependencies: ['close_price'],
  frequency: 'daily',
  lookback_period: 14,
  is_builtin: true,
  is_active: true,
  created_at: '2024-01-01T10:00:00Z',
  updated_at: '2024-01-01T10:00:00Z',
  usage_count: 0,
  rating: 0
};

// 简化的MSW handlers - TDD专用
const handlers = [
  // getFactors - 基础成功场景
  http.get('/api/factors', () => {
    return HttpResponse.json({
      success: true,
      data: [mockFactor],
      total: 1,
      page: 1,
      page_size: 10
    });
  }),
  
  // getFactor - 基础成功场景
  http.get('/api/factors/:id', ({ params }) => {
    const { id } = params;
    if (id === 'factor-1') {
      return HttpResponse.json({
        success: true,
        data: mockFactor
      });
    }
    return HttpResponse.json(
      { success: false, message: 'Factor not found' },
      { status: 404 }
    );
  }),
  
  // createCustomFactor - 基础成功场景
  http.post('/api/factors/custom', () => {
    return HttpResponse.json({
      success: true,
      data: { ...mockFactor, id: 'custom-factor-1', is_builtin: false }
    });
  }),
  
  // testFactor - 基础成功场景
  http.post('/api/factors/:id/test', () => {
    return HttpResponse.json({
      success: true,
      data: {
        factor_id: 'factor-1',
        test_period: { start_date: '2024-01-01', end_date: '2024-12-31' },
        statistics: { mean: 0.05, std: 0.15 }
      }
    });
  })
];

const server = setupServer(...handlers);

describe('Factors API - TDD', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('getFactors', () => {
    it('should return paginated factor list successfully', async () => {
      const response = await getFactors();
      expect(response.success).toBe(true);
      expect(response.data).toEqual([mockFactor]);
      expect(response.total).toBe(1);
    });

    it('should handle server error (500)', async () => {
      server.use(
        http.get('/api/factors', () => {
          return HttpResponse.json(
            { success: false, message: 'Internal server error' },
            { status: 500 }
          );
        })
      );

      await expect(getFactors()).rejects.toThrow('Internal server error');
    });
  });

  describe('getFactor', () => {
    it('should return single factor successfully', async () => {
      const response = await getFactor('factor-1');
      expect(response.success).toBe(true);
      expect(response.data).toEqual(mockFactor);
    });

    it('should handle factor not found (404)', async () => {
      await expect(getFactor('non-existent')).rejects.toThrow('Factor not found');
    });
  });

  describe('createCustomFactor', () => {
    it('should create custom factor successfully', async () => {
      const customFactor: CustomFactorDefinition = {
        name: 'Custom_RSI',
        description: '自定义RSI',
        category: 'custom',
        data_type: 'numeric',
        formula: 'custom formula',
        parameters: []
      };

      const response = await createCustomFactor(customFactor);
      expect(response.success).toBe(true);
      expect(response.data.is_builtin).toBe(false);
    });

    it('should handle validation error (400)', async () => {
      server.use(
        http.post('/api/factors/custom', () => {
          return HttpResponse.json(
            { success: false, message: 'Invalid factor definition' },
            { status: 400 }
          );
        })
      );

      const invalidFactor: CustomFactorDefinition = {
        name: '',
        description: '',
        category: 'custom',
        data_type: 'numeric',
        formula: '',
        parameters: []
      };

      await expect(createCustomFactor(invalidFactor)).rejects.toThrow('Invalid factor definition');
    });
  });

  describe('testFactor', () => {
    it('should test factor successfully', async () => {
      const testRequest: FactorTestRequest = {
        factor_id: 'factor-1',
        symbols: ['AAPL'],
        start_date: '2024-01-01',
        end_date: '2024-12-31'
      };

      const response = await testFactor(testRequest);
      expect(response.success).toBe(true);
      expect(response.data.factor_id).toBe('factor-1');
    });

    it('should handle test error (400)', async () => {
      server.use(
        http.post('/api/factors/:id/test', () => {
          return HttpResponse.json(
            { success: false, message: 'Invalid test parameters' },
            { status: 400 }
          );
        })
      );

      const invalidTestRequest: FactorTestRequest = {
        factor_id: 'factor-1',
        symbols: [],
        start_date: 'invalid-date',
        end_date: 'invalid-date'
      };

      await expect(testFactor(invalidTestRequest)).rejects.toThrow('Invalid test parameters');
    });
  });
});