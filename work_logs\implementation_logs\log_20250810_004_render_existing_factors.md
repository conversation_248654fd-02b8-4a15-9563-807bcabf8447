# 可视化因子编辑器 - 第0步 渲染已有因子

日期: 2025-08-10
角色: 实现AI

## 目标
在`StrategyWorkshop.vue`右侧编辑器中，基于`currentSelectedStrategy`的`buy_factors`与`sell_factors`渲染已有因子列表：
- 列表为空时显示空状态与“+ 添加因子”按钮
- 列表非空时，使用`ElCard`逐项展示因子卡片（标题为友好名称、内容为参数键值），并提供“编辑/删除”按钮位于卡片右上角

## 变更文件
- `frontend/src/views/StrategyWorkshop.vue`

## 关键实现
1. 模板层：
   - 买入、卖出规则区域新增`v-if / v-else`：
     - 为空：显示空状态与“+ 添加因子”按钮
     - 非空：`v-for`渲染`ElCard`因子卡片，卡片右上角提供“编辑/删除”按钮
   - 使用`el-descriptions`展示参数键值对

2. 脚本层：
   - 预加载因子元数据：`onMounted`时调用`factorsStore.fetchFactors()`
   - 计算属性：
     - `currentSelectedStrategy`（引用 store）
     - `selectedBuyFactors`、`selectedSellFactors`（容错，确保为数组）
   - 助手方法：
     - `getFriendlyFactorName(factor)`：基于`class_name`匹配`factorsStore.factors`，找不到回退为`class_name`
     - `getFactorParameters(factor)`：统一获取参数对象（兼容`parameters/param/kwargs`）
     - `formatParameterValue(val)`：常见类型格式化（布尔/数组/对象/基础类型）
   - 事件占位：
     - `handleEditFactor`、`handleDeleteFactor`（后续步骤实现）

3. 样式层：
   - 新增`.factors-list`、`.factor-item-card`、`.factor-card-header`、`.factor-actions`、`.factor-params`等样式，统一列表与卡片观感

## 修复与优化（基于用户反馈）
1. 滚动交互优化：
   - 采用“左右分栏各自滚动”的设计，避免页面统一滚动导致左侧内容被推走。
   - `.workshop-layout`固定高度`height: calc(100vh - 120px); min-height: 0;`。
   - `.right-panel`使用flex列布局并设置`min-height: 0;`。
   - `.editor-content`作为右侧唯一滚动容器：`flex: 1; min-height: 0; overflow: auto; padding: 20px;`。
2. 实现“编辑/删除”按钮功能：
   - 编辑：点击后打开参数配置视图（复用对话框的`configuration`视图），预填现有参数，确认保存后直接更新`currentSelectedStrategy.buy_factors / sell_factors[index]`的`parameters`。
   - 删除：从对应数组中`splice`移除项，UI立即联动更新。

## 结果
- 页面加载后：若选中策略存在因子，右侧“买入/卖出规则”即时反映真实数据；若为空，则展示引导性空状态。
- 因子卡片包含友好名称、参数展示以及“编辑/删除”实际可用的操作入口。

## 后续步骤（按序）
1. “+ 添加因子”按钮与“选择因子”对话框
2. 动态参数配置对话框
3. 增删改（直接修改`currentSelectedStrategy.buy_factors/sell_factors`数组，触发UI响应）


