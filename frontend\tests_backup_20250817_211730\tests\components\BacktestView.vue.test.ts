import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import BacktestView from '@/views/BacktestView.vue'
import { useBacktestStore } from '@/stores/useBacktestStore'
import type { BacktestResult, BacktestConfig } from '@/api/types/backtest'
import { BacktestStatus } from '@/api/types/backtest'

// 模拟子组件
vi.mock('@/components/BacktestForm.vue', () => ({
  default: {
    name: 'BacktestForm',
    template: '<div data-testid="backtest-form">BacktestForm</div>',
    emits: ['submit', 'reset'],
    props: ['loading']
  }
}))

vi.mock('@/components/BacktestResults.vue', () => ({
  default: {
    name: 'BacktestResults', 
    template: '<div data-testid="backtest-results">BacktestResults</div>',
    props: ['result', 'loading']
  }
}))

vi.mock('@/components/BacktestAnalysis.vue', () => ({
  default: {
    name: 'BacktestAnalysis',
    template: '<div data-testid="backtest-analysis">BacktestAnalysis</div>',
    props: ['result', 'metrics']
  }
}))

// 模拟回测Store
vi.mock('@/stores/useBacktestStore')

describe('BacktestView.vue', () => {
  let wrapper: VueWrapper<any>
  let mockBacktestStore: any

  const mockBacktestResult: BacktestResult = {
    task_id: 'test-task-001',
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 125000,
    total_return: 0.25,
    sharpe_ratio: 1.5,
    max_drawdown: 0.08,
    trades: [],
    daily_returns: []
  }

  const mockBacktestConfig: BacktestConfig = {
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000
  }

  beforeEach(() => {
    const pinia = createPinia()
    setActivePinia(pinia)

    // 修正：使用实际的store API
    mockBacktestStore = {
      isBacktesting: false,
      backtestResult: null,
      backtestError: '',
      currentBacktestTask: null,
      backtestProgress: 0,
      isLoadingResults: false,
      startBacktest: vi.fn(),
      resetBacktestState: vi.fn(),
      loadBacktestResults: vi.fn(),
      stopCurrentBacktest: vi.fn(),
      clearError: vi.fn()
    }

    vi.mocked(useBacktestStore).mockReturnValue(mockBacktestStore)

    wrapper = mount(BacktestView, {
      global: {
        plugins: [pinia]
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染回测视图标题', () => {
      expect(wrapper.find('h1').text()).toBe('回测分析')
    })

    it('应该显示专业副标题', () => {
      expect(wrapper.find('.subtitle').text()).toBe('专业量化策略回测平台')
    })

    it('应该始终渲染回测表单', () => {
      expect(wrapper.find('[data-testid="backtest-form"]').exists()).toBe(true)
    })

    it('当没有结果时不应显示结果和分析组件', () => {
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(false)
      expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(false)
    })
  })

  describe('条件渲染测试', () => {
    it('当有回测结果时应显示结果组件', async () => {
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      await nextTick()

      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })

    it('当有回测结果时应显示分析组件', async () => {
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      await nextTick()

      expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(true)
    })
  })

  // 新增：完整回测业务流程测试
  describe('回测完整业务流程', () => {
    it('应该支持完整的回测执行流程', async () => {
      // 1. 初始状态验证
      expect(wrapper.find('[data-testid="backtest-form"]').exists()).toBe(true)
      expect(mockBacktestStore.isBacktesting).toBe(false)
      
      // 2. 提交回测配置
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)
      
      // 3. 验证startBacktest被调用
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
      
      // 4. 模拟回测运行状态
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      expect(form.props('loading')).toBe(true)
      
      // 5. 模拟回测完成
      mockBacktestStore.isBacktesting = false
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      
      // 6. 验证结果显示
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="backtest-analysis"]').exists()).toBe(true)
    })

    it('应该在回测运行时禁用表单提交', async () => {
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
    })

    it('应该处理回测中断场景', async () => {
      // 模拟回测运行中
      mockBacktestStore.isBacktesting = true
      mockBacktestStore.currentBacktestTask = { id: 'test-task', status: BacktestStatus.RUNNING }
      await wrapper.vm.$forceUpdate()
      
      // 验证可以停止回测
      expect(mockBacktestStore.stopCurrentBacktest).toBeDefined()
    })
  })

  // 新增：回测错误处理测试
  describe('回测错误处理', () => {
    it('应该处理回测启动失败', async () => {
      const errorMessage = '策略配置无效'
      mockBacktestStore.startBacktest.mockRejectedValue(new Error(errorMessage))
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)
      
      // 等待异步操作完成
      await nextTick()
      
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
    })

    it('应该显示回测错误信息', async () => {
      const errorMessage = '回测执行失败'
      mockBacktestStore.backtestError = errorMessage
      await wrapper.vm.$forceUpdate()
      
      // 验证错误状态反映在UI上
      expect(mockBacktestStore.backtestError).toBe(errorMessage)
    })

    it('应该允许用户从错误状态恢复', async () => {
      mockBacktestStore.backtestError = '测试错误'
      await wrapper.vm.$forceUpdate()
      
      // 模拟清除错误
      mockBacktestStore.clearError()
      expect(mockBacktestStore.clearError).toHaveBeenCalled()
    })
  })

  // 新增：异步操作测试
  describe('异步操作测试', () => {
    it('应该正确处理回测API调用的异步响应', async () => {
      mockBacktestStore.startBacktest.mockResolvedValue(undefined)
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)
      
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
    })

    it('应该正确处理Promise rejection', async () => {
      const error = new Error('网络错误')
      mockBacktestStore.startBacktest.mockRejectedValue(error)
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      
      // 不应该抛出未捕获的异常
      await expect(form.vm.$emit('submit', mockBacktestConfig)).resolves.not.toThrow()
    })

    it('应该验证加载状态的正确时序', async () => {
      // 初始状态
      expect(mockBacktestStore.isBacktesting).toBe(false)
      
      // 模拟启动回测
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
      
      // 模拟完成
      mockBacktestStore.isBacktesting = false
      await wrapper.vm.$forceUpdate()
      
      expect(form.props('loading')).toBe(false)
    })
  })

  // 新增：组件Props传递验证
  describe('组件Props传递', () => {
    it('应该正确传递Props给Results组件', async () => {
      mockBacktestStore.backtestResult = mockBacktestResult
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const results = wrapper.findComponent({ name: 'BacktestResults' })
      expect(results.props('result')).toEqual(mockBacktestResult)
      expect(results.props('loading')).toBe(true)
    })
    
    it('应该正确传递Props给Analysis组件', async () => {
      const mockMetrics = { sharpe: 1.5, maxDrawdown: 0.08 }
      mockBacktestStore.backtestResult = mockBacktestResult
      mockBacktestStore.currentMetrics = mockMetrics
      await wrapper.vm.$forceUpdate()
      
      const analysis = wrapper.findComponent({ name: 'BacktestAnalysis' })
      expect(analysis.props('result')).toEqual(mockBacktestResult)
      expect(analysis.props('metrics')).toEqual(mockMetrics)
    })

    it('应该正确传递loading状态给Form组件', async () => {
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
    })
  })

  // 新增：响应式更新测试
  describe('响应式更新测试', () => {
    it('Store状态变化应立即反映在UI上', async () => {
      // 初始状态：无结果
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(false)
      
      // 更新状态：有结果
      mockBacktestStore.backtestResult = mockBacktestResult
      await wrapper.vm.$forceUpdate()
      
      // 验证UI更新
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })

    it('应该处理并发状态更新', async () => {
      // 模拟快速状态变化
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      
      mockBacktestStore.backtestResult = mockBacktestResult
      mockBacktestStore.isBacktesting = false
      await wrapper.vm.$forceUpdate()
      
      // 验证最终状态
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(false)
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })
  })

  describe('事件处理测试', () => {
    it('应该处理回测提交事件', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', mockBacktestConfig)

      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
    })

    it('应该处理表单重置事件', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('reset')

      expect(mockBacktestStore.resetBacktestState).toHaveBeenCalled()
    })
  })

  describe('生命周期测试', () => {
    it('组件挂载时应获取历史记录', () => {
      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('Store集成测试', () => {
    it('应该正确使用回测Store', () => {
      expect(useBacktestStore).toHaveBeenCalled()
    })

    it('应该响应Store状态变化', async () => {
      mockBacktestStore.isBacktesting = true
      await wrapper.vm.$forceUpdate()
      await nextTick()

      const form = wrapper.findComponent({ name: 'BacktestForm' })
      expect(form.props('loading')).toBe(true)
    })
  })

  describe('样式和布局测试', () => {
    it('应该具有正确的CSS类', () => {
      expect(wrapper.find('.backtest-view').exists()).toBe(true)
      expect(wrapper.find('.backtest-header').exists()).toBe(true)
      expect(wrapper.find('.backtest-content').exists()).toBe(true)
    })

    it('应该使用网格布局', () => {
      const content = wrapper.find('.backtest-content')
      expect(content.classes()).toContain('backtest-content')
    })
  })

  // 新增：边界条件测试
  describe('边界条件测试', () => {
    it('应该处理空的回测配置', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      await form.vm.$emit('submit', {})
      
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledWith({})
    })

    it('应该处理null结果状态', async () => {
      mockBacktestStore.backtestResult = null
      await wrapper.vm.$forceUpdate()
      
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(false)
    })

    it('应该处理undefined状态', async () => {
      mockBacktestStore.backtestResult = undefined
      await wrapper.vm.$forceUpdate()
      
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(false)
    })
  })

  // 新增：用户交互复杂性测试
  describe('用户交互测试', () => {
    it('应该处理快速连续点击提交', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      
      // 快速连续提交
      await form.vm.$emit('submit', mockBacktestConfig)
      await form.vm.$emit('submit', mockBacktestConfig)
      
      // startBacktest应该被调用多次（实际的防重复逻辑在store中）
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledTimes(2)
    })

    it('应该支持重置后重新提交', async () => {
      const form = wrapper.findComponent({ name: 'BacktestForm' })
      
      // 提交 -> 重置 -> 再次提交
      await form.vm.$emit('submit', mockBacktestConfig)
      await form.vm.$emit('reset')
      await form.vm.$emit('submit', mockBacktestConfig)
      
      expect(mockBacktestStore.startBacktest).toHaveBeenCalledTimes(2)
      expect(mockBacktestStore.resetBacktestState).toHaveBeenCalledTimes(1)
    })
  })
})