import { apiClient } from '../client';
import type {
  Factor,
  FactorQueryParams,
  CustomFactorDefinition,
  FactorTestRequest,
  FactorTestResult,
  FactorCalculationRequest,
  FactorsResponse,
  FactorResponse,
  FactorTestResponse,
  FactorValuesResponse,
  FactorCreationResponse,
  FactorValidationResponse
} from './types/factors';

// 获取因子库列表
export const getFactors = async (params?: FactorQueryParams): Promise<{buy_factors: Factor[], sell_factors: Factor[]}> => {
  // 从后台API获取数据
  try {
    const response = await apiClient.get('/api/v1/strategy/factors/', { params });
    console.log('getFactors API响应:', response);
    return response;
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
}

// 根据ID获取特定因子详情
export const getFactor = async (id: string): Promise<FactorResponse> => {
  const response = await apiClient.get(`/api/v1/factors/${id}`);
  return response.data;
};

// 创建自定义因子
export const createCustomFactor = async (factorData: CustomFactorDefinition): Promise<FactorCreationResponse> => {
  const response = await apiClient.post('/api/v1/factors/custom', factorData);
  return response.data;
};

// 测试因子计算
export const testFactor = async (testRequest: FactorTestRequest): Promise<FactorTestResponse> => {
  const response = await apiClient.post('/api/v1/factors/test', testRequest);
  return response.data;
};

// 获取因子值
export const getFactorValues = async (calculationRequest: FactorCalculationRequest): Promise<FactorValuesResponse> => {
  const response = await apiClient.post('/api/v1/factors/calculate', calculationRequest);
  return response.data;
};

// 验证因子定义
export const validateFactor = async (factorData: CustomFactorDefinition): Promise<FactorValidationResponse> => {
  const response = await apiClient.post('/api/v1/factors/validate', factorData);
  return response.data;
};