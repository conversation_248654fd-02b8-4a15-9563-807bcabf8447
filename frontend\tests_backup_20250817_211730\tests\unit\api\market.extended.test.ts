// Market API Extended 测试 - 集成测试阶段
// 测试复杂场景、边界条件和性能

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { getSymbols, getKlineData } from '../../../src/api/market';
import { SimpleMarketDataFactory } from '../../factories/SimpleMarketDataFactory';

// 创建MSW服务器
const server = setupServer();

describe('Market API Extended Tests', () => {
  beforeAll(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterAll(() => {
    server.close();
  });

  beforeEach(() => {
    server.resetHandlers();
  });

  describe('getSymbols - 扩展场景测试', () => {
    it('应该处理大量股票代码数据', async () => {
      const largeSymbolsList = SimpleMarketDataFactory.createSymbols(1000);
      const response = SimpleMarketDataFactory.createSymbolsResponse(largeSymbolsList);

      server.use(
        http.get('/market/symbols', () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getSymbols();
      expect(result.data).toHaveLength(1000);
      expect(result.total).toBe(1000);
      expect(result.success).toBe(true);
    });

    it('应该处理空的股票代码列表', async () => {
      const response = SimpleMarketDataFactory.createSymbolsResponse([]);

      server.use(
        http.get('/market/symbols', () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getSymbols();
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
      expect(result.success).toBe(true);
    });

    it('应该处理网络超时', async () => {
      server.use(
        http.get('/market/symbols', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json(
                SimpleMarketDataFactory.createErrorResponse('Request timeout')
              ));
            }, 15000); // 超过默认超时时间
          });
        })
      );

      await expect(getSymbols()).rejects.toThrow();
    });

    it('应该处理边界情况的股票代码', async () => {
      const boundarySymbol = SimpleMarketDataFactory.createBoundarySymbol();
      const response = SimpleMarketDataFactory.createSymbolsResponse([boundarySymbol]);

      server.use(
        http.get('/market/symbols', () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getSymbols();
      expect(result.data[0].name).toHaveLength(50);
      expect(result.data[0].industry).toHaveLength(20);
    });

    it('应该处理不同市场的股票代码', async () => {
      const symbols = [
        SimpleMarketDataFactory.createSymbol({ symbol: '000001.SZ', market: 'SZ' }),
        SimpleMarketDataFactory.createSymbol({ symbol: '600000.SH', market: 'SH' }),
        SimpleMarketDataFactory.createSymbol({ symbol: '300001.SZ', market: 'SZ' }) // 创业板
      ];
      const response = SimpleMarketDataFactory.createSymbolsResponse(symbols);

      server.use(
        http.get('/market/symbols', () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getSymbols();
      const markets = result.data.map(s => s.market);
      expect(markets).toContain('SZ');
      expect(markets).toContain('SH');
    });
  });

  describe('getKlineData - 扩展场景测试', () => {
    it('应该处理不同周期的K线数据', async () => {
      const periods = ['1m', '5m', '15m', '30m', '1h', '1d', '1w', '1M'];
      
      for (const period of periods) {
        const klineData = SimpleMarketDataFactory.createKlineDataSeries(100);
        const response = SimpleMarketDataFactory.createKlineDataResponse('000001.SZ', period, klineData);

        server.use(
          http.get('/market/kline/000001.SZ', ({ request }) => {
            const url = new URL(request.url);
            if (url.searchParams.get('period') === period) {
              return HttpResponse.json(response);
            }
            return HttpResponse.json(
              SimpleMarketDataFactory.createErrorResponse('Period not supported')
            );
          })
        );

        const result = await getKlineData('000001.SZ', period);
        expect(result.data.period).toBe(period);
        expect(result.data.data).toHaveLength(100);
      }
    });

    it('应该处理长时间序列的K线数据', async () => {
      const longSeries = SimpleMarketDataFactory.createKlineDataSeries(1000); // 1000天数据
      const response = SimpleMarketDataFactory.createKlineDataResponse('000001.SZ', '1d', longSeries);

      server.use(
        http.get('/market/kline/000001.SZ', () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getKlineData('000001.SZ', '1d');
      expect(result.data.data).toHaveLength(1000);
      expect(result.data.indicators).toBeDefined();
      expect(result.data.indicators.ma5).toBeTypeOf('number');
      expect(result.data.indicators.ma20).toBeTypeOf('number');
    });

    it('应该处理无效的股票代码', async () => {
      server.use(
        http.get('/market/kline/INVALID', () => {
          return HttpResponse.json(
            SimpleMarketDataFactory.createErrorResponse('Symbol not found'),
            { status: 404 }
          );
        })
      );

      await expect(getKlineData('INVALID', '1d')).rejects.toThrow();
    });

    it('应该处理数据质量问题', async () => {
      const invalidKlineData = [
        SimpleMarketDataFactory.createInvalidKlineData(),
        SimpleMarketDataFactory.createKlineData() // 正常数据
      ] as any;
      
      const response = SimpleMarketDataFactory.createKlineDataResponse('000001.SZ', '1d', invalidKlineData);

      server.use(
        http.get('/market/kline/000001.SZ', () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getKlineData('000001.SZ', '1d');
      // 应该能够处理部分无效数据
      expect(result.data.data).toHaveLength(2);
    });

    it('应该处理并发请求', async () => {
      const symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH'];
      
      symbols.forEach(symbol => {
        const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, '1d');
        server.use(
          http.get(`/market/kline/${symbol}`, () => {
            return HttpResponse.json(response);
          })
        );
      });

      // 并发请求多个股票的K线数据
      const promises = symbols.map(symbol => getKlineData(symbol, '1d'));
      const results = await Promise.all(promises);

      expect(results).toHaveLength(4);
      results.forEach((result, index) => {
        expect(result.data.symbol).toBe(symbols[index]);
        expect(result.success).toBe(true);
      });
    });
  });

  describe('性能测试', () => {
    it('getSymbols 应该在合理时间内完成', async () => {
      const response = SimpleMarketDataFactory.createSymbolsResponse(
        SimpleMarketDataFactory.createSymbols(100)
      );

      server.use(
        http.get('/market/symbols', () => {
          return HttpResponse.json(response);
        })
      );

      const startTime = Date.now();
      await getSymbols();
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(5000); // 5秒内完成
    });

    it('getKlineData 应该在合理时间内完成', async () => {
      const response = SimpleMarketDataFactory.createKlineDataResponse(
        '000001.SZ', 
        '1d', 
        SimpleMarketDataFactory.createKlineDataSeries(252) // 一年数据
      );

      server.use(
        http.get('/market/kline/000001.SZ', () => {
          return HttpResponse.json(response);
        })
      );

      const startTime = Date.now();
      await getKlineData('000001.SZ', '1d');
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(3000); // 3秒内完成
    });
  });

  describe('数据契约验证', () => {
    it('应该验证Symbol数据契约', () => {
      const validSymbol = SimpleMarketDataFactory.createSymbol();
      expect(SimpleMarketDataFactory.validateSymbolContract(validSymbol)).toBe(true);

      const invalidSymbol = { ...validSymbol, symbol: 'INVALID' };
      expect(SimpleMarketDataFactory.validateSymbolContract(invalidSymbol)).toBe(false);
    });

    it('应该验证KlineData数据契约', () => {
      const validKlineData = SimpleMarketDataFactory.createKlineData();
      expect(SimpleMarketDataFactory.validateKlineDataContract(validKlineData)).toBe(true);

      const invalidKlineData = { ...validKlineData, high: -1 };
      expect(SimpleMarketDataFactory.validateKlineDataContract(invalidKlineData)).toBe(false);
    });
  });
});