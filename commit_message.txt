feat(refactor): Split StrategyExecutor and unify test styles

This commit introduces a major refactoring of the `StrategyExecutor` module and standardizes the testing style across the suite.

- **Refactor `StrategyExecutor`**: The original `StrategyExecutor` has been decomposed into smaller, more focused modules (`DataPreprocessor`, `AbuPyCaller`, `ResultProcessor`, `ExecutorFacade`) to improve modularity, testability, and maintainability.

- **Unify Test Assertions**: All tests have been updated to use the `pytest` assertion style (`assert`) instead of `unittest`'s `self.assertEqual`, `self.assertIn`, etc. This provides a more consistent and readable test suite.

- **Fix Test Logic**: Corrected the logic in `test_load_strategy_invalid_data` to ensure it properly triggers and validates a `pydantic.ValidationError`.

- **Update Work Log**: Added a new entry to the work log detailing these refactoring and testing improvements.