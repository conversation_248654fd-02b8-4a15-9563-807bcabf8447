<template>
  <div v-loading="dashboardStore.loading">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover">
          <el-statistic title="今日涨跌" :value="dashboardStore.summary?.today_gain ?? 0">
            <template #suffix>¥</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <el-statistic title="活跃策略数" :value="dashboardStore.summary?.active_strategies ?? 0"></el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <el-statistic title="总成交额 (万)" :value="dashboardStore.summary?.total_turnover_wan ?? 0"></el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <el-statistic title="信号数量" :value="dashboardStore.summary?.signals_count ?? 0"></el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div>
              <span>市场表现</span>
            </div>
          </template>
          <div id="main-chart" style="width: 100%; height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { useDashboardStore } from '@/stores/useDashboardStore';

const dashboardStore = useDashboardStore();

let myChart: echarts.ECharts;

function updateChart(marketPerformance: any) {
  if (!myChart) return;
  const option = {
    xAxis: {
      type: 'category',
      data: marketPerformance.date,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: marketPerformance.value,
        type: 'line',
      },
    ],
  };
  myChart.setOption(option);
}

onMounted(() => {
  dashboardStore.fetchDashboardSummary();

  const chartDom = document.getElementById('main-chart');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    // Initially, you might want to show a loading state or an empty chart
    myChart.setOption({
      // Initial empty options
    });
  }
});

watch(() => dashboardStore.summary, (newSummary) => {
  if (newSummary && newSummary.market_performance) {
    updateChart(newSummary.market_performance);
  }
}, { deep: true });
</script>

<style scoped>
.el-statistic {
  text-align: center;
}
</style>