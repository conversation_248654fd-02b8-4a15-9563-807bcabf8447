<template>
  <div class="dashboard-page" v-loading="dashboardStore.loading">
    <el-page-header title="仪表盘" content="量化投资数据概览" />
    
    <el-row :gutter="16">
      <el-col :span="6">
        <el-card class="is-hover-shadow" :class="{ 'card-profit': (dashboardStore.summary?.today_gain ?? 0) > 0, 'card-loss': (dashboardStore.summary?.today_gain ?? 0) < 0 }">
          <el-statistic title="今日涨跌" :value="dashboardStore.summary?.today_gain ?? 0">
            <template #suffix>¥</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="is-hover-shadow">
          <el-statistic title="活跃策略数" :value="dashboardStore.summary?.active_strategies ?? 0"></el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="is-hover-shadow">
          <el-statistic title="总成交额 (万)" :value="dashboardStore.summary?.total_turnover_wan ?? 0"></el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="is-hover-shadow">
          <el-statistic title="信号数量" :value="dashboardStore.summary?.signals_count ?? 0"></el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>市场表现</span>
            </div>
          </template>
          <div id="main-chart" style="width: 100%; height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { useDashboardStore } from '@/stores';

const dashboardStore = useDashboardStore();

let myChart: echarts.ECharts;

function updateChart(marketPerformance: any) {
  if (!myChart) return;
  const option = {
    xAxis: {
      type: 'category',
      data: marketPerformance.date,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: marketPerformance.value,
        type: 'line',
      },
    ],
  };
  myChart.setOption(option);
}

onMounted(() => {
  dashboardStore.fetchDashboardSummary();

  const chartDom = document.getElementById('main-chart');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    // Initially, you might want to show a loading state or an empty chart
    myChart.setOption({
      // Initial empty options
    });
  }
});

watch(() => dashboardStore.summary, (newSummary) => {
  if (newSummary && newSummary.market_performance) {
    updateChart(newSummary.market_performance);
  }
}, { deep: true });
</script>

<style scoped>
.dashboard-page {
  /* 页面自定义样式 */
}

.el-statistic {
  text-align: center;
}

/* 统一卡片间距 */
.el-row + .el-row {
  margin-top: var(--space-lg);
}

/* 统计卡片样式优化 */
.el-card {
  transition: var(--transition-base);
  
  .el-statistic {
    padding: var(--space-sm) 0;
  }
}
</style>