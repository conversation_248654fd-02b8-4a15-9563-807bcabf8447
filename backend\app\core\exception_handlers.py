# -*- coding: utf-8 -*-
"""
全局异常处理器
"""
import logging
import traceback
from fastapi import Request, FastAPI
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from backend.app.core.exceptions import (
    BaseAbuModernException, 
    DataNotFoundError, 
    ExternalAPIError, 
    ValidationError, 
    SymbolError
)

logger = logging.getLogger(__name__)


def setup_exception_handlers(app: FastAPI) -> None:
    """设置全局异常处理器
    
    Args:
        app: FastAPI 应用实例
    """
    

    @app.exception_handler(SymbolError)
    async def handle_symbol_error(request: Request, exc: SymbolError):
        logger.warning(f"SymbolError: {exc.message}")
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error_code": exc.error_code,
                "message": exc.message,
                "data": exc.data
            }
        )
        
    @app.exception_handler(ValidationError)
    async def handle_validation_error(request: Request, exc: ValidationError):
        logger.warning(f"ValidationError: {exc.message}")
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error_code": exc.error_code,
                "message": exc.message,
                "data": exc.data
            }
        )
        
    @app.exception_handler(ExternalAPIError)
    async def handle_external_api_error(request: Request, exc: ExternalAPIError):
        logger.error(f"ExternalAPIError: {exc.message}")
        return JSONResponse(
            status_code=503,
            content={
                "success": False,
                "error_code": exc.error_code,
                "message": exc.message,
                "data": exc.data
            }
        )
    
    # 处理基类异常
    @app.exception_handler(BaseAbuModernException)
    async def handle_abu_modern_exception(request: Request, exc: BaseAbuModernException):
        logger.error(f"BaseAbuModernException: {exc.message}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error_code": exc.error_code,
                "message": exc.message,
                "data": exc.data
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def handle_http_exception(request: Request, exc: StarletteHTTPException):
        logger.warning(f"HTTPException: {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error_code": f"HTTP_{exc.status_code}",
                "message": str(exc.detail),
                "data": {}
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def handle_validation_exception(request: Request, exc: RequestValidationError):
        logger.warning(f"RequestValidationError: {exc.errors()}")
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error_code": "VALIDATION_ERROR",
                "message": "请求参数验证失败",
                "data": {"errors": exc.errors()}
            }
        )
    
    @app.exception_handler(Exception)
    async def handle_general_exception(request: Request, exc: Exception):
        # 关键修复：不要重复处理FastAPI自己的HTTPException
        if isinstance(exc, StarletteHTTPException):
            raise exc

        error_details = traceback.format_exc()
        logger.error(f"Unhandled exception caught:\n{error_details}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error_code": "INTERNAL_SERVER_ERROR",
                "message": f"服务器内部发生意外错误: {type(exc).__name__}",
                "data": {"details": str(exc)}
            }
        )
