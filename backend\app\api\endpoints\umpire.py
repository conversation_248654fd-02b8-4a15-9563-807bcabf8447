# backend/app/api/endpoints/umpire.py

from typing import Dict, Any

from fastapi import APIRouter, Depends, Body

from backend.app.services.umpire_service import UmpireService, get_umpire_service

router = APIRouter()

@router.post("/train", response_model=Dict[str, Any], summary="训练裁判模型")
def train_umpire_models(
    *, 
    train_data: Dict[str, Any] = Body(...),
    umpire_service: UmpireService = Depends(get_umpire_service)
):
    """
    接收训练数据和裁判规则，启动裁判模型的训练过程。

    - **train_data**: 包含 `market_info` 和 `umpire_rules` 的字典。
      - `market_info`: 包含 `start`, `end`, `benchmark` 和 `choice_symbols`
      - `umpire_rules`: 裁判规则列表
    """
    return umpire_service.train_models(train_data=train_data)