from pydantic import BaseModel
from typing import List, Dict, Any, Optional

class GridSearchRunRequest(BaseModel):
    choice_symbols: List[str]
    buy_factors: List[Dict[str, Any]]
    sell_factors: List[Dict[str, Any]]
    read_cash: int = 1000000

class GridSearchRunResponse(BaseModel):
    task_id: str

class GridSearchStatusResponse(BaseModel):
    task_id: str
    status: str
    result: Optional[Dict[str, Any]] = None