import { defineStore } from 'pinia'

interface StrategyLike {
  id?: string | null
  name?: string
  description?: string
  content?: string
  is_public?: boolean
  buy_factors?: any[]
  sell_factors?: any[]
  author?: string
  create_time?: string | null
  update_time?: string | null
}

export const useStrategyEditorStore = defineStore('strategyEditor', {
  state: () => ({
    currentSelectedStrategy: null as StrategyLike | null,
    isEditing: false as boolean,
  }),
  actions: {
    setCurrentSelectedStrategy(strategy: StrategyLike) {
      this.currentSelectedStrategy = strategy
    },
    clearCurrentSelectedStrategy() {
      this.currentSelectedStrategy = null
    },
    addBuyFactor(factorConfig: any) {
      if (!this.currentSelectedStrategy) return
      const existing = this.currentSelectedStrategy.buy_factors
      const safeList = Array.isArray(existing) ? [...existing] : []
      safeList.push(factorConfig)
      this.currentSelectedStrategy.buy_factors = safeList
    },
  },
})


