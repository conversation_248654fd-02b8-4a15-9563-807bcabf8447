# -*- coding: utf-8 -*-
"""
模拟Jupyter相关模块
为非Jupyter环境提供必要的模拟实现
"""
import sys
import logging

class MockIpywidgets:
    """ipywidgets模块的模拟实现"""
    
    class Widget:
        def __init__(self, **kwargs):
            self.value = kwargs.get('value')
            self.description = kwargs.get('description', '')
            self.disabled = kwargs.get('disabled', False)
            self.continuous_update = kwargs.get('continuous_update', True)
            self.orientation = kwargs.get('orientation', 'horizontal')
            self.readout = kwargs.get('readout', True)
            self.readout_format = kwargs.get('readout_format', 'd')
            self.layout = kwargs.get('layout', None)
            self.style = kwargs.get('style', None)
            
        def observe(self, callback, names=None):
            """Mock observe method"""
            pass
            
        def close(self):
            """Mock close method"""
            pass
            
        def add_class(self, class_name):
            """Mock add_class method"""
            pass
    
    class FloatProgress(Widget):
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
            self.min = kwargs.get('min', 0.0)
            self.max = kwargs.get('max', 100.0)
            self.step = kwargs.get('step', 0.1)
            
    class IntProgress(FloatProgress):
        pass
        
    class Text(Widget):
        pass
        
    class Textarea(Widget):
        pass
        
    class Button(Widget):
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
            self.icon = kwargs.get('icon', '')
            self.button_style = kwargs.get('button_style', '')
            
        def on_click(self, callback):
            """Mock on_click method"""
            pass
            
    class Box(Widget):
        def __init__(self, children=None, **kwargs):
            super().__init__(**kwargs)
            self.children = children or []
            
    class HBox(Box):
        pass
        
    class VBox(Box):
        pass
        
    class Label(Widget):
        pass
        
    class HTML(Widget):
        pass
    
    Layout = type('Layout', (object,), {})
    Style = type('Style', (object,), {})
    ProgressStyle = type('ProgressStyle', (object,), {'description_width': 'initial'})
    
    @staticmethod
    def interact(*args, **kwargs):
        """Mock interact function"""
        if args and callable(args[0]):
            return args[0]() if args else None
        return lambda f: f() if callable(f) else None
        
    @staticmethod
    def interactive(f, **kwargs):
        """Mock interactive function"""
        return f


def install_mock_modules():
    """安装所有模拟模块"""
    try:
        # 检查ipywidgets模块是否已经存在
        try:
            import ipywidgets
            logging.info("ipywidgets模块已存在，无需模拟")
        except ImportError:
            # 注册模拟模块
            sys.modules['ipywidgets'] = MockIpywidgets
            logging.info("成功安装ipywidgets模拟模块")
    except Exception as e:
        logging.error(f"安装模拟模块失败: {str(e)}")
