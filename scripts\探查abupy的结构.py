import abupy
import inspect

# 查找 ABuBenchmark 类的位置
print("=== 查找 ABuBenchmark ===")
for name in dir(abupy):
    try:
        obj = getattr(abupy, name)
        if inspect.ismodule(obj):
            for subname in dir(obj):
                if 'benchmark' in subname.lower():
                    print(f"Found in {name}: {subname}")
    except:
        pass

# 特别检查这些可能的位置
possible_locations = [
    'abupy.MarketBu',
    'abupy.CoreBu',
    'abupy.BenchmarkBu',
    'abupy.TradeBu'
]

for loc in possible_locations:
    try:
        module = eval(loc)
        print(f"\n=== Checking {loc} ===")
        for item in dir(module):
            if 'benchmark' in item.lower():
                print(f"  {item}")
    except:
        print(f"  Module {loc} not found")