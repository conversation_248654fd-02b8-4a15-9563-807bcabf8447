"""
符号格式转换工具模块
"""
import re
from typing import List

def to_abupy_symbol_format(symbol_str: str) -> str:
    """将 '600519.SH' 转换为 'sh600519'"""
    if '.' in symbol_str:
        parts = symbol_str.split('.')
        if len(parts) == 2 and parts[1].lower() in ['sh', 'sz']:
            return f"{parts[1].lower()}{parts[0]}"
    if isinstance(symbol_str, str) and symbol_str.startswith(('sh', 'sz')):
        return symbol_str
    return symbol_str

def from_abupy_symbol_format(abupy_symbol: str, original_symbols: List[str]) -> str:
    """将 'sh600519' 转换回 '600519.SH'"""
    match = next((s for s in original_symbols if to_abupy_symbol_format(s) == abupy_symbol), None)
    return match if match else abupy_symbol

def to_tushare_symbol_format(symbol_str: str) -> str:
    """将 'sh600519'、'sz000001'、'sh000300'、'000300' 等格式转换为 '600519.SH'、'000001.SZ'、'000300.SH' 等标准格式。"""
    if '.' in symbol_str:
        return symbol_str
    
    # 处理带前缀的情况（如'sh600519'）
    if symbol_str.startswith(('sh', 'sz')):
        prefix = symbol_str[:2].upper()
        code = symbol_str[2:]
        return f"{code}.{prefix}"
    
    # 处理纯数字的情况（如'000300'）
    if symbol_str.isdigit():
        # 优先处理指数代码
        if symbol_str.startswith('000'):
            return f"{symbol_str}.SH"
        # 根据A股代码规则判断交易所
        if symbol_str.startswith(('0', '3')):
            return f"{symbol_str}.SZ"
        elif symbol_str.startswith('6'):
            return f"{symbol_str}.SH"
        
    return symbol_str