import unittest
import pandas as pd
import numpy as np
import pytest
import os
from unittest.mock import patch, MagicMock
from types import SimpleNamespace
from backend.tests.abupy_adapter.test_strategy_adapter import TestStrategyAdapterExecuteStrategy

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 1. 创建一个高仿真的幽灵对象，它拥有一个.data属性
# 这个.data属性是一个列表，里面有一个字典，包含了所有必需的列
configured_mock_kline_data = MagicMock()
configured_mock_kline_data.data = [
    SimpleNamespace(
        date='20230105', open=100, high=105, low=99, 
        close=102, volume=10000, pre_close=98
    )
]

# 导入项目中的类
from backend.app.abupy_adapter.strategy_executor import StrategyExecutor
from backend.app.abupy_adapter.exceptions import AdapterError, ParameterError
from backend.app.core.exceptions import FactorError
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor
# 删除 KlineData 的导入
# 导入测试兼容性测试需要的类
# from backend.tests.abupy_adapter.test_strategy_adapter import TestStrategyAdapterExecuteStrategy

# 导入必要的abupy模块
import abupy
print(f"pytest loaded abupy from: {abupy.__file__}")

# 从正确的模块路径导入我们需要的每一个【类】
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.TradeBu.ABuKLManager import AbuKLManager
# 注意：因子类已经在 strategy_executor 内部处理，测试脚本如果需要直接实例化它们时
# 才需要导入。当前测试是通过 factor_class 字符串创建，所以测试脚本本身不需要导入因子类。
# 但如果测试断言需要检查因子类型，则必须导入，如下：
from abupy.FactorBuyBu.ABuFactorBuyBreak import AbuFactorBuyBreak
from abupy.FactorSellBu.ABuFactorSellNDay import AbuFactorSellNDay

# 在类的顶部，确保这些都已导入
from backend.app.services.market_service import MarketService


class TestStrategyRealExecution(unittest.TestCase):
    """策略真实执行的集成测试"""

    def test_kline_data_source(self):
        """测试策略执行时K线数据的获取机制"""
        # 1. 准备测试数据
        strategy = Strategy(
            name="测试K线数据获取",
            parameters={
                "choice_symbols": ["600036"],  # 招商银行
                "initial_capital": 100000,
                "start_date": "2021-03-01",
                "end_date": "2021-03-31",
                "benchmark_symbol": "000300"  # 沪深300指数
            }
        )
        market_data = {
            "market": "CN",
            "data_source": "tushare",  # 明确指定一个网络数据源
            "choice_symbols": strategy.parameters.get("choice_symbols"),
            "start_date": strategy.parameters.get("start_date"),
            "end_date": strategy.parameters.get("end_date"),
            "benchmark_symbol": strategy.parameters.get("benchmark_symbol")
        }

        # 2. 使用日志记录器来捕获K线数据获取的过程
        with self.assertLogs(level="DEBUG") as log_context:
            try:
                # 执行策略
                StrategyExecutor.execute_strategy(strategy, market_data)
            except FactorError as fe:
                # 这是预期的，因为我们没有提供买入因子，测试主要关注数据获取日志。
                print(f"捕获到预期的 FactorError 直接类型: {fe}")
            except AdapterError as ae:
                # 检查是否是包装后的FactorError
                if "策略中必须至少包含一个买入因子" in str(ae) or "FactorError" in str(ae):
                    print(f"捕获到预期的 FactorError (可能被包装为 AdapterError): {ae}")
                else:
                    # 其他 AdapterError 应该是意外的
                    print(f"执行过程中发生意外AdapterError: {ae}")
                    raise # 重新抛出，让测试失败
            except Exception as e:
                # 其他意外的异常应该导致测试失败。
                print(f"执行过程中发生其他意外异常: {e}")
                raise # 重新抛出，让测试失败

        # 3. 分析日志，验证K线数据获取机制。
        log_output = "\n".join(log_context.output)
        print("日志输出：")
        print(log_output)

        # 4. 验证是否通过 MarketService 获取K线数据。
        #    我们检查更稳定、更具体的日志片段。
        self.assertIn(f"MarketService: 正在为 {strategy.parameters['benchmark_symbol']} 获取K线数据", log_output)
        self.assertIn(f"MarketService: 正在为 {strategy.parameters['choice_symbols'][0]} 获取K线数据", log_output)

        # 5. 验证日志中记录的日期范围是否正确
        #    注意：根据日志，日期格式在内部被转换为了 YYYYMMDD
        start_date_fmt = strategy.parameters['start_date'].replace('-', '')
        end_date_fmt = strategy.parameters['end_date'].replace('-', '')

        # 检查包含基准代码和新日期格式的日志行
        self.assertIn(f"源: tushare，日期: {start_date_fmt}-{end_date_fmt}", log_output)
        # 检查包含选股代码和新日期格式的日志行
        self.assertIn(f"源: tushare，日期: {start_date_fmt}-{end_date_fmt}", log_output)

    def test_parameters_passing(self):
        """测试传递给do_symbols_with_same_factors的参数是否正确（最终修正版）"""
        # 1. 准备基础测试数据
        strategy_params = {
            "choice_symbols": ["000001.SZ", "600000.SH"],
            "initial_capital": 200000,
            "benchmark_symbol": "000300.SH",
            "start_date": "2022-01-01",
            "end_date": "2022-03-01"
        }
        strategy = Strategy(
            name="参数传递测试",
            parameters=strategy_params,
            buy_factors=[
                BuyFactor(name="买入-突破", factor_class="FactorBuyBreak", parameters={"xd": 20})
            ],
            sell_factors=[
                SellFactor(name="卖出-N日", factor_class="FactorSellNDay", parameters={"sell_n": 5})
            ]
        )
        market_data = {
            "market": "CN",
            "data_source": "mock",  # 可以用一个虚拟的名字，表示我们不关心。
            "choice_symbols": strategy_params["choice_symbols"],
            "start_date": strategy_params["start_date"],
            "end_date": strategy_params["end_date"],
            "benchmark_symbol": strategy_params["benchmark_symbol"],
            "capital": strategy_params["initial_capital"]
        }

        # 2. 使用mock来拦截do_symbols_with_same_factors调用并检查参数。
        # 在 test_parameters_passing 方法内
        from backend.app.services.market_service import MarketService
        # 删除 KlineData 的导入和实例化
        # 同时模拟 MarketService 和 do_symbols_with_same_factors
        with patch.object(MarketService, 'get_kline_data', return_value=configured_mock_kline_data) as mock_get_kline, \
            patch('backend.app.abupy_adapter.strategy_executor.do_symbols_with_same_factors') as mock_do_symbols:

            mock_do_symbols.return_value = (pd.DataFrame(columns=['symbol']), pd.DataFrame(), None)

            # 执行策略 (现在 market_data 里的 data_source 是什么已经不重要了)
            StrategyExecutor.execute_strategy(strategy, market_data)

            # 3. 验证调用
            mock_do_symbols.assert_called_once()

            # 4. 提取参数
            kw_args = mock_do_symbols.call_args.kwargs

            # 5. 验证参数
            self.assertIn("kl_pd_manager", kw_args)
            self.assertTrue(isinstance(kw_args["kl_pd_manager"], AbuKLManager))

            self.assertEqual(kw_args["target_symbols"], ["000001.SZ", "600000.SH"])

            self.assertIn("capital", kw_args)
            capital_obj = kw_args["capital"]
            self.assertTrue(isinstance(capital_obj, AbuCapital))
            self.assertEqual(capital_obj.read_cash, 200000)

            self.assertIn("benchmark", kw_args)
            # 使用修正后的正确类名进行断言
            self.assertTrue(isinstance(kw_args["benchmark"], AbuBenchmark))
            self.assertEqual(kw_args["benchmark"].benchmark, "000300.SH")

            self.assertIn("buy_factors", kw_args)
            self.assertTrue(isinstance(kw_args["buy_factors"][0], AbuFactorBuyBreak))
            self.assertEqual(kw_args["buy_factors"][0].xd, 20)

            self.assertIn("sell_factors", kw_args)
            self.assertTrue(isinstance(kw_args["sell_factors"][0], AbuFactorSellNDay))
            self.assertEqual(kw_args["sell_factors"][0].sell_n, 5)

    def test_real_result_processing(self):
        """
        测试对真实执行结果的解析处理。
        通过mock abupy的核心返回，来验证StrategyExecutor的完整结果处理流程。
        """
        # 1. 准备abupy核心函数将要返回的模拟数据。
        orders_data = {
            'symbol': ['600036', '600036'], 'buy_date': [20220105, 20220115],
            'buy_price': [65.5, 68.2], 'buy_cnt': [100, 200],
            'buy_type_str': ['buy', 'buy'], 'sell_date': [20220110, 20220120],
            'sell_price': [67.8, 69.5], 'profit': [230, 260],
            # 确保有'result' 列，abupy的真实返回通常有
            'result': [1, 1]
        }
        orders_pd = pd.DataFrame(orders_data)

        action_data = {
            'date': pd.to_datetime(pd.date_range(start='2022-01-01', periods=20, freq='D')),
            'capital_blance': np.linspace(100000, 100490, 20) # 资金从10万增长到10.049万。
        }
        action_pd = pd.DataFrame(action_data)

        # 2. 准备一个最小化的策略和市场数据，以驱动execute_strategy
        #    这些数据的大部分在mock下不会被深层使用，但需要存在以通过前期检查。
        strategy = Strategy(
            name="结果处理测试",
            parameters={
                "choice_symbols": ["600036"],
                "initial_capital": 100000,
                "start_date": "2022-01-01",
                "end_date": "2022-01-20",
                "benchmark_symbol": "000300.SH"
            },
            buy_factors=[BuyFactor(name="占位符买入因子", factor_class="FactorBuyBreak", parameters={"xd": 20})],
        )
        market_data = {
            "market": "CN",
            "data_source": "mock",  # 明确指定数据源，即使它将被mock
            "choice_symbols": strategy.parameters["choice_symbols"],
            "start_date": strategy.parameters["start_date"],
            "end_date": strategy.parameters["end_date"],
            "benchmark_symbol": strategy.parameters["benchmark_symbol"],
            "capital": strategy.parameters["initial_capital"]
        }

        # 3. 使用patch来mock掉abupy的核心函数，
        #    目标是让 StrategyExecutor.execute_strategy 内部的 do_symbols_with_same_factors
        #    返回我们上面构造的数据。
        from backend.app.services.market_service import MarketService
        # 删除 KlineData 的导入和实例化
        # 同时模拟 MarketService 和 do_symbols_with_same_factors
        with patch.object(MarketService, 'get_kline_data', return_value=configured_mock_kline_data) as mock_get_kline, \
            patch('backend.app.abupy_adapter.strategy_executor.do_symbols_with_same_factors') as mock_do_symbols:

            # 修正：确保mock返回的是我们构造的测试数据
            mock_do_symbols.return_value = (orders_pd, action_pd, None)

            # 修正：接收 execute_strategy 的返回值
            result = StrategyExecutor.execute_strategy(strategy, market_data)

            # 5. 断言最终的返回结果
            self.assertEqual(result["status"], "success")
            self.assertEqual(result["message"], "策略执行完成")
            self.assertIn("results", result)
            self.assertIn("execution_summary", result)

            # 验证摘要信息
            summary = result["execution_summary"]
            self.assertEqual(summary["initial_capital"], 100000)
            self.assertEqual(summary["final_capital"], 100490.0) # 应为action_pd的最后一个资金值
            self.assertEqual(summary["total_trades"], 2)

            # 验证具体交易结果
            symbol_result = result["results"][0]
            self.assertEqual(symbol_result["symbol"], "600036")
            self.assertEqual(symbol_result["orders_count"], 2)

            # 验证第一笔订单的细节
            first_order = symbol_result["orders"][0]
            self.assertEqual(first_order["buy_date"], 20220105)
            self.assertEqual(first_order["buy_price"], 65.5)
            self.assertEqual(first_order["buy_cnt"], 100)
            self.assertEqual(first_order["profit"], 230)
            self.assertEqual(first_order["sell_date"], 20220110)
            self.assertEqual(first_order["sell_price"], 67.8)

    # 在 test_exception_handling 方法的【正上方】添加这个装饰器
    @patch.object(MarketService, 'get_kline_data', return_value=configured_mock_kline_data)
    def test_exception_handling(self, mock_get_kline): # 注意：方法参数中多了一个 mock_get_kline
        """测试策略执行过程中的异常处理"""
        # 1. 准备测试数据
        strategy = Strategy(
            name="异常处理测试",
            parameters={
                "choice_symbols": ["INVALID_SYMBOL"],  # 无效的股票代码，会导致执行失败，
                "initial_capital": 100000,
                "benchmark_symbol": "000300",
                "start_date": "2023-01-01",
                "end_date": "2023-01-31"
            },
            # 为了让它能通过因子检查，我们给它一个空的因子列表。
            buy_factors=[]
        )
        # market_data 现在甚至不需要 data_source，因为 get_kline_data 永远不会被真实调用。
        market_data = {"market": "CN"}

        # 2. 执行策略，预期会出现异常
        #    因为 get_kline_data 被mock了，所以代码会继续执行，直到遇到我们真正想测试的异常点
        #    注意：由于我们mock了数据获取，INVALID_SYMBOL可能不会直接报错。
        #    但后续缺少因子会导致FactorError，这同样能测试我们的异常捕获链路。
        #    我们把策略改成缺少因子，来更稳定地触发一个已知错误。
        strategy.buy_factors = [] # 确保没有买入因子
        with self.assertRaises(AdapterError) as context:
            StrategyExecutor.execute_strategy(strategy, market_data)

        # 3. 验证异常转换是否正确
        exception_message = str(context.exception)
        print(f"捕获到的异常 (缺少因子): {exception_message}")
        self.assertIn("策略中必须至少包含一个买入因子", exception_message) # 验证正确的错误信息。

        # 4. 测试不同类型的异常。
        # 4.1 测试参数错误
        strategy_param_error = Strategy(
            name="参数错误测试",
            parameters={
                "choice_symbols": [],  # 空股票列表，应该触发参数错误
                "initial_capital": 100000,
                "start_date": "2023-01-01",
                "end_date": "2023-01-31",
                "benchmark_symbol": "000300"
            },
            buy_factors=[BuyFactor(name="占位符", factor_class="FactorBuyBreak", parameters={"xd": 20})]
        )

        with self.assertRaises(AdapterError) as param_context:
            StrategyExecutor.execute_strategy(strategy_param_error, market_data)
        self.assertIn("choice_symbols", str(param_context.exception))

        # 4.2 测试因子错误 (这个部分因为依赖真实类查找，可以保持原样)
        strategy_factor_error = Strategy(
            name="因子错误测试",
            parameters={
                "choice_symbols": ["000001.SZ"],
                "initial_capital": 100000,
                "start_date": "2023-01-01",
                "end_date": "2023-01-31",
                "benchmark_symbol": "000300.SH"
            },
            buy_factors=[BuyFactor(name="不存在的因子", factor_class="NonExistentFactor")]
        )

        with self.assertRaises(AdapterError) as factor_context:
            StrategyExecutor.execute_strategy(strategy_factor_error, market_data)

        factor_error_message = str(factor_context.exception)
        self.assertIn("NonExistentFactor", factor_error_message)

    # @unittest.skip("Temporarily skipping compatibility test to resolve cascading failures.")
    def test_mock_compatibility(self):
        """测试真实实现与现有mock测试的兼容性。"""
        # 1. 设置测试环境变量，强制使用真实执行。
        os.environ["ABU_TEST_USE_REAL_EXECUTE"] = "1"

        try:
            # 2. 运行原有的mock测试用例，看是否仍然通过
            # 注意: 这里执行的是原有测试类中的方法。
            test_instance = TestStrategyAdapterExecuteStrategy()
            test_instance.test_execute_strategy_success_with_trades()
            test_instance.test_execute_strategy_success_no_trades()

            # 3. 检查结果（如果上面的测试没有抛出异常，则认为通过。）
            self.assertTrue(True, "兼容性测试通过")

        finally:
            # 4. 清理环境变量
            os.environ.pop("ABU_TEST_USE_REAL_EXECUTE", None)

    # @unittest.skip("Skipping local E2E test because it requires pre-downloaded local data, which is not currently available.")
    def test_end_to_end_execution(self):
        """端到端集成测试，验证完整的策略执行流程（使用本地数据源）"""
        # 1. 准备符合当前Pydantic模型的完整策略配置。
        # 注意：这里的股票代码和市场需要与本地数据匹配
        strategy = Strategy(
            name="端到端本地数据源测试策略",
            parameters={
                "choice_symbols": ["600000.SH"],  # 修改为您本地有的A股股票。
                "initial_capital": 1000000,
                "start_date": "2021-01-01",
                "end_date": "2021-12-31",
                "benchmark_symbol": "sh000300"  # 修改为您本地有的A股指数。
            },
            buy_factors=[
                BuyFactor(name="买入-突破", factor_class="FactorBuyBreak", parameters={"xd": 20}),
                BuyFactor(name="买入-双均线", factor_class="FactorBuyMA", parameters={"ma_list": [5, 60]})
            ],
            sell_factors=[
                SellFactor(name="卖出-N日", factor_class="FactorSellNDay", parameters={"sell_n": 10}),
                SellFactor(name="卖出-ATR止损", factor_class="FactorSellAtrStop", parameters={"stop_loss_n": 1.5})
            ]
        )

        # 2. 准备 market_data，明确指定使用本地数据源
        market_data = {
            "market": "CN",  # 根据你的本地数据调整，可以是 "CN"
            "data_source": "local",
            "choice_symbols": strategy.parameters["choice_symbols"],
            "start_date": strategy.parameters["start_date"],
            "end_date": strategy.parameters["end_date"],
            "benchmark_symbol": strategy.parameters["benchmark_symbol"],
            "capital": strategy.parameters["initial_capital"]
        }

        # 3. 执行策略
        try:
            result = StrategyExecutor.execute_strategy(strategy, market_data)
        except Exception as e:
            import traceback
            self.fail(f"本地E2E测试执行过程中发生意外异常： {e}\n{traceback.format_exc()}")

        # 4. 验证结果的完整性。
        self.assertEqual(result.get("status"), "success", f"策略执行状态不是'success', 消息: {result.get('message')}")
        self.assertIn("results", result)
        self.assertIn("execution_summary", result)

        # 5. 验证摘要信息
        summary = result["execution_summary"]
        self.assertEqual(len(result["results"]), 1) # 检查返回结果的数量
        symbols_in_results = [res["symbol"] for res in result["results"]]
        self.assertIn("600000.SH", symbols_in_results)

        # 6. 打印关键结果指标，用于人工验证。
        print("\n--- 本地数据E2E策略执行结果摘要 ---")
        print(f"初始资金: {summary['initial_capital']}")
        print(f"最终资金: {summary['final_capital']}")
        print(f"总交易数: {summary['total_trades']}")

        # 7. 检查是否有交易发生
        if summary['total_trades'] > 0:
            print("INFO: 检测到交易发生，本地数据链路和因子计算正常工作。")
        else:
            print("WARNING: 未发生任何交易。请确认策略、本地数据范围和内容是否合理。")

    @pytest.mark.e2e_network # 使用一个专门的标记，与本地E2E区分
    @unittest.skipIf(not os.environ.get("RUN_NETWORK_TESTS"), "Skipping network E2E tests unless RUN_NETWORK_TESTS is set")
    def test_end_to_end_with_real_network_source(self):
        """
        【网络端到端测试】
        验证使用真实网络数据源（如Tushare）的完整策略执行流程。
        这个测试依赖于真实的网络连接和有效的数据源API Token。
        """
        # 1. 准备一个简单的、真实可执行的策略。
        #    确保使用的股票代码和日期范围内有数据，且时间跨度不要太长，以免测试过慢。
        strategy = Strategy(
            name="网络端到端真实数据源测试",
            parameters={
                "choice_symbols": ["600309.SH"],  # 使用一个真实存在的代码，如平安银行
                "initial_capital": 100000,
                "start_date": "2023-04-01",  # <-- 修改为一个肯定有交易的近期月份
                "end_date": "2023-04-30",    # <-- 修改 
                "benchmark_symbol": "000300.SH"
            },
            buy_factors=[
                # 使用一个简单的、容易触发的买入因子
                BuyFactor(name="买入-N日均线", factor_class="FactorBuyMA", parameters={"ma_list": [5, 10]})
            ],
            sell_factors=[
                SellFactor(name="卖出-N日", factor_class="FactorSellNDay", parameters={"sell_n": 10})
            ]
        )

        # 2. 准备 market_data，这次不指定 data_source 或指定为你的网络数据源，
        #    这样就会触发 MarketService 去网络上获取数据
        market_data = {
            "market": "CN",
            # 假设你的 MarketService 在不指定 data_source 时，默认使用网络源。
            # 或者你可以明确指定 "data_source": "tushare"
            "data_source": "tushare",
            "choice_symbols": strategy.parameters["choice_symbols"],
            "start_date": strategy.parameters["start_date"],
            "end_date": strategy.parameters["end_date"],
            "benchmark_symbol": strategy.parameters["benchmark_symbol"],
            "capital": strategy.parameters["initial_capital"]
        }

        # 3. 执行策略 (这次不mock任何东西）
        #    用try...except 包裹，以便在失败时能看到更详细的日志
        try:
            result = StrategyExecutor.execute_strategy(strategy, market_data)
        except Exception as e:
            import traceback
            # 如果发生异常，让测试失败并打印详细信息。
            self.fail(f"网络端到端测试执行过程中发生意外异常: {e}\n{traceback.format_exc()}")

        # 4. 验证结果的合理性，而不是具体数值。
        self.assertIsNotNone(result, "执行结果不应为None")
        self.assertEqual(result.get("status"), "success", f"策略执行状态不是'success', 消息: {result.get('message')}")

        # 5. 验证执行摘要是否生成且结构正确。
        self.assertIn("execution_summary", result)
        summary = result["execution_summary"]
        self.assertEqual(summary["initial_capital"], 100000)
        self.assertIn("final_capital", summary)
        self.assertIn("total_trades", summary)

        # 6. 打印一些关键信息，用于人工检查。
        print("\n--- 网络端到端测试执行结果摘要 ---")
        print(f"初始资金: {summary['initial_capital']}")
        print(f"最终资金: {summary['final_capital']}")
        print(f"总交易数: {summary['total_trades']}")

        # 7. 这是一个很好的实践：检查是否真的有交易发生
        #    如果这个简单的策略在真实数据上一个交易都没有，可能也暗示着数据链路或因子计算有问题
        if summary['total_trades'] > 0:
            print("INFO: 检测到交易发生，数据链路和因子计算可能正常工作。")
        else:
            print("WARNING: 未发生任何交易。请确认策略、日期范围和数据是否合理。")

        for res in result["results"]:
            print(f"\n股票 {res['symbol']}:")
            print(f"  订单数量: {res['orders_count']}")
            print(f"  最终资金: {res['final_capital']}")
            # 注释掉 benchmark_info 相关的检查，因为这是未来功能
            # if "benchmark_info" in res and res["benchmark_info"]: # 增加一个检查，确保不为空。
            #     bench = res["benchmark_info"]
            #     print(f"  基准收益率： {bench['benchmark_returns']:.2%}")
            #     print(f"  策略收益率： {bench['algorithm_returns']:.2%}")


if __name__ == "__main__":
    unittest.main()