<template>
  <div class="left-panel">
    <div class="panel-header">
      <h3>{{ UI_TEXT.MY_STRATEGIES || '我的策略' }}</h3>
      <el-button 
        type="primary" 
        size="default" 
        class="new-strategy-btn" 
        @click="$emit('create-strategy')"
      >
        {{ UI_TEXT.NEW_STRATEGY }}
      </el-button>
    </div>
    
    <div v-if="loading" data-testid="loading-indicator" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    
    <el-alert
      v-else-if="error"
      data-testid="error-message"
      type="error"
      :title="error"
      show-icon
      class="error-alert"
    />
    
    <div v-else class="strategy-list" data-testid="strategy-table">
      <el-card 
        v-for="strategy in displayedStrategies" 
        :key="strategy.id" 
        data-testid="strategy-row"
        class="strategy-card"
        :class="{ 'is-active': isStrategyActive(strategy) }"
        shadow="hover"
        @click="handleSelectStrategy(strategy)"
      >
        <div class="card-content">
          <div class="card-header">
            <h4 data-testid="strategy-name" class="strategy-name">{{ strategy.name }}</h4>
            <el-tag type="success" size="small">
              {{ strategy.is_public ? UI_TEXT.PUBLIC_STRATEGY : UI_TEXT.PRIVATE_STRATEGY }}
            </el-tag>
          </div>
          <p class="strategy-description">{{ strategy.description || UI_TEXT.NO_DESCRIPTION }}</p>
          <div class="card-footer">
            <span class="strategy-author">作者: {{ strategy.author }}</span>
            <span class="strategy-time">{{ formatDate(strategy.create_time) }}</span>
          </div>
        </div>
      </el-card>
      
      <!-- 展开/收起按钮 -->
      <div v-if="hasMoreStrategies" class="expand-toggle">
        <el-button 
          type="text" 
          size="small" 
          class="expand-btn"
          @click="toggleExpanded"
        >
          <el-icon class="expand-icon" :class="{ 'is-expanded': isExpanded }">
            <ArrowDown />
          </el-icon>
          {{ isExpanded ? '收起' : `查看更多 (${strategies.length - MAX_VISIBLE_STRATEGIES})` }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import type { Strategy } from '@/api/types'
import { formatDate } from '@/utils/dateUtils'
import { UI_TEXT } from '@/constants/strategy'

interface Props {
  strategies: Strategy[]
  currentStrategy: Strategy | null
  loading: boolean
  error: string | null
}

interface Emits {
  (e: 'select-strategy', strategy: Strategy): void
  (e: 'create-strategy'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const isExpanded = ref(false)
const MAX_VISIBLE_STRATEGIES = 3

// 计算属性
const displayedStrategies = computed(() => {
  if (isExpanded.value || props.strategies.length <= MAX_VISIBLE_STRATEGIES) {
    return props.strategies
  }
  return props.strategies.slice(0, MAX_VISIBLE_STRATEGIES)
})

const hasMoreStrategies = computed(() => {
  return props.strategies.length > MAX_VISIBLE_STRATEGIES
})

const isStrategyActive = (strategy: Strategy) => {
  return props.currentStrategy?.id === strategy.id
}

// 事件处理
const handleSelectStrategy = (strategy: Strategy) => {
  emit('select-strategy', strategy)
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped>
.left-panel {
  width: 320px;
  background: white;
  border-radius: 8px;
  padding: var(--space-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.new-strategy-btn {
  font-size: 14px;
}

.loading-container {
  margin: var(--space-lg) 0;
}

.error-alert {
  margin: var(--space-lg) 0;
}

.strategy-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.strategy-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.strategy-card:hover {
  border-color: #409eff;
  transform: translateY(-2px);
}

.strategy-card.is-active {
  border: 2px solid #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.strategy-card.is-active .strategy-name {
  color: #409eff;
  font-weight: 700;
}

.card-content {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-sm);
}

.strategy-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.strategy-description {
  margin: 0 0 var(--space-md) 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.strategy-author {
  font-size: 12px;
}

.strategy-time {
  font-size: 12px;
}

.expand-toggle {
  display: flex;
  justify-content: center;
  margin-top: var(--space-md);
  padding-top: var(--space-md);
  border-top: 1px solid #f0f0f0;
}

.expand-btn {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: #606266;
  font-size: 13px;
  padding: var(--space-sm) var(--space-md);
  transition: all 0.3s ease;
}

.expand-btn:hover {
  color: #409eff;
  background-color: #f5f7fa;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.is-expanded {
  transform: rotate(180deg);
}
</style>
