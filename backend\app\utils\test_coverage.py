"""测试覆盖率分析工具

提供测试覆盖率分析、测试质量评估和测试建议功能。
"""

import ast
import os
import re
import json
import subprocess
import sys
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from collections import defaultdict
import xml.etree.ElementTree as ET

@dataclass
class TestCoverageInfo:
    """测试覆盖率信息"""
    file_path: str
    total_lines: int
    covered_lines: int
    coverage_percentage: float
    uncovered_lines: List[int]
    functions_tested: int
    total_functions: int
    classes_tested: int
    total_classes: int

@dataclass
class TestQualityMetrics:
    """测试质量指标"""
    test_file_count: int = 0
    total_test_functions: int = 0
    assertion_count: int = 0
    mock_usage_count: int = 0
    fixture_count: int = 0
    parametrized_tests: int = 0
    test_complexity_score: int = 0

@dataclass
class TestSuggestion:
    """测试建议"""
    file_path: str
    suggestion_type: str
    priority: str  # 'high', 'medium', 'low'
    message: str
    code_example: Optional[str] = None

class TestCoverageAnalyzer:
    """使用 coverage.py 分析测试覆盖率"""
    """测试覆盖率分析器"""
    
    def __init__(self, source_dir: str, test_dir: str):
        self.source_dir = Path(source_dir)
        self.test_dir = Path(test_dir)
        self.coverage_info: Dict[str, TestCoverageInfo] = {}
        self.test_metrics = TestQualityMetrics()
        self.suggestions: List[TestSuggestion] = []
        
        # 测试文件模式
        self.coverage_file = self.test_dir / ".coverage"
        self.coverage_xml_report = self.test_dir / "coverage.xml"
        
    def analyze_coverage(self) -> Dict[str, Any]:
        """运行 coverage.py 并分析其输出"""
        # 运行 coverage.py 获取精确覆盖率
        self._run_coverage()
        self._parse_coverage_xml()

        # 分析测试质量
        test_files = self._get_test_files()
        self._analyze_test_quality(test_files)

        # 生成测试建议
        self._generate_test_suggestions()
        
        return self._generate_coverage_report()
    
    def _get_test_files(self) -> List[Path]:
        """获取所有测试文件"""
        test_files = []
        test_file_patterns = [r'test_.*\.py$', r'.*_test\.py$']
        for file_path in self.test_dir.rglob('*.py'):
            if any(re.search(pattern, file_path.name) for pattern in test_file_patterns):
                test_files.append(file_path)
        return test_files

    def _run_coverage(self):
        """使用 coverage.py 运行 pytest"""
        # 确保在正确的虚拟环境中运行
        python_executable = sys.executable
        
        cmd = [
            python_executable, '-m',
            'coverage', 'run',
            '--source', str(self.source_dir),
            '-m', 'pytest', str(self.test_dir)
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True, text=True, cwd=self.source_dir.parent.parent)
            
            # 生成XML报告
            xml_cmd = [
                python_executable, '-m',
                'coverage', 'xml', '-o', str(self.coverage_xml_report)
            ]
            subprocess.run(xml_cmd, check=True, capture_output=True, text=True, cwd=self.source_dir.parent.parent)
            
        except subprocess.CalledProcessError as e:
            print(f"Coverage command failed: {e.stderr}")
            raise

    def _parse_coverage_xml(self):
        """解析 coverage.xml 报告"""
        if not self.coverage_xml_report.exists():
            return

        tree = ET.parse(self.coverage_xml_report)
        root = tree.getroot()

        for package in root.findall('.//package'):
            for cls in package.findall('.//class'):
                file_path = cls.get('filename')
                
                # 转换为绝对路径
                abs_file_path = self.source_dir.parent.parent / file_path
                
                lines = cls.find('lines').findall('line')
                total_lines = len(lines)
                covered_lines = sum(1 for line in lines if line.get('hits') == '1')
                uncovered_lines = [int(line.get('number')) for line in lines if line.get('hits') == '0']
                
                self.coverage_info[str(abs_file_path)] = TestCoverageInfo(
                    file_path=str(abs_file_path),
                    total_lines=total_lines,
                    covered_lines=covered_lines,
                    coverage_percentage=(covered_lines / total_lines) * 100 if total_lines > 0 else 0,
                    uncovered_lines=uncovered_lines,
                    # 以下为简化信息，可根据需要扩展
                    functions_tested=0, 
                    total_functions=0,
                    classes_tested=0,
                    total_classes=0
                 )
    
    def _get_module_name(self, file_path: Path) -> str:
        """获取模块名"""
        # 简化版本，基于文件路径生成模块名
        relative_path = file_path.relative_to(self.source_dir.parent)
        module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
        return '.'.join(module_parts)
    
    def _estimate_covered_lines(self, functions: List[str], classes: List[str], 
                               tested_functions: Set[str], tested_classes: Set[str]) -> int:
        """估算覆盖的代码行数"""
        # 简化估算：假设每个函数平均10行，每个类平均30行
        covered_lines = 0
        covered_lines += len(tested_functions) * 10
        covered_lines += len(tested_classes) * 30
        return covered_lines
    
    def _analyze_test_quality(self, test_files: List[Path]):
        """分析测试质量"""
        self.test_metrics.test_file_count = len(test_files)
        
        for test_file in test_files:
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                # 分析测试函数
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                        self.test_metrics.total_test_functions += 1
                        
                        # 分析断言数量
                        assertions = self._count_assertions(node)
                        self.test_metrics.assertion_count += assertions
                        
                        # 分析复杂度
                        complexity = self._calculate_test_complexity(node)
                        self.test_metrics.test_complexity_score += complexity
                
                # 分析mock使用
                if 'mock' in content.lower() or 'patch' in content:
                    self.test_metrics.mock_usage_count += 1
                
                # 分析fixture使用
                if '@pytest.fixture' in content or 'fixture' in content:
                    self.test_metrics.fixture_count += content.count('@pytest.fixture')
                
                # 分析参数化测试
                if '@pytest.mark.parametrize' in content:
                    self.test_metrics.parametrized_tests += content.count('@pytest.mark.parametrize')
                    
            except Exception:
                continue
    
    def _count_assertions(self, test_function: ast.FunctionDef) -> int:
        """计算测试函数中的断言数量"""
        assertion_count = 0
        for node in ast.walk(test_function):
            if isinstance(node, ast.Assert):
                assertion_count += 1
            elif isinstance(node, ast.Call):
                if hasattr(node.func, 'attr') and 'assert' in node.func.attr:
                    assertion_count += 1
        return assertion_count
    
    def _calculate_test_complexity(self, test_function: ast.FunctionDef) -> int:
        """计算测试函数复杂度"""
        complexity = 1
        for node in ast.walk(test_function):
            if isinstance(node, (ast.If, ast.While, ast.For)):
                complexity += 1
        return complexity
    
    def _generate_test_suggestions(self):
        """生成测试建议"""
        for file_path, coverage_info in self.coverage_info.items():
            # 低覆盖率建议
            if coverage_info.coverage_percentage < 50:
                self.suggestions.append(TestSuggestion(
                    file_path=file_path,
                    suggestion_type="low_coverage",
                    priority="high",
                    message=f"Coverage is only {coverage_info.coverage_percentage:.1f}%. Consider adding more tests.",
                    code_example="def test_function_name():\n    # Arrange\n    # Act\n    # Assert\n    pass"
                ))
            
            # 未测试函数建议
            untested_functions = coverage_info.total_functions - coverage_info.functions_tested
            if untested_functions > 0:
                self.suggestions.append(TestSuggestion(
                    file_path=file_path,
                    suggestion_type="untested_functions",
                    priority="medium",
                    message=f"{untested_functions} functions are not tested. Add unit tests for them."
                ))
            
            # 未测试类建议
            untested_classes = coverage_info.total_classes - coverage_info.classes_tested
            if untested_classes > 0:
                self.suggestions.append(TestSuggestion(
                    file_path=file_path,
                    suggestion_type="untested_classes",
                    priority="medium",
                    message=f"{untested_classes} classes are not tested. Add class-level tests."
                ))
        
        # 测试质量建议
        if self.test_metrics.total_test_functions > 0:
            avg_assertions = self.test_metrics.assertion_count / self.test_metrics.total_test_functions
            if avg_assertions < 1.5:
                self.suggestions.append(TestSuggestion(
                    file_path="test_suite",
                    suggestion_type="test_quality",
                    priority="medium",
                    message="Tests have few assertions. Consider adding more comprehensive assertions."
                ))
        
        if self.test_metrics.mock_usage_count == 0 and self.test_metrics.test_file_count > 3:
            self.suggestions.append(TestSuggestion(
                file_path="test_suite",
                suggestion_type="test_isolation",
                priority="low",
                message="Consider using mocks to isolate units under test.",
                code_example="from unittest.mock import Mock, patch\n\n@patch('module.dependency')\ndef test_with_mock(mock_dep):\n    # Test implementation"
            ))
    
    def _generate_coverage_report(self) -> Dict[str, any]:
        """生成覆盖率报告"""
        total_lines = sum(info.total_lines for info in self.coverage_info.values())
        total_covered = sum(info.covered_lines for info in self.coverage_info.values())
        overall_coverage = (total_covered / max(total_lines, 1)) * 100
        
        total_functions = sum(info.total_functions for info in self.coverage_info.values())
        total_tested_functions = sum(info.functions_tested for info in self.coverage_info.values())
        function_coverage = (total_tested_functions / max(total_functions, 1)) * 100
        
        total_classes = sum(info.total_classes for info in self.coverage_info.values())
        total_tested_classes = sum(info.classes_tested for info in self.coverage_info.values())
        class_coverage = (total_tested_classes / max(total_classes, 1)) * 100
        
        return {
            'summary': {
                'overall_coverage': overall_coverage,
                'function_coverage': function_coverage,
                'class_coverage': class_coverage,
                'files_analyzed': len(self.coverage_info),
                'total_lines': total_lines,
                'covered_lines': total_covered
            },
            'test_metrics': asdict(self.test_metrics),
            'file_coverage': {path: asdict(info) for path, info in self.coverage_info.items()},
            'suggestions': [asdict(suggestion) for suggestion in self.suggestions],
            'recommendations': self._get_recommendations()
        }
    
    def _get_recommendations(self) -> List[str]:
        """获取改进建议"""
        recommendations = []
        
        if len(self.coverage_info) > 0:
            avg_coverage = sum(info.coverage_percentage for info in self.coverage_info.values()) / len(self.coverage_info)
            
            if avg_coverage < 60:
                recommendations.append("Increase overall test coverage to at least 80%")
            
            if self.test_metrics.parametrized_tests == 0:
                recommendations.append("Consider using parametrized tests for testing multiple scenarios")
            
            if self.test_metrics.fixture_count < self.test_metrics.test_file_count:
                recommendations.append("Use pytest fixtures to reduce test setup duplication")
            
            if self.test_metrics.total_test_functions > 0:
                avg_complexity = self.test_metrics.test_complexity_score / self.test_metrics.total_test_functions
                if avg_complexity > 3:
                    recommendations.append("Simplify complex test functions for better maintainability")
        
        return recommendations

# 便捷函数
def analyze_test_coverage(source_dir: str, test_dir: str) -> Dict[str, any]:
    """分析测试覆盖率的便捷函数"""
    analyzer = TestCoverageAnalyzer(source_dir, test_dir)
    return analyzer.analyze_coverage()

def generate_coverage_html_report(coverage_data: Dict[str, any], output_file: str):
    """生成HTML格式的覆盖率报告"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Coverage Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; }
            .coverage-high { color: green; }
            .coverage-medium { color: orange; }
            .coverage-low { color: red; }
            table { border-collapse: collapse; width: 100%; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
        </style>
    </head>
    <body>
        <h1>Test Coverage Report</h1>
        
        <div class="summary">
            <h2>Summary</h2>
            <p>Overall Coverage: <span class="coverage-{coverage_class}">{overall_coverage:.1f}%</span></p>
            <p>Function Coverage: {function_coverage:.1f}%</p>
            <p>Class Coverage: {class_coverage:.1f}%</p>
            <p>Files Analyzed: {files_analyzed}</p>
        </div>
        
        <h2>File Coverage Details</h2>
        <table>
            <tr>
                <th>File</th>
                <th>Coverage</th>
                <th>Functions</th>
                <th>Classes</th>
            </tr>
            {file_rows}
        </table>
        
        <h2>Recommendations</h2>
        <ul>
            {recommendations}
        </ul>
    </body>
    </html>
    """
    
    summary = coverage_data['summary']
    overall_coverage = summary['overall_coverage']
    
    # 确定覆盖率等级
    if overall_coverage >= 80:
        coverage_class = 'high'
    elif overall_coverage >= 60:
        coverage_class = 'medium'
    else:
        coverage_class = 'low'
    
    # 生成文件行
    file_rows = []
    for file_path, file_info in coverage_data['file_coverage'].items():
        file_name = Path(file_path).name
        coverage_pct = file_info['coverage_percentage']
        functions_info = f"{file_info['functions_tested']}/{file_info['total_functions']}"
        classes_info = f"{file_info['classes_tested']}/{file_info['total_classes']}"
        
        file_rows.append(f"""
            <tr>
                <td>{file_name}</td>
                <td>{coverage_pct:.1f}%</td>
                <td>{functions_info}</td>
                <td>{classes_info}</td>
            </tr>
        """)
    
    # 生成建议列表
    recommendations = [f"<li>{rec}</li>" for rec in coverage_data['recommendations']]
    
    html_content = html_template.format(
        overall_coverage=overall_coverage,
        function_coverage=summary['function_coverage'],
        class_coverage=summary['class_coverage'],
        files_analyzed=summary['files_analyzed'],
        coverage_class=coverage_class,
        file_rows=''.join(file_rows),
        recommendations=''.join(recommendations)
    )
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)