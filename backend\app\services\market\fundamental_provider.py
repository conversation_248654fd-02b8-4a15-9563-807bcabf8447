"""基本面数据提供者模块

该模块负责获取和处理股票基本面数据，包括财务指标、公司信息等。
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import numpy as np

# 导入abu相关模块
try:
    import abupy
    from abupy import ABuSymbolPd
    abu_available = True
except ImportError:
    abu_available = False
    logging.warning("abupy模块导入失败，部分功能可能不可用")

# 导入tushare
try:
    import tushare as ts
    tushare_available = True
except ImportError:
    tushare_available = False
    logging.warning("tushare模块导入失败，A股基本面数据功能不可用")

# 导入项目内部模块
from backend.app.schemas.market import FundamentalData
from backend.app.core.exceptions import ResourceNotFoundError, ExternalAPIError
from backend.app.abupy_adapter.symbol_adapter import SymbolAdapter


class FundamentalProvider:
    """基本面数据提供者"""
    
    # 缓存最近获取的基本面数据
    _fundamental_cache = {}
    
    @classmethod
    def get_fundamental(cls, symbol: str, market: str, date: Optional[str] = None) -> FundamentalData:
        """获取基本面数据
        
        Args:
            symbol: 股票代码
            market: 市场类型 (CN, US, HK)
            date: 日期，格式为YYYYMMDD，如果为None则取最新一期
            
        Returns:
            FundamentalData: 基本面数据对象
            
        Raises:
            ResourceNotFoundError: 当找不到指定的基本面数据时
            ExternalAPIError: 当外部API调用失败时
        """
        try:
            # 检查abu是否可用
            if not abu_available:
                raise ExternalAPIError("abupy模块不可用，无法获取基本面数据")
                
            # 标准化市场代码
            market = market.upper()
            
            # 根据市场类型选择不同的数据源和处理方式
            if market == "CN":
                return cls._get_cn_fundamental(symbol, date)
            elif market == "US":
                return cls._get_us_fundamental(symbol, date)
            elif market == "HK":
                return cls._get_hk_fundamental(symbol, date)
            else:
                raise ValueError(f"不支持的市场类型: {market}")
                
        except ResourceNotFoundError:
            raise
        except ExternalAPIError:
            raise
        except Exception as e:
            logging.error(f"获取财务指标出错: {str(e)}", exc_info=True)
            raise ExternalAPIError(f"获取基本面数据失败: {str(e)}")
    
    @classmethod
    def _get_cn_fundamental(cls, symbol: str, date: Optional[str] = None) -> FundamentalData:
        """获取A股基本面数据
        
        Args:
            symbol: 股票代码
            date: 日期，格式为YYYYMMDD，如果为None则取最新一期
            
        Returns:
            FundamentalData: 基本面数据对象
        """
        if not tushare_available:
            raise ExternalAPIError("tushare模块不可用，无法获取A股基本面数据")
            
        try:
            # 转换为tushare格式的代码
            ts_code = cls._abu_to_tushare_code(symbol)
            
            # 获取基本面数据
            pro = ts.pro_api()
            
            # 获取基本信息
            basic_info = pro.stock_basic(ts_code=ts_code, fields='ts_code,symbol,name,area,industry,market,list_date')
            if basic_info.empty:
                raise ResourceNotFoundError(f"未找到股票 {symbol} 的基本信息")
                
            # 获取最新财务指标
            if date:
                # 转换日期格式 YYYYMMDD -> YYYYMMDD
                period = date[:6] + "00"  # 转为季度末
                financials = pro.fina_indicator(ts_code=ts_code, period=period)
            else:
                # 获取最新财务指标
                financials = pro.fina_indicator(ts_code=ts_code)
                
            if financials.empty:
                # 如果没有财务数据，至少返回基本信息
                result = {
                    "symbol": symbol,
                    "name": basic_info.iloc[0]['name'],
                    "industry": basic_info.iloc[0]['industry'],
                    "area": basic_info.iloc[0]['area'],
                    "market": "CN",
                    "listing_date": basic_info.iloc[0]['list_date']
                }
            else:
                # 提取最新一期的财务指标
                latest = financials.iloc[0]
                
                # 构建结果
                result = {
                    "symbol": symbol,
                    "name": basic_info.iloc[0]['name'],
                    "industry": basic_info.iloc[0]['industry'],
                    "area": basic_info.iloc[0]['area'],
                    "market": "CN",
                    "listing_date": basic_info.iloc[0]['list_date'],
                    "report_date": latest.get('end_date'),
                    "pe_ratio": float(latest.get('pe', 0)),
                    "pe_ttm": float(latest.get('pe_ttm', 0)),
                    "pb_ratio": float(latest.get('pb', 0)),
                    "ps_ratio": float(latest.get('ps', 0)),
                    "ps_ttm": float(latest.get('ps_ttm', 0)),
                    "dividend_yield": float(latest.get('dv_ratio', 0)),
                    "market_cap": float(latest.get('total_mv', 0)),
                    "circulating_cap": float(latest.get('circ_mv', 0)),
                    "roe": float(latest.get('roe', 0)),
                    "roa": float(latest.get('roa', 0)),
                    "net_profit_margin": float(latest.get('netprofit_margin', 0)),
                    "gross_profit_margin": float(latest.get('grossprofit_margin', 0)),
                    "debt_to_asset_ratio": float(latest.get('debt_to_assets', 0)),
                    "current_ratio": float(latest.get('current_ratio', 0)),
                    "quick_ratio": float(latest.get('quick_ratio', 0))
                }
                
                # 填充额外的财务指标
                result.update(cls._get_additional_cn_metrics(ts_code))
                
            return FundamentalData(**result)
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logging.error(f"获取A股基本面数据出错: {str(e)}", exc_info=True)
            raise ExternalAPIError(f"获取A股基本面数据失败: {str(e)}")
    
    @classmethod
    def _get_us_fundamental(cls, symbol: str, date: Optional[str] = None) -> FundamentalData:
        """获取美股基本面数据
        
        Args:
            symbol: 股票代码
            date: 日期，格式为YYYYMMDD，如果为None则取最新一期
            
        Returns:
            FundamentalData: 基本面数据对象
        """
        # 目前使用abu的基本数据，未来可以替换为更专业的数据源
        try:
            # 获取股票名称
            stock_info = ABuSymbolPd.get_stock_info(symbol)
            if stock_info is None or len(stock_info) == 0:
                raise ResourceNotFoundError(f"未找到股票 {symbol} 的基本信息")
                
            # 构建基本结果
            result = {
                "symbol": symbol,
                "name": stock_info.get('name', ''),
                "industry": stock_info.get('industry', ''),
                "market": "US",
                "pe_ratio": float(stock_info.get('pe', 0)),
                "market_cap": float(stock_info.get('market_cap', 0)) * 1e8 if 'market_cap' in stock_info else 0,
            }
            
            return FundamentalData(**result)
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logging.error(f"获取美股基本面数据出错: {str(e)}", exc_info=True)
            raise ExternalAPIError(f"获取美股基本面数据失败: {str(e)}")
    
    @classmethod
    def _get_hk_fundamental(cls, symbol: str, date: Optional[str] = None) -> FundamentalData:
        """获取港股基本面数据
        
        Args:
            symbol: 股票代码
            date: 日期，格式为YYYYMMDD，如果为None则取最新一期
            
        Returns:
            FundamentalData: 基本面数据对象
        """
        # 目前仅返回基本信息，未来可以扩展
        try:
            # 获取股票名称
            stock_info = ABuSymbolPd.get_stock_info(symbol)
            if stock_info is None or len(stock_info) == 0:
                raise ResourceNotFoundError(f"未找到股票 {symbol} 的基本信息")
                
            # 构建基本结果
            result = {
                "symbol": symbol,
                "name": stock_info.get('name', ''),
                "market": "HK",
            }
            
            return FundamentalData(**result)
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logging.error(f"获取港股基本面数据出错: {str(e)}", exc_info=True)
            raise ExternalAPIError(f"目前仅支持A股市场的详细基本面数据，{market}市场基本面数据将在后续版本中支持")
    
    @classmethod
    def _get_additional_cn_metrics(cls, ts_code: str) -> Dict[str, Any]:
        """获取额外的A股财务指标
        
        Args:
            ts_code: tushare格式的股票代码
            
        Returns:
            Dict[str, Any]: 额外的财务指标字典
        """
        # 这里可以添加更多的财务指标获取逻辑
        # 目前返回空字典，未来可以扩展
        return {}
    
    @staticmethod
    def _abu_to_tushare_code(abu_symbol: str) -> str:
        """将abu格式代码转换为tushare格式代码
        
        Args:
            abu_symbol: abu格式的股票代码，如sh000001
            
        Returns:
            str: tushare格式的股票代码，如000001.SH
        """
        try:
            # 处理常见指数
            if abu_symbol == 'sh000001':  # 上证指数
                return '000001.SH'
            elif abu_symbol == 'sz399001':  # 深证成指
                return '399001.SZ'
            elif abu_symbol == 'sz399300':  # 沪深300
                return '399300.SZ'
            elif abu_symbol == 'sz399005':  # 中小板指
                return '399005.SZ'
            elif abu_symbol == 'sz399006':  # 创业板指
                return '399006.SZ'
            elif abu_symbol == 'sz399905':  # 中证500
                return '399905.SZ'
            elif abu_symbol == 'sh000016':  # 上证50
                return '000016.SH'
            elif abu_symbol == 'sh000688':  # 科创50
                return '000688.SH'
                
            # 处理普通股票代码
            if abu_symbol.startswith('sh'):
                code = abu_symbol[2:]
                # 确保后缀是大写
                return f"{code}.SH"
            elif abu_symbol.startswith('sz'):
                code = abu_symbol[2:]
                return f"{code}.SZ"
            elif abu_symbol.startswith('hk'):
                code = abu_symbol[2:]
                return f"{code}.HK"
            else:
                # 处理标准格式
                if '.' in abu_symbol:
                    parts = abu_symbol.split('.')
                    code = parts[0]
                    market = parts[1].upper()
                    return f"{code}.{market}"
                    
                # 处理纯数字代码
                if abu_symbol.isdigit():
                    # 根据规则判断市场
                    code = abu_symbol
                    
                    # 沪市主板6, 科创板通常以68、69开头, ETF 5xxxx, 可转债11xxxx
                    if len(code) == 6 and (code.startswith('6') or code.startswith('5') or code.startswith('11')):
                        return f"{code}.SH"
                    # 沪市B股以9开头
                    elif len(code) == 6 and code.startswith('9'):
                        return f"{code}.SH"
                    # 深市主板0, 创业板3, ETF 15xxxx, 可转债12xxxx
                    elif len(code) == 6 and (code.startswith('0') or code.startswith('3') or code.startswith('15') or code.startswith('12')):
                        return f"{code}.SZ"
                    # 深市B股以2开头
                    elif len(code) == 6 and code.startswith('2'):
                        return f"{code}.SZ"
                    # 对于其他未明确规则的6位数字代码，记录警告并尝试默认为上海
                    elif len(code) == 6:
                        logging.warning(f"无法确定代码 {code} 的市场，默认为上海")
                        return f"{code}.SH"
                    # 港股代码通常5位纯数字，例如0700，但也有4位如0005汇丰，或不足5位但以0开头的旧代码
                    elif len(code) <= 5:
                        # 港股代码补齐5位，如5 -> 00005.HK
                        padded_code = code.zfill(5)
                        return f"{padded_code}.HK"
                        
            # 如果无法判断，尝试使用适配器标准化
            try:
                std_symbol = SymbolAdapter.standardize(abu_symbol)
                if std_symbol and '.' in std_symbol:
                    parts = std_symbol.split('.')
                    return f"{parts[0]}.{parts[1].upper()}"
            except Exception as e:
                logging.warning(f"使用适配器标准化代码失败: {str(e)}")
                
            # 最后返回原始代码
            return abu_symbol
            
        except Exception as e:
            logging.error(f"无法将{abu_symbol}转换为tushare格式: {str(e)}")
            return abu_symbol  # 返回原始代码
    
    @staticmethod
    def get_market_from_symbol(symbol: str) -> str:
        """从股票代码判断市场类型
        
        Args:
            symbol: 股票代码
            
        Returns:
            str: 市场类型 (CN, US, HK)
        """
        try:
            # 处理带后缀的代码，如'0700.HK'
            if '.' in symbol:
                suffix = symbol.split('.')[1].upper()
                if suffix in ['SH', 'SZ']:
                    return 'CN'
                elif suffix == 'HK':
                    return 'HK'
                elif suffix in ['US', 'N', 'O', 'OQ', 'NY']:
                    return 'US'
                # 可以添加其他市场后缀的判断
                
            # 处理abu格式代码
            if symbol.startswith('sh') or symbol.startswith('sz'):
                return 'CN'
            elif symbol.startswith('us'):
                return 'US'
            elif symbol.startswith('hk'):
                return 'HK'
                
            # 根据代码特征判断
            if symbol.isdigit():
                # 6位纯数字，视为A股
                if len(symbol) == 6:
                    return 'CN'
                # 5位或以下纯数字，视为港股
                elif len(symbol) <= 5:
                    return 'HK'
                    
            # 尝试使用SymbolAdapter来获取市场类型
            try:
                market = SymbolAdapter.get_market(symbol)
                if market:
                    return market.upper()
            except Exception:
                pass
                
            # 默认返回
            logging.warning(f"无法明确判断符号 {symbol} 的市场，默认为'CN'")
            return 'CN'
            
        except Exception as e:
            logging.error(f"判断市场类型出错: {str(e)}")
            return 'CN'  # 默认返回A股市场
