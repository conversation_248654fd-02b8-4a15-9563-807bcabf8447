# -*- coding: utf-8 -*-
"""
基本面数据提供者模块，负责获取和处理股票基本面数据
"""
import logging
from datetime import datetime
from typing import Dict, List, Optional

import pandas as pd
import tushare as ts

from app.abupy_adapter.symbol_adapter import SymbolAdapter
from app.core.exceptions import DataNotFoundError, ExternalAPIError, ValidationError, SymbolError
from app.schemas.market import StockFundamental
from app.services.market.utils import safe_float
from app.core.config import settings


class FundamentalProvider:
    """基本面数据提供者"""
    
    @staticmethod
    def get_fundamental_data(pro, symbol: str, date: Optional[str] = None) -> StockFundamental:
        """
        获取基本面数据
        
        Args:
            pro: tushare pro_api实例
            symbol: 股票代码
            date: 日期，格式YYYYMMDD
            
        Returns:
            StockFundamental: 基本面数据
        """
        # 确定市场类型
        market = FundamentalProvider._get_market_from_symbol(symbol)
        
        # 如果未指定日期，使用最新日期
        if not date:
            date = datetime.now().strftime('%Y%m%d')
        
        # 获取股票名称
        name = SymbolAdapter.get_symbol_name(symbol)
        
        if market == 'CN':
            # 使用tushare获取A股基本面数据
            ts_symbol = FundamentalProvider._convert_to_tushare_symbol(symbol)
            
            # 获取基本指标
            try:
                df_basic = pro.daily_basic(ts_code=ts_symbol, trade_date=date)
                if df_basic.empty:
                    # 如果指定日期没有数据，获取最近的交易日数据
                    df_basic = pro.daily_basic(ts_code=ts_symbol)
                    if not df_basic.empty:
                        df_basic = df_basic.iloc[0:1]  # 取最新一条
                        date = df_basic['trade_date'].iloc[0]
            except Exception as e:
                # 处理可能的API错误
                logging.error(f"获取基本指标出错: {str(e)}")
                df_basic = pd.DataFrame()
            
            # 获取财务指标
            try:
                # 财务指标通常按季度发布，使用年份和季度查询
                year = date[:4]
                month = int(date[4:6])
                quarter = (month - 1) // 3 + 1
                end_date_for_fina = f"{year}{quarter*3:02d}31" # 获取该季度末的数据
                
                df_finance = pro.fina_indicator(ts_code=ts_symbol, end_date=end_date_for_fina)
                if df_finance.empty:
                    # 如果指定期间没有数据，获取最近的财报数据
                    df_finance = pro.fina_indicator(ts_code=ts_symbol)
                    if not df_finance.empty:
                        df_finance = df_finance.iloc[0:1]  # 取最新一条
            except Exception as e:
                logging.error(f"获取财务指标出错: {str(e)}")
                df_finance = pd.DataFrame()
            
            # 构建响应数据
            fundamental = StockFundamental(
                symbol=symbol,
                name=name,
                market=market,
                date=date
            )
            
            # 填充基本指标
            if not df_basic.empty:
                fundamental.pe = float(df_basic['pe'].iloc[0]) if 'pe' in df_basic and not pd.isna(df_basic['pe'].iloc[0]) else None
                fundamental.pe_ttm = float(df_basic['pe_ttm'].iloc[0]) if 'pe_ttm' in df_basic and not pd.isna(df_basic['pe_ttm'].iloc[0]) else None
                fundamental.pb = float(df_basic['pb'].iloc[0]) if 'pb' in df_basic and not pd.isna(df_basic['pb'].iloc[0]) else None
                fundamental.ps = float(df_basic['ps'].iloc[0]) if 'ps' in df_basic and not pd.isna(df_basic['ps'].iloc[0]) else None
                fundamental.ps_ttm = float(df_basic['ps_ttm'].iloc[0]) if 'ps_ttm' in df_basic and not pd.isna(df_basic['ps_ttm'].iloc[0]) else None
                fundamental.dv_ratio = float(df_basic['dv_ratio'].iloc[0]) if 'dv_ratio' in df_basic and not pd.isna(df_basic['dv_ratio'].iloc[0]) else None
                fundamental.dv_ttm = float(df_basic['dv_ttm'].iloc[0]) if 'dv_ttm' in df_basic and not pd.isna(df_basic['dv_ttm'].iloc[0]) else None
                fundamental.total_mv = float(df_basic['total_mv'].iloc[0]) if 'total_mv' in df_basic and not pd.isna(df_basic['total_mv'].iloc[0]) else None
                fundamental.circ_mv = float(df_basic['circ_mv'].iloc[0]) if 'circ_mv' in df_basic and not pd.isna(df_basic['circ_mv'].iloc[0]) else None
            
            # 填充额外的财务指标
            extra_data = {}
            if not df_finance.empty:
                indicators = ['roe', 'roa', 'grossprofit_margin', 'netprofit_margin', 'debt_to_assets']
                for indicator in indicators:
                    if indicator in df_finance and not pd.isna(df_finance[indicator].iloc[0]):
                        extra_data[indicator] = float(df_finance[indicator].iloc[0])
            
            if extra_data:
                fundamental.extra_data = extra_data
            
            return fundamental
        
        else:
            # 对于其他市场，可以使用abu原有接口或其他数据源
            # 这里简化处理，返回基本信息
            return StockFundamental(
                symbol=symbol,
                name=name,
                market=market,
                date=date,
                extra_data={"message": f"目前仅支持A股市场的详细基本面数据，{market}市场基本面数据将在后续版本中支持"}
            )

    @staticmethod
    def _convert_to_tushare_symbol(abu_symbol: str) -> str:
        """将abu格式代码转换为tushare格式代码
        
        支持的输入格式：
        - 标准格式：'sh600000', 'sz000001', 'hk00700'
        - 纯数字格式：'600000', '000001', '00700'
        - 后缀格式：'600000.SH', '00700.HK'
        
        Returns:
            转换后的tushare格式代码，如'600000.SH', '00700.HK'
        """
        # 如果已经是tushare格式，直接返回
        if '.' in abu_symbol:
            # 确保后缀是大写
            code, market = abu_symbol.split('.')
            return f"{code}.{market.upper()}"
            
        # 处理标准格式
        if abu_symbol.startswith('sh'):
            return f"{abu_symbol[2:]}.SH"
        elif abu_symbol.startswith('sz'):
            return f"{abu_symbol[2:]}.SZ"
        elif abu_symbol.startswith('hk'): # Restore hk stock handling
            return f"{abu_symbol[2:]}.HK"
    
        # 处理纯数字代码
        if abu_symbol.isdigit():
            # 优先处理已知的常见A股指数
            if abu_symbol == '000001': # 上证指数
                return "000001.SH"
            elif abu_symbol == '399001': # 深证成指
                return "399001.SZ"
            elif abu_symbol == '000300': # 沪深300
                return "000300.SH"
            elif abu_symbol == '399005': # 中小板指 (深)
                return "399005.SZ"
            elif abu_symbol == '399006': # 创业板指 (深)
                return "399006.SZ"
            elif abu_symbol == '000905': # 中证500 (沪)
                return "000905.SH"
            elif abu_symbol == '000016': # 上证50 (沪)
                return "000016.SH"
            elif abu_symbol == '000688': # 科创50
                return "000688.SH"

            # 根据数字长度和开头判断市场 (适用于股票)
            if len(abu_symbol) == 6:
                # 6位数字，A股股票
                # 沪市主板6, 科创板通常以688或689开头, ETF 5xxxx, 可转债 11xxxx
                if abu_symbol.startswith('6') or \
                   (abu_symbol.startswith('5') and len(abu_symbol) == 6) or \
                   (abu_symbol.startswith('11') and len(abu_symbol) == 6) or \
                   abu_symbol.startswith('9'): # 沪市B股以9开头
                    return f"{abu_symbol}.SH"
                # 深市主板0, 创业板3, ETF 15xxxx, 可转债 12xxxx
                elif abu_symbol.startswith('0') or abu_symbol.startswith('3') or \
                     ((abu_symbol.startswith('15') or abu_symbol.startswith('16')) and len(abu_symbol) == 6) or \
                     (abu_symbol.startswith('12') and len(abu_symbol) == 6) or \
                     abu_symbol.startswith('2'): # 深市B股以2开头
                    return f"{abu_symbol}.SZ"
                else:
                    # 对于其他未明确规则的6位数字代码，记录警告并尝试默认为上海
                    logging.warning(f"无法根据首位数字明确判断市场，纯数字代码 {abu_symbol} 默认为.SH")
                    return f"{abu_symbol}.SH"
            elif len(abu_symbol) == 5 or \
                 (len(abu_symbol) == 4 and abu_symbol.startswith('00')) or \
                 (len(abu_symbol) <= 5 and not abu_symbol.startswith('00')): # 港股代码通常是5位纯数字，例如00700，但也有4位如0005汇丰，或不足5位但非00开头的旧代码
                return f"{abu_symbol.zfill(5)}.HK" # 港股代码补齐到5位，如 5 -> 00005.HK

        # 如果无法判断，尝试使用适配器标准化
        try:
            normalized_symbol, market = SymbolAdapter.normalize_symbol(abu_symbol)
            return FundamentalProvider._convert_to_tushare_symbol(normalized_symbol)
        except Exception as e:
            logging.warning(f"无法将{abu_symbol}转换为tushare格式: {str(e)}")
            # 如果所有尝试都失败，返回原始代码
            return abu_symbol
    
    @staticmethod
    def _get_market_from_symbol(symbol: str) -> str:
        """从代码判断市场类型"""
        # 首先处理带市场前缀的代码
        if symbol.startswith('sh') or symbol.startswith('sz'):
            return 'CN'
        elif symbol.startswith('us'):
            return 'US'
        elif symbol.startswith('hk'):
            return 'HK'
        
        # 处理带后缀的代码，如'0700.HK'
        if '.' in symbol:
            code, market_suffix = symbol.split('.')
            if market_suffix.upper() == 'HK':
                return 'HK'
            elif market_suffix.upper() in ['SH', 'SZ']:
                return 'CN'
            # 可以添加其他市场后缀的判断
        
        # 处理纯数字代码，根据长度判断
        if symbol.isdigit():
            if len(symbol) == 6:
                # 6位纯数字，视为A股
                return 'CN'
            elif len(symbol) <= 5:
                # 5位或以下纯数字，视为港股
                return 'HK'
        
        # 尝试使用SymbolAdapter来获取市场类型
        try:
            _, market = SymbolAdapter.normalize_symbol(symbol)
            return market
        except Exception:
            pass
            
        # 默认返回A股
        logging.warning(f"无法明确判断符号 {symbol} 的市场，默认为 'CN'")
        return 'CN'
