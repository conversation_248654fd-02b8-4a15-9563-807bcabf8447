最佳实践：结构化的“测试需求包”
我们应该将我们的指令，组织成一个结构清晰的**“测试需求包”**。这个“包”里，包含了测试AI编写测试用例所需要的所有信息，并且每个信息都有明确的上下文。
这个“测试需求包”应该包含以下三个核心部分：
第一部分：高层级用户故事 (The User Story)
目的： 告诉测试AI，我们为什么要做这个功能。让它理解用户的动机和目标。
格式： 经典的“As a ..., I want ..., so that ...”格式。
示例提示词：
“用户故事：
作为一个策略研究员 (As a strategy researcher)，
我希望能够为一个特定的买入因子，配置只对它生效的专属卖出规则 (I want to configure exclusive sell rules for a specific buy factor)，
以便我能实现更精细化的、针对不同入场条件的风险控制策略 (so that I can implement more granular risk control tailored to different entry conditions)。”
第二部分：视觉验收标准 (The Visual Acceptance Criteria) - 这里就是设计图发挥作用的地方
目的： 告诉测试AI，这个功能最终应该长什么样，以及关键的UI元素是什么。
格式： 将设计图的关键部分进行**“注解”和“组件化”**描述。我们不需要把整张图扔过去，而是要告诉它图里的关键信息。
示例提示词：
“视觉验收标准：
我们的目标是实现附件中的UI设计。请注意以下关键点：
入口点 (Entry Point): 在class为.buy-factor-card的买入因子卡片内，会有一个新的图标按钮，其data-testid应为'exclusive-sell-rule-btn'。
对话框 (Dialog): 点击该按钮后，一个ElDialog组件会弹出。其title应该动态地包含当前买入因子的名称，例如‘为‘双均线交叉策略’配置专属卖出规则’。
结果反馈 (Feedback): 当在对话框中成功添加了专属卖-出规则后，在父级的.buy-factor-card内部，应该出现一个新的div，其data-testid为'exclusive-rules-display'，并在其中渲染出ElTag组件来显示规则名称。”
(注：使用data-testid是前端测试的最佳实践，它能让测试脚本与具体的CSS类名解耦，变得更稳定。)
第三部分：交互与逻辑测试点 (The Interaction & Logic Test Points)
目的： 告诉测试AI，需要验证哪些具体的交互行为和数据逻辑。
格式： 一个清晰的、分点的测试用例列表。
示例提示词：
“请为以上功能编写一套完整的测试用例，必须覆盖以下场景：
入口点测试：
验证‘专属卖出规则’按钮在买入因子卡片上存在。
模拟点击该按钮，断言专属卖出规则的对话框被打开。
对话框内部逻辑测试（单元测试范畴，可复用StrategyFormDialog的逻辑）：
验证对话框内部的‘+ 添加卖出因子’流程工作正常。
数据流与状态更新测试 (集成测试核心)：
模拟在对话框中成功添加了一个新的专属卖出因子。
断言useStrategyStore（或相关Store）中，对应买入因子的sell_factors数组被正确更新了。
UI反馈测试：
基于上面的状态更新，断言主界面对应的买入因子卡片下方，成功渲染出了新的ElTag。”