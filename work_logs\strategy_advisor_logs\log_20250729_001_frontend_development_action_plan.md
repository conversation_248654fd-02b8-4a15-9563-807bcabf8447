立即行动计划
阶段一：质量基础设施建设（1-2天）
目标：建立TDD开发环境和代码质量保障体系

markdown

# 给前端开发AI的第一个任务提示词

## 任务：前端质量基础设施建设

### 背景
基于诊断报告，当前前端项目是"高质量的空壳"，需要先建立完善的质量保障体系，再进行业务功能开发。

### 核心任务
1. **集成测试框架**（最高优先级）
   - 安装并配置 Vitest + Vue Test Utils + @vue/test-utils
   - 配置 vitest.config.ts，确保能测试.vue文件
   - 创建一个简单的示例测试，验证测试环境工作正常
   - 在package.json中添加测试相关的scripts

2. **集成ESLint作为AI代码审查员**
   - 安装 @typescript-eslint/eslint-plugin, @typescript-eslint/parser
   - 安装 eslint-plugin-vue (Vue3专用规则)
   - 创建 .eslintrc.js 配置文件，专注于错误检测而非风格
   - 配置忽略文件 .eslintignore
   - 在package.json中添加lint相关scripts

3. **配置Git Hooks**
   - 安装 husky 和 lint-staged
   - 配置pre-commit hook，自动运行lint和test
   - 确保代码提交前的质量检查

4. **创建测试示例和文档**
   - 为现有的Dashboard.vue创建一个基础测试用例
   - 创建 docs/testing-guide.md，说明测试规范和TDD流程
   - 验证整个工具链工作正常

### 验收标准
- npm run test 能成功运行测试
- npm run lint 能检查代码质量
- git commit 时自动运行质量检查
- 有一个完整的测试示例作为后续开发参考

### 输出文件清单
- vitest.config.ts
- .eslintrc.js
- .eslintignore
- .husky/ 目录和相关配置
- package.json (更新的依赖和scripts)
- tests/ 目录结构
- docs/testing-guide.md
- 示例测试文件：tests/components/Dashboard.test.ts
阶段二：API层设计（2-3天）
markdown

# 给前端开发AI的第二个任务提示词

## 任务：API客户端层设计与实现

### 背景
基于后端FastAPI的稳定状态，需要创建完整的前端API客户端层，为业务功能开发提供数据支撑。

### 参考资料
请参考项目后端API文档和以下端点设计：
- /api/market/* (市场数据)
- /api/strategy/* (策略管理)
- /api/backtest/* (回测)
- /api/factors/* (因子管理)

### 核心任务
1. **API客户端基础设施**
   - 基于axios创建统一的API客户端实例
   - 配置请求/响应拦截器（错误处理、loading状态）
   - 创建TypeScript类型定义文件（基于后端schema）
   - 实现统一的错误处理机制

2. **分模块API服务**
   - src/api/market.ts - 市场数据相关API
   - src/api/strategy.ts - 策略管理相关API  
   - src/api/backtest.ts - 回测相关API
   - src/api/factors.ts - 因子相关API
   - src/api/types/ - 所有TypeScript类型定义

3. **API层测试**
   - 为每个API模块编写单元测试
   - 使用Mock Service Worker (MSW) 模拟API响应
   - 测试各种场景：成功、失败、网络错误等

4. **API使用文档**
   - 创建 docs/api-guide.md
   - 包含所有API的使用示例和错误处理

### 验收标准
- 所有API模块都有完整的TypeScript类型支持
- API调用有统一的错误处理和loading状态管理
- 每个API模块都有对应的测试用例
- 能成功连接后端并获取真实数据

### TDD要求
- 先编写API调用的测试用例
- 再实现API功能直至测试通过
- 确保测试覆盖率达到80%以上
阶段三：状态管理设计（2天）
markdown

# 给前端开发AI的第三个任务提示词

## 任务：Pinia状态管理层设计

### 背景
基于V2.0前端蓝图，需要设计完整的状态管理方案，支持策略管理、市场数据、回测结果等业务场景。

### 核心任务
1. **Store模块设计**
   - stores/strategy.ts - 策略相关状态
   - stores/market.ts - 市场数据状态  
   - stores/backtest.ts - 回测结果状态
   - stores/user.ts - 用户设置状态
   - stores/app.ts - 应用全局状态

2. **状态设计原则**
   - 遵循单一数据源原则
   - 实现乐观更新和错误回滚
   - 支持数据缓存和自动刷新
   - 提供loading/error状态管理

3. **Store测试**
   - 为每个store编写完整的单元测试
   - 测试actions、getters和状态变更
   - 模拟API调用和错误场景

### 验收标准
- 所有核心业务都有对应的store
- Store设计支持复杂的业务逻辑
- 有完整的测试覆盖
- 提供清晰的使用文档
阶段四：核心页面开发（基于TDD）
从"策略工场"开始，这是最复杂也是最核心的页面：

markdown

# 给前端开发AI的第四个任务提示词

## 任务：策略工场页面实现（TDD方式）

### 背景
基于V2.0蓝图中的策略工场设计，实现三标签页的策略管理界面。这是项目最核心的业务页面。

### 参考设计
请严格按照 frontend_blueprint_v2.md 中的策略工场设计：
- 标签页一：核心配置（可视化因子编辑器）
- 标签页二：高级设置（仓位管理、风险控制）  
- 标签页三：参数优化（网格搜索、结果可视化）

### TDD开发流程
1. **先写测试**：为每个组件和功能编写测试用例
2. **红色阶段**：运行测试，确认失败
3. **绿色阶段**：编写最少代码使测试通过
4. **重构阶段**：优化代码质量和性能
5. **重复循环**：继续下一个功能

### 核心任务
1. **组件架构设计**
   - StrategyWorkshop.vue (主页面)
   - components/strategy/FactorEditor.vue (因子编辑器)
   - components/strategy/PositionConfig.vue (仓位配置)
   - components/strategy/RiskControl.vue (风险控制)
   - components/strategy/ParameterOptimizer.vue (参数优化)

2. **功能实现优先级**
   - P0: 策略列表展示和基本CRUD
   - P0: 可视化因子编辑器（买入/卖出因子）
   - P1: 仓位管理配置
   - P1: 风险控制配置
   - P2: 参数优化界面

### 验收标准
- 每个组件都有完整的测试用例
- 测试覆盖率达到85%以上
- UI/UX符合专业量化平台标准
- 所有业务逻辑都通过测试验证
🎯 总体开发策略
严格按阶段推进：不跳过质量基础设施建设
坚持TDD：特别是复杂业务组件
持续集成：每个阶段完成后都要确保项目可运行
渐进式开发：从核心功能开始，逐步扩展
💡 给您的建议
立即开始阶段一：质量基础设施是后续开发的保障
严格控制范围：先实现MVP，再考虑高级功能
保持测试优先：这是项目质量的根本保障
定期评审：每个阶段完成后进行代码评审