# -*- coding: utf-8 -*-
"""
模拟IPython模块，替代abu框架中的IPython依赖
"""
import logging

# 创建模拟的display类
class DisplayMock:
    @staticmethod
    def clear_output(wait=False):
        """模拟IPython的clear_output函数"""
        # 在API环境中，这个函数实际上不需要做任何事情
        logging.debug("Mock clear_output called")
        
    @staticmethod
    def display(obj, **kwargs):
        """模拟IPython的display函数"""
        # 在API环境中，可以选择记录这些信息，但不做实际显示
        logging.debug(f"Mock display called with: {obj}")

# 导出mock对象
display = DisplayMock()
clear_output = DisplayMock.clear_output
