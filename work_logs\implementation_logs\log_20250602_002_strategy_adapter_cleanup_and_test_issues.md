# 工作日志 - Cascade AI
日志ID： 46c8f9e8-c1a1-4f26-8e0a-9e9b8f8c8e8c
日志版本： 2.0
创建日期： 2025-06-02 11:11:05
AI角色： Cascade AI
开发者确认人： [USER]
确认日期： 

## 1. 任务名称与描述
**任务名称**：`StrategyAdapter.py` 代码清理与相关单元测试问题排查

**任务描述**：本次会话主要集中于清理 `StrategyAdapter.py` 文件中发现的结构性错误，特别是 `_get_factor_params` 和 `execute_strategy` 方法。在代码结构修复后，继续分析和诊断 `TestStrategyAdapter` 中的单元测试失败原因，并为后续修复步骤做准备。

**相关资源/参考材料**：
- 核心代码文件：`d:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_adapter.py`
- 相关测试文件：`d:\智能投顾\量化相关\abu_modern\backend\tests\abupy_adapter\test_strategy_adapter.py`
- 会话起始检查点：Checkpoint 20 (概述了修复开始前的状态和目标)

## 2. 实现内容

### 2.1 `StrategyAdapter.py` 文件结构修复
- **背景**: 在之前的会话中，识别出 `StrategyAdapter.py` 文件，特别是 `execute_strategy` 方法末尾，存在大量因复制粘贴或合并错误导致的冗余和错位代码。此外，`_get_factor_params` 方法也因一次错误的编辑操作而损坏。
- **修复 `_get_factor_params` 方法 (Steps 603-607)**:
    - **问题**: 该方法缺少了关键的 `params[param_name] = param.default` 和 `return params` 语句，导致 lint 错误 (ID: d5461322-5e6f-4ae8-9434-5cf496b99099) 和功能不完整。
    - **操作**: 使用 `view_file` 确认损坏细节，然后通过 `replace_file_content` 工具精确恢复了缺失的行。
    - **结果**: 方法恢复正常功能，lint 错误消除。
- **清理 `execute_strategy` 方法 (Steps 609-611)**:
    - **问题**: 方法末尾存在大段与原始 `try-except` 逻辑不符的重复代码块，严重影响代码可读性和正确性，并可能是导致之前工具（如 `grep_search`）行为异常的原因。
    - **操作**: 使用 `view_file` 精确定位了错误代码块的起始（从 `# ... (下面的代码保持不变)` 注释行开始）和结束。通过 `replace_file_content` 工具将这整段错误代码替换为空字符串，从而将其彻底删除。
    - **结果**: `execute_strategy` 方法的结构恢复了清晰和正确性，只保留了原始设计的主要 `try-except` 逻辑。

### 2.2 测试执行与初步分析 (Step 613)
- **操作**: 在对 `strategy_adapter.py` 进行上述结构性修复后，指导用户手动执行 `pytest -k TestStrategyAdapter` 以评估修复效果。
- **观察到的变化**:
    - 先前因 `execute_strategy` 返回英文成功消息 ("Strategy execution successful (basic implementation).") 而失败的测试 (`test_execute_strategy_success_with_trades`, `test_execute_strategy_success_no_trades`)，其失败原因发生了变化。
    - 新的失败信息为：
        - `test_execute_strategy_success_with_trades`: `KeyError: 'parameters_used'`
        - `test_execute_strategy_success_no_trades`: `KeyError: 'results'`
- **初步结论**: 代码清理后，`execute_strategy` 方法现在能够执行到返回初步成功摘要的部分（其 `message` 字段为中文 "策略执行完成"）。然而，该成功摘要的字典结构与测试用例中断言所期望的结构不符，缺少顶层的 `results` 和 `parameters_used` 键。

## 3. 技术实现细节

- **`view_file` 工具**: 在多次修复尝试中，该工具被用来精确查看特定代码区域，以确保提供给 `replace_file_content` 的 `TargetContent` 参数的准确性。这是避免编辑失败或引入新错误的关键。
- **`replace_file_content` 工具**:
    - 成功用于小范围、精确的代码恢复（如 `_get_factor_params` 方法）。
    - 成功用于大范围的代码块删除（如清理 `execute_strategy` 方法末尾的冗余代码）。
    - 强调了提供完全匹配的 `TargetContent` 的重要性，包括空格和换行符。
- **问题诊断流程**:
    - 从测试失败的表面现象（如错误的返回消息）入手。
    - 当直接修改不成功时，使用工具（`grep_search`, `view_file`, `view_code_item`）深入探查代码实际状态。
    - 识别出更深层次的结构性问题（如文件损坏）是解决表面问题的突破口。

## 4. 遇到的问题与解决方案

1.  **`replace_file_content` 的 `TargetContent` 匹配精度要求高**:
    *   **问题**: 多次因 `TargetContent` 与文件实际内容（包括空格、换行等细微差别）不完全一致，导致编辑失败或错误修改。
    *   **解决方案**: 更加依赖 `view_file` 和 `view_code_item` 来获取准确的代码片段作为 `TargetContent`。在发现文件结构问题后，理解了早期定位困难的原因。
2.  **工具操作引入新错误**:
    *   **问题**: 在一次尝试清理 `execute_strategy` 时 (Step 601)，`replace_file_content` 意外修改并损坏了 `_get_factor_params` 方法。
    *   **解决方案**: 通过后续的 `view_file` (Step 603) 准确定位了新问题，并使用 `replace_file_content` (Step 605) 针对性地修复了 `_get_factor_params`。
3.  **对文件损坏程度的初步低估**:
    *   **问题**: 最初认为 `execute_strategy` 的问题可能仅限于返回消息字符串，未立即意识到方法末尾存在大段结构性损坏。
    *   **解决方案**: 在多次尝试修复表层问题失败后，通过 `view_code_item` (Step 599) 完整查看方法代码，才最终确认了问题的严重性，并制定了正确的清理策略。

## 5. 当前的挑战 (截至本次会话结束)

1.  **`execute_strategy` 返回结构与测试期望不符 (`KeyError`)**:
    *   当前 `execute_strategy` 在初步成功路径上返回的字典 (`result_summary`) 结构为：
      ```python
      {
          "status": "preliminary_success",
          "message": "策略执行完成",
          "received_strategy_name": strategy.name,
          # ... 其他调试信息 ...
      }
      ```
    *   而测试用例期望的结构包含顶层的 `results` 和 `parameters_used` 键。
2.  **`TestStrategyAdapterExecuteStrategy` 中其他持续失败的测试**:
    *   `test_execute_strategy_missing_capital_in_market_data_uses_strategy_params` (mock 调用次数问题)
    *   `test_execute_strategy_abupy_exception` (未按预期抛出 `AdapterError`)
    *   `test_execute_strategy_invalid_factor_module` (错误消息的正则表达式不匹配)
    这些问题需要在解决 `KeyError` 后进一步排查，可能涉及对 `execute_strategy` 内部逻辑（如实际调用 `abupy` 的部分，目前被 `result_summary` 短路）的进一步实现或 mock。
3.  **`TestStrategyAdapterGetAvailableAbuFactors` 测试失败**:
    *   `test_get_buy_factors_only` 仍因 `AdapterError` 失败，暗示 `get_available_abu_factors` 方法或其与 `abupy` 模块（或其 mock）的交互存在问题。
4.  **API 及 Symbol Adapter 测试失败**:
    *   `tests/api/endpoints/test_strategy_api.py` 和 `tests/test_symbol_adapter.py` 中的大量失败 (共10个) 大概率是 `StrategyAdapter` 中未解决问题的下游影响。

## 6. 后续建议

1.  **调整 `StrategyAdapter.execute_strategy` 返回结构**:
    *   修改 `result_summary` 的构造方式，使其包含测试用例期望的 `results: list` 和 `parameters_used: dict` 键。
    *   `parameters_used` 应包含策略执行时使用的关键参数（如 `capital`, `choice_symbols`, `start_date`, `end_date`, `benchmark_symbol`, `n_folds` 等）。
    *   在当前阶段，`results` 可以暂时为一个空列表或包含简单执行信息的列表。
2.  **逐个攻克 `TestStrategyAdapterExecuteStrategy` 中剩余的失败用例**:
    *   在解决 `KeyError` 后，按顺序处理其他与 `execute_strategy` 相关的失败。
    *   仔细检查每个测试的 mock 设置、被测方法的预期行为以及异常抛出逻辑。
3.  **排查 `get_available_abu_factors` 问题**:
    *   分析 `test_get_buy_factors_only` 失败的具体原因，检查 `get_available_abu_factors` 方法内部与 `abupy` 模块的交互逻辑。
4.  **关注代码同步**: 持续确保测试环境运行的是最新版本的代码，避免因缓存或同步问题导致不一致的测试结果。

## 7. 附录-最后测试结果
'{actual_class_name_in_module}') 在模块 '{module_path}' 或其子模块 '{factor_submodule_path}' (尝 试模块名 {actual_module_name}) 或 '{original_submodule_path}' (尝试模块名 {base_class_name_from_schema}) 中均未找到. Errors: Submodule({actual_module_name}): {e_submodule}, Submodule({base_class_name_from_schema}): {e_orig_submodule}") from e_submodule
                                factor_class_obj = getattr(base_module, actual_class_name_in_module)
                                logger.warning(f"因子 '{actual_class_name_in_module}' 直接从基础模块 '{module_path}' 加载.")
                            else:
                                factor_class_obj = getattr(base_module, base_class_name_from_schema)
                                logger.warning(f"因子 '{base_class_name_from_schema}' 直接从基础模块 '{module_path}' 加载 (无ABu/Abu前缀).")
                        except ImportError as e_base:
                            raise FactorError(f"无法导入基础因子模块 '{module_path}' 也无法导入任 何尝试的子模块. Base: {e_base}, Submodule({actual_module_name}): {e_submodule}, Submodule({base_class_name_from_schema}): {e_orig_submodule}") from e_submodule
                else:
                    if not hasattr(submodule, actual_class_name_in_module):
                        raise FactorError(f"成功导入因子子模块 '{factor_submodule_path}' 但未在其 中找到预期的类 '{actual_class_name_in_module}'.")
                    factor_class_obj = getattr(submodule, actual_class_name_in_module)

                # Ensure inspect is imported at the top of the file: import inspect
                if not inspect.isclass(factor_class_obj):
>                   raise FactorError(f"获取到的 '{module_path}.{factor_class_name}' 不是一个有效 的类 (type: {type(factor_class_obj).__name__}).")
E                   app.core.exceptions.FactorError: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock).

app\abupy_adapter\strategy_adapter.py:114: FactorError

During handling of the above exception, another exception occurred:

self = <test_strategy_adapter.TestStrategyAdapterExecuteStrategy object at 0x000001C7F4480E00>
mock_import_module = <MagicMock name='import_module' id='1958335213984'>

    @patch('app.abupy_adapter.strategy_adapter.importlib.import_module')
    def test_execute_strategy_invalid_factor_module(self, mock_import_module):
        base_factor_name_from_schema = "NonExistentFactor"  # This is what comes from BuyFactor.factor_class
        # StrategyAdapter will try to prepend ABu for module name and Abu for class name
        actual_module_name_suffix = f"ABu{base_factor_name_from_schema}"  # e.g., ABuNonExistentFactor
        actual_class_name_to_get = f"Abu{base_factor_name_from_schema}" # e.g., AbuNonExistentFactor

        strategy = create_sample_strategy(
            buy_factors=[
                BuyFactor(name=base_factor_name_from_schema, factor_class=base_factor_name_from_schema, factor_type="buy", parameters={})
            ]
        )
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }

        # This is the full path StrategyAdapter will try to import first
        # e.g., "abupy.FactorBuyBu.ABuNonExistentFactor"
        expected_import_path = f"{StrategyAdapter.BUY_FACTOR_MODULE}.{actual_module_name_suffix}"

        # Mock the submodule object that import_module will return for the expected_import_path
        mock_submodule_instance = MagicMock()
        # Set the attribute that getattr will try to access, but make it an instance (not a class)
        setattr(mock_submodule_instance, actual_class_name_to_get, MagicMock())

        # Configure mock_import_module's side_effect
        def import_module_side_effect(module_name_called):
            if module_name_called == expected_import_path:
                return mock_submodule_instance # Return our specially crafted mock submodule
            # For any other module name, raise ImportError to simulate it's not found.
            # This ensures that fallback mechanisms in convert_to_abu_factors are also tested implicitly if needed,
            # but for this specific test, we expect the first attempt to 'succeed' in importing, then fail on isclass().
            raise ImportError(f"Mock: Module {module_name_called} not found during test_execute_strategy_invalid_factor_module")

        mock_import_module.side_effect = import_module_side_effect

        # This is the error message from inspect.isclass(factor_class_obj) failing
        # The path in the message should be the one that was successfully imported, and the class name that was getattr'd
        # The type is 'MagicMock' because getattr(mock_submodule_instance, actual_class_name_to_get) returns a MagicMock *instance*.
        raw_factor_error_msg = f"获取到的 '{expected_import_path}.{actual_class_name_to_get}' 不是一个有效的类 (type: MagicMock)."

        expected_adapter_error_msg = f"执行策略时遇到因子错误: {raw_factor_error_msg}"

        with pytest.raises(AdapterError, match=re.escape(expected_adapter_error_msg)):
>           StrategyAdapter.execute_strategy(strategy, market_data)

tests\abupy_adapter\test_strategy_adapter.py:330:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

cls = <class 'app.abupy_adapter.strategy_adapter.StrategyAdapter'>
strategy = Strategy(id='test_strategy_id', name='Test Strategy', description='A test strategy', create_time=None, update_time=Non...factor_class='NonExistentFactor', parameters={})], sell_factors=[], parameters={'initial_capital': 1000000}, tags=None)
market_data = {'benchmark_symbol': 'sh000300', 'capital': 1000000, 'choice_symbols': ['SH600000'], 'end_date': '2023-12-31', ...}

    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行策略 (初步实现)

        Args:
            strategy: 策略对象 (app.schemas.strategy.Strategy)
            market_data: 市场数据字典，期望包含:
                         'choice_symbols': List[str] - 股票代码列表 (e.g., ['usTSLA', 'usAAPL'])
                         'start_date': str - 开始日期 (e.g., '2020-01-01')
                         'end_date': str - 结束日期 (e.g., '2021-01-01')
                         'capital': float - 初始资金 (e.g., 1000000.0)
                         'benchmark_symbol': str - 基准代码 (e.g., 'usSPY')

        Returns:
            策略执行的初步结果字典

        Raises:
            AdapterError: 当执行过程中发生错误时
            ParameterError: 当输入参数不足或无效时
        """
        if not abu_import_success:
            raise AdapterError("无法导入abu模块，策略执行中止。")

        logging.info(f"开始执行策略: {strategy.name}")
        logging.info(f"市场数据: {market_data}")

        # 1. 输入参数验证和提取
        choice_symbols = market_data.get('choice_symbols')
        start_date = market_data.get('start_date')
        end_date = market_data.get('end_date')
        benchmark_symbol = market_data.get('benchmark_symbol')

        # 处理 capital，实现从 market_data 到 strategy.parameters 的回退
        capital = market_data.get('capital')
        if capital is None:  # 显式检查 None，因为 0 可能是一个有效的资本值
            capital = strategy.parameters.get('initial_capital')

        # 验证所有必需参数是否已提供
        # 首先检查 capital 是否在两处都缺失
        if capital is None: # 如果在回退后 capital 仍然是 None
            # 这个特定的错误消息是为了匹配 test_execute_strategy_missing_capital_in_both
            raise ParameterError("必须在 market_data 或 strategy.parameters 中指定 'capital'/'initial_capital'")

        # 然后检查其他核心参数
        required_params_map = {
            "choice_symbols": choice_symbols,
            "start_date": start_date,
            "end_date": end_date,
            "benchmark_symbol": benchmark_symbol
            # capital 已单独处理和验证
        }
        missing_core_params = [f"'{name}'" for name, value in required_params_map.items() if not value]

        if missing_core_params:
            raise ParameterError(f"市场数据中缺少必要的参数: {', '.join(missing_core_params)}")

        if not isinstance(choice_symbols, list) or not all(isinstance(s, str) for s in choice_symbols):
            raise ParameterError("'choice_symbols' 必须是一个字符串列表。")

        try:
            # 2. 转换因子
            abu_buy_factors = cls.convert_to_abu_factors(strategy.buy_factors)
            abu_sell_factors = cls.convert_to_abu_factors(strategy.sell_factors)

            if not abu_buy_factors:
                raise FactorError("策略中必须至少包含一个买入因子")

            # 3. 设置Abu环境 (如果需要)
            # abupy.env.g_data_source = abupy.env.EMarketDataFetchMode.E_DATA_SOURCE_BD # Example
            # abupy.env.g_enable_to_csv = False

            logging.info(f"准备调用 do_symbols_with_same_factors with buy_factors: {len(abu_buy_factors)}, sell_factors: {len(abu_sell_factors)}")

            # TODO: Replace below with actual call to do_symbols_with_same_factors
            # and process results_tuple
            # results_tuple = do_symbols_with_same_factors(
            #     read_cash=capital,
            #     choice_symbols=choice_symbols,
            #     benchmark=abupy.ABuSymbolPd.ABuBenchmark(benchmark_symbol),
            #     buy_factors=abu_buy_factors,
            #     sell_factors=abu_sell_factors,
            #     start=start_date,
            #     end=end_date,
            # )

            result_summary = {
                "status": "preliminary_success",
                "message": "策略执行完成",
                "received_strategy_name": strategy.name,
                "num_buy_factors_converted": len(abu_buy_factors),
                "num_sell_factors_converted": len(abu_sell_factors),
                "market_data_received": market_data,
                "abu_buy_factors_config": abu_buy_factors,
                "abu_sell_factors_config": abu_sell_factors
            }
            logging.info(f"初步策略执行完成: {result_summary}")

            return result_summary

        except ParameterError as pe:
            logging.error(f"参数错误: {str(pe)}", exc_info=True)
            raise AdapterError(f"执行策略时遇到参数错误: {str(pe)}")
        except FactorError as fe:
            logging.error(f"因子转换错误: {str(fe)}", exc_info=True)
>           raise AdapterError(f"执行策略时遇到因子错误: {str(fe)}")
E           app.core.exceptions.AdapterError: 执行策略时遇到因子错误: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock).

app\abupy_adapter\strategy_adapter.py:377: AdapterError

During handling of the above exception, another exception occurred:

self = <test_strategy_adapter.TestStrategyAdapterExecuteStrategy object at 0x000001C7F4480E00>
mock_import_module = <MagicMock name='import_module' id='1958335213984'>

    @patch('app.abupy_adapter.strategy_adapter.importlib.import_module')
    def test_execute_strategy_invalid_factor_module(self, mock_import_module):
        base_factor_name_from_schema = "NonExistentFactor"  # This is what comes from BuyFactor.factor_class
        # StrategyAdapter will try to prepend ABu for module name and Abu for class name
        actual_module_name_suffix = f"ABu{base_factor_name_from_schema}"  # e.g., ABuNonExistentFactor
        actual_class_name_to_get = f"Abu{base_factor_name_from_schema}" # e.g., AbuNonExistentFactor

        strategy = create_sample_strategy(
            buy_factors=[
                BuyFactor(name=base_factor_name_from_schema, factor_class=base_factor_name_from_schema, factor_type="buy", parameters={})
            ]
        )
        market_data = {
            "choice_symbols": ["SH600000"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "capital": 1000000,
            "benchmark_symbol": "sh000300"
        }

        # This is the full path StrategyAdapter will try to import first
        # e.g., "abupy.FactorBuyBu.ABuNonExistentFactor"
        expected_import_path = f"{StrategyAdapter.BUY_FACTOR_MODULE}.{actual_module_name_suffix}"

        # Mock the submodule object that import_module will return for the expected_import_path
        mock_submodule_instance = MagicMock()
        # Set the attribute that getattr will try to access, but make it an instance (not a class)
        setattr(mock_submodule_instance, actual_class_name_to_get, MagicMock())

        # Configure mock_import_module's side_effect
        def import_module_side_effect(module_name_called):
            if module_name_called == expected_import_path:
                return mock_submodule_instance # Return our specially crafted mock submodule
            # For any other module name, raise ImportError to simulate it's not found.
            # This ensures that fallback mechanisms in convert_to_abu_factors are also tested implicitly if needed,
            # but for this specific test, we expect the first attempt to 'succeed' in importing, then fail on isclass().
            raise ImportError(f"Mock: Module {module_name_called} not found during test_execute_strategy_invalid_factor_module")

        mock_import_module.side_effect = import_module_side_effect

        # This is the error message from inspect.isclass(factor_class_obj) failing
        # The path in the message should be the one that was successfully imported, and the class name that was getattr'd
        # The type is 'MagicMock' because getattr(mock_submodule_instance, actual_class_name_to_get) returns a MagicMock *instance*.
        raw_factor_error_msg = f"获取到的 '{expected_import_path}.{actual_class_name_to_get}' 不是一个有效的类 (type: MagicMock)."

        expected_adapter_error_msg = f"执行策略时遇到因子错误: {raw_factor_error_msg}"

>       with pytest.raises(AdapterError, match=re.escape(expected_adapter_error_msg)):
E       AssertionError: Regex pattern did not match.
E        Regex: "执行策略时遇到因子错误:\\ 获取到的\\ 'abupy\\.FactorBuyBu\\.ABuNonExistentFactor\\.AbuNonExistentFactor'\\ 不是一个有效的类\\ \\(type:\\ MagicMock\\)\\."
E        Input: "执行策略时遇到因子错误: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock)."

tests\abupy_adapter\test_strategy_adapter.py:329: AssertionError
------------------------------------- Captured stderr call --------------------------------------
因子转换错误: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock).
Traceback (most recent call last):
  File "D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_adapter.py", line 334, in execute_strategy
    abu_buy_factors = cls.convert_to_abu_factors(strategy.buy_factors)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_adapter.py", line 114, in convert_to_abu_factors
    raise FactorError(f"获取到的 '{module_path}.{factor_class_name}' 不是一个有效的类 (type: {type(factor_class_obj).__name__}).")
app.core.exceptions.FactorError: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock).
--------------------------------------- Captured log call ---------------------------------------
ERROR    root:strategy_adapter.py:376 因子转换错误: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock).
Traceback (most recent call last):
  File "D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_adapter.py", line 334, in execute_strategy
    abu_buy_factors = cls.convert_to_abu_factors(strategy.buy_factors)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_adapter.py", line 114, in convert_to_abu_factors
    raise FactorError(f"获取到的 '{module_path}.{factor_class_name}' 不是一个有效的类 (type: {type(factor_class_obj).__name__}).")
app.core.exceptions.FactorError: 获取到的 'abupy.FactorBuyBu.NonExistentFactor' 不是一个有效的类 (type: MagicMock).
______________ TestStrategyAdapterGetAvailableAbuFactors.test_get_buy_factors_only ______________

cls = <class 'app.abupy_adapter.strategy_adapter.StrategyAdapter'>, factor_type = 'buy'

    @classmethod
    def get_available_abu_factors(cls, factor_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从abu框架中动态获取可用的买入和卖出因子信息

        Args:
            factor_type: 因子类型，可选值为"buy"或"sell"，如果为None则返回所有类型

        Returns:
            因子信息字典列表，每个字典包含因子的名称、描述、参数等信息

        Raises:
            AdapterError: 当获取因子信息时出现错误
        """
        if not abu_import_success:
            raise AdapterError("无法导入abu模块")

        factors_info = []

        try:
            # 获取买入因子
            if factor_type is None or factor_type == "buy":
                buy_module = importlib.import_module(cls.BUY_FACTOR_MODULE)
>               for name, obj in inspect.getmembers(buy_module):

app\abupy_adapter\strategy_adapter.py:190:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\unittest\mock.py:1134: in __call__
    return self._mock_call(*args, **kwargs)
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\unittest\mock.py:1138: in _mock_call
    return self._execute_mock_call(*args, **kwargs)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <MagicMock name='getmembers' id='1958335228880'>
args = (<MagicMock name='mocked_abupy.FactorBuyBu' id='1958335270112'>,), kwargs = {}
effect = <function TestStrategyAdapterGetAvailableAbuFactors.test_get_buy_factors_only.<locals>.getmembers_side_effect_func at 0x000001C7F5C1FB00>

    def _execute_mock_call(self, /, *args, **kwargs):
        # separate from _increment_mock_call so that awaited functions are
        # executed separately from their call, also AsyncMock overrides this method

        effect = self.side_effect
        if effect is not None:
            if _is_exception(effect):
                raise effect
            elif not _callable(effect):
                result = next(effect)
                if _is_exception(result):
                    raise result
            else:
>               result = effect(*args, **kwargs)
E               TypeError: TestStrategyAdapterGetAvailableAbuFactors.test_get_buy_factors_only.<locals>.getmembers_side_effect_func() missing 1 required positional argument: 'predicate'

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\unittest\mock.py:1199: TypeError

During handling of the above exception, another exception occurred:

self = <test_strategy_adapter.TestStrategyAdapterGetAvailableAbuFactors object at 0x000001C7F4480FB0>
mock_getmembers_in_sut = <MagicMock name='getmembers' id='1958335228880'>
mock_get_params_in_sut = <MagicMock name='_get_factor_params' id='1958335232768'>

    @patch.object(StrategyAdapter, '_get_factor_params')
    @patch('app.abupy_adapter.strategy_adapter.inspect.getmembers')
    def test_get_buy_factors_only(self, mock_getmembers_in_sut, mock_get_params_in_sut):
        original_import_module = real_importlib_module_for_test.import_module
        original_getmembers = real_inspect_module_for_test.getmembers

        mock_params_to_return = {"p1": 10, "p2": "val"}
        mock_get_params_in_sut.return_value = mock_params_to_return

        # Mock base/mixin classes that SUT will use for identity and issubclass checks
        class PatchedAbuFactorBuyBase: pass
        class PatchedBuyCallMixin: pass
        class PatchedBuyPutMixin: pass

        # Mock factor classes that inspect.getmembers will "find"
        class FoundFactorAlpha(PatchedAbuFactorBuyBase, PatchedBuyCallMixin):
            __doc__ = " Alpha Factor Doc " # Test stripping of whitespace
            # factor_name is not used by SUT for get_available_abu_factors, it uses the class name

        class FoundFactorBeta(PatchedAbuFactorBuyBase, PatchedBuyPutMixin):
            __doc__ = "Beta Factor Doc"

        class FoundFactorGamma(PatchedAbuFactorBuyBase): # No specific call/put mixin
            __doc__ = "Gamma Factor Doc"

        mock_buy_module = MagicMock(name=f"mocked_{StrategyAdapter.BUY_FACTOR_MODULE}")

        def getmembers_side_effect_func(module_obj, predicate):
            # SUT calls with inspect.isclass
            self.assertTrue(predicate is inspect.isclass or str(predicate) == str(inspect.isclass))
            if module_obj is mock_buy_module:
                return [
                    ('FoundFactorAlpha', FoundFactorAlpha),
                    ('FoundFactorBeta', FoundFactorBeta),
                    ('FoundFactorGamma', FoundFactorGamma),
                    ('FoundFactorDeltaWithNoDoc', FoundFactorDeltaWithNoDoc),
                    ('SomeOtherMember', MagicMock(__doc__=None)), # Should be filtered out by isclass or issubclass
                    ('PatchedAbuFactorBuyBase', PatchedAbuFactorBuyBase), # Should be filtered by SUT
                    ('PatchedBuyCallMixin', PatchedBuyCallMixin),       # Should be filtered by SUT
                    ('PatchedBuyPutMixin', PatchedBuyPutMixin),         # Should be filtered by SUT
                    ('some_other_var', lambda x: x)                   # Should be filtered by SUT (not a class)
                ]
            return original_getmembers(module_obj, predicate) # Fallback for safety
        mock_getmembers_in_sut.side_effect = getmembers_side_effect_func

        def import_module_side_effect_func(module_name):
            if module_name == StrategyAdapter.BUY_FACTOR_MODULE:
                return mock_buy_module
            return original_import_module(module_name) # Fallback for safety

        with patch('app.abupy_adapter.strategy_adapter.importlib.import_module', side_effect=import_module_side_effect_func) as mock_import_module_sut_call:
            with patch('app.abupy_adapter.strategy_adapter.AbuFactorBuyBase', PatchedAbuFactorBuyBase), \
                 patch('app.abupy_adapter.strategy_adapter.BuyCallMixin', PatchedBuyCallMixin), \
                 patch('app.abupy_adapter.strategy_adapter.BuyPutMixin', PatchedBuyPutMixin):

>               available_factors = StrategyAdapter.get_available_abu_factors(factor_type="buy")

tests\abupy_adapter\test_strategy_adapter.py:394:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

cls = <class 'app.abupy_adapter.strategy_adapter.StrategyAdapter'>, factor_type = 'buy'

    @classmethod
    def get_available_abu_factors(cls, factor_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从abu框架中动态获取可用的买入和卖出因子信息

        Args:
            factor_type: 因子类型，可选值为"buy"或"sell"，如果为None则返回所有类型

        Returns:
            因子信息字典列表，每个字典包含因子的名称、描述、参数等信息

        Raises:
            AdapterError: 当获取因子信息时出现错误
        """
        if not abu_import_success:
            raise AdapterError("无法导入abu模块")

        factors_info = []

        try:
            # 获取买入因子
            if factor_type is None or factor_type == "buy":
                buy_module = importlib.import_module(cls.BUY_FACTOR_MODULE)
                for name, obj in inspect.getmembers(buy_module):
                    # 筛选出买入因子类
                    if (inspect.isclass(obj) and issubclass(obj, AbuFactorBuyBase) and
                        obj is not AbuFactorBuyBase and
                        'Base' not in name):

                        # 检查是否混入了方向类
                        is_call = issubclass(obj, BuyCallMixin)
                        is_put = issubclass(obj, BuyPutMixin)

                        if is_call or is_put:
                            # 获取类的文档字符串作为描述
                            description = obj.__doc__.strip() if obj.__doc__ else f"{name} 买入因 子"

                            # 获取参数信息
                            params = cls._get_factor_params(obj)

                            factors_info.append({
                                'id': name,
                                'name': name,
                                'description': description,
                                'factor_type': 'buy',
                                'factor_class': name,
                                'parameters': params,
                                'direction': 'call' if is_call else 'put'
                            })

            # 获取卖出因子
            if factor_type is None or factor_type == "sell":
                sell_module = importlib.import_module(cls.SELL_FACTOR_MODULE)
                for name, obj in inspect.getmembers(sell_module):
                    # 筛选出卖出因子类
                    if (inspect.isclass(obj) and issubclass(obj, AbuFactorSellBase) and
                        obj is not AbuFactorSellBase and
                        'Base' not in name):

                        # 获取类的文档字符串作为描述
                        description = obj.__doc__.strip() if obj.__doc__ else f"{name} 卖出因子"

                        # 获取参数信息
                        params = cls._get_factor_params(obj)

                        factors_info.append({
                            'id': name,
                            'name': name,
                            'description': description,
                            'factor_type': 'sell',
                            'factor_class': name,
                            'parameters': params
                        })

            return factors_info

        except Exception as e:
>           raise AdapterError(f"获取可用因子信息时出错: {str(e)}")
E           app.core.exceptions.AdapterError: 获取可用因子信息时出错: TestStrategyAdapterGetAvailableAbuFactors.test_get_buy_factors_only.<locals>.getmembers_side_effect_func() missing 1 required positional argument: 'predicate'

app\abupy_adapter\strategy_adapter.py:244: AdapterError
______________________________________ test_get_strategies ______________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958335221776'>
sample_strategy = Strategy(id='test-strategy-id', name='测试策略', description='这是一个用于测试的策略', create_time=None, update_time=None, owner=None...sell', factor_class='AbuFactorSellBreak', parameters={'xd': 15})], parameters={'init_cash': 100000}, tags=['测试', '示例'])

    def test_get_strategies(mock_strategy_service, sample_strategy):
        """测试获取策略列表API"""
        # 设置模拟服务的返回值
        mock_strategy_service.get_strategies.return_value = [sample_strategy]

        # 发送GET请求
        response = client.get("/api/v1/strategy/")

        # 验证响应
>       assert response.status_code == 200
E       assert 500 == 200
E        +  where 500 = <Response [500 Internal Server Error]>.status_code

tests\api\endpoints\test_strategy_api.py:86: AssertionError
------------------------------------- Captured stderr call --------------------------------------
获取策略列表时发生内部错误: 'StrategyService' object has no attribute 'get_all_strategies_paginated'
Traceback (most recent call last):
  File "D:\智能投顾\量化相关\abu_modern\backend\app\api\endpoints\strategy.py", line 80, in get_strategies_endpoint
    strategies, total_count = service.get_all_strategies_paginated(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StrategyService' object has no attribute 'get_all_strategies_paginated'
HTTPException: 获取策略列表时发生内部错误
--------------------------------------- Captured log call ---------------------------------------
ERROR    root:strategy.py:88 获取策略列表时发生内部错误: 'StrategyService' object has no attribute 'get_all_strategies_paginated'
Traceback (most recent call last):
  File "D:\智能投顾\量化相关\abu_modern\backend\app\api\endpoints\strategy.py", line 80, in get_strategies_endpoint
    strategies, total_count = service.get_all_strategies_paginated(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'StrategyService' object has no attribute 'get_all_strategies_paginated'
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 获取策略列表时发生内 部错误
_______________________________________ test_get_strategy _______________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958335372928'>
sample_strategy = Strategy(id='test-strategy-id', name='测试策略', description='这是一个用于测试的策略', create_time=None, update_time=None, owner=None...sell', factor_class='AbuFactorSellBreak', parameters={'xd': 15})], parameters={'init_cash': 100000}, tags=['测试', '示例'])

    def test_get_strategy(mock_strategy_service, sample_strategy):
        """测试获取单个策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.get_strategy_by_id.return_value = sample_strategy

        # 发送GET请求
        response = client.get(f"/api/v1/strategy/{sample_strategy.id}")

        # 验证响应
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests\api\endpoints\test_strategy_api.py:106: AssertionError
------------------------------------- Captured stderr call --------------------------------------
HTTPException: 未找到ID为 test-strategy-id 的策略
--------------------------------------- Captured log call ---------------------------------------
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 未找到ID为 test-strategy-id 的策略
__________________________________ test_get_strategy_not_found __________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958336311664'>

    def test_get_strategy_not_found(mock_strategy_service):
        """测试获取不存在的策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.get_strategy_by_id.return_value = None

        # 发送GET请求
        response = client.get("/api/v1/strategy/non-existent-id")

        # 验证响应
        assert response.status_code == 404
        data = response.json()
        assert "success" in data
        assert data["success"] is False
        assert "message" in data
        assert "未找到" in data["message"]

        # 验证服务调用
>       mock_strategy_service.get_strategy_by_id.assert_called_once_with("non-existent-id")

tests\api\endpoints\test_strategy_api.py:133:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <MagicMock name='mock.get_strategy_by_id' id='1958336310080'>, args = ('non-existent-id',)
kwargs = {}, msg = "Expected 'get_strategy_by_id' to be called once. Called 0 times."

    def assert_called_once_with(self, /, *args, **kwargs):
        """assert that the mock was called exactly once and that that call was
        with the specified arguments."""
        if not self.call_count == 1:
            msg = ("Expected '%s' to be called once. Called %s times.%s"
                   % (self._mock_name or 'mock',
                      self.call_count,
                      self._calls_repr()))
>           raise AssertionError(msg)
E           AssertionError: Expected 'get_strategy_by_id' to be called once. Called 0 times.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\unittest\mock.py:955: AssertionError
------------------------------------- Captured stderr call --------------------------------------
HTTPException: 未找到ID为 non-existent-id 的策略
--------------------------------------- Captured log call ---------------------------------------
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 未找到ID为 non-existent-id 的策略
_____________________________________ test_create_strategy ______________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958336158400'>
sample_strategy = Strategy(id='test-strategy-id', name='测试策略', description='这是一个用于测试的策略', create_time=None, update_time=None, owner=None...sell', factor_class='AbuFactorSellBreak', parameters={'xd': 15})], parameters={'init_cash': 100000}, tags=['测试', '示例'])

    def test_create_strategy(mock_strategy_service, sample_strategy):
        """测试创建策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.create_strategy.return_value = sample_strategy

        # 准备请求数据
        strategy_data = {
            "name": "新测试策略",
            "description": "这是一个新的测试策略",
            "is_public": True,
            "buy_factors": [
                {
                    "name": "测试买入因子",
                    "description": "测试用的买入因子",
                    "factor_class": "AbuFactorBuyBreak",
                    "parameters": {"xd": 20}
                }
            ],
            "sell_factors": [
                {
                    "name": "测试卖出因子",
                    "description": "测试用的卖出因子",
                    "factor_class": "AbuFactorSellBreak",
                    "parameters": {"xd": 15}
                }
            ],
            "parameters": {"init_cash": 100000},
            "tags": ["测试", "示例"]
        }

        # 发送POST请求
        response = client.post(
            "/api/v1/strategy/",
            json=strategy_data
        )

        # 验证响应
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
>       assert data["data"]["id"] == sample_strategy.id
E       AssertionError: assert '64d345548044...08eb84def6028' == 'test-strategy-id'
E
E         - test-strategy-id
E         + 64d345548044446fbb308eb84def6028

tests\api\endpoints\test_strategy_api.py:176: AssertionError
_____________________________________ test_update_strategy ______________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958336475888'>
sample_strategy = Strategy(id='test-strategy-id', name='测试策略', description='这是一个用于测试的策略', create_time=None, update_time=None, owner=None...sell', factor_class='AbuFactorSellBreak', parameters={'xd': 15})], parameters={'init_cash': 100000}, tags=['测试', '示例'])

    def test_update_strategy(mock_strategy_service, sample_strategy):
        """测试更新策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.update_strategy.return_value = sample_strategy

        # 准备请求数据
        update_data = {
            "name": "更新后的策略名称",
            "description": "这是更新后的描述",
            "is_public": False
        }

        # 发送PUT请求
        response = client.put(
            f"/api/v1/strategy/{sample_strategy.id}",
            json=update_data
        )

        # 验证响应
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests\api\endpoints\test_strategy_api.py:202: AssertionError
------------------------------------- Captured stderr call --------------------------------------
HTTPException: 未找到ID为 test-strategy-id 的策略，无法更新
--------------------------------------- Captured log call ---------------------------------------
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 未找到ID为 test-strategy-id 的策略，无法更新
________________________________ test_update_strategy_not_found _________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958336084640'>

    def test_update_strategy_not_found(mock_strategy_service):
        """测试更新不存在的策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.update_strategy.return_value = None

        # 准备请求数据
        update_data = {
            "name": "更新后的策略名称"
        }

        # 发送PUT请求
        response = client.put(
            "/api/v1/strategy/non-existent-id",
            json=update_data
        )

        # 验证响应
        assert response.status_code == 404
        data = response.json()
        assert "success" in data
        assert data["success"] is False
        assert "message" in data
        assert "未找到" in data["message"]

        # 验证服务调用
>       mock_strategy_service.update_strategy.assert_called_once_with("non-existent-id", ANY)

tests\api\endpoints\test_strategy_api.py:237:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <MagicMock name='mock.update_strategy' id='1958336154704'>
args = ('non-existent-id', <ANY>), kwargs = {}
msg = "Expected 'update_strategy' to be called once. Called 0 times."

    def assert_called_once_with(self, /, *args, **kwargs):
        """assert that the mock was called exactly once and that that call was
        with the specified arguments."""
        if not self.call_count == 1:
            msg = ("Expected '%s' to be called once. Called %s times.%s"
                   % (self._mock_name or 'mock',
                      self.call_count,
                      self._calls_repr()))
>           raise AssertionError(msg)
E           AssertionError: Expected 'update_strategy' to be called once. Called 0 times.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\unittest\mock.py:955: AssertionError
------------------------------------- Captured stderr call --------------------------------------
HTTPException: 未找到ID为 non-existent-id 的策略，无法更新
--------------------------------------- Captured log call ---------------------------------------
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 未找到ID为 non-existent-id 的策略，无法更新
_____________________________________ test_delete_strategy ______________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958336089216'>

    def test_delete_strategy(mock_strategy_service):
        """测试删除策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.delete_strategy.return_value = True

        # 发送DELETE请求
        response = client.delete("/api/v1/strategy/test-strategy-id")

        # 验证响应
>       assert response.status_code == 200
E       assert 404 == 200
E        +  where 404 = <Response [404 Not Found]>.status_code

tests\api\endpoints\test_strategy_api.py:249: AssertionError
------------------------------------- Captured stderr call --------------------------------------
HTTPException: 未找到ID为 test-strategy-id 的策略，无法删除
--------------------------------------- Captured log call ---------------------------------------
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 未找到ID为 test-strategy-id 的策略，无法删除
________________________________ test_delete_strategy_not_found _________________________________

mock_strategy_service = <MagicMock spec='StrategyService' id='1958336303600'>

    def test_delete_strategy_not_found(mock_strategy_service):
        """测试删除不存在的策略API"""
        # 设置模拟服务的返回值
        mock_strategy_service.delete_strategy.return_value = False

        # 发送DELETE请求
        response = client.delete("/api/v1/strategy/non-existent-id")

        # 验证响应
        assert response.status_code == 404
        data = response.json()
        assert "success" in data
        assert data["success"] is False
        assert "message" in data
        assert "未找到" in data["message"]

        # 验证服务调用
>       mock_strategy_service.delete_strategy.assert_called_once_with("non-existent-id")

tests\api\endpoints\test_strategy_api.py:275:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <MagicMock name='mock.delete_strategy' id='1958333469792'>, args = ('non-existent-id',)
kwargs = {}, msg = "Expected 'delete_strategy' to be called once. Called 0 times."

    def assert_called_once_with(self, /, *args, **kwargs):
        """assert that the mock was called exactly once and that that call was
        with the specified arguments."""
        if not self.call_count == 1:
            msg = ("Expected '%s' to be called once. Called %s times.%s"
                   % (self._mock_name or 'mock',
                      self.call_count,
                      self._calls_repr()))
>           raise AssertionError(msg)
E           AssertionError: Expected 'delete_strategy' to be called once. Called 0 times.

C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\unittest\mock.py:955: AssertionError
------------------------------------- Captured stderr call --------------------------------------
HTTPException: 未找到ID为 non-existent-id 的策略，无法删除
--------------------------------------- Captured log call ---------------------------------------
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 未找到ID为 non-existent-id 的策略，无法删除
__________________________________ test_get_available_factors ___________________________________

mock_factor_service = <MagicMock spec='FactorService' id='1958335209136'>

    def test_get_available_factors(mock_factor_service):
        """测试获取可用因子列表API"""
        # 设置模拟服务的返回值
        mock_factor_service.get_available_factors.return_value = {
            "buy_factors": [
                {
                    "id": "buy-factor-1",
                    "name": "测试买入因子",
                    "description": "测试用的买入因子",
                    "factor_type": "buy",
                    "factor_class": "AbuFactorBuyBreak",
                    "parameters": {"xd": 20}
                }
            ],
            "sell_factors": [
                {
                    "id": "sell-factor-1",
                    "name": "测试卖出因子",
                    "description": "测试用的卖出因子",
                    "factor_type": "sell",
                    "factor_class": "AbuFactorSellBreak",
                    "parameters": {"xd": 15}
                }
            ]
        }

        # 发送GET请求
        response = client.get("/api/v1/strategy/factors/")

        # 验证响应
>       assert response.status_code == 200
E       assert 500 == 200
E        +  where 500 = <Response [500 Internal Server Error]>.status_code

tests\api\endpoints\test_strategy_api.py:308: AssertionError
------------------------------------- Captured stderr call --------------------------------------
获取可用因子列表时发生错误: FactorService.get_available_factors() got an unexpected keyword argument 'factor_type'
Traceback (most recent call last):
  File "D:\智能投顾\量化相关\abu_modern\backend\app\api\endpoints\strategy.py", line 240, in get_available_factors_endpoint
    factor_list = service.get_available_factors(factor_type=factor_type)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: FactorService.get_available_factors() got an unexpected keyword argument 'factor_type'
HTTPException: 获取可用因子列表时发生内部错误
--------------------------------------- Captured log call ---------------------------------------
ERROR    root:strategy.py:245 获取可用因子列表时发生错误: FactorService.get_available_factors() got an unexpected keyword argument 'factor_type'
Traceback (most recent call last):
  File "D:\智能投顾\量化相关\abu_modern\backend\app\api\endpoints\strategy.py", line 240, in get_available_factors_endpoint
    factor_list = service.get_available_factors(factor_type=factor_type)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: FactorService.get_available_factors() got an unexpected keyword argument 'factor_type'
WARNING  app.core.exception_handlers:exception_handlers.py:98 HTTPException: 获取可用因子列表时发 生内部错误
____________________________ TestSymbolAdapter.test_get_symbol_name _____________________________

self = <tests.test_symbol_adapter.TestSymbolAdapter testMethod=test_get_symbol_name>

    def test_get_symbol_name(self):
        """测试获取股票名称"""
        # 测试常见股票
        self.assertTrue(SymbolAdapter.get_symbol_name("sh600000") != "sh600000")  # 确保返回名称而不是代码
        self.assertTrue(SymbolAdapter.get_symbol_name("sz000001") != "sz000001")

        # 测试指数
        self.assertEqual(SymbolAdapter.get_symbol_name("sh000001"), "上证指数")
        self.assertEqual(SymbolAdapter.get_symbol_name("sz399001"), "深证成指")

        # 测试美股
        self.assertTrue("美股" in SymbolAdapter.get_symbol_name("usAAPL"))

        # 测试港股
>       self.assertTrue("港股" in SymbolAdapter.get_symbol_name("hk00700"))
E       AssertionError: False is not true

tests\test_symbol_adapter.py:119: AssertionError
------------------------------------- Captured stderr call --------------------------------------
使用tushare获取usAAPL的名称失败: 无法转换为tushare格式: usAAPL
--------------------------------------- Captured log call ---------------------------------------
WARNING  root:symbol_adapter.py:289 使用tushare获取usAAPL的名称失败: 无法转换为tushare格式: usAAPL
======================================= warnings summary ========================================
..\..\abu\abupy\DLBu\ABuDLImgStd.py:11
  D:\智能投顾\量化相关\abu\abupy\DLBu\ABuDLImgStd.py:11: DeprecationWarning: 'imghdr' is deprecated and slated for removal in Python 3.13
    import imghdr

app\api\router.py:7
  D:\智能投顾\量化相关\abu_modern\backend\app\api\router.py:7: DeprecationWarning: `example` has been deprecated, please use `examples` instead
    from app.api.endpoints import market, strategy

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== short test summary info ====================================
FAILED tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterExecuteStrategy::test_execute_strategy_success_with_trades - KeyError: 'parameters_used'
FAILED tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterExecuteStrategy::test_execute_strategy_success_no_trades - KeyError: 'results'
FAILED tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterExecuteStrategy::test_execute_strategy_missing_capital_in_market_data_uses_strategy_params - AssertionError: Expected 'do_symbols_with_same_factors' to have been called once. Called 0 ti...
FAILED tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterExecuteStrategy::test_execute_strategy_abupy_exception - Failed: DID NOT RAISE <class 'app.core.exceptions.AdapterError'>
FAILED tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterExecuteStrategy::test_execute_strategy_invalid_factor_module - AssertionError: Regex pattern did not match.
FAILED tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterGetAvailableAbuFactors::test_get_buy_factors_only - app.core.exceptions.AdapterError: 获取可用因子信息时出错: TestStrategyAdapterGetAvailableAbuF...
FAILED tests/api/endpoints/test_strategy_api.py::test_get_strategies - assert 500 == 200
FAILED tests/api/endpoints/test_strategy_api.py::test_get_strategy - assert 404 == 200
FAILED tests/api/endpoints/test_strategy_api.py::test_get_strategy_not_found - AssertionError: Expected 'get_strategy_by_id' to be called once. Called 0 times.
FAILED tests/api/endpoints/test_strategy_api.py::test_create_strategy - AssertionError: assert '64d345548044...08eb84def6028' == 'test-strategy-id'
FAILED tests/api/endpoints/test_strategy_api.py::test_update_strategy - assert 404 == 200
FAILED tests/api/endpoints/test_strategy_api.py::test_update_strategy_not_found - AssertionError: Expected 'update_strategy' to be called once. Called 0 times.
FAILED tests/api/endpoints/test_strategy_api.py::test_delete_strategy - assert 404 == 200
FAILED tests/api/endpoints/test_strategy_api.py::test_delete_strategy_not_found - AssertionError: Expected 'delete_strategy' to be called once. Called 0 times.
FAILED tests/api/endpoints/test_strategy_api.py::test_get_available_factors - assert 500 == 200
FAILED tests/test_symbol_adapter.py::TestSymbolAdapter::test_get_symbol_name - AssertionError: False is not true
===================== 16 failed, 21 passed, 2 warnings in 90.88s (0:01:30) ======================

