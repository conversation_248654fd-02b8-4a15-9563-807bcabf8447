# -*- coding: utf-8 -*-
"""
FastAPI应用入口
"""
# ！！！兼容性补丁必须在所有其他导入之前应用！！！
# 这将确保在导入任何可能依赖旧库（如abupy）的模块之前，
# 我们的Python环境已经被正确地"修补"了。
from app.core.compatibility import apply_patches
apply_patches()

import logging
from logging.config import fileConfig
from contextlib import asynccontextmanager
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

from app.api.router import api_router
from app.core.config import settings
from app.core.exception_handlers import setup_exception_handlers
from app.core.database import create_db_and_tables, get_session


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 在应用启动时执行
    print("--- Starting up... ---")
    create_db_and_tables()
    print("--- Database and tables created. ---")
    yield
    # 在应用关闭时执行
    print("--- Shutting down... ---")

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册全局异常处理器
setup_exception_handlers(app)

# 路由
app.include_router(api_router, prefix=settings.API_V1_STR)





# 配置日志
# 启动时加载日志配置
try:
    fileConfig('app/core/logging.conf', disable_existing_loggers=False)
except Exception as e:
    print(f"Error loading logging config: {e}")
    # Fallback to basic config if file is missing or invalid
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


# 数据库会话依赖
# 数据库会话依赖
# 此处不直接定义get_db_session，因为FastAPI的依赖注入系统
# 可以直接使用 Depends(get_session)


@app.get("/")
def root():
    """根路径返回"""
    return {"message": "欢迎使用ABU量化投资系统API"}
