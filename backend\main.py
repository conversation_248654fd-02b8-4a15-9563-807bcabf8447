# backend/main.py (最终、干净的版本)

# -*- coding: utf-8 -*-
"""
FastAPI应用入口
"""
from backend.app.bootstrap.safe_date_patch import install as _install
_install(raise_on_yyyymmdd=True, stack_on_print=True)
# ！！！兼容性补丁必须在所有其他导入之前应用！！！
from backend.app.core.compatibility import apply_patches
apply_patches()

# --- 标准库和第三方库导入 ---
import logging
from logging.config import fileConfig
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

# --- 项目内部模块导入 ---
from backend.app.api.router import api_router
from backend.app.core.config import settings
from backend.app.core.exception_handlers import setup_exception_handlers
from backend.app.core.database import create_db_and_tables

# --- 日志配置 (唯一的、正确的位置) ---
try:
    # 使用 Path 构建一个相对于当前文件 (__file__) 的绝对路径，确保健壮性
    log_conf_path = Path(__file__).parent / 'app' / 'core' / 'logging.conf'
    if log_conf_path.exists():
        fileConfig(str(log_conf_path), disable_existing_loggers=False)
    else:
        # 如果文件不存在，明确地抛出错误，而不是静默失败
        raise FileNotFoundError(f"Logging config file not found at: {log_conf_path}")
except Exception as e:
    print(f"CRITICAL: Error loading logging config: {e}")
    print("CRITICAL: Falling back to basic logging.")
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


# --- 应用生命周期管理 ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    print("--- Starting up... ---")
    create_db_and_tables()
    print("--- Database and tables created. ---")
    
    # 启动时补丁验证
    try:
        from backend.app.core.compatibility import verify_patches
        patch_status = verify_patches()
        if patch_status:
            print("--- ✅ 所有兼容性补丁验证通过 ---")
        else:
            print("--- ⚠️ 部分兼容性补丁验证失败，请检查日志 ---")
    except Exception as e:
        print(f"--- ❌ 补丁验证过程出错: {e} ---")
    
    yield
    print("--- Shutting down... ---")


# --- FastAPI 应用实例化 ---
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    lifespan=lifespan
)

# --- 中间件配置 ---
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- 注册组件 ---
setup_exception_handlers(app) # 全局异常处理器
app.include_router(api_router, prefix=settings.API_V1_STR) # API 路由


# --- 根路径端点 ---
@app.get("/")
def root():
    """根路径返回"""
    return {"message": "欢迎使用ABU量化投资系统API"}


# --- 健康检查端点 ---
@app.get("/health/compatibility")
async def check_compatibility():
    """检查兼容性补丁状态"""
    try:
        # 验证关键补丁是否生效
        import abupy.AlphaBu.ABuPickTimeWorker as ABuPickTimeWorker
        
        has_patch = hasattr(ABuPickTimeWorker.AbuPickTimeWorker, '_execute_fixed_fit')
        
        return {
            "status": "healthy" if has_patch else "warning",
            "pandas_timedelta_patch": has_patch,
            "message": "ABuPickTimeWorker补丁已应用" if has_patch else "ABuPickTimeWorker补丁未检测到"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"兼容性检查失败: {str(e)}"
        }