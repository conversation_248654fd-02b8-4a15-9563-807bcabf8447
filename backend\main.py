# -*- coding: utf-8 -*-
"""
FastAPI应用入口
"""
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlmodel import Session

from backend.app.core.config import settings
from backend.app.api.router import api_router
from backend.app.core.exception_handlers import setup_exception_handlers
from backend.app.core.database import create_db_and_tables, get_session

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册全局异常处理器
setup_exception_handlers(app)

# 路由
app.include_router(api_router, prefix=settings.API_V1_STR)


# 数据库初始化事件
@app.on_event("startup")
def init_db():
    """启动时初始化数据库"""
    create_db_and_tables()


# 数据库会话依赖
def get_db_session():
    """获取数据库会话依赖"""
    return get_session()


@app.get("/")
def root():
    """根路径返回"""
    return {"message": "欢迎使用ABU量化投资系统API"}
