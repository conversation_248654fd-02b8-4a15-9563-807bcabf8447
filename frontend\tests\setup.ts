import { vi, beforeAll, afterEach, afterAll, beforeEach } from 'vitest';
import { config } from '@vue/test-utils';
import { createPinia, setActivePinia, type Pinia } from 'pinia';
import { setupServer } from 'msw/node';
import { simpleHandlers } from './mocks/simple';

// 全局测试设置

// 1. 设置MSW服务器
const server = setupServer(...simpleHandlers);

// 2. MSW生命周期管理
beforeAll(() => {
  // 启动MSW服务器
  server.listen({ onUnhandledRequest: 'warn' });
});

afterEach(() => {
  // 重置处理器状态
  server.resetHandlers();
});

afterAll(() => {
  // 关闭MSW服务器
  server.close();
});

// 【已修正】建立统一的、权威的测试生命周期管理
let pinia: Pinia;

beforeEach(() => {
  // 为每个测试创建一个全新的 Pinia 实例，确保环境隔离
  pinia = createPinia();
  setActivePinia(pinia);

  // Vue Test Utils 全局配置，使用当前测试的Pinia实例
  config.global.plugins = [pinia];

  // 为每个测试启用伪造定时器
  vi.useFakeTimers();
  
  // 为每个测试清理 Mocks 和 Storage
  vi.clearAllMocks();
  localStorage.clear();
  sessionStorage.clear();
});

afterEach(() => {
  // 再次清理以防万一
  vi.restoreAllMocks();
});


// 3. Mock 全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// 4. Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 5. Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 6. Mock Canvas API (for charts)
HTMLCanvasElement.prototype.getContext = vi.fn().mockReturnValue({
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn(() => ({ data: new Array(4) })),
  putImageData: vi.fn(),
  createImageData: vi.fn(() => ({ data: new Array(4) })),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  fillText: vi.fn(),
  restore: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  translate: vi.fn(),
  scale: vi.fn(),
  rotate: vi.fn(),
  arc: vi.fn(),
  fill: vi.fn(),
  measureText: vi.fn(() => ({ width: 0 })),
  transform: vi.fn(),
  rect: vi.fn(),
  clip: vi.fn(),
});

// 7. Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url');
global.URL.revokeObjectURL = vi.fn();

// 8. Mock fetch API
global.fetch = vi.fn();

// 9. 【已修正】强化 localStorage Mock
Object.defineProperty(window, 'localStorage', {
  writable: true,
  value: (() => {
    let store: Record<string, string> = {};
    return {
      getItem: (key: string) => store[key] || null,
      setItem: (key: string, value: string) => {
        store[key] = value.toString();
      },
      removeItem: (key: string) => {
        delete store[key];
      },
      clear: () => {
        store = {};
      },
      key: (index: number) => Object.keys(store)[index] || null,
      get length() {
        return Object.keys(store).length;
      },
    };
  })(),
});

// 10. Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// 11. Mock console methods for cleaner test output
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
  const message = args[0];
  if (
    typeof message === 'string' &&
    (
      message.includes('[Vue warn]') ||
      message.includes('Download the Vue Devtools') ||
      message.includes('You are running Vue in development mode')
    )
  ) {
    return;
  }
  originalConsoleError.apply(console, args);
};

// 12. Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByName: vi.fn(() => []),
    getEntriesByType: vi.fn(() => []),
  },
  writable: true
});

// 13. Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id));

// 14. Mock Element.scrollIntoView
Element.prototype.scrollIntoView = vi.fn();

// 15. Mock Element.getBoundingClientRect
Element.prototype.getBoundingClientRect = vi.fn(() => ({
  bottom: 0,
  height: 0,
  left: 0,
  right: 0,
  top: 0,
  width: 0,
  x: 0,
  y: 0,
  toJSON: vi.fn(),
}));

// 16. 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.VITE_API_BASE_URL = 'http://localhost:3000/api';

// 17. 全局测试工具函数
declare global {
  var testUtils: {
    createMockBacktestResult: () => any;
    createMockStrategy: () => any;
    createMockHistoryItem: () => any;
    waitForNextTick: () => Promise<void>;
    flushPromises: () => Promise<void>;
  };
}

global.testUtils = {
  createMockBacktestResult: () => ({
    task_id: 'mock-task-id',
    strategy_name: 'Mock Strategy',
    symbol: 'AAPL',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 120000,
    metrics: {
      total_return: 0.20,
      annual_return: 0.20,
      max_drawdown: -0.05,
      sharpe_ratio: 1.5,
      win_rate: 0.65,
      profit_loss_ratio: 1.8,
      total_trades: 50,
      winning_trades: 32,
      losing_trades: 18,
      avg_holding_period: 15,
      volatility: 0.15
    },
    trades: [],
    positions: [],
    equity_curve: [
      { date: '2023-01-01', value: 100000 },
      { date: '2023-06-01', value: 110000 },
      { date: '2023-12-31', value: 120000 }
    ],
    generated_at: '2024-01-01T12:00:00Z'
  }),

  createMockStrategy: () => ({
    id: 'mock-strategy-id',
    name: 'Mock Strategy',
    description: 'A mock strategy for testing',
    parameters: {},
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }),

  createMockHistoryItem: () => ({
    id: 'mock-history-id',
    strategy_name: 'Mock Strategy',
    symbol: 'AAPL',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    status: 'completed',
    created_at: '2024-01-01T10:00:00Z'
  }),

  waitForNextTick: () => new Promise(resolve => setTimeout(resolve, 0)),
  
  flushPromises: () => new Promise(resolve => setTimeout(resolve, 0))
};

// 18. 导出MSW服务器实例供测试使用
export { server };