import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useBacktestStore } from '@/stores/useBacktestStore';
import { BacktestStatus } from '@/api/types/backtest';
import * as backtestApi from '@/api/backtest';

// Mock API
vi.mock('@/api/backtest');

// Mock data
const mockBacktestConfig = {
  strategy_id: 'test-strategy',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  capital: 100000
};

const mockBacktestTask = {
  id: 'test-task-123',
  strategy_id: 'test-strategy',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  status: BacktestStatus.RUNNING,
  created_at: '2023-01-01T00:00:00Z',
  config: mockBacktestConfig
};

const mockBacktestResult = {
  task_id: 'test-task-123',
  strategy_name: 'Test Strategy',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  initial_capital: 100000,
  final_capital: 120000,
  metrics: {
    total_return: 0.2,
    annual_return: 0.2,
    max_drawdown: 0.1,
    sharpe_ratio: 1.5,
    win_rate: 0.6
  },
  equity_curve: [
    { date: '2023-01-01', value: 100000 },
    { date: '2023-12-31', value: 120000 }
  ],
  trades: [
    {
      symbol: 'AAPL',
      entry_date: '2023-01-01',
      exit_date: '2023-01-15',
      entry_price: 150,
      exit_price: 160,
      quantity: 100,
      pnl: 1000
    }
  ]
};

const mockHistoryItem = {
  id: 'history-1',
  strategy_name: 'Test Strategy',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  status: BacktestStatus.COMPLETED,
  created_at: '2023-01-01T00:00:00Z',
  metrics: {
    total_return: 0.15,
    annual_return: 0.15,
    max_drawdown: 0.08,
    sharpe_ratio: 1.2,
    win_rate: 0.55
  }
};

// Helper function for successful API responses
const mockSuccess = (data: any) => ({ data });

describe('useBacktestStore Extended Tests', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  // ===== TDD 测试 (保持原有后缀) =====
  describe('契约 A: 初始状态 - TDD', () => {
    it('应该有正确的初始状态 - TDD', () => {
      const store = useBacktestStore();
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestProgress).toBe(0);
      expect(store.backtestResult).toBeNull();
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toBeNull();
      expect(store.backtestHistory).toEqual([]);
    });
  });

  describe('契约 B: 启动回测流程 - TDD', () => {
    it('成功启动回测时，状态应正确更新 - TDD', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockResolvedValue({ data: { ...mockBacktestTask, strategy_name: 'Test Strategy' }, success: true });
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(true);
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
      expect(store.backtestError).toBe('');
      expect(backtestApi.runBacktest).toHaveBeenCalledWith(mockBacktestConfig);
    });

    it('启动回测失败时，应设置错误信息 - TDD', async () => {
      const store = useBacktestStore();
      const errorMessage = '启动失败';
      vi.mocked(backtestApi.runBacktest).mockRejectedValue(new Error(errorMessage));
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(false);
      expect(store.currentBacktestTask).toBeNull();
      expect(store.backtestError).toBe(errorMessage);
    });
  });

  describe('契约 C: 获取结果 - TDD', () => {
    it('成功获取结果时，应更新backtestResult - TDD', async () => {
      const store = useBacktestStore();
      const expectedResult = {
        ...mockBacktestResult,
        positions: [],
        generated_at: '2023-01-01T00:00:00Z'
      };
      vi.mocked(backtestApi.getBacktestResults).mockResolvedValue({ 
        data: expectedResult, 
        success: true 
      });
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestResult).toEqual(expectedResult);
      expect(store.backtestError).toBe('');
    });

    it('获取结果失败时，应设置错误信息 - TDD', async () => {
      const store = useBacktestStore();
      const errorMessage = '获取结果失败';
      vi.mocked(backtestApi.getBacktestResults).mockRejectedValue(new Error(errorMessage));
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestResult).toBeNull();
      expect(store.backtestError).toBe(errorMessage);
    });
  });

  describe('契约 D: 停止回测 - TDD', () => {
    it('成功停止回测时，应清理状态 - TDD', async () => {
      const store = useBacktestStore();
      store.$patch({
        isBacktesting: true,
        currentBacktestTask: mockBacktestTask
      });
      vi.mocked(backtestApi.stopBacktest).mockResolvedValue(mockSuccess({ message: 'stopped' }));
      
      await store.stopCurrentBacktest();
      
      expect(store.isBacktesting).toBe(false);
      expect(store.currentBacktestTask).toBeNull();
      expect(store.backtestError).toBe('');
    });

    it('停止回测失败时，应设置错误信息 - TDD', async () => {
      const store = useBacktestStore();
      store.$patch({
        isBacktesting: true,
        currentBacktestTask: mockBacktestTask
      });
      const errorMessage = '停止失败';
      vi.mocked(backtestApi.stopBacktest).mockRejectedValue(new Error(errorMessage));
      
      await store.stopCurrentBacktest();
      
      expect(store.backtestError).toBe(errorMessage);
    });
  });

  describe('契约 E: 状态清理 - TDD', () => {
    it('clearError应清除错误信息 - TDD', () => {
      const store = useBacktestStore();
      store.$patch({ backtestError: '测试错误' });
      
      store.clearError();
      
      expect(store.backtestError).toBe('');
    });

    it('resetBacktestState应重置所有状态 - TDD', () => {
      const store = useBacktestStore();
      store.$patch({
        isBacktesting: true,
        backtestProgress: 50,
        backtestResult: mockBacktestResult,
        backtestError: '测试错误',
        currentBacktestTask: mockBacktestTask
      });
      
      store.resetBacktestState();
      
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestProgress).toBe(0);
      expect(store.backtestResult).toBeNull();
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toBeNull();
    });
  });

  describe('契约 F: 计算属性 - TDD', () => {
    it('hasResult: 必须在backtestResult有值时为true - TDD', () => {
      const store = useBacktestStore();
      expect(store.hasResult).toBe(false);
      
      store.$patch({ backtestResult: mockBacktestResult });
      expect(store.hasResult).toBe(true);
    });

    it('isCompleted: 必须在结果状态为COMPLETED时为true - TDD', () => {
      const store = useBacktestStore();
      expect(store.isCompleted).toBe(false);
      
      store.$patch({ currentBacktestTask: { ...mockBacktestTask, status: BacktestStatus.COMPLETED } });
      expect(store.isCompleted).toBe(true);
    });

    it('hasActiveBacktest: 必须在isBacktesting为true时为true - TDD', () => {
      const store = useBacktestStore();
      expect(store.hasActiveBacktest).toBe(false);
      
      store.$patch({ isBacktesting: true });
      expect(store.hasActiveBacktest).toBe(true);
    });
  });

  // ===== Extended 测试 (新增测试加上后缀) =====
  describe('契约 B: API调用期间状态管理 - Extended TDD', () => {
    it('在API调用期间，isBacktesting状态应正确管理 - Extended TDD', async () => {
      const store = useBacktestStore();
      let resolvePromise: (value: any) => void;
      const pendingPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      
      vi.mocked(backtestApi.runBacktest).mockReturnValue(pendingPromise);
      
      const startPromise = store.startBacktest(mockBacktestConfig);
      
      // API调用期间，状态应该已经更新
      expect(store.isBacktesting).toBe(true);
      
      // 完成API调用
      resolvePromise!(mockSuccess(mockBacktestTask));
      await startPromise;
      
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
    });
  });

  describe('契约 D: 获取运行中的结果 - Extended TDD', () => {
    it('成功获取仍在运行的结果 - Extended TDD', async () => {
      const store = useBacktestStore();
      const runningResult = { ...mockBacktestResult };
      store.$patch({ currentBacktestTask: { ...mockBacktestTask, status: BacktestStatus.RUNNING } });
      vi.mocked(backtestApi.getBacktestResults).mockResolvedValue(mockSuccess(runningResult));
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestResult).toEqual(runningResult);
      expect(store.backtestError).toBe('');
      // 仍在运行时，isBacktesting应该保持true
      expect(store.isBacktesting).toBe(true);
    });

    it('在没有任务ID时，action 内部应有卫兵语句阻止API调用 - Extended TDD', async () => {
      const store = useBacktestStore();
      await store.loadBacktestResults('');
      expect(backtestApi.getBacktestResults).not.toHaveBeenCalled();
      expect(store.backtestError).not.toBe('');
    });
  });

  describe('契约 F: 停止回测失败处理 - Extended TDD', () => {
    it('应该处理停止回测失败 - Extended TDD', async () => {
      const store = useBacktestStore();
      store.$patch({ isBacktesting: true });
      store.$patch({ currentBacktestTask: { id: 'test-task-123' } as any });
      const errorMessage = '无法停止已完成的任务';
      vi.mocked(backtestApi.stopBacktest).mockRejectedValue(new Error(errorMessage));
      
      await store.stopCurrentBacktest();

      expect(store.backtestError).toBe(errorMessage);
      expect(store.isBacktesting).toBe(true);
    });
    
    it('应该在没有任务ID时返回错误 - Extended TDD', async () => {
      const store = useBacktestStore();
      store.$patch({ currentBacktestTask: null });
      
      await store.stopCurrentBacktest();
      
      // 注意：当前实现中stopCurrentBacktest在没有任务ID时直接返回，不设置错误
      // 这个测试预期会失败，这是TDD红阶段的正常情况
      expect(store.backtestError).not.toBe('');
      expect(backtestApi.stopBacktest).not.toHaveBeenCalled();
    });
  });

  describe('契约 J: 网络错误处理 - Extended TDD', () => {
    it('应该处理网络连接超时错误 - Extended TDD', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockRejectedValue(new Error('Network timeout'));
      
      await store.startBacktest(mockBacktestConfig);

      expect(store.backtestError).toContain('Network timeout');
      expect(store.currentBacktestTask).toBeNull();
    });
    
    it('应该在网络恢复后能够重新启动回测 - Extended TDD', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockRejectedValueOnce(new Error('Network timeout'));
      
      await store.startBacktest(mockBacktestConfig);
      expect(store.backtestError).toContain('Network timeout');
      
      vi.mocked(backtestApi.runBacktest).mockResolvedValue(mockSuccess(mockBacktestTask));
      
      store.clearError();
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
    });
    
    it('应该处理获取结果时的网络错误 - Extended TDD', async () => {
      const store = useBacktestStore();
      store.$patch({ currentBacktestTask: mockBacktestTask });
      vi.mocked(backtestApi.getBacktestResults).mockRejectedValue(new Error('Network timeout'));
      
      await store.loadBacktestResults('test-task-123');

      expect(store.backtestError).toContain('Network timeout');
      expect(store.backtestResult).toBeNull();
    });
  });
});