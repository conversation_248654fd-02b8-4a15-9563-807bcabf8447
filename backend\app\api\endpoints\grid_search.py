from fastapi import APIRouter, HTTPException
from celery.result import As<PERSON><PERSON><PERSON><PERSON>
from backend.app.core.celery_app import celery_app
from backend.app.schemas.grid_search import GridSearchRunRequest, GridSearchRunResponse, GridSearchStatusResponse
from backend.app.tasks.grid_search_task import run_grid_search

router = APIRouter()

@router.post("/run", response_model=GridSearchRunResponse)
def run_grid_search_api(request: GridSearchRunRequest):
    """
    Endpoint to start a grid search task.
    """
    try:
        task = run_grid_search.delay(
            choice_symbols=request.choice_symbols,
            buy_factors=request.buy_factors,
            sell_factors=request.sell_factors,
            read_cash=request.read_cash
        )
        return {"task_id": task.id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}", response_model=GridSearchStatusResponse)
def get_grid_search_status(task_id: str):
    """
    Endpoint to get the status of a grid search task.
    """
    task_result = AsyncResult(task_id, app=celery_app)
    response = {
        "task_id": task_id,
        "status": task_result.status,
        "result": task_result.result if task_result.ready() else None
    }
    return response