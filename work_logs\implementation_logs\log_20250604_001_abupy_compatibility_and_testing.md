# abu_modern 实现日志：abupy 兼容性修复与测试改进

**日期**: 2025-06-04
**工程师**: Cascade AI & USER
**任务**: 解决 `abupy` 库在 Python 3.10+/3.12 环境下的兼容性问题，修复因子初始化错误，并通过 mock 网络请求确保单元测试的稳定性。
**相关文件**:
- `backend/app/abupy_adapter/compatibility_patch.py`
- `backend/app/abupy_adapter/mock_modules.py`
- `backend/app/abupy_adapter/compat.py`
- `backend/app/abupy_adapter/factors_converter.py`
- `backend/app/abupy_adapter/strategy_executor.py`
- `backend/tests/abupy_adapter/test_factors_converter.py`
- `backend/tests/abupy_adapter/test_strategy_adapter.py`
- `backend/app/abupy_adapter/data_cache_adapter.py` (涉及 `ABuSymbolPd.make_kl_df`)

## 1. 任务概述

本项目旨在解决 `abupy` 库在较新 Python 版本（3.10 及 3.12）中遇到的兼容性问题，并修复在因子实例化过程中出现的参数错误。核心目标是使 `abupy_adapter` 模块能够在现代化 Python 环境下稳定运行，并通过改进测试（特别是通过 mock 外部依赖如网络请求）来确保其可靠性。所有修改均遵循不直接更改 `abupy` 原始代码的原则，而是通过适配层、monkeypatch 或 mock 技术实现。

## 2. 本次会话焦点 (自上次重要检查点后)

自上次重要检查点 (Checkpoint 22, Step 729) 以来，我们的工作主要集中在以下几个方面：

1.  **`combine_kl_pd` 参数缺失导致的 `TypeError`**：在执行 `test_execute_strategy_success_with_trades` 测试时，因子 `AbuFactorBuyBase` 初始化失败，因为它缺少必需的 `combine_kl_pd` 参数。
2.  **网络请求依赖导致的测试不稳定**：`test_execute_strategy_success_with_trades` 测试用例因依赖 `ABuSymbolPd.make_kl_df` 进行真实网络请求而失败或不稳定。
3.  **完善 `FactorsConverter` 的参数处理**：确保 `FactorsConverter` 能够正确接收并传递所有必要的上下文参数（如 `capital`, `kl_pd`, `benchmark`, `combine_kl_pd`）给因子构造函数。

## 3. 实施细节

### 3.1 早期兼容性补丁与模块模拟 (回顾自项目初期)

为解决 `abupy` 对旧版库的依赖，已实施以下措施：

-   **`collections.abc` 兼容性**:
    *   **问题描述**: Python 3.10+ 中 `collections` 模块不再直接提供某些 ABC 类 (如 `Iterable`, `Mapping` 等)，导致 `abupy` 导入失败。
    *   **解决方案**: 在 `backend/app/abupy_adapter/compatibility_patch.py` 中，通过 `collections.abc` 重新注册这些类型到 `collections` 模块。
-   **`IPython`, `ipywidgets`, `scipy.interpolate` 模拟**:
    *   **问题描述**: `abupy` 依赖这些可选模块，但在测试或部署环境中可能未安装，导致导入错误。
    *   **解决方案**:
        *   在 `backend/app/abupy_adapter/mock_modules.py` 中创建了这些模块的模拟版本。
        *   在 `backend/app/abupy_adapter/compat.py` 中，尝试导入真实模块，如果失败则导入模拟模块，确保代码在缺少这些依赖时仍能运行。

### 3.2 修复 `FactorsConverter` 因子实例化参数问题

-   **问题描述**: `FactorsConverter.convert_to_abu_factors` 在实例化因子时，未能正确传递 `capital`, `kl_pd`, `benchmark` 等执行上下文参数，导致 `TypeError`。
-   **调查与根本原因**: 原始 `FactorsConverter` 实现中，因子构造函数仅接收因子特定参数，未合并执行上下文参数。
-   **解决方案实施**:
    *   修改 `backend/app/abupy_adapter/factors_converter.py` 中的 `convert_to_abu_factors` 方法。
    *   使其接受 `capital`, `kl_pd`, `benchmark`, `combine_kl_pd` 作为额外参数。
    *   在实例化因子前，将这些执行上下文参数与因子自身的参数合并到 `final_params` 字典中。
    *   修改了异常捕获逻辑，从 `except TypeError` 改为 `except Exception`，以更广泛地捕获实例化错误并包装为 `FactorError`。
-   **验证**: 通过后续运行 `test_convert_factors_instantiation_error` (见下文) 和其他集成测试验证。

### 3.3 修正 `test_convert_factors_instantiation_error` 测试用例

-   **问题描述**: `backend/tests/abupy_adapter/test_factors_converter.py` 中的 `test_convert_factors_instantiation_error` 测试用例因 `pytest.raises` 的 `match` 参数与实际抛出的 `FactorError` 异常消息不完全匹配而失败。
-   **调查与根本原因**: 正则表达式匹配不够精确。
-   **解决方案实施**: 更新了 `test_convert_factors_instantiation_error` 中 `pytest.raises` 的 `match` 参数，使其能准确匹配包装后的 `FactorError` 消息。
-   **验证**: 测试用例通过。

### 3.4 解决 `test_execute_strategy_success_with_trades` 中的网络依赖与 `combine_kl_pd` 问题

此测试用例是本次会话的核心关注点。

#### 3.4.1 Mock 网络请求 (`ABuSymbolPd.make_kl_df`)

-   **问题描述**: 测试因调用 `ABuSymbolPd.make_kl_df` 尝试进行真实网络数据获取而失败或不稳定。
-   **解决方案实施**:
    *   在 `backend/tests/abupy_adapter/test_strategy_adapter.py` 中添加了辅助函数 `create_sample_kl_df()`，用于生成符合 `abupy` 期望格式的模拟 K 线 DataFrame。
    *   使用 `@patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')` 来 mock `make_kl_df` 方法，使其返回由 `create_sample_kl_df()` 生成的模拟数据。
    *   确保 mock 装饰器的顺序和测试函数参数的顺序正确。
-   **验证**: 初步运行时，网络问题得到缓解，但暴露了 `combine_kl_pd` 问题。

#### 3.4.2 修复 `combine_kl_pd` 参数缺失导致的 `TypeError`

-   **问题描述**: 在 mock 了网络请求后，测试失败并抛出 `TypeError: AbuFactorBuyBase.__init__() missing 1 required positional argument: 'combine_kl_pd'`。
-   **调查与根本原因**:
    1.  `StrategyExecutor.execute_strategy` 在调用 `FactorsConverter.convert_to_abu_factors` 时，未传递 `combine_kl_pd` 参数。
    2.  `FactorsConverter.convert_to_abu_factors` 内部逻辑是：如果 `combine_kl_pd` 参数为 `None` (Python 中未传递参数时的默认行为)，则不会将其添加到传递给因子构造函数的参数字典 `final_params` 中。
    3.  然而，`abupy` 中的基础因子类如 `AbuFactorBuyBase`在其 `__init__` 方法中强制要求 `combine_kl_pd` 参数。
-   **解决方案实施**:
    *   修改 `backend/app/abupy_adapter/strategy_executor.py` 中的 `StrategyExecutor.execute_strategy` 方法。
    *   在对 `factors_converter.convert_to_abu_factors` 的两次调用（分别针对买入因子和卖出因子）中，均显式添加 `combine_kl_pd=True` 参数。
    *   此修改通过两次 `replace_file_content` 工具调用完成，第一次调用因 `TargetContent` 不唯一导致仅部分成功，第二次调用修正了剩余部分。
-   **验证**:
    *   用户手动执行了 `pytest -k "test_execute_strategy_success_with_trades" ...` 命令。
    *   PowerShell 输出和 `test_results_latest.txt` 文件内容均确认该测试用例状态为 `PASSED`。

### 3.5 全面 Mock `test_strategy_executor.py` 中的基准K线数据获取

在解决了 `test_strategy_adapter.py` 中特定测试的网络依赖后，我们继续处理 `test_strategy_executor.py` 中存在的类似问题。该测试文件中的多个测试用例因调用 `StrategyExecutor.execute_strategy`，进而触发 `ABuSymbolPd.make_kl_df` 获取基准K线数据，导致网络请求失败。

-   **问题定位**:
    *   `StrategyExecutor.execute_strategy` 内部调用 `ABuSymbolPd.make_kl_df(benchmark_symbol, ...)` 获取基准股票数据。
    *   多个测试用例直接或间接调用此方法，未对该网络请求进行 mock。

-   **解决方案实施**:
    1.  **统一Mock辅助函数**: 复用之前在 `test_strategy_adapter.py` 中引入的 `create_sample_kl_df()` 辅助函数，将其移至 `test_strategy_executor.py` 文件顶部，用于生成模拟的K线DataFrame。
    2.  **系统性应用Mock**:
        *   针对 `TestStrategyExecutor` 类中的以下测试方法，均添加了 `@patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')` 装饰器，并使其返回 `create_sample_kl_df()` 的结果：
            *   `test_execute_strategy_basic`
            *   `test_execute_strategy_no_trades`
            *   `test_execute_strategy_capital_fallback`
            *   `test_execute_strategy_abupy_exception` (此测试在之前的修改中可能被意外删除或损坏，已恢复并添加mock)
            *   `test_execute_strategy_factor_error` (同上，已恢复并添加mock)
            *   `test_execute_strategy_with_mocked_execution`
            *   `test_parameter_extraction` (在初步修复并通过其他测试后，发现此测试仍因网络请求失败，随后补充了mock)
    3.  **修正 `test_execute_strategy_missing_capital`**:
        *   该测试用例的逻辑和断言存在问题，已修正以正确测试完全缺少资金参数（同时不存在于市场数据和策略参数中）的场景，并期望抛出 `ParameterError`。

-   **验证**:
    *   用户按照指导，逐步运行 `pytest -v backend\tests\abupy_adapter\test_strategy_executor.py`。
    *   经过几轮迭代修复（包括修正 `replace_file_content` 工具因 `TargetContent` 不精确导致的修改不完整问题），最终 `test_strategy_executor.py` 中的全部10个测试用例均成功通过。

## 4. 测试与验证

经过本轮的兼容性修复、参数传递修正以及广泛的网络请求mock：

-   **`backend/tests/abupy_adapter/test_strategy_adapter.py`**:
    *   `test_execute_strategy_success_with_trades`: 已通过。验证了 `combine_kl_pd` 参数修复和对目标股票 `make_kl_df` 的mock。
-   **`backend/tests/abupy_adapter/test_factors_converter.py`**:
    *   `test_convert_factors_instantiation_error`: 已通过。
-   **`backend/tests/abupy_adapter/test_strategy_executor.py`**:
    *   **全部10个测试用例均已通过**。这包括 `test_execute_strategy_basic`, `test_execute_strategy_no_trades`, `test_execute_strategy_missing_symbols`, `test_execute_strategy_missing_dates`, `test_execute_strategy_capital_fallback`, `test_execute_strategy_missing_capital`, `test_execute_strategy_abupy_exception`, `test_execute_strategy_factor_error`, `test_execute_strategy_with_mocked_execution`, 和 `test_parameter_extraction`。
    *   所有测试均已mock掉对基准K线数据 (`ABuSymbolPd.make_kl_df`) 的网络请求，确保了测试的稳定性和独立性。

目前，核心的策略执行和因子转换相关的单元测试已基本扫清了由于 `abupy` 兼容性、参数传递错误以及网络依赖导致的不稳定因素。

## 5. 遇到的挑战和解决方案

1.  **`replace_file_content` 工具的 `TargetContent` 唯一性**: 在修改 `strategy_executor.py` 以添加 `combine_kl_pd=True` 时，由于买入因子和卖出因子转换调用的代码片段相似，导致第一次尝试替换时 `TargetContent` 不唯一，未能完全应用修改。通过后续查看文件内容并进行针对性的第二次替换解决了此问题。
2.  **逐步暴露问题**: 修复一个问题（如网络依赖）后，会暴露更深层次的问题（如参数缺失）。这要求细致的调试和迭代修复。
3.  **理解 `abupy` 内部机制**: 准确诊断 `combine_kl_pd` 问题需要对 `abupy` 因子类的构造函数签名和 `FactorsConverter` 的参数传递逻辑有一定理解。

### 5.2 讨论：单元测试中Mock数据与真实API行为的一致性

在本次修复过程中，我们大量使用了 mock 技术来替代真实的网络请求，这引发了用户关于“mock数据与真实API行为可能不一致”的疑问。对此，我们进行了如下讨论：

-   **单元测试与Mock的目的**：
    *   **隔离性**: 单元测试旨在独立测试代码单元的逻辑。
    *   **速度与稳定性**: Mock外部依赖（如网络请求）可以使测试运行更快、更稳定，不受外部环境影响。
    *   **关注点**: 确保被测代码在给定（模拟的）输入下能产生预期输出。

-   **Mock数据与真实数据的不一致风险**：
    *   **契约漂移 (Contract Drift)**: 如果真实API的行为（如返回结构、错误码）发生变化，而mock数据未同步更新，单元测试可能依然通过，但实际应用会出错。
    *   **覆盖不全**: Mock数据通常只覆盖预期的主要场景，可能遗漏真实API的边缘情况或未预料到的错误模式。

-   **如何降低风险**：
    *   **集成测试 (Integration Tests)**: 测试多个组件协同工作，可包括与真实外部API（或其测试环境）的交互，验证集成点。
    *   **契约测试 (Contract Testing)**: 使用如Pact等工具，让API消费者和提供者共同定义和遵守一个“契约”，独立测试。
    *   **谨慎和准确地创建Mock**: Mock数据应尽可能模拟真实API成功返回时的数据结构和行为。
    *   **监控与告警**: 生产环境中对API调用进行充分监控，及时发现实际调用与预期不符的问题。

-   **结论**: 单元测试中的mock是测试策略的一部分，用于保证代码单元的内部逻辑。它不能完全替代对真实API交互的测试，需要结合集成测试等其他测试手段来确保整体系统的健壮性。我们当前的工作成功使单元测试摆脱了网络依赖，为后续更高级别的测试打下了良好基础。

## 6. 后续工作建议

在本轮修复工作中，`test_strategy_adapter.py` 中的主要网络依赖、`NameError`、`AssertionError` 以及 `pytest` 警告已得到解决。所有先前失败的测试（除一个标记为 `skip` 的集成测试外）均已通过。后续工作建议调整为：

1.  **全面审查与巩固单元测试**:
    *   虽然主要的失败测试已修复，但仍建议对 `backend/tests/abupy_adapter/test_strategy_adapter.py` 和 `backend/tests/abupy_adapter/test_strategy_executor.py` 进行一次全面审查，确保所有必要的 mock 都已正确实施，并且测试逻辑清晰、覆盖全面。
    *   特别关注之前修复的测试用例，确保其 mock 策略的鲁棒性和准确性。

2.  **提升测试覆盖率**:
    *   系统性评估 `abupy_adapter` 模块的测试覆盖率。
    *   针对尚未覆盖或覆盖不足的代码路径（特别是错误处理、边缘条件、不同参数组合等）补充新的单元测试。
    *   例如，可以为 `StrategyAdapter.get_available_abu_factors` 方法在 `factor_type="sell"` 或 `factor_type=None` 时，因子模块导入失败的情况补充更细致的测试。

3.  **规划和实施集成测试**:
    *   目前跳过的 `test_get_available_factors_integration` 集成测试，应在具备合适测试环境（确保 `abupy` 及其依赖可正常导入）的前提下进行配置和运行。
    *   根据项目需求和资源，考虑增加更多集成测试，以验证 `abupy_adapter` 与 `abupy` 库在关键数据获取、因子转换和策略执行流程上的真实交互，弥补单元测试中 mock 带来的局限性。

4.  **代码质量与可维护性**:
    *   在添加新测试或重构现有测试时，注意保持测试代码的清晰、简洁和可维护性。
    *   考虑将一些通用的 mock 设置或辅助函数提取出来，以便在多个测试用例中复用。

## 7. 结论

本次会话在解决 `abupy` 兼容性和测试问题方面取得了显著进展。通过细致的诊断和修复，成功解决了关键的 `combine_kl_pd` 参数缺失导致的 `TypeError`，并为 `test_execute_strategy_success_with_trades` 测试用例实现了有效的网络 mock，使其能够稳定通过。这些工作为后续全面修复测试套件和确保 `abupy_adapter` 模块的健壮性奠定了坚实的基础。

## 8. 附件
测试结果：
PowerShell
(backend) PS D:\智能投顾\量化相关\abu_modern\backend> pytest -k "test_execute_strategy_success_with_trades" -v --log-cli-level=INFO --capture=no --disable-warnings > D:\智能投顾\量化相关\abu_modern\temporary_for_test\test_results_latest.txt
成功应用collections兼容性补丁，添加了7个类型
IPython模块已存在，无需补丁
ipywidgets模块已存在，无需模拟
使用模拟IPython模块
使用模拟ipywidgets模块
Abu模块导入成功
成功导入原始ABuDataCache模块的函数: ['ABuEnv', 'AbuProgress', 'EDataCacheType', 'EMarketSubType', 'EMarketTargetType', 'HDF5ExtError', 'absolute_import', 'check_csv_local', 'covert_hdf_to_csv', 'del_file', 'division', 'dump_del_hdf5', 'dump_df_csv', 'dump_kline_df', 'ensure_dir', 'file_exist', 'filter', 'load_all_kline', 'load_df_csv', 'load_hdf5', 'load_kline_df', 'load_kline_df_net', 'os', 'pd', 'print_function', 'range', 'rm_data_from_symbol', 'save_kline_df', 'xrange']
IPython模块已存在，无需补丁
ipywidgets模块已存在，无需模拟
从策略参数中提取资金: 1000000
准备执行策略 Test Strategy 与参数: capital=1000000, buy_factors=1, sell_factors=0
已创建benchmark对象，symbol=sh000300, n_folds=2
Using MOCKED do_symbols_with_same_factors for execution (type: <class 'unittest.mock.MagicMock'>).
即将调用mock对象: do_symbols_with_same_factors
调用参数: {'target_symbols': ['SH600000'], 'benchmark': SimpleBenchmark(symbol=sh000300, n_folds=2), 'buy_factors': [AbuFactorBuyBreak: slippage:<class 'abupy.SlippageBu.ABuSlippageBuyMean.AbuSlippageBuyMean'>, position:<class 'abupy.BetaBu.ABuAtrPosition.AbuAtrPosition'>
kl:
None], 'sell_factors': [], 'capital': 1000000, 'show_progress': True}
调用成功，返回值类型: <class 'list'>
结果处理: 是否为mock结果 = True
处理 mock 对象返回的结果
订单状态: has_trades=True, orders_pd类型=<class 'unittest.mock.MagicMock'>
结果处理: symbol=SH600000, final_capital=1200000, message='交易完成'
策略执行完成，返回: success, 消息: 策略执行完成
执行统计: 总股票数=1, 有交易股票数=1, 总交易数=2

测试结果保存在D:\智能投顾\量化相关\abu_modern\temporary_for_test\test_results_latest.txt
============================= test session starts =============================
platform win32 -- Python 3.12.2, pytest-8.3.5, pluggy-1.6.0 -- D:\智能投顾\量化相关\abu_modern\backend\.venv\Scripts\python.exe
cachedir: .pytest_cache
rootdir: D:\智能投顾\量化相关\abu_modern\backend
configfile: pyproject.toml
plugins: anyio-4.9.0
collecting ... 
----------------------------- live log collection -----------------------------
INFO     root:compatibility_patch.py:59 成功应用collections兼容性补丁，添加了7个类型
INFO     root:compatibility_patch.py:71 IPython模块已存在，无需补丁
INFO     root:mock_modules.py:102 ipywidgets模块已存在，无需模拟
INFO     root:compat.py:32 使用模拟IPython模块
INFO     root:compat.py:52 使用模拟ipywidgets模块
INFO     root:symbol_adapter.py:22 Abu模块导入成功
INFO     root:data_cache_adapter.py:24 成功导入原始ABuDataCache模块的函数: ['ABuEnv', 'AbuProgress', 'EDataCacheType', 'EMarketSubType', 'EMarketTargetType', 'HDF5ExtError', 'absolute_import', 'check_csv_local', 'covert_hdf_to_csv', 'del_file', 'division', 'dump_del_hdf5', 'dump_df_csv', 'dump_kline_df', 'ensure_dir', 'file_exist', 'filter', 'load_all_kline', 'load_df_csv', 'load_hdf5', 'load_kline_df', 'load_kline_df_net', 'os', 'pd', 'print_function', 'range', 'rm_data_from_symbol', 'save_kline_df', 'xrange']
INFO     root:compatibility_patch.py:71 IPython模块已存在，无需补丁
INFO     root:mock_modules.py:102 ipywidgets模块已存在，无需模拟
collected 69 items / 68 deselected / 1 selected

tests/abupy_adapter/test_strategy_adapter.py::TestStrategyAdapterExecuteStrategy::test_execute_strategy_success_with_trades STRATEGY_EXECUTOR_DIAG: execute_strategy called. Class source: D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_executor.py, Module source: D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\strategy_executor.py

-------------------------------- live log call --------------------------------
INFO     root:strategy_executor.py:69 从策略参数中提取资金: 1000000
INFO     root:strategy_executor.py:137 准备执行策略 Test Strategy 与参数: capital=1000000, buy_factors=1, sell_factors=0
INFO     root:strategy_executor.py:153 已创建benchmark对象，symbol=sh000300, n_folds=2
INFO     root:strategy_executor.py:178 Using MOCKED do_symbols_with_same_factors for execution (type: <class 'unittest.mock.MagicMock'>).
INFO     root:strategy_executor.py:195 即将调用mock对象: do_symbols_with_same_factors
<class 'pandas.core.frame.DataFrame'>
DatetimeIndex: 252 entries, 2024-06-18 16:32:42.634531 to 2025-06-04 16:32:42.634531
Freq: B
Data columns (total 10 columns):
 #   Column     Non-Null Count  Dtype  
---  ------     --------------  -----  
 0   open       252 non-null    float64
 1   high       252 non-null    float64
 2   low        252 non-null    float64
 3   close      252 non-null    float64
 4   pre_close  252 non-null    float64
 5   volume     252 non-null    int64  
 6   p_change   252 non-null    float64
 7   log_ret    252 non-null    float64
 8   atr21      252 non-null    float64
 9   atr14      252 non-null    float64
dtypes: float64(9), int64(1)
memory usage: 21.7 KB
INFO     root:strategy_executor.py:198 调用参数: {'target_symbols': ['SH600000'], 'benchmark': SimpleBenchmark(symbol=sh000300, n_folds=2), 'buy_factors': [AbuFactorBuyBreak: slippage:<class 'abupy.SlippageBu.ABuSlippageBuyMean.AbuSlippageBuyMean'>, position:<class 'abupy.BetaBu.ABuAtrPosition.AbuAtrPosition'> 
kl:
None], 'sell_factors': [], 'capital': 1000000, 'show_progress': True}
INFO     root:strategy_executor.py:205 调用成功，返回值类型: <class 'list'>
INFO     root:strategy_executor.py:237 结果处理: 是否为mock结果 = True
INFO     root:strategy_executor.py:241 处理 mock 对象返回的结果
INFO     root:strategy_executor.py:262 订单状态: has_trades=True, orders_pd类型=<class 'unittest.mock.MagicMock'>
INFO     root:strategy_executor.py:320 结果处理: symbol=SH600000, final_capital=1200000, message='交易完成'
INFO     root:strategy_executor.py:405 策略执行完成，返回: success, 消息: 策略执行完成
INFO     root:strategy_executor.py:406 执行统计: 总股票数=1, 有交易股票数=1, 总交易数=2
PASSED

================= 1 passed, 68 deselected, 1 warning in 3.09s =================

============================= test session starts =============================
platform win32 -- Python 3.12.2, pytest-8.3.5, pluggy-1.6.0 -- D:\智能投顾\量化相关\abu_modern\backend\.venv\Scripts\python.exe
cachedir: .pytest_cache
rootdir: D:\智能投顾\量化相关\abu_modern\backend
configfile: pyproject.toml
plugins: anyio-4.9.0
collecting ... collected 10 items

backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_basic PASSED [ 10%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_no_trades PASSED [ 20%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_missing_symbols PASSED [ 30%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_missing_dates PASSED [ 40%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_capital_fallback PASSED [ 50%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_missing_capital PASSED [ 60%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_abupy_exception PASSED [ 70%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_factor_error PASSED [ 80%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_execute_strategy_with_mocked_execution PASSED [ 90%]
backend\tests\abupy_adapter\test_strategy_executor.py::TestStrategyExecutor::test_parameter_extraction PASSED [100%]

============================= 10 passed in 2.68s ==============================

============================= test session starts =============================
platform win32 -- Python 3.12.2, pytest-8.3.5, pluggy-1.6.0
rootdir: D:\智能投顾\量化相关\abu_modern\backend
configfile: pyproject.toml
plugins: anyio-4.9.0
collected 14 items

backend\tests\abupy_adapter\test_strategy_adapter.py ...........s..      [100%]

======================== 13 passed, 1 skipped in 2.65s ========================


---
*日志条目结束*
---
