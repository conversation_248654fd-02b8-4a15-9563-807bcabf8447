"""
Umpire Adapter (裁判系统适配器)

该模块负责将从前端接收到的风控规则（Umpire Rules）转换为abupy回测引擎可识别和使用的裁判（Umpire）对象。
"""

from typing import List, Dict, Any, Type
from abupy import AbuUmpMainDeg, AbuUmpEdgeDeg, AbuUmpMainJump, AbuUmpMainPrice, AbuUmpMainWave, AbuUmpEdgePrice, AbuUmpEdgeWave

# 映射前端传入的类名字符串到实际的abupy裁判类
# key: 前端传入的唯一标识符 (e.g., 'AbuUmpMainDeg')
# value: 对应的abupy裁判类
UMPIRE_CLASS_MAP: Dict[str, Type] = {
    'AbuUmpMainDeg': AbuUmpMainDeg,
    'AbuUmpEdgeDeg': AbuUmpEdgeDeg,
    'AbuUmpMainJump': AbuUmpMainJump,
    'AbuUmpMainPrice': AbuUmpMainPrice,
    'AbuUmpMainWave': AbuUmpMainWave,
    'AbuUmpEdgePrice': AbuUmpEdgePrice,
    'AbuUmpEdgeWave': AbuUmpEdgeWave,
    # ... 可根据需要扩展更多裁判
}

def create_umpire_managers(umpire_rules: List[Dict[str, Any]]) -> List[Any]:
    """
    根据前端传入的规则列表，创建并配置abupy裁判实例。

    Args:
        umpire_rules: 一个字典列表，每个字典代表一个裁判规则的配置。
                      例如: [{'class_name': 'AbuUmpMainDeg', 'market_name': 'my_main_ump'}, ...]

    Returns:
        一个包含实例化的abupy裁判对象的列表。

    Raises:
        ValueError: 如果传入的规则中包含未知的裁判类名。
    """
    if not umpire_rules:
        return []

    umpire_instances = []
    for rule in umpire_rules:
        class_name = rule.get('class_name')
        if not class_name or class_name not in UMPIRE_CLASS_MAP:
            raise ValueError(f"未知的裁判类型: {class_name}")

        UmpireClass = UMPIRE_CLASS_MAP[class_name]
        
        # 准备传递给裁判类构造函数的参数
        # 核心参数是 predict=True 和 market_name
        # 其他参数可以从rule中获取
        params = rule.get('parameters', {})
        params['predict'] = True # 必须设置为预测模式
        if 'market_name' not in params:
            raise ValueError(f"裁判 '{class_name}' 的配置缺少 'market_name' 参数")

        # 创建裁判实例
        try:
            umpire_instance = UmpireClass(**params)
            umpire_instances.append(umpire_instance)
        except Exception as e:
            raise ValueError(f"创建裁判 '{class_name}' 实例失败: {e}")

    return umpire_instances