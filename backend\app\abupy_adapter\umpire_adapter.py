"""
Umpire Adapter (裁判系统适配器)

该模块负责将从前端接收到的风控规则（Umpire Rules）转换为abupy回测引擎可识别和使用的裁判（Umpire）对象。
"""

from typing import List, Dict, Any, Type
from abupy import AbuUmpMainDeg, AbuUmpEdgeDeg, AbuUmpMainJump, AbuUmpMainPrice, AbuUmpMainWave, AbuUmpEdgePrice, AbuUmpEdgeWave

# 映射前端传入的类名字符串到实际的abupy裁判类
# key: 前端传入的唯一标识符 (e.g., 'AbuUmpMainDeg')
# value: 对应的abupy裁判类
UMPIRE_CLASS_MAP: Dict[str, Type] = {
    'AbuUmpMainDeg': AbuUmpMainDeg,
    'AbuUmpEdgeDeg': AbuUmpEdgeDeg,
    'AbuUmpMainJump': AbuUmpMainJump,
    'AbuUmpMainPrice': AbuUmpMainPrice,
    'AbuUmpMainWave': AbuUmpMainWave,
    'AbuUmpEdgePrice': AbuUmpEdgePrice,
    'AbuUmpEdgeWave': AbuUmpEdgeWave,
    # 容错处理，兼容数据库中可能存在的拼写错误
    'ABuUmpireEdgeDeg': AbuUmpEdgeDeg,
    # ... 可根据需要扩展更多裁判
}

def create_umpire_managers(umpire_rules: List[Dict[str, Any]], market_name: str) -> List[Any]:
    """
    根据前端传入的规则列表，创建并配置abupy裁判实例。

    Args:
        umpire_rules: 一个字典列表，每个字典代表一个裁判规则的配置。
                      例如: [{'class_name': 'AbuUmpMainDeg', 'params': {...}}]
        market_name: 市场名称 (例如 'cn', 'us')，由执行上下文提供。

    Returns:
        一个包含实例化的abupy裁判对象的列表。

    Raises:
        ValueError: 如果传入的规则中包含未知的裁判类名或创建实例失败。
    """
    if not umpire_rules:
        return []

    umpire_instances = []
    for rule in umpire_rules:
        class_name = rule.get('class_name')
        # 查找时忽略大小写，以提高容错性
        found_class_name = next((key for key in UMPIRE_CLASS_MAP if key.lower() == class_name.lower()), None)
        if not found_class_name:
            raise ValueError(f"未知的裁判类型: {class_name}")

        UmpireClass = UMPIRE_CLASS_MAP[found_class_name]
        
        # 准备传递给裁判类构造函数的参数
        # 从规则本身获取参数，而不是从嵌套的 'parameters' 字段
        params = rule.copy()
        params.pop('class_name', None)  # 移除class_name，因为它不是构造函数参数

        # 核心参数是 predict=True 和 market_name
        params['predict'] = True  # 必须设置为预测模式
        params['market_name'] = market_name # 从执行上下文中注入 market_name

        # 创建裁判实例
        try:
            umpire_instance = UmpireClass(**params)
            umpire_instances.append(umpire_instance)
        except Exception as e:
            raise ValueError(f"创建裁判 '{class_name}' 实例失败: {e}")

    return umpire_instances