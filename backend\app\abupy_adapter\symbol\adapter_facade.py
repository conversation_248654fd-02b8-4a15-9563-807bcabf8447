# -*- coding: utf-8 -*-
"""
新的、轻量化的SymbolAdapter门面
"""
import pandas as pd
from typing import List, Optional, Any

# 导入abu原有模块
try:
    from abupy import ABuSymbolPd
    from abupy.CoreBu.ABuEnv import EMarketTargetType
    abu_import_success = True
except ImportError:
    abu_import_success = False

# 导入自定义异常和新模块
from backend.app.core.exceptions import SymbolError, DataNotFoundError
from .symbol_validator import SymbolValidator
from .symbol_converter import SymbolConverter
from .symbol_name_resolver import SymbolNameResolver

class SymbolAdapterFacade:
    """新的Symbol适配器门面"""

    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """验证股票代码格式"""
        return SymbolValidator.validate_symbol(symbol)

    @staticmethod
    def normalize_symbol(symbol: str) -> tuple[str, str]:
        """标准化股票代码"""
        return SymbolConverter.normalize_symbol(symbol)

    @staticmethod
    def get_symbol_name(symbol: str) -> str:
        """获取股票名称"""
        return SymbolNameResolver.get_symbol_name(symbol)

    @staticmethod
    def get_kline_data(symbol_str: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """
        获取K线数据，包装abu原有接口
        """
        if not abu_import_success:
            raise SymbolError("Abu模块导入失败")
        try:
            SymbolValidator.validate_symbol(symbol_str)
            df = ABuSymbolPd.make_kl_df(symbol_str, start=start, end=end)
            if df is None or df.empty:
                raise DataNotFoundError(f"未找到{symbol_str}的K线数据")
            return df
        except (SymbolError, DataNotFoundError) as e:
            raise e
        except Exception as e:
            raise DataNotFoundError(f"获取K线数据失败: {e}")

    @staticmethod
    def is_index(symbol: str) -> bool:
        """
        判断一个符号是否为指数
        """
        symbol_lower = symbol.lower()
        if symbol_lower in SymbolNameResolver.CN_INDEX_CODES or \
           symbol_lower in SymbolNameResolver.US_INDEX_CODES or \
           symbol_lower in SymbolNameResolver.HK_INDEX_CODES:
            return True
        if symbol_lower.startswith(('sh000', 'sz399')):
            return True
        if '.' in symbol:
            code, market_suffix = symbol.split('.')
            if market_suffix.upper() in ['SH', 'SZ'] and (code.startswith('000') or code.startswith('399')):
                return True
        if symbol.isdigit() and (symbol.startswith('000') or symbol.startswith('399')):
            return True
        return False