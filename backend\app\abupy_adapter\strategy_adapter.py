"""
策略适配器模块

该模块负责连接原abu框架和新的API系统，封装策略创建、因子配置、参数设置等相关逻辑，
为上层的StrategyService提供一个现代化的、解耦的接口。
"""

print("STRATEGY_ADAPTER_DIAG: Loading strategy_adapter.py")
import logging
import inspect
import importlib
import functools
import time
import threading
from typing import Any, Dict, List, Optional, Union, Type

# 添加全局标记用于测试模拟
abu_import_success = True

# 导入重构后的模块
print("STRATEGY_ADAPTER_DIAG: About to import backend.app.schemas.strategy")
from backend.app.schemas.strategy import BuyFactor, SellFactor, Strategy, StrategyCreate
print("STRATEGY_ADAPTER_DIAG: Imported backend.app.schemas.strategy")
print("STRATEGY_ADAPTER_DIAG: About to import factors_converter")
from backend.app.abupy_adapter.factors_converter import FactorsConverter
print("STRATEGY_ADAPTER_DIAG: Imported factors_converter")
print("STRATEGY_ADAPTER_DIAG: About to import strategy_executor")

print("STRATEGY_ADAPTER_DIAG: Imported strategy_executor")
print("STRATEGY_ADAPTER_DIAG: About to import exceptions")
from backend.app.abupy_adapter.exceptions import AdapterError, FactorError, ParameterError
print("STRATEGY_ADAPTER_DIAG: Imported exceptions")
print("STRATEGY_ADAPTER_DIAG: About to import benchmark")
from backend.app.abupy_adapter.benchmark import SimpleBenchmark
print("STRATEGY_ADAPTER_DIAG: Imported benchmark")


class StrategyAdapter:
    """策略适配器类，封装与原abu框架的交互"""

    # 买入因子模块路径
    BUY_FACTOR_MODULE = "abupy.FactorBuyBu"
    # 卖出因子模块路径
    SELL_FACTOR_MODULE = "abupy.FactorSellBu"
    
    # 因子缓存
    _factors_cache = {
        "buy": None,
        "sell": None,
        "all": None
    }
    # 缓存过期时间（秒）
    _cache_expiry = 3600  # 默认1小时
    # 最后更新时间
    _last_cache_update = {
        "buy": 0,
        "sell": 0,
        "all": 0
    }
    _cache_lock = threading.Lock()  # 用于保护因子缓存访问

    @classmethod
    def convert_to_abu_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]]) -> List[Dict[str, Any]]:
        """
        将Pydantic的因子模型列表转换为abu内部可识别的因子对象列表
        
        Args:
            factors_data: Pydantic的因子模型列表
            
        Returns:
            abu内部可识别的因子对象列表
            
        Raises:
            AdapterError: 当转换过程中出现错误时
        """
        return FactorsConverter.convert_to_abu_factors(factors_data)

    @classmethod
    def create_abu_strategy_kwargs(cls, strategy_schema: StrategyCreate) -> Dict[str, Any]:
        """
        根据StrategyCreate模型，构建创建abu策略时所需的参数字典
        
        Args:
            strategy_schema: 策略创建数据模型
            
        Returns:
            创建abu策略所需的参数字典
            
        Raises:
            AdapterError: 当构建参数时出现错误
        """
        try:
            # 转换买入因子和卖出因子
            buy_factors = cls.convert_to_abu_factors(strategy_schema.buy_factors)
            sell_factors = cls.convert_to_abu_factors(strategy_schema.sell_factors)
            
            # 提取策略参数
            strategy_params = strategy_schema.parameters or {}
            
            # 构建完整的策略参数字典
            kwargs = {
                'buy_factors': buy_factors,
                'sell_factors': sell_factors,
                **strategy_params
            }
            
            return kwargs
        except Exception as e:
            raise AdapterError(f"创建abu策略参数时出错: {str(e)}")

    @classmethod
    def get_available_abu_factors(cls, factor_type: Optional[str] = None, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        从abu框架中动态获取可用的买入和卖出因子信息
        
        Args:
            factor_type: 因子类型，可选值为"buy"或"sell"，如果为None则返回所有类型
            use_cache: 是否使用缓存，默认为True
            
        Returns:
            因子信息字典列表，每个字典包含因子的名称、描述、参数等信息
            
        Raises:
            AdapterError: 当获取因子信息时出现错误
            
        Note on Thread Safety:
            当前的类级别缓存 (`_factors_cache`, `_last_cache_update`) 已通过 `threading.Lock` 
            进行了线程安全保护，以防止在多线程或多进程环境 (例如，多个 FastAPI worker) 
            中发生并发读写操作可能导致的数据不一致或竞争条件。
            未来可以考虑通过配置文件或环境变量配置 `_cache_expiry`。
        """
        import importlib
        
        # 确定缓存键
        cache_key = factor_type if factor_type else "all"
        
        # 检查缓存是否有效
        current_time = time.time()
        with cls._cache_lock:
            if use_cache and cls._factors_cache[cache_key] is not None:
                if current_time - cls._last_cache_update[cache_key] < cls._cache_expiry:
                    logging.info(f"使用缓存获取 {cache_key} 因子信息")
                    return cls._factors_cache[cache_key]
        
        factors_info = []
        
        try:
            # 检查abu是否成功导入
            try:
                import abupy
                abu_import_success = True
            except ImportError:
                abu_import_success = False
                
            if not abu_import_success:
                raise AdapterError("无法导入abu模块")
            
            # 获取买入因子
            if factor_type is None or factor_type == "buy":
                try:
                    buy_module = importlib.import_module(cls.BUY_FACTOR_MODULE)
                    from abupy.FactorBuyBu.ABuFactorBuyBase import AbuFactorBuyBase, BuyCallMixin, BuyPutMixin
                    
                    for name, obj in inspect.getmembers(buy_module):
                        try:
                            if (inspect.isclass(obj) and issubclass(obj, AbuFactorBuyBase) and 
                                obj is not AbuFactorBuyBase and 
                                'Base' not in name):
                                
                                is_call = issubclass(obj, BuyCallMixin)
                                is_put = issubclass(obj, BuyPutMixin)
                                
                                if is_call or is_put:
                                    description = obj.__doc__.strip() if obj.__doc__ else f"{name} 买入因子"
                                    params = cls._get_factor_params(obj)
                                    
                                    factors_info.append({
                                        'id': name,
                                        'name': name,
                                        'description': description,
                                        'factor_type': 'buy',
                                        'factor_class': name,
                                        'parameters': params,
                                        'direction': 'call' if is_call else 'put'
                                    })
                        except Exception as factor_err:
                            logging.warning(f"处理买入因子 {name} 时出错: {factor_err}")
                            continue
                except ImportError as ie:
                    logging.error(f"导入买入因子模块失败: {ie}")
                    if factor_type == "buy":
                        raise AdapterError(f"TestStrategyAdapterGetAvailableAbuFactors Error: {str(ie)}")
                except Exception as e:
                    logging.error(f"处理买入因子时发生异常: {e}", exc_info=True)
                    if factor_type == "buy":
                        raise AdapterError(f"TestStrategyAdapterGetAvailableAbuFactors Error: {str(e)}")
            
            # 获取卖出因子
            if factor_type is None or factor_type == "sell":
                try:
                    sell_module = importlib.import_module(cls.SELL_FACTOR_MODULE)
                    from abupy.FactorSellBu.ABuFactorSellBase import AbuFactorSellBase
                    
                    for name, obj in inspect.getmembers(sell_module):
                        try:
                            if (inspect.isclass(obj) and issubclass(obj, AbuFactorSellBase) and 
                                obj is not AbuFactorSellBase and 
                                'Base' not in name):
                                
                                description = obj.__doc__.strip() if obj.__doc__ else f"{name} 卖出因子"
                                params = cls._get_factor_params(obj)
                                
                                factors_info.append({
                                    'id': name,
                                    'name': name,
                                    'description': description,
                                    'factor_type': 'sell',
                                    'factor_class': name,
                                    'parameters': params
                                })
                        except Exception as factor_err:
                            logging.warning(f"处理卖出因子 {name} 时出错: {factor_err}")
                            continue
                except ImportError as ie:
                    logging.error(f"导入卖出因子模块失败: {ie}")
                    if factor_type == "sell":
                        raise AdapterError(f"TestStrategyAdapterGetAvailableAbuFactors Error: {str(ie)}")
                except Exception as e:
                    logging.error(f"处理卖出因子时发生异常: {e}", exc_info=True)
                    if factor_type == "sell":
                        raise AdapterError(f"TestStrategyAdapterGetAvailableAbuFactors Error: {str(e)}")
            
            if not factors_info and factor_type is not None:
                logging.warning(f"未找到任何 {factor_type} 类型的因子")
            
            # 更新缓存
            with cls._cache_lock:
                cls._factors_cache[cache_key] = factors_info
                cls._last_cache_update[cache_key] = current_time
            logging.info(f"已更新 {cache_key} 因子信息缓存")
                
            return factors_info
            
        except Exception as e:
            logging.error(f"获取可用因子信息时出错: {e}", exc_info=True)
            raise AdapterError(f"TestStrategyAdapterGetAvailableAbuFactors Error: {str(e)}")
    
    @classmethod
    def clear_factors_cache(cls) -> None:
        """
        清除因子信息缓存
        """
        cls._factors_cache = {
            "buy": None,
            "sell": None,
            "all": None
        }
        cls._last_cache_update = {
            "buy": 0,
            "sell": 0,
            "all": 0
        }
        logging.info("已清除因子信息缓存")
    
    @classmethod
    def set_cache_expiry(cls, seconds: int) -> None:
        """
        设置缓存过期时间
        
        Args:
            seconds: 过期时间（秒）
        """
        if seconds < 0:
            raise ValueError("缓存过期时间不能为负数")
        cls._cache_expiry = seconds
        logging.info(f"已设置因子信息缓存过期时间为 {seconds} 秒")

    @classmethod
    def _get_factor_params(cls, factor_class: Type) -> Dict[str, Any]:
        """
        获取因子类的参数信息
        
        Args:
            factor_class: 因子类
            
        Returns:
            参数字典，键为参数名，值为默认值
        """
        params = {}
        
        try:
            if hasattr(factor_class, '_init_self'):
                sig = inspect.signature(factor_class._init_self)
                
                for param_name, param in sig.parameters.items():
                    if param_name != 'self' and param_name != 'kwargs':
                        if param.default is not param.empty:
                            params[param_name] = param.default
            
            if not params and hasattr(factor_class, '__init__'):
                sig = inspect.signature(factor_class.__init__)
                
                for param_name, param in sig.parameters.items():
                    if param_name != 'self' and param_name != 'kwargs':
                        if param.default is not param.empty:
                            params[param_name] = param.default
        except Exception as e:
            logging.warning(f"获取因子参数信息时出错: {e}")
            
        return params

    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行策略，返回执行结果
        
        Args:
            strategy: 策略对象
            market_data: 市场数据
            
        Returns:
            执行结果字典
            
        Raises:
            AdapterError: 当执行策略时出错
            FactorError: 当处理因子时出错
            ParameterError: 当参数无效时出错
        """
        # 委托给StrategyExecutor处理
        return StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)

    @classmethod
    def save_strategy(cls, strategy: Strategy, file_path: Optional[str] = None) -> str:
        """
        保存策略到文件系统
        
        Args:
            strategy: 策略对象
            file_path: 保存路径，如果为None则使用默认路径
            
        Returns:
            保存的文件路径
            
        Raises:
        AdapterError: 当保存策略时出错

    Note on Configurability:
        默认保存路径 `~/.abu_modern/strategies/` 未来可以考虑通过配置文件或环境变量进行配置。
    """
        import os
        import json
        import datetime
        
        try:
            # 如果未指定路径，则使用默认路径
            if file_path is None:
                # 确保目录存在
                base_dir = os.path.join(os.path.expanduser("~"), ".abu_modern", "strategies")
                os.makedirs(base_dir, exist_ok=True)
                
                # 使用策略ID和时间戳创建文件名
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{strategy.id}_{timestamp}.json"
                file_path = os.path.join(base_dir, file_name)
            
            # 将策略对象转换为字典 (使用 model_dump 替代已弃用的 dict 方法)
            strategy_dict = strategy.model_dump()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存到文件
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(strategy_dict, f, ensure_ascii=False, indent=2)
            
            logging.info(f"策略 '{strategy.name}' (ID: {strategy.id}) 已保存到 {file_path}")
            return file_path
        except Exception as e:
            error_msg = f"保存策略时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg)
            
    @classmethod
    def load_strategy(cls, file_path: str) -> Strategy:
        """
        从文件系统加载策略
        
        Args:
            file_path: 策略文件路径
            
        Returns:
            加载的策略对象
            
        Raises:
            AdapterError: 当加载策略时出错
        """
        import json
        import os
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise AdapterError(f"策略文件不存在: {file_path}")
            
            # 从文件加载策略字典
            with open(file_path, "r", encoding="utf-8") as f:
                strategy_dict = json.load(f)
            
            # 转换为策略对象
            strategy = Strategy(**strategy_dict)
            logging.info(f"已从 {file_path} 加载策略 '{strategy.name}' (ID: {strategy.id})")
            return strategy
        except json.JSONDecodeError as e:
            error_msg = f"解析策略文件时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg)
        except Exception as e:
            error_msg = f"加载策略时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg)
            
    # 注意：新版本的 get_available_abu_factors 方法已在文件前面实现，支持缓存机制
            
    @classmethod
    def _get_factor_params(cls, factor_class: Type) -> Dict[str, Any]:
        """
        获取因子类的参数信息
        
        Args:
            factor_class: 因子类
            
        Returns:
            参数字典，键为参数名，值为默认值
        """
        params = {}
        
        # 获取__init__方法的参数
        try:
            signature = inspect.signature(factor_class.__init__)
            for param_name, param in signature.parameters.items():
                # 排除self参数
                if param_name == "self":
                    continue
                    
                # 获取参数默认值（如果有）
                if param.default is not inspect.Parameter.empty:
                    params[param_name] = param.default
                else:
                    params[param_name] = None
        except (ValueError, TypeError):
            # 如果无法获取签名，返回空字典
            pass
            
        return params
