"""策略适配器模块

该模块负责连接原abu框架和新的API系统，封装策略创建、因子配置、参数设置等相关逻辑，
为上层的StrategyService提供一个现代化的、解耦的接口。
"""

print("STRATEGY_ADAPTER_DIAG: Loading strategy_adapter.py")
import logging
import inspect
import importlib
import functools
import time
import threading
import os
import json
from typing import Any, Dict, List, Optional, Union, Type
from backend.app.abupy_adapter.benchmark import SimpleBenchmark

def _factor_cache_decorator(func):
    """一个用于处理因子发现的缓存和线程安全的装饰器。"""
    @functools.wraps(func)
    def wrapper(cls, factor_type: Optional[str] = None, use_cache: bool = True):
        cache_key = factor_type if factor_type else "all"

        # 第一重检查：在无锁状态下检查缓存
        if use_cache:
            cached_result = cls._factors_cache.get(cache_key)
            if cached_result is not None and time.time() - cls._last_cache_update.get(cache_key, 0) < cls._cache_expiry:
                logging.info(f"使用缓存获取 {cache_key} 因子信息")
                return cached_result

        # 如果缓存不存在或已过期，则进入加锁的关键区域
        with cls._cache_lock:
            # 第二重检查：在获取锁之后，再次检查缓存
            if use_cache:
                cached_result = cls._factors_cache.get(cache_key)
                if cached_result is not None and time.time() - cls._last_cache_update.get(cache_key, 0) < cls._cache_expiry:
                    logging.info(f"在锁内发现缓存，直接返回 {cache_key} 因子信息")
                    return cached_result
            
            logging.info(f"缓存未命中或已过期，开始发现 {cache_key} 因子...")
            result = func(cls, factor_type)
            
            # 更新缓存和时间戳
            cls._factors_cache[cache_key] = result
            cls._last_cache_update[cache_key] = time.time()
            logging.info(f"已更新 {cache_key} 因子信息缓存")
            
            return result
    return wrapper

# 添加全局标记用于测试模拟
abu_import_success = True

# 导入重构后的模块
print("STRATEGY_ADAPTER_DIAG: About to import backend.app.schemas.strategy")
from backend.app.schemas.strategy import BuyFactor, SellFactor, Strategy, StrategyCreate
print("STRATEGY_ADAPTER_DIAG: Imported backend.app.schemas.strategy")
print("STRATEGY_ADAPTER_DIAG: About to import factors_converter")
from backend.app.abupy_adapter.factors_converter import FactorsConverter
print("STRATEGY_ADAPTER_DIAG: Imported factors_converter")
print("STRATEGY_ADAPTER_DIAG: About to import strategy_executor")
from backend.app.abupy_adapter.execution.executor_facade import StrategyExecutor
print("STRATEGY_ADAPTER_DIAG: Imported strategy_executor")
print("STRATEGY_ADAPTER_DIAG: About to import exceptions")
from pydantic import ValidationError
from backend.app.abupy_adapter.exceptions import AdapterError, FactorError, ParameterError
print("STRATEGY_ADAPTER_DIAG: Imported exceptions")
print("STRATEGY_ADAPTER_DIAG: About to import benchmark")
from backend.app.abupy_adapter.benchmark import SimpleBenchmark
print("STRATEGY_ADAPTER_DIAG: Imported benchmark")


class StrategyAdapter:
    """策略适配器类，封装与原abu框架的交互"""
    """策略适配器类，封装与原abu框架的交互"""

    # 买入因子模块路径
    BUY_FACTOR_MODULE = "abupy.FactorBuyBu"
    # 卖出因子模块路径
    SELL_FACTOR_MODULE = "abupy.FactorSellBu"
    
    # 因子缓存
    _factors_cache = {
        "buy": None,
        "sell": None,
        "all": None
    }
    # 缓存过期时间（秒）
    _cache_expiry = 3600  # 默认1小时
    # 最后更新时间
    _last_cache_update = {
        "buy": 0,
        "sell": 0,
        "all": 0
    }
    _cache_lock = threading.Lock()  # 用于保护因子缓存访问

    @classmethod
    def convert_to_abu_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]]) -> List[Dict[str, Any]]:
        """
        将Pydantic的因子模型列表转换为abu内部可识别的因子对象列表
        
        Args:
            factors_data: Pydantic的因子模型列表
            
        Returns:
            abu内部可识别的因子对象列表
            
        Raises:
            AdapterError: 当转换过程中出现错误时
        """
        return FactorsConverter.convert_to_abu_factors(factors_data)

    @classmethod
    def create_abu_strategy_kwargs(cls, strategy_schema: StrategyCreate) -> Dict[str, Any]:
        """
        根据StrategyCreate模型，构建创建abu策略时所需的参数字典
        
        Args:
            strategy_schema: 策略创建数据模型
            
        Returns:
            创建abu策略所需的参数字典
            
        Raises:
            AdapterError: 当构建参数时出现错误
        """
        try:
            # 转换买入因子和卖出因子
            buy_factors = cls.convert_to_abu_factors(strategy_schema.buy_factors)
            sell_factors = cls.convert_to_abu_factors(strategy_schema.sell_factors)
            
            # 提取策略参数
            strategy_params = strategy_schema.parameters or {}
            
            # 构建完整的策略参数字典
            kwargs = {
                'buy_factors': buy_factors,
                'sell_factors': sell_factors,
                **strategy_params
            }
            
            return kwargs
        except Exception as e:
            raise AdapterError(f"创建abu策略参数时出错: {str(e)}")

    @classmethod
    def _fetch_and_discover_factors(cls, factor_type: Optional[str]) -> Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
        """
        从abupy库中发现因子的核心逻辑。
        此方法旨在由缓存装饰器包装。
        """
        try:
            import abupy
        except ImportError:
            raise AdapterError("无法导入abupy模块，请检查安装和环境配置")

        factors_by_type = {"buy": [], "sell": []}
        types_to_process = []
        if factor_type is None:
            types_to_process = [('buy', cls.BUY_FACTOR_MODULE), ('sell', cls.SELL_FACTOR_MODULE)]
        elif factor_type == 'buy':
            types_to_process = [('buy', cls.BUY_FACTOR_MODULE)]
        elif factor_type == 'sell':
            types_to_process = [('sell', cls.SELL_FACTOR_MODULE)]

        for f_type, module_path in types_to_process:
            try:
                if f_type == 'buy':
                    from abupy.FactorBuyBu.ABuFactorBuyBase import AbuFactorBuyBase, BuyCallMixin, BuyPutMixin
                    base_cls = AbuFactorBuyBase
                    sub_classes = (BuyCallMixin, BuyPutMixin)
                else:  # f_type == 'sell'
                    from abupy.FactorSellBu.ABuFactorSellBase import AbuFactorSellBase
                    base_cls = AbuFactorSellBase
                    sub_classes = None
                
                factors_by_type[f_type].extend(cls._discover_factors(
                    f_type, module_path, base_cls, sub_classes
                ))
            except (ImportError, AdapterError) as e:
                logging.warning(f"处理 {f_type} 因子时出错: {e}，已跳过。")
                if factor_type == f_type:
                    raise AdapterError(f"无法导入abupy {f_type} 因子基础模块") from e

        if factor_type == 'buy':
            return factors_by_type.get('buy', [])
        elif factor_type == 'sell':
            return factors_by_type.get('sell', [])
        else:  # factor_type is None
            return factors_by_type
    
    @classmethod
    @_factor_cache_decorator
    def get_available_abu_factors(cls, factor_type: Optional[str] = None, use_cache: bool = True) -> Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
        """
        从abu框架中动态获取可用的买入和卖出因子信息。
        此方法是线程安全的并使用缓存。

        Args:
            factor_type: 因子类型，可选值为"buy"或"sell"，如果为None则返回所有类型
            use_cache: 是否使用缓存，默认为True。此参数由装饰器处理。

        Returns:
            因子信息字典列表或按类型划分的字典

        Raises:
            AdapterError: 当获取因子信息时出现错误
        """
        # `use_cache`参数由装饰器处理。
        # 核心逻辑现在位于`_fetch_and_discover_factors`中。
        return cls._fetch_and_discover_factors(factor_type)
    
    @classmethod
    def clear_factors_cache(cls) -> None:
        """
        清除因子信息缓存
        """
        cls._factors_cache = {
            "buy": None,
            "sell": None,
            "all": None
        }
        cls._last_cache_update = {
            "buy": 0,
            "sell": 0,
            "all": 0
        }
        logging.info("已清除因子信息缓存")
    
    @classmethod
    def set_cache_expiry(cls, seconds: int) -> None:
        """
        设置缓存过期时间
        
        Args:
            seconds: 过期时间（秒）
        """
        if seconds < 0:
            raise ValueError("缓存过期时间不能为负数")
        cls._cache_expiry = seconds
        logging.info(f"已设置因子信息缓存过期时间为 {seconds} 秒")

    @classmethod
    def _discover_factors(cls, factor_type: str, module_path: str, base_class: Type, sub_classes: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        通用的因子发现辅助函数 (重构版)
        通过遍历文件系统并动态导入来发现因子，更加健壮。
        """
        factors_list = []
        try:
            package = importlib.import_module(module_path)
            package_path = os.path.dirname(package.__file__)
        except (ImportError, AttributeError):
            logging.error(f"无法找到或加载因子基础模块: {module_path}")
            return []

        for root, _, files in os.walk(package_path):
            for filename in files:
                if not filename.endswith('.py') or filename.startswith('__'):
                    continue

                module_name = filename[:-3]
                full_module_path = f"{module_path}.{module_name}"
                factors_list.extend(cls._process_factor_module(
                    full_module_path, factor_type, base_class, sub_classes
                ))

        return factors_list

    @classmethod
    def _process_factor_module(cls, full_module_path: str, factor_type: str, base_class: Type, sub_classes: Optional[tuple]) -> List[Dict[str, Any]]:
        """处理单个因子模块文件，提取因子信息。"""
        factors_in_module = []
        try:
            module = importlib.import_module(full_module_path)
            for name, obj in inspect.getmembers(module):
                if not (inspect.isclass(obj) and issubclass(obj, base_class) and obj is not base_class and 'Base' not in name):
                    continue

                if sub_classes and not any(issubclass(obj, sc) for sc in sub_classes):
                    continue

                try:
                    params = cls._get_factor_params(obj)
                    description = inspect.getdoc(obj) or f"{name} {factor_type} factor"
                    
                    # 生成唯一的因子ID，包含模块名和因子类型
                    module_name = full_module_path.split('.')[-1]
                    unique_id = f"{factor_type}_{module_name}_{name}"

                    factor_info = {
                        'id': unique_id,
                        'name': name,
                        'description': description.strip(),
                        'factor_type': factor_type,
                        'class_name': name,
                        'parameters': params
                    }

                    if factor_type == 'buy' and sub_classes:
                        from abupy.FactorBuyBu.ABuFactorBuyBase import BuyCallMixin
                        factor_info['direction'] = 'call' if issubclass(obj, BuyCallMixin) else 'put'

                    factors_in_module.append(factor_info)
                except Exception as e:
                    logging.warning(f"处理因子 {name} (来自 {full_module_path}) 时出错: {e}", exc_info=True)

        except (ImportError, SyntaxError) as e:
            logging.warning(f"跳过无法导入的因子文件 {full_module_path}: {e}")
        except Exception as e:
            logging.error(f"在 {full_module_path} 中发现 {factor_type} 因子时发生未知异常: {e}", exc_info=True)
        
        return factors_in_module

    @classmethod
    def execute_strategy(cls, strategy: Strategy, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行策略，返回执行结果
        
        Args:
            strategy: 策略对象
            market_data: 市场数据
            
        Returns:
            执行结果字典
            
        Raises:
            AdapterError: 当执行策略时出错
            FactorError: 当处理因子时出错
            ParameterError: 当参数无效时出错
        """
        # 委托给StrategyExecutor处理
        return StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)

    @classmethod
    def save_strategy(cls, strategy: Strategy, file_path: Optional[str] = None) -> str:
        """
        保存策略到文件系统
        
        Args:
            strategy: 策略对象
            file_path: 保存路径，如果为None则使用默认路径
            
        Returns:
            保存的文件路径
            
        Raises:
        AdapterError: 当保存策略时出错

    Note on Configurability:
        默认保存路径 `~/.abu_modern/strategies/` 未来可以考虑通过配置文件或环境变量进行配置。
        """
        import os
        import json
        import datetime
        
        try:
            # 如果未指定路径，则使用默认路径
            if file_path is None:
                # 确保目录存在
                base_dir = os.path.join(os.path.expanduser("~"), ".abu_modern", "strategies")
                os.makedirs(base_dir, exist_ok=True)
                
                # 使用策略ID和时间戳创建文件名
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{strategy.id}_{timestamp}.json"
                file_path = os.path.join(base_dir, file_name)
            
            # 将策略对象转换为字典 (使用 model_dump 替代已弃用的 dict 方法)
            strategy_dict = strategy.model_dump(by_alias=True)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存到文件
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(strategy_dict, f, ensure_ascii=False, indent=2)
            
            logging.info(f"策略 '{strategy.name}' (ID: {strategy.id}) 已保存到 {file_path}")
            return file_path
        except Exception as e:
            error_msg = f"保存策略时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg)
            

    @classmethod
    def load_strategy(cls, file_path: str) -> Strategy:
        """
        从文件系统加载策略
        
        Args:
            file_path: 策略文件路径
            
        Returns:
            加载的策略对象
            
        Raises:
            AdapterError: 当加载策略时出错
        """
        import json
        import os
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise AdapterError(f"策略文件不存在: {file_path}")
            
            # 从文件加载策略字典
            with open(file_path, "r", encoding="utf-8") as f:
                strategy_dict = json.load(f)
            
            # 转换为策略对象
            strategy = Strategy(**strategy_dict)
            logging.info(f"已从 {file_path} 加载策略 '{strategy.name}' (ID: {strategy.id})")
            return strategy
        except ValidationError as e:
            error_msg = f"策略数据格式不正确: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg) from e
        except json.JSONDecodeError as e:
            error_msg = f"解析策略文件时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg)
        except Exception as e:
            error_msg = f"加载策略时出错: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise AdapterError(error_msg)
            
    @classmethod
    def _get_factor_params(cls, factor_cls: Type) -> Dict[str, Any]:
        """
        智能地从因子类中提取用户可配置参数信息。
        优先使用原有的参数提取逻辑（适用于测试用例），
        对于真实的abupy因子类，使用增强版参数提取器。
        """
        # 首先尝试使用原有的回退方法（适用于测试用例和简单情况）
        try:
            params = cls._get_factor_params_fallback(factor_cls)
            if params:
                logging.debug(f"使用回退方法从 {factor_cls.__name__} 提取到 {len(params)} 个用户可配置参数: {list(params.keys())}")
                return params
        except Exception as e:
            logging.debug(f"回退方法从 {factor_cls.__name__} 提取参数时出错: {e}")
        
        # 如果回退方法没有提取到参数，且是真实的abupy因子类，尝试使用增强版提取器
        try:
            # 检查是否是真实的abupy因子类（通过模块名判断）
            module_name = getattr(factor_cls, '__module__', '')
            if 'abupy' in module_name and hasattr(factor_cls, '_init_self'):
                from backend.app.utils.enhanced_factor_param_extractor import EnhancedFactorParamExtractor
                
                extractor = EnhancedFactorParamExtractor()
                params = extractor.extract_factor_params(factor_cls)
                
                if params:
                    logging.debug(f"使用增强版提取器从 {factor_cls.__name__} 提取到 {len(params)} 个用户可配置参数: {list(params.keys())}")
                    return params
        except Exception as e:
            logging.debug(f"使用增强版参数提取器从 {factor_cls.__name__} 提取参数时出错: {e}")
        
        logging.debug(f"未从 {factor_cls.__name__} 提取到用户可配置参数")
        return {}
    
    @classmethod
    def _get_factor_params_fallback(cls, factor_cls: Type) -> Dict[str, Any]:
        """回退的参数提取方法，使用原有的提取逻辑"""
        # 系统注入的参数，需要过滤掉
        SYSTEM_PARAMS = {
            'capital', 'kl_pd', 'combine_kl_pd', 'benchmark', 
            'self', 'kwargs', 'args'
        }
        
        params_extractors = [
            cls._extract_from_params_info,
            cls._extract_from_init_self,
            cls._extract_from_init_signature
        ]

        for extractor in params_extractors:
            try:
                params = extractor(factor_cls)
                if params:
                    # 过滤掉系统参数，只保留用户可配置参数
                    user_params = {
                        name: info for name, info in params.items() 
                        if name not in SYSTEM_PARAMS and not name.startswith('_')
                    }
                    if user_params:
                        logging.debug(f"回退方法：从 {factor_cls.__name__} 提取到 {len(user_params)} 个用户可配置参数: {list(user_params.keys())}")
                        return user_params
            except Exception as e:
                logging.warning(f"使用回退方法 {extractor.__name__} 从 {factor_cls.__name__} 提取参数时出错: {e}")
        
        logging.debug(f"回退方法：未从 {factor_cls.__name__} 提取到用户可配置参数")
        return {}

    @classmethod
    def _extract_from_params_info(cls, factor_cls: Type) -> Optional[Dict[str, Any]]:
        """从 _params_info 属性提取参数。"""
        if not (hasattr(factor_cls, '_params_info') and isinstance(getattr(factor_cls, '_params_info'), list)):
            return None
        
        params_info = getattr(factor_cls, '_params_info')
        if not all(isinstance(p, dict) and 'name' in p for p in params_info):
            return None

        return {
            p['name']: {
                'default': p.get('default'),
                'type': p.get('type', type(p.get('default')).__name__ if p.get('default') is not None else 'any'),
                'comment': p.get('comment', p.get('name', ''))
            } for p in params_info
        }

    @classmethod
    def _extract_from_init_self(cls, factor_cls: Type) -> Optional[Dict[str, Any]]:
        """从 _init_self 和 __init__ 签名结合提取参数。"""
        if not (hasattr(factor_cls, '_init_self') and hasattr(factor_cls, '__init__')):
            return None

        param_names_to_find = factor_cls._init_self
        
        # 检查 _init_self 的格式，如果不是期望的格式则跳过
        if not isinstance(param_names_to_find, (list, tuple)):
            logging.debug(f"跳过 {factor_cls.__name__}._init_self: 不是列表或元组格式 (类型: {type(param_names_to_find)})")
            return None
            
        if not all(isinstance(p, str) for p in param_names_to_find):
            logging.debug(f"跳过 {factor_cls.__name__}._init_self: 包含非字符串元素")
            return None

        all_params_dict = cls._extract_params_from_signature(factor_cls.__init__)

        found_params = {}
        for name in param_names_to_find:
            if name in all_params_dict:
                found_params[name] = all_params_dict[name]
        
        return found_params if found_params else None

    @classmethod
    def _extract_from_init_signature(cls, factor_cls: Type) -> Optional[Dict[str, Any]]:
        """直接从 __init__ 方法签名提取参数。"""
        if hasattr(factor_cls, '__init__'):
            return cls._extract_params_from_signature(factor_cls.__init__)
        return None

    @classmethod
    def _extract_params_from_signature(cls, method: callable) -> Dict[str, Any]:
        """从方法签名中提取参数和默认值，并转换为标准格式"""
        params_dict = {}
        try:
            signature = inspect.signature(method)
            for param_name, param in signature.parameters.items():
                if param_name in ('self', 'kwargs', 'args') or param_name.startswith('_'):
                    continue
                
                param_type = 'any'
                if param.annotation is not inspect.Parameter.empty:
                    if hasattr(param.annotation, '__name__'):
                        param_type = param.annotation.__name__
                    else:
                        param_type = str(param.annotation) # 支持 typing.Any
                elif param.default is not inspect.Parameter.empty and param.default is not None:
                    # 如果没有注解，但有默认值，则推断类型
                    param_type = type(param.default).__name__

                params_dict[param_name] = {
                    'default': param.default if param.default is not inspect.Parameter.empty else None,
                    'type': param_type,
                    'comment': param_name
                }
        except (ValueError, TypeError):
            logging.debug(f"无法获取 {method.__qualname__} 的签名")
        return params_dict