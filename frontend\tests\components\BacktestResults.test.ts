import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import BacktestResults from '../../src/components/backtest/BacktestResults.vue'
import type { BacktestResult } from '../../src/api/types/backtest'

// 不需要mock formatters，因为它们是组件内部定义的

describe('BacktestResults', () => {
  const mockResult: BacktestResult = {
    task_id: 'test-task-123',
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 120000,
    total_return: 0.2,
    sharpe_ratio: 1.5,
    max_drawdown: -0.1,
    trades: [
      {
        date: '2023-01-15',
        action: 'buy',
        price: 10.5,
        quantity: 1000,
        amount: 10500
      },
      {
        date: '2023-02-15',
        action: 'sell',
        price: 12.0,
        quantity: 1000,
        amount: 12000
      }
    ],
    created_at: '2023-01-01T00:00:00Z'
  }

  const createWrapper = (props = {}) => {
    return mount(BacktestResults, {
      props: {
        result: null,
        loading: false,
        error: null,
        ...props
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('渲染', () => {
    it('应该正确渲染组件标题', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.find('h2').text()).toBe('回测结果')
    })

    it('应该在加载状态下显示加载指示器', () => {
      const wrapper = createWrapper({ loading: true })
      
      const loadingState = wrapper.find('[data-testid="loading-state"]')
      expect(loadingState.exists()).toBe(true)
      expect(loadingState.text()).toContain('正在计算回测结果')
    })

    it('应该在有结果时显示结果内容', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      expect(wrapper.find('.results-content').exists()).toBe(true)
      expect(wrapper.find('.metrics-grid').exists()).toBe(true)
    })

    it('应该显示状态指示器', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const statusIndicator = wrapper.find('.status-indicator')
      expect(statusIndicator.exists()).toBe(true)
      expect(statusIndicator.find('.status-dot').exists()).toBe(true)
    })
  })

  describe('核心指标卡片', () => {
    it('应该显示总收益率卡片', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const totalReturnCard = wrapper.find('[data-testid="total-return-card"]')
      expect(totalReturnCard.exists()).toBe(true)
      expect(totalReturnCard.text()).toContain('总收益率')
      expect(totalReturnCard.text()).toContain('20.00%')
    })

    it('应该显示夏普比率卡片', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const sharpeCard = wrapper.find('[data-testid="sharpe-ratio-card"]')
      expect(sharpeCard.exists()).toBe(true)
      expect(sharpeCard.text()).toContain('夏普比率')
      expect(sharpeCard.text()).toContain('1.50')
    })

    it('应该显示最大回撤卡片', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const drawdownCard = wrapper.find('[data-testid="max-drawdown-card"]')
      expect(drawdownCard.exists()).toBe(true)
      expect(drawdownCard.text()).toContain('最大回撤')
      expect(drawdownCard.text()).toContain('-10.00%')
    })

    it('应该显示期末资金卡片', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const finalCapitalCard = wrapper.find('[data-testid="final-capital-card"]')
      expect(finalCapitalCard.exists()).toBe(true)
      expect(finalCapitalCard.text()).toContain('期末资金')
      expect(finalCapitalCard.text()).toContain('¥120,000')
    })

    it('应该为正收益率应用正确的样式类', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const totalReturnCard = wrapper.find('[data-testid="total-return-card"]')
      expect(totalReturnCard.classes()).toContain('positive')
    })

    it('应该为负收益率应用正确的样式类', () => {
      const negativeResult = {
        ...mockResult,
        total_return: -0.1
      }
      
      const wrapper = createWrapper({ result: negativeResult })
      
      const totalReturnCard = wrapper.find('[data-testid="total-return-card"]')
      expect(totalReturnCard.classes()).toContain('negative')
    })
  })

  describe('详细信息', () => {
    it('应该显示策略名称', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const strategyName = wrapper.find('[data-testid="strategy-name"]')
      expect(strategyName.text()).toBe('测试策略')
    })

    it('应该显示交易标的', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const symbol = wrapper.find('[data-testid="symbol"]')
      expect(symbol.text()).toBe('000001.SZ')
    })

    it('应该显示回测期间', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const dateRange = wrapper.find('[data-testid="date-range"]')
      expect(dateRange.text()).toBe('2023-01-01 至 2023-12-31')
    })

    it('应该显示初始资金', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const initialCapital = wrapper.find('[data-testid="initial-capital"]')
      expect(initialCapital.text()).toBe('¥100,000.00')
    })

    it('应该显示交易次数', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const tradeCount = wrapper.find('[data-testid="trade-count"]')
      expect(tradeCount.text()).toBe('2 次')
    })

    it('应该显示任务ID', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const taskId = wrapper.find('[data-testid="task-id"]')
      expect(taskId.text()).toBe('test-task-123')
    })

    it('应该处理没有交易记录的情况', () => {
      const resultWithoutTrades = {
        ...mockResult,
        trades: []
      }
      
      const wrapper = createWrapper({ result: resultWithoutTrades })
      
      const tradeCount = wrapper.find('[data-testid="trade-count"]')
      expect(tradeCount.text()).toBe('0 次')
    })

    it('应该处理trades为null的情况', () => {
      const resultWithNullTrades = {
        ...mockResult,
        trades: null as any
      }
      
      const wrapper = createWrapper({ result: resultWithNullTrades })
      
      const tradeCount = wrapper.find('[data-testid="trade-count"]')
      expect(tradeCount.text()).toBe('0 次')
    })
  })

  describe('操作按钮', () => {
    it('应该显示导出结果按钮', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const exportBtn = wrapper.find('[data-testid="export-btn"]')
      expect(exportBtn.exists()).toBe(true)
      expect(exportBtn.text()).toBe('导出结果')
    })

    it('应该显示查看分析按钮', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const analysisBtn = wrapper.find('[data-testid="analysis-btn"]')
      expect(analysisBtn.exists()).toBe(true)
      expect(analysisBtn.text()).toBe('查看分析')
    })

    it('应该在点击导出按钮时触发导出功能', async () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const exportBtn = wrapper.find('[data-testid="export-btn"]')
      await exportBtn.trigger('click')
      
      expect(wrapper.emitted('export')).toBeTruthy()
    })

    it('应该在点击分析按钮时触发分析功能', async () => {
      const wrapper = createWrapper({ result: mockResult })
      
      const analysisBtn = wrapper.find('[data-testid="analysis-btn"]')
      await analysisBtn.trigger('click')
      
      expect(wrapper.emitted('view-analysis')).toBeTruthy()
    })
  })

  describe('状态管理', () => {
    it('应该根据结果状态显示不同的状态文本', () => {
      const completedResult = {
        ...mockResult,
        status: 'completed'
      }
      
      const wrapper = createWrapper({ result: completedResult })
      
      const statusText = wrapper.find('.status-text')
      expect(statusText.text()).toBe('已完成')
    })

    it('应该为不同状态应用不同的CSS类', () => {
      const runningResult = {
        ...mockResult,
        status: 'running'
      }
      
      const wrapper = createWrapper({ result: runningResult })
      
      const statusIndicator = wrapper.find('.status-indicator')
      expect(statusIndicator.classes()).toContain('status-running')
    })
  })

  describe('错误处理', () => {
    it('应该在有错误时显示错误信息', () => {
      const wrapper = createWrapper({ error: '回测计算失败' })
      
      const errorElement = wrapper.find('.error-message, .alert-error, [data-testid="error-message"]')
      if (errorElement.exists()) {
        expect(errorElement.text()).toContain('回测计算失败')
      }
    })
  })

  describe('空状态', () => {
    it('应该在没有结果且不在加载状态时显示空状态', () => {
      const wrapper = createWrapper({ result: null, loading: false })
      
      expect(wrapper.find('.results-content').exists()).toBe(false)
      expect(wrapper.find('[data-testid="loading-state"]').exists()).toBe(false)
    })
  })

  describe('响应式行为', () => {
    it('应该响应result prop的变化', async () => {
      const wrapper = createWrapper({ result: null })
      
      expect(wrapper.find('.results-content').exists()).toBe(false)
      
      await wrapper.setProps({ result: mockResult })
      
      expect(wrapper.find('.results-content').exists()).toBe(true)
    })

    it('应该响应loading状态的变化', async () => {
      const wrapper = createWrapper({ loading: false })
      
      expect(wrapper.find('[data-testid="loading-state"]').exists()).toBe(false)
      
      await wrapper.setProps({ loading: true })
      
      expect(wrapper.find('[data-testid="loading-state"]').exists()).toBe(true)
    })
  })

  describe('数据格式化', () => {
    it('应该正确格式化百分比数据', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      // 验证百分比格式化结果
      expect(wrapper.text()).toContain('20.00%') // total_return: 0.2
      expect(wrapper.text()).toContain('-10.00%') // max_drawdown: -0.1
    })

    it('应该正确格式化数字数据', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      // 验证数字格式化结果
      expect(wrapper.text()).toContain('1.500') // sharpe_ratio: 1.5
    })

    it('应该正确格式化货币数据', () => {
      const wrapper = createWrapper({ result: mockResult })
      
      // 验证货币格式化结果
      expect(wrapper.text()).toContain('¥100,000.00') // initial_capital: 100000
      expect(wrapper.text()).toContain('¥120,000.00') // final_capital: 120000
    })
  })

  describe('边界情况', () => {
    it('应该处理极值数据', () => {
      const extremeResult = {
        ...mockResult,
        total_return: 999.99,
        sharpe_ratio: -999.99,
        max_drawdown: -0.99
      }
      
      const wrapper = createWrapper({ result: extremeResult })
      
      expect(wrapper.find('.results-content').exists()).toBe(true)
    })

    it('应该处理缺失字段的结果数据', () => {
      const incompleteResult = {
        task_id: 'test-task',
        strategy_name: '测试策略',
        symbol: '000001.SZ'
        // 缺少其他字段
      } as any
      
      const wrapper = createWrapper({ result: incompleteResult })
      
      expect(wrapper.find('.results-content').exists()).toBe(true)
    })
  })
})