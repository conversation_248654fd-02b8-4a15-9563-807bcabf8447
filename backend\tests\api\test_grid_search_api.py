import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock, patch

from backend.main import app  # 假设您的FastAPI实例在main.py中

client = TestClient(app)

@pytest.fixture
def mock_celery_task():
    """Fixture to mock the celery task delay method."""
    mock_task = MagicMock()
    mock_task.id = "test-task-id-123"
    with patch('backend.app.api.endpoints.grid_search.run_grid_search.delay', return_value=mock_task) as mock_delay:
        yield mock_delay

@pytest.fixture
def mock_async_result():
    """Fixture to mock the Celery AsyncResult."""
    with patch('backend.app.api.endpoints.grid_search.AsyncResult') as mock_async:
        yield mock_async

def test_run_grid_search_api_success(mock_celery_task):
    """Test the /api/grid-search/run endpoint for successful task creation."""
    payload = {
        "choice_symbols": ["usTSLA"],
        "buy_factors": [{"class": "AbuFactorBuyBreak", "xd": [20]}],
        "sell_factors": [{"class": "AbuFactorSellBreak", "xd": [10]}],
        "read_cash": 500000
    }
    response = client.post("/api/v1/grid-search/run", json=payload)
    assert response.status_code == 200
    assert response.json() == {"task_id": "test-task-id-123"}
    mock_celery_task.assert_called_once_with(
        choice_symbols=payload['choice_symbols'],
        buy_factors=payload['buy_factors'],
        sell_factors=payload['sell_factors'],
        read_cash=payload['read_cash']
    )

def test_get_grid_search_status_pending(mock_async_result):
    """Test the /api/grid-search/status/{task_id} endpoint when task is pending."""
    mock_result_instance = MagicMock()
    mock_result_instance.status = "PENDING"
    mock_result_instance.ready.return_value = False
    mock_result_instance.result = None
    mock_async_result.return_value = mock_result_instance

    task_id = "some-pending-task-id"
    response = client.get(f"/api/v1/grid-search/status/{task_id}")

    assert response.status_code == 200
    assert response.json() == {
        "task_id": task_id,
        "status": "PENDING",
        "result": None
    }
    mock_async_result.assert_called_once_with(task_id, app=ANY) # ANY from unittest.mock

def test_get_grid_search_status_success(mock_async_result):
    """Test the /api/grid-search/status/{task_id} endpoint when task succeeded."""
    mock_result_instance = MagicMock()
    mock_result_instance.status = "SUCCESS"
    mock_result_instance.ready.return_value = True
    mock_result_instance.result = {"status": "SUCCESS", "result": {"best_score": 0.99}}
    mock_async_result.return_value = mock_result_instance

    task_id = "some-success-task-id"
    response = client.get(f"/api/v1/grid-search/status/{task_id}")

    assert response.status_code == 200
    assert response.json() == {
        "task_id": task_id,
        "status": "SUCCESS",
        "result": {"status": "SUCCESS", "result": {"best_score": 0.99}}
    }

# Note: To run these tests, you might need to adjust the import path for `app`
# and ensure the testing environment is set up correctly (e.g., environment variables).
# The use of `ANY` from `unittest.mock` might be needed for the app instance check.
from unittest.mock import ANY