import { describe, it, expect, vi, afterEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useDashboardStore } from '../../../src/stores/useDashboardStore';
import * as dashboardApi from '@/api/dashboard';
import type { DashboardSummary } from '@/api/dashboard';

// TDD专用测试文件 - useDashboardStore
// 专注于核心功能的快速验证

// 契约 1: 外部依赖必须被完全模拟
vi.mock('@/api/dashboard');

// 契约 2: 定义标准的模拟数据
const mockDashboardSummary: DashboardSummary = {
  today_gain: 1500.50,
  active_strategies: 5,
  total_turnover_wan: 125.8,
  signals_count: 12,
  market_performance: {
    date: ['2023-01-01', '2023-01-02', '2023-01-03'],
    value: [100, 102, 105]
  }
};

describe('useDashboardStore - TDD专用测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态 - TDD', () => {
      const store = useDashboardStore();
      expect(store.summary).toBeNull();
      expect(store.loading).toBe(false);
    });
  });

  describe('契约 B: 获取仪表板摘要 (fetchDashboardSummary)', () => {
    it('成功获取摘要后，应更新summary状态并清除loading - TDD', async () => {
      const store = useDashboardStore();
      vi.mocked(dashboardApi.getDashboardSummary).mockResolvedValue(mockDashboardSummary);
      
      await store.fetchDashboardSummary();
      
      expect(store.summary).toEqual(mockDashboardSummary);
      expect(store.loading).toBe(false);
      expect(dashboardApi.getDashboardSummary).toHaveBeenCalledOnce();
    });

    it('获取失败时，应保持summary为null并清除loading状态 - TDD', async () => {
      const store = useDashboardStore();
      const errorMessage = '网络错误';
      vi.mocked(dashboardApi.getDashboardSummary).mockRejectedValue(new Error(errorMessage));
      
      await store.fetchDashboardSummary();
      
      expect(store.summary).toBeNull();
      expect(store.loading).toBe(false);
    });

    it('请求过程中应设置loading为true - TDD', async () => {
      const store = useDashboardStore();
      let resolvePromise: (value: DashboardSummary) => void;
      const promise = new Promise<DashboardSummary>((resolve) => {
        resolvePromise = resolve;
      });
      vi.mocked(dashboardApi.getDashboardSummary).mockReturnValue(promise);
      
      const fetchPromise = store.fetchDashboardSummary();
      expect(store.loading).toBe(true);
      
      resolvePromise!(mockDashboardSummary);
      await fetchPromise;
      expect(store.loading).toBe(false);
    });
  });

  describe('契约 C: 状态管理', () => {
    it('多次调用fetchDashboardSummary应正确更新状态 - TDD', async () => {
      const store = useDashboardStore();
      const firstSummary = { ...mockDashboardSummary, today_gain: 1000 };
      const secondSummary = { ...mockDashboardSummary, today_gain: 2000 };
      
      vi.mocked(dashboardApi.getDashboardSummary)
        .mockResolvedValueOnce(firstSummary)
        .mockResolvedValueOnce(secondSummary);
      
      await store.fetchDashboardSummary();
      expect(store.summary?.today_gain).toBe(1000);
      
      await store.fetchDashboardSummary();
      expect(store.summary?.today_gain).toBe(2000);
    });
  });
});