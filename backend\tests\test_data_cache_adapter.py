# -*- coding: utf-8 -*-
"""
测试数据缓存适配器的线程安全性
"""
import unittest
import os
import threading
import time
import random
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                    format='%(asctime)s [%(threadName)s] %(levelname)s: %(message)s')

from backend.app.abupy_adapter.data_cache_adapter import (
    get_file_lock, safe_read_file, safe_write_file, safe_read_modify_write_file
)
from backend.app.core.exceptions import CacheError


class TestDataCacheAdapter(unittest.TestCase):
    """测试数据缓存适配器的线程安全性"""
    
    def setUp(self):
        """测试前准备工作"""
        self.test_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_cache")
        if not os.path.exists(self.test_dir):
            os.makedirs(self.test_dir)
            
    def tearDown(self):
        """测试后清理工作"""
        # 清理测试生成的文件
        for file in os.listdir(self.test_dir):
            try:
                os.remove(os.path.join(self.test_dir, file))
            except:
                pass
                
        try:
            os.rmdir(self.test_dir)
        except:
            pass
    
    def test_file_lock(self):
        """测试文件锁的获取和重入性"""
        lock1 = get_file_lock("test_file.txt")
        lock2 = get_file_lock("test_file.txt")
        
        # 同一文件应该获得相同的锁对象
        self.assertIs(lock1, lock2)
        
        # 不同文件应该获得不同的锁对象
        lock3 = get_file_lock("another_file.txt")
        self.assertIsNot(lock1, lock3)
        
        # 测试锁的重入性
        with lock1:
            self.assertTrue(lock1.acquire(blocking=False))
            lock1.release()  # 释放额外获取的锁
    
    def test_safe_file_operations(self):
        """测试安全的文件读写操作"""
        test_file = os.path.join(self.test_dir, "test_data.txt")
        test_content = "测试数据内容"
        
        # 测试写入
        self.assertTrue(safe_write_file(test_file, test_content))
        
        # 测试读取
        content = safe_read_file(test_file)
        self.assertEqual(content, test_content)
        
        # 测试读取不存在的文件
        self.assertIsNone(safe_read_file(os.path.join(self.test_dir, "not_exist.txt")))
        
        # 测试原子读取-修改-写入操作
        def double_content(content):
            return content + content
            
        success, result = safe_read_modify_write_file(test_file, double_content)
        self.assertTrue(success)
        self.assertEqual(result, test_content + test_content)
        
        # 验证文件内容确实被修改
        new_content = safe_read_file(test_file)
        self.assertEqual(new_content, test_content + test_content)
    
    def test_concurrent_file_operations(self):
        """测试并发文件操作的线程安全性"""
        # 使用原子计数器跟踪操作
        counter_file = os.path.join(self.test_dir, "counter.txt")
        # 先确保文件不存在
        if os.path.exists(counter_file):
            os.remove(counter_file)
            
        iterations = 100
        threads_count = 10
        total_operations = iterations * threads_count
        
        # 使用线程安全的计数器
        counter = {"success": 0, "failure": 0}
        counter_lock = threading.RLock()
        
        # 初始化文件
        with open(counter_file, 'w') as f:
            f.write("0")
        
        # 创建所有线程完成的事件
        all_done = threading.Event()
        
        def worker(worker_id):
            thread_name = f"Worker-{worker_id}"
            threading.current_thread().name = thread_name
            
            logging.info(f"{thread_name} 开始执行 {iterations} 次操作")
            
            for i in range(iterations):
                retry_count = 0
                max_retries = 3
                success = False
                
                while not success and retry_count < max_retries:
                    try:
                        # 使用原子操作函数递增计数器
                        def increment_counter(content):
                            # 如果文件不存在或内容为None，初始化为0
                            if content is None or content.strip() == "":
                                logging.warning(f"{thread_name}: 文件内容为空，初始化为0")
                                return "0"
                                
                            # 模拟一些处理时间
                            time.sleep(random.uniform(0.001, 0.003))
                            
                            try:
                                # 尝试将内容转换为整数并递增
                                current_value = int(content.strip())
                                new_value = current_value + 1
                                logging.debug(f"{thread_name}: 递增 {current_value} -> {new_value}")
                                return str(new_value)
                            except ValueError as ve:
                                logging.error(f"{thread_name}: 当前值 '{content}' 不是有效整数: {ve}")
                                # 如果出错，返回一个有效值
                                return "0"
                        
                        # 执行原子操作
                        operation_result, _ = safe_read_modify_write_file(counter_file, increment_counter)
                        
                        if operation_result:
                            success = True
                            with counter_lock:
                                counter["success"] += 1
                                if counter["success"] % 100 == 0 or counter["success"] + counter["failure"] == total_operations:
                                    logging.info(f"进度: {counter['success']}/{total_operations} 成功操作")
                        else:
                            retry_count += 1
                            logging.warning(f"{thread_name}: 操作失败，尝试重试 ({retry_count}/{max_retries})")
                            time.sleep(0.01)  # 等待一下再重试
                    
                    except Exception as e:
                        retry_count += 1
                        logging.error(f"{thread_name}: 异常 - {str(e)}")
                        time.sleep(0.01)  # 等待一下再重试
                
                # 如果重试耗尽仍未成功
                if not success:
                    with counter_lock:
                        counter["failure"] += 1
                        logging.error(f"{thread_name}: 操作最终失败，重试耗尽")
            
            logging.info(f"{thread_name} 完成所有操作")
            
            # 检查是否所有线程都完成了
            with counter_lock:
                if counter["success"] + counter["failure"] >= total_operations:
                    all_done.set()
        
        # 创建并启动多个线程
        threads = []
        for i in range(threads_count):
            t = threading.Thread(target=worker, args=(i,))
            threads.append(t)
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证所有操作完成
        self.assertTrue(all_done.is_set(), "不是所有操作都完成了")
        
        # 验证最终值是否正确
        final_content = safe_read_file(counter_file)
        logging.info(f"最终计数器值: {final_content}")
        expected_value = str(total_operations)
        
        # 检查成功写入的次数
        logging.info(f"操作统计: 成功={counter['success']}, 失败={counter['failure']}, 总计={total_operations}")
        self.assertEqual(counter["success"], total_operations, "成功操作数量不匹配")
        self.assertEqual(counter["failure"], 0, "存在失败的操作")
        
        # 检查最终值
        self.assertEqual(final_content.strip(), expected_value, f"最终计数器值 {final_content} 不等于预期值 {expected_value}")


if __name__ == "__main__":
    unittest.main()
