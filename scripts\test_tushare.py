import tushare as ts
import os

# 从 .env 文件加载 TUSHARE_TOKEN (如果您的项目使用 python-dotenv)
# 或者直接在这里设置 token 字符串
try:
    from dotenv import load_dotenv
    load_dotenv(dotenv_path='backend/.env') # 根据您的 .env 文件实际路径调整
    print("Attempting to load .env file from backend/.env")
except ImportError:
    print(".env file not loaded, python-dotenv might not be installed or .env path is incorrect.")
    pass # 如果没有 python-dotenv 或 .env 文件，则忽略

tushare_token = os.getenv('TUSHARE_TOKEN')

if not tushare_token:
    # 如果环境变量中没有，或者您想直接指定，请取消下面一行的注释并填入您的 token
    # tushare_token = "YOUR_ACTUAL_TUSHARE_TOKEN"
    print("TUSHARE_TOKEN not found in environment variables or .env file.")
    # exit() # 如果没有token，可以选择退出

print(f"Attempting to initialize Tushare with token: {tushare_token[:10]}...") # 只打印部分token以保护隐私

try:
    ts.set_token(tushare_token)
    pro = ts.pro_api()
    print("Tushare pro_api initialized successfully.")

    # 进行一次简单的数据查询
    df = pro.trade_cal(exchange='', start_date='20240101', end_date='20240105')
    if df is not None and not df.empty:
        print("Tushare API call (trade_cal) successful.")
        print(df.head())
    else:
        print("Tushare API call (trade_cal) returned no data or an error.")

except Exception as e:
    print(f"An error occurred: {e}")
    import traceback
    traceback.print_exc()
