/// <reference types="vitest" />
import { fileURLToPath, URL } from 'node:url'
import { mergeConfig } from 'vite'
import { defineConfig } from 'vite' // 【重大修正】从 'vite' 导入 defineConfig
import viteConfig from './vite.config' // 我们仍然可以导入基础配置

// 导出最终的、合并后的配置
export default defineConfig({
  // 使用展开运算符 (...) 将您的 vite.config.ts 中的基础配置合并进来
  ...viteConfig,

  // 【重大修正】将 'test' 配置作为顶级属性直接放在这里
  test: {
    // 测试环境配置
    environment: 'jsdom',
    
    // 全局设置文件
    setupFiles: ['./tests/setup.ts'],
    
    // 全局变量
    globals: true,
    
    // 测试文件匹配模式 (原文内容)
    include: [
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // 排除文件 (原文内容)
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      'e2e/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**'
    ],
    
    // 根目录 (原文内容)
    root: fileURLToPath(new URL('./', import.meta.url)),
    
    // 依赖处理 (原文内容，但放在正确的位置)
    deps: {
      inline: ['element-plus', '@element-plus/icons-vue']
    },
    
    // TypeScript 类型检查 (原文内容)
    typecheck: {
      tsconfig: './tsconfig.test.json'
    },
    
    // 覆盖率配置 (原文内容)
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      exclude: [
        'coverage/**',
        'dist/**',
        'packages/*/test{,s}/**',
        '**/*.d.ts',
        'cypress/**',
        'test{,s}/**',
        'test{,-*}.{js,cjs,mjs,ts,tsx,jsx}',
        '**/*{.,-}test.{js,cjs,mjs,ts,tsx,jsx}',
        '**/*{.,-}spec.{js,cjs,mjs,ts,tsx,jsx}',
        '**/__tests__/**',
        '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
        '**/.{eslint,mocha,prettier}rc.{js,cjs,yml}',
        'src/main.ts',
        'src/router/**',
        'src/assets/**',
        'src/styles/**',
        '**/*.config.{js,ts}',
        '**/mock/**',
        'tests/factories/**',
        'tests/utils/**',
        'tests/mocks/**'
      ],
      thresholds: {
        global: {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        }
      }
    },
    
    // 测试超时 (原文内容)
    testTimeout: 30000,
    hookTimeout: 30000,
    
    // 性能基准配置 (原文内容)
    benchmark: {
      include: ['**/*.{bench,benchmark}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
      exclude: ['node_modules', 'dist', '.git'],
      reporters: ['default']
    },
    
    // 并发配置 (原文内容)
    pool: 'threads',
    
    // 报告器配置 (原文内容)
    reporters: ['default', 'verbose'],
    
    // 输出文件 (原文内容)
    outputFile: {
      json: './test-results.json',
      junit: './junit.xml'
    },
    
    // 监听模式配置 (原文内容)
    watch: false,
    
    // 环境变量 (原文内容)
    env: {
      NODE_ENV: 'test',
      VITE_API_BASE_URL: 'http://localhost:3000/api',
      MSW_ENABLED: 'true'
    },
    
    // 测试序列化配置 (原文内容)
    sequence: {
      hooks: 'stack'
    },
    
    // 失败时停止 (原文内容)
    bail: 0,
    
    // 重试配置 (原文内容)
    retry: 0
  }
})
