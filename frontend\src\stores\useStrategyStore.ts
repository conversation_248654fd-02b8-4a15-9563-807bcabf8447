import { defineStore } from 'pinia';
import * as strategyApi from '../api/strategy';

export const useStrategyStore = defineStore('strategy', {
  state: () => ({
    strategies: [] as any[], // 后面我们会用TS接口来定义类型
    currentStrategy: null as any, // 用于存储单个策略详情
    loading: false,
  }),
  actions: {
    async fetchStrategies() {
      this.loading = true;
      try {
        const response = await strategyApi.getStrategies();
        // 响应拦截器已处理了外层结构, response.data 就是 ResponseSchema
        this.strategies = response.data; 
      } catch (error) {
        console.error('Failed to fetch strategies:', error);
        // 可以在这里添加更复杂的错误处理逻辑，例如设置一个错误状态
      } finally {
        this.loading = false;
      }
    },

    async fetchStrategyById(id: string) {
        this.loading = true;
        try {
            const response = await strategyApi.getStrategyById(id);
            this.currentStrategy = response.data;
        } catch (error) {
            console.error(`Failed to fetch strategy with id ${id}:`, error);
        } finally {
            this.loading = false;
        }
    },

    async createNewStrategy(strategyData: any) {
        this.loading = true;
        try {
            await strategyApi.createStrategy(strategyData);
            // 创建成功后，重新获取列表以保证数据同步
            await this.fetchStrategies(); 
        } catch (error) {
            console.error('Failed to create strategy:', error);
        } finally {
            this.loading = false;
        }
    },

    async updateExistingStrategy(id: string, strategyData: any) {
        this.loading = true;
        try {
            await strategyApi.updateStrategy(id, strategyData);
            await this.fetchStrategies(); // 同步列表
        } catch (error) {
            console.error(`Failed to update strategy with id ${id}:`, error);
        } finally {
            this.loading = false;
        }
    },

    async deleteExistingStrategy(id: string) {
        this.loading = true;
        try {
            await strategyApi.deleteStrategy(id);
            await this.fetchStrategies(); // 同步列表
        } catch (error) {
            console.error(`Failed to delete strategy with id ${id}:`, error);
        } finally {
            this.loading = false;
        }
    }
  },
});