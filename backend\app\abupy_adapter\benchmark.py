"""
基准对象模块

提供与abupy基准对象相关的适配器类和工具函数
"""

import logging
from typing import Optional


class SimpleBenchmark:
    """
    简化的Benchmark对象，提供与abupy.AbuBenchmark兼容的最小实现
    
    用于替代原始字符串基准符号，确保具有n_folds等必要属性
    """
    
    def __init__(self, symbol: str, n_folds: int = 2):
        """
        初始化SimpleBenchmark对象
        
        Args:
            symbol: 基准符号，如'sh000300'
            n_folds: 交叉验证折数，默认为2
        """
        self.symbol = symbol
        self.n_folds = n_folds
        logging.debug(f"创建SimpleBenchmark: symbol={self.symbol}, n_folds={self.n_folds}")

    def __str__(self) -> str:
        return f"SimpleBenchmark(symbol={self.symbol}, n_folds={self.n_folds})"
    
    def __repr__(self) -> str:
        return self.__str__()


from backend.app.abupy_adapter.exceptions import ParameterError # Ensure ParameterError is imported

def create_benchmark(benchmark_symbol: str, n_folds: int = 2) -> SimpleBenchmark:
    """
    创建基准对象的工厂函数
    
    Args:
        benchmark_symbol: 基准符号
        n_folds: 交叉验证折数
        
    Returns:
        SimpleBenchmark对象
    """
    if benchmark_symbol is None:
        raise ParameterError("基准符号 (benchmark_symbol) 不能为空 (None)。")
    
    if not isinstance(benchmark_symbol, str):
        raise ParameterError(f"基准符号 (benchmark_symbol) 必须是字符串，但收到了 {type(benchmark_symbol)} 类型。")
        
    if not benchmark_symbol.strip(): # Check for empty or whitespace-only strings
        raise ParameterError("基准符号 (benchmark_symbol) 不能为空字符串。")

    return SimpleBenchmark(benchmark_symbol, n_folds)
