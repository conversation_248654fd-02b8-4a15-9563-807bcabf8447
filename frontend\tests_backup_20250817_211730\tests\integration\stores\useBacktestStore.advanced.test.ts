import { describe, it, expect, vi, afterEach } from 'vitest';
import { useBacktestStore } from '@/stores/useBacktestStore';
import { BacktestConfig, BacktestResult, BacktestStatus, BacktestTask } from '@/api/types/backtest';
import * as backtestApi from '@/api/backtest';

// 契约 1: 外部依赖必须被完全模拟。
// 所有对 @/api/backtest 的调用都将被拦截。
vi.mock('@/api/backtest');

// 契约 2: 定义一套标准的、可复用的模拟数据作为测试基准。
// 这套数据结构是 Store 与 API 交互的“语言”。
const mockBacktestConfig: BacktestConfig = {
  strategy_name: 'test_strategy',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  initial_capital: 100000,
  commission: 0.001,
  slippage: 0.001,
  benchmark: '000300.SH',
  parameters: {
    period: 20,
    threshold: 0.05
  }
};

const mockBacktestTask: BacktestTask = {
  id: 'test-task-123',
  strategy_name: 'test_strategy',
  status: BacktestStatus.RUNNING,
  progress: 0,
  created_at: '2023-01-01T00:00:00Z',
};

const mockBacktestResult: BacktestResult = {
  id: 'test-task-123',
  status: BacktestStatus.COMPLETED,
  config: mockBacktestConfig,
  metrics: {
    total_return: 0.15,
    annual_return: 0.12,
    max_drawdown: 0.08,
    sharpe_ratio: 1.2,
    volatility: 0.18,
    win_rate: 0.65,
    progress: 100,
  },
  equity_curve: [ { date: '2023-01-01', value: 100000 }, { date: '2023-06-01', value: 105000 }, { date: '2023-12-31', value: 115000 } ],
  trades: [ { symbol: '000001.SZ', side: 'buy', quantity: 1000, price: 10.5, timestamp: '2023-01-15T09:30:00Z', commission: 10.5 } ],
  created_at: '2023-01-01T00:00:00Z',
  completed_at: '2023-01-01T01:00:00Z'
};

const mockHistoryItem = {
  id: 'history-task-456', // 【修正】统一使用 id
  strategy_name: 'history_strategy',
  status: BacktestStatus.COMPLETED,
  created_at: '2023-01-01T00:00:00Z',
  completed_at: '2023-01-01T01:00:00Z',
  metrics: {
    total_return: 0.10,
    annual_return: 0.08,
    max_drawdown: 0.05,
    sharpe_ratio: 1.0,
    volatility: 0.15,
    win_rate: 0.60,
    progress: 100,
  }
};

// 为简化契约，我们约定所有成功的API响应都包裹在 { data: ... } 中。
const mockSuccess = <T>(data: T) => ({ data });

describe('useBacktestStore - 最终测试契约', () => {

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态', () => {
      const store = useBacktestStore();
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestProgress).toBe(0);
      expect(store.backtestResult).toBeNull();
      expect(store.backtestHistory).toEqual([]);
      expect(store.backtestError).toBe('');
      expect(store.isLoadingResults).toBe(false);
    });
  });
  
  describe('契约 B: 启动回测流程 (startBacktest)', () => {
    it('成功启动后，状态应立即变为isBacktesting=true，并设置当前任务', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockResolvedValue(mockSuccess(mockBacktestTask));
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(true);
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
      expect(backtestApi.runBacktest).toHaveBeenCalledWith(mockBacktestConfig);
    });
    
    it('启动失败时，必须设置错误信息，且状态不能是isBacktesting', async () => {
      const store = useBacktestStore();
      const errorMessage = '策略参数无效';
      vi.mocked(backtestApi.runBacktest).mockRejectedValue(new Error(errorMessage));
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestError).toBe(errorMessage);
      expect(store.currentBacktestTask).toBeNull();
    });
    
    it('在API调用期间，isBacktesting状态必须为true', async () => {
      const store = useBacktestStore();
      const { promise, resolve } = Promise.withResolvers<any>();
      vi.mocked(backtestApi.runBacktest).mockReturnValue(promise);
      
      const runPromise = store.startBacktest(mockBacktestConfig);
      
      expect(store.isBacktesting).toBe(true);

      resolve(mockSuccess(mockBacktestTask));
      await runPromise;
      
      expect(store.isBacktesting).toBe(true);
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
    });
    
    it('当一个回测正在进行时，必须阻止启动新的回测', async () => {
      const store = useBacktestStore();
      store.isBacktesting = true;
      
      await store.startBacktest(mockBacktestConfig);
      
      expect(backtestApi.runBacktest).not.toHaveBeenCalled();
    });
  });

  describe('契约 C: 轮询与进度更新', () => {
    it('启动回测后，必须能通过轮询更新进度，并最终在完成后更新结果和状态', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockResolvedValue(mockSuccess(mockBacktestTask));

      const runningResult = { ...mockBacktestResult, status: BacktestStatus.RUNNING, metrics: { ...mockBacktestResult.metrics, progress: 50 } };
      const completedResult = { ...mockBacktestResult, status: BacktestStatus.COMPLETED };

      vi.mocked(backtestApi.getBacktestResults)
        .mockResolvedValueOnce(mockSuccess(runningResult))
        .mockResolvedValueOnce(mockSuccess(completedResult));

      await store.startBacktest(mockBacktestConfig);

      await vi.advanceTimersByTimeAsync(2000);
      expect(backtestApi.getBacktestResults).toHaveBeenCalledTimes(1);
      expect(store.backtestProgress).toBe(50);
      expect(store.isBacktesting).toBe(true);

      await vi.advanceTimersByTimeAsync(2000);
      expect(backtestApi.getBacktestResults).toHaveBeenCalledTimes(2);
      expect(store.backtestResult).toEqual(completedResult);
      expect(store.isBacktesting).toBe(false);
      expect(store.isCompleted).toBe(true);
    });
  });
  
  describe('契约 D: 获取结果 (loadBacktestResults)', () => {
    it('成功获取已完成的结果后，应更新结果，并设置isBacktesting为false', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.getBacktestResults).mockResolvedValue(mockSuccess(mockBacktestResult));
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestResult).toEqual(mockBacktestResult);
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestError).toBe('');
    });
    
    it('成功获取仍在运行的结果后，应更新结果，并保持isBacktesting为true', async () => {
      const store = useBacktestStore();
      const runningResult = { ...mockBacktestResult, status: BacktestStatus.RUNNING };
      vi.mocked(backtestApi.getBacktestResults).mockResolvedValue(mockSuccess(runningResult));
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestResult).toEqual(runningResult);
      expect(store.isBacktesting).toBe(true);
    });
    
    it('获取结果失败时，必须设置错误信息', async () => {
      const store = useBacktestStore();
      const errorMessage = '任务未找到';
      vi.mocked(backtestApi.getBacktestResults).mockRejectedValue(new Error(errorMessage));
      
      await store.loadBacktestResults('test-task-123');
      
      expect(store.backtestError).toBe(errorMessage);
      expect(store.backtestResult).toBeNull();
    });
    
    it('在没有任务ID时，action 内部应有卫兵语句阻止API调用', async () => {
      const store = useBacktestStore();
      await store.loadBacktestResults('');
      expect(backtestApi.getBacktestResults).not.toHaveBeenCalled();
      expect(store.backtestError).not.toBe('');
    });
  });
  
  describe('契约 E: 获取历史 (loadBacktestHistory)', () => {
    it('应该成功获取回测历史', async () => {
      const store = useBacktestStore();
      const historyResponse = { data: [mockHistoryItem], total: 1, page: 1, page_size: 10 };
      vi.mocked(backtestApi.getBacktestHistory).mockResolvedValue(mockSuccess(historyResponse));
      
      await store.loadBacktestHistory();
      
      expect(store.backtestHistory).toEqual(historyResponse.data);
      expect(store.backtestError).toBe('');
    });
    
    it('应该支持分页参数', async () => {
      const store = useBacktestStore();
      const params = { page: 2, limit: 20 }; // 使用 limit
      const historyResponse = { data: [], total: 0, page: 2, page_size: 20 };
      vi.mocked(backtestApi.getBacktestHistory).mockResolvedValue(mockSuccess(historyResponse));
      
      await store.loadBacktestHistory(params);
      
      expect(backtestApi.getBacktestHistory).toHaveBeenCalledWith(params);
    });
    
    it('应该处理获取历史失败', async () => {
      const store = useBacktestStore();
      const errorMessage = '获取历史失败';
      vi.mocked(backtestApi.getBacktestHistory).mockRejectedValue(new Error(errorMessage));
      
      await store.loadBacktestHistory();
      
      expect(store.backtestError).toBe(errorMessage);
      expect(store.backtestHistory).toEqual([]);
    });
  });
  
  describe('契约 F: 停止回测 (stopCurrentBacktest)', () => {
    it('成功停止后，状态应更新，isBacktesting为false，任务被清理', async () => {
      const store = useBacktestStore();
      store.isBacktesting = true;
      store.currentBacktestTask = { id: 'test-task-123' } as any;
      vi.mocked(backtestApi.stopBacktest).mockResolvedValue(mockSuccess({ message: 'stopped' }));
      
      await store.stopCurrentBacktest();
      
      expect(store.isBacktesting).toBe(false);
      expect(store.currentBacktestTask).toBeNull();
      expect(store.backtestError).toBe('');
      expect(backtestApi.stopBacktest).toHaveBeenCalledWith('test-task-123');
    });
    
    it('应该处理停止回测失败', async () => {
      const store = useBacktestStore();
      store.isBacktesting = true;
      store.currentBacktestTask = { id: 'test-task-123' } as any;
      const errorMessage = '无法停止已完成的任务';
      vi.mocked(backtestApi.stopBacktest).mockRejectedValue(new Error(errorMessage));
      
      await store.stopCurrentBacktest();

      expect(store.backtestError).toBe(errorMessage);
      expect(store.isBacktesting).toBe(true);
    });
    
    it('应该在没有任务ID时返回错误', async () => {
      const store = useBacktestStore();
      store.currentBacktestTask = null;
      
      await store.stopCurrentBacktest();
      
      expect(store.backtestError).not.toBe('');
      expect(backtestApi.stopBacktest).not.toHaveBeenCalled();
    });
  });
  
  describe('契约 G: 状态清理', () => {
    it('clearError: 应该清除错误信息', () => {
      const store = useBacktestStore();
      store.backtestError = '测试错误';
      store.clearError();
      expect(store.backtestError).toBe('');
    });
  
    it('resetBacktestState: 应该重置所有相关状态', () => {
      const store = useBacktestStore();
      store.isBacktesting = true;
      store.backtestProgress = 50;
      store.backtestResult = mockBacktestResult;
      store.backtestError = '测试错误';
      store.currentBacktestTask = { id: 'test-task' } as any;
      
      store.resetBacktestState();
      
      expect(store.isBacktesting).toBe(false);
      expect(store.backtestProgress).toBe(0);
      expect(store.backtestResult).toBeNull();
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toBeNull();
    });
  });
  
  describe('契约 H: 计算属性', () => {
    it('hasResult: 必须在backtestResult有值时为true，否则为false', () => {
      const store = useBacktestStore();
      expect(store.hasResult).toBe(false);
      store.backtestResult = mockBacktestResult;
      expect(store.hasResult).toBe(true);
    });
    
    it('isCompleted: 必须在结果状态为COMPLETED时为true', () => {
      const store = useBacktestStore();
      expect(store.isCompleted).toBe(false);
      store.backtestResult = { ...mockBacktestResult, status: BacktestStatus.RUNNING };
      expect(store.isCompleted).toBe(false);
      store.backtestResult.status = BacktestStatus.COMPLETED;
      expect(store.isCompleted).toBe(true);
    });

    it('hasActiveBacktest: 必须在isBacktesting为true时为true', () => {
      const store = useBacktestStore();
      expect(store.hasActiveBacktest).toBe(false);
      store.isBacktesting = true;
      expect(store.hasActiveBacktest).toBe(true);
      store.isBacktesting = false;
      expect(store.hasActiveBacktest).toBe(false);
    });
  });
  
  describe('契约 I: 复杂与异步场景', () => {
    it('多策略优化场景：必须能循环执行回测并收集结果', async () => {
      const store = useBacktestStore();
      const paramVariations = [
        { commission: 0.001, slippage: 0.001 },
        { commission: 0.002, slippage: 0.002 }
      ];
      const results = [];
      for (const params of paramVariations) {
        const config = { ...mockBacktestConfig, ...params };
        const mockTask = { ...mockBacktestTask, id: `task-${params.commission}` };
        const mockResult = { ...mockBacktestResult, id: mockTask.id, metrics: { ...mockBacktestResult.metrics, total_return: 0.20 - params.commission * 100 } };
        
        vi.mocked(backtestApi.runBacktest).mockResolvedValue(mockSuccess(mockTask));
        vi.mocked(backtestApi.getBacktestResults).mockResolvedValue(mockSuccess(mockResult));
        
        await store.startBacktest(config);
        if (store.currentBacktestTask) {
          await store.loadBacktestResults(store.currentBacktestTask.id);
        }
        results.push({ params, result: store.backtestResult });
        store.resetBacktestState();
      }
      expect(results).toHaveLength(2);
      const returns = results.map(r => r.result?.metrics?.total_return || 0);
      expect(returns[0]).toBeGreaterThan(returns[1]);
    });

    it('状态转换原子性：必须通过卫兵语句防止并发API调用', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(mockSuccess(mockBacktestTask)), 100))
      );
      
      const promise1 = store.startBacktest(mockBacktestConfig);
      const promise2 = store.startBacktest(mockBacktestConfig);
      
      await vi.advanceTimersByTimeAsync(100);
      
      await Promise.allSettled([promise1, promise2]);
      
      expect(vi.mocked(backtestApi.runBacktest)).toHaveBeenCalledTimes(1);
      expect(store.currentBacktestTask).not.toBeNull();
    });
  });

  describe('契约 J: 网络错误处理与恢复', () => {
    it('应该处理网络连接超时错误', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockRejectedValue(new Error('Network timeout'));
      
      await store.startBacktest(mockBacktestConfig);

      expect(store.backtestError).toContain('Network timeout');
      expect(store.currentBacktestTask).toBeNull();
    });
    
    it('应该在网络恢复后能够重新启动回测', async () => {
      const store = useBacktestStore();
      vi.mocked(backtestApi.runBacktest).mockRejectedValueOnce(new Error('Network timeout'));
      
      await store.startBacktest(mockBacktestConfig);
      expect(store.backtestError).toContain('Network timeout');
      
      vi.mocked(backtestApi.runBacktest).mockResolvedValue(mockSuccess(mockBacktestTask));
      
      store.clearError();
      await store.startBacktest(mockBacktestConfig);
      
      expect(store.backtestError).toBe('');
      expect(store.currentBacktestTask).toEqual(mockBacktestTask);
    });
    
    it('应该处理获取结果时的网络错误', async () => {
      const store = useBacktestStore();
      store.currentBacktestTask = mockBacktestTask;
      vi.mocked(backtestApi.getBacktestResults).mockRejectedValue(new Error('Network timeout'));
      
      await store.loadBacktestResults('test-task-123');

      expect(store.backtestError).toContain('Network timeout');
      expect(store.backtestResult).toBeNull();
    });
  });

  describe('契约 K: 状态持久化与恢复', () => {
    afterEach(() => {
      localStorage.clear();
    });

    it('应该正确保存和恢复回测状态', async () => {
      const stateToSave = {
        currentBacktestTask: mockBacktestTask,
        backtestResult: mockBacktestResult,
        backtestHistory: [mockHistoryItem]
      };
      localStorage.setItem('backtest-store', JSON.stringify(stateToSave));
      
      const storeForLoading = useBacktestStore();
      
      // 模拟 store 初始化时从 localStorage 恢复状态
      const savedData = localStorage.getItem('backtest-store');
      if (savedData) {
        storeForLoading.$patch(JSON.parse(savedData));
      }
      
      expect(storeForLoading.currentBacktestTask).toEqual(mockBacktestTask);
      expect(storeForLoading.backtestResult).toEqual(mockBacktestResult);
      expect(storeForLoading.backtestHistory).toEqual([mockHistoryItem]);
    });
    
    it('应该能优雅地处理无效的持久化数据', async () => {
      localStorage.setItem('backtest-store', '{"invalid": "json"}');
      let store: any;
      
      expect(() => {
        store = useBacktestStore();
        // 模拟恢复逻辑
        try {
          const savedData = localStorage.getItem('backtest-store');
          if (savedData) {
            const parsed = JSON.parse(savedData);
            if (parsed && parsed.currentBacktestTask) { // 简单验证
               store.$patch(parsed);
            }
          }
        } catch (e) { /* 静默处理 */ }
      }).not.toThrow();

      store = useBacktestStore();
      expect(store.currentBacktestTask).toBeNull();
      expect(store.backtestResult).toBeNull();
    });
    
    it('应该能优雅地处理localStorage不可用的情况', async () => {
      const originalLocalStorage = window.localStorage;
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: () => { throw new Error('localStorage not available'); },
          setItem: () => { throw new Error('localStorage not available'); }
        },
        writable: true
      });
      
      let store: any;
      expect(() => {
        store = useBacktestStore();
        // 模拟恢复逻辑
        try {
          const savedData = localStorage.getItem('backtest-store');
          if (savedData) store.$patch(JSON.parse(savedData));
        } catch (e) { /* 静默处理 */ }
      }).not.toThrow();

      store = useBacktestStore();
      expect(store.currentBacktestTask).toBeNull();
      
      Object.defineProperty(window, 'localStorage', {
        value: originalLocalStorage,
        writable: true
      });
    });
  });
});