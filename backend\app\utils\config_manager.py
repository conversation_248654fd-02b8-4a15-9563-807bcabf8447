"""配置管理模块

提供统一的配置管理、环境变量处理和配置验证功能。
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self.config = self._load_config()
        self._setup_logging()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        # 默认配置
        default_config = {
            'database': {
                'url': os.getenv('DATABASE_URL', 'sqlite:///abu_modern.db'),
                'echo': os.getenv('DATABASE_ECHO', 'false').lower() == 'true',
                'pool_size': int(os.getenv('DATABASE_POOL_SIZE', '10')),
                'max_overflow': int(os.getenv('DATABASE_MAX_OVERFLOW', '20'))
            },
            'logging': {
                'level': os.getenv('LOG_LEVEL', 'INFO'),
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': os.getenv('LOG_FILE', 'logs/app.log')
            },
            'abupy': {
                'data_dir': os.getenv('ABUPY_DATA_DIR', 'data'),
                'cache_dir': os.getenv('ABUPY_CACHE_DIR', 'cache'),
                'log_level': os.getenv('ABUPY_LOG_LEVEL', 'INFO')
            },
            'performance': {
                'enable_monitoring': os.getenv('ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true',
                'metrics_retention_days': int(os.getenv('METRICS_RETENTION_DAYS', '30')),
                'alert_threshold_ms': int(os.getenv('ALERT_THRESHOLD_MS', '5000'))
            },
            'security': {
                'secret_key': os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production'),
                'token_expiry_hours': int(os.getenv('TOKEN_EXPIRY_HOURS', '24')),
                'max_login_attempts': int(os.getenv('MAX_LOGIN_ATTEMPTS', '5'))
            },
            'quality_thresholds': {
                'overall_score': 80,
                'test_coverage': 80,
                'documentation_coverage': 70,
                'code_quality_score': 85
            }
        }
        
        # 如果提供了配置文件路径，尝试加载
        if self.config_path and Path(self.config_path).exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    # 合并配置
                    default_config.update(file_config)
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_path}: {e}")
        
        return default_config
    
    def _setup_logging(self):
        """设置日志"""
        import logging
        
        log_config = self.config.get('logging', {})
        log_level = getattr(logging, log_config.get('level', 'INFO').upper())
        log_format = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        log_file = log_config.get('file')
        
        # 创建日志目录
        if log_file:
            log_dir = Path(log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置根日志记录器
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file) if log_file else logging.NullHandler()
            ]
        )
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_database_url(self) -> str:
        """获取数据库URL"""
        return self.get('database.url')
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get('database', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_abupy_config(self) -> Dict[str, Any]:
        """获取AbuPy配置"""
        return self.get('abupy', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.get('performance', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self.get('security', {})
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return os.getenv('ENVIRONMENT', 'development').lower() == 'development'
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return os.getenv('ENVIRONMENT', 'development').lower() == 'production'
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        warnings = []
        
        # 验证数据库配置
        db_url = self.get_database_url()
        if not db_url:
            issues.append("Database URL is not configured")
        
        # 验证安全配置
        secret_key = self.get('security.secret_key')
        if secret_key == 'dev-secret-key-change-in-production' and self.is_production():
            issues.append("Default secret key is being used in production")
        
        # 验证日志配置
        log_file = self.get('logging.file')
        if log_file:
            log_dir = Path(log_file).parent
            if not log_dir.exists():
                try:
                    log_dir.mkdir(parents=True, exist_ok=True)
                except Exception:
                    warnings.append(f"Cannot create log directory: {log_dir}")
        
        # 验证数据目录
        data_dir = self.get('abupy.data_dir')
        if data_dir and not Path(data_dir).exists():
            warnings.append(f"AbuPy data directory does not exist: {data_dir}")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }
    
    def save_config(self, output_path: str):
        """保存当前配置到文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, updates)

# 全局配置实例
_global_config = None

def get_config() -> ConfigManager:
    """获取全局配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = ConfigManager()
    return _global_config

def init_config(config_path: Optional[str] = None) -> ConfigManager:
    """初始化全局配置"""
    global _global_config
    _global_config = ConfigManager(config_path)
    return _global_config