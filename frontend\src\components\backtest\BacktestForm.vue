<template>
  <div class="backtest-form">
    <h2>回测与方案</h2>
    
    <form @submit.prevent="handleSubmit" class="form-container">
      <!-- 策略ID -->
      <div class="form-group">
        <label for="strategy-id">策略ID *</label>
        <input
          id="strategy-id"
          v-model="form.strategy_id"
          type="text"
          placeholder="请输入策略ID"
          required
          data-testid="strategy-id-input"
        />
      </div>
      
      <!-- 策略名称 -->
      <div class="form-group">
        <label for="strategy">策略名称</label>
        <input
          id="strategy"
          v-model="form.strategy_name"
          type="text"
          placeholder="请输入策略名称"
          data-testid="strategy-input"
        />
      </div>
      
      <!-- 股票代码 -->
      <div class="form-group">
        <label for="symbol">股票代码 *</label>
        <input
          id="symbol"
          v-model="form.symbol"
          type="text"
          placeholder="如：000001.SZ"
          required
          data-testid="symbol-input"
        />
      </div>
      
      <!-- 日期范围 -->
      <div class="form-row">
        <div class="form-group">
          <label for="start-date">开始日期 *</label>
          <input
            id="start-date"
            v-model="form.start_date"
            type="date"
            required
            data-testid="start-date-input"
          />
        </div>
        
        <div class="form-group">
          <label for="end-date">结束日期 *</label>
          <input
            id="end-date"
            v-model="form.end_date"
            type="date"
            required
            data-testid="end-date-input"
          />
        </div>
      </div>
      
      <!-- 初始资金 -->
      <div class="form-group">
        <label for="capital">初始资金 *</label>
        <input
          id="capital"
          v-model.number="form.capital"
          type="number"
          min="1000"
          step="1000"
          placeholder="100000"
          required
          data-testid="capital-input"
        />
      </div>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <button 
          type="button" 
          @click="handleReset"
          class="btn btn-secondary"
          data-testid="reset-btn"
        >
          重置
        </button>
        
        <button 
          type="submit" 
          :disabled="loading || !isFormValid"
          class="btn btn-primary"
          data-testid="submit-btn"
        >
          <span v-if="loading">运行中...</span>
          <span v-else>开始回测</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { BacktestConfig } from '@/api/types/backtest'

interface Props {
  loading?: boolean
}

interface Emits {
  submit: [config: BacktestConfig]
  reset: []
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 表单数据
const form = ref({
  strategy_id: '7dce6a92d3594218b48d9242de07f598', // 使用有效的策略ID
  strategy_name: 'Test Strategy with Umpire',
  symbol: '000001.SZ',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  capital: 100000
})

// 表单验证
const isFormValid = computed(() => {
  return form.value.strategy_id.trim() !== '' &&
         form.value.strategy_name.trim() !== '' &&
         form.value.symbol.trim() !== '' &&
         form.value.start_date !== '' &&
         form.value.end_date !== '' &&
         form.value.capital > 0 &&
         new Date(form.value.start_date) < new Date(form.value.end_date)
})

// 提交处理
const handleSubmit = () => {
  if (isFormValid.value && !props.loading) {
    const config: BacktestConfig = {
      strategy_id: form.value.strategy_id,
      symbol: form.value.symbol,
      start_date: form.value.start_date,
      end_date: form.value.end_date,
      capital: form.value.capital
    }
    emit('submit', config)
  }
}

// 重置处理
const handleReset = () => {
  form.value = {
    strategy_id: '',
    strategy_name: '',
    symbol: '',
    start_date: '',
    end_date: '',
    capital: 100000
  }
  emit('reset')
}
</script>

<style scoped>
.backtest-form {
  background: white;
  border-radius: 8px;
  padding: 24px;
}

.backtest-form h2 {
  margin-bottom: 24px;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 600;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>