// src/api/types/index.ts

// 导出回测相关类型
export * from './backtest';

// 导出因子相关类型
export * from './factors';

// 导出策略相关类型
export * from './strategy';

export interface Symbol {
    symbol: string;
    name: string;
    market: string;
    industry: string;
    list_date: string;
}

export interface KlineData {
    date: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    amount?: number | null;
    turnover_rate?: number | null;
    change_rate?: number | null;
}

// API 响应包装类型
export interface ApiResponse<T> {
    data: T;
    success: boolean;
    message: string | null;
    total?: number;
}

// 市场数据相关的响应类型
export interface SymbolsResponse extends ApiResponse<Symbol[]> {
    total: number;
}

export interface KlineDataResponse extends ApiResponse<{
    symbol: string;
    name: string;
    market: string;
    period: string;
    data: KlineData[];
    latest_date: string;
    indicators: any;
}> {}