// 市场数据API Mock处理器 - 严格按照API契约
// 路径、方法、数据结构完全按照backend/app/api/endpoints/market.py定义

import { http, HttpResponse } from 'msw';

export const marketHandlers = [
  // GET /market/kline -// 获取K线数据
  http.get('/api/market/kline', ({ request }) => {
    const url = new URL(request.url);
    const symbol = url.searchParams.get('symbol');
    const period = url.searchParams.get('period') || 'D';
    const start_date = url.searchParams.get('start_date');
    const end_date = url.searchParams.get('end_date');
    
    if (!symbol) {
      return HttpResponse.json({
        success: false,
        message: "股票代码不能为空",
        error_code: "MISSING_SYMBOL"
      }, { status: 400 });
    }
    
    if (symbol === 'INVALID') {
      return HttpResponse.json({
        success: false,
        message: "股票代码不存在",
        error_code: "SYMBOL_NOT_FOUND"
      }, { status: 404 });
    }
    
    // 模拟K线数据响应
    return HttpResponse.json({
      success: true,
      message: "获取K线数据成功",
      data: {
        symbol: symbol,
        name: symbol === '000001' ? '平安银行' : '测试股票',
        market: 'SZ',
        period: period,
        data: [
          {
            date: '2024-01-01',
            open: 10.5,
            high: 11.2,
            low: 10.3,
            close: 11.0,
            volume: 1000000,
            amount: 10500000.0,
            turnover_rate: 0.85,
            change_rate: 0.048
          },
          {
            date: '2024-01-02',
            open: 11.0,
            high: 11.5,
            low: 10.8,
            close: 11.3,
            volume: 1200000,
            amount: 13200000.0,
            turnover_rate: 1.02,
            change_rate: 0.027
          }
        ],
        latest_date: '2024-01-02',
        indicators: {
          ma5: 11.15,
          ma10: 10.95,
          ma20: 10.75,
          rsi: 65.5,
          macd: 0.15
        }
      }
    });
  }),

  // GET /market/fundamentals -// 获取基本面数据
  http.get('/api/market/fundamentals', ({ request }) => {
    const url = new URL(request.url);
    const symbol = url.searchParams.get('symbol');
    
    if (!symbol) {
      return HttpResponse.json({
        success: false,
        message: "股票代码不能为空",
        error_code: "MISSING_SYMBOL"
      }, { status: 400 });
    }
    
    return HttpResponse.json({
      success: true,
      message: "获取基本面数据成功",
      data: {
        symbol: symbol,
        name: symbol === '000001' ? '平安银行' : '测试股票',
        pe_ratio: 8.5,
        pb_ratio: 0.85,
        roe: 0.125,
        eps: 1.25,
        revenue: 125000000000,
        net_profit: 35000000000,
        market_cap: 280000000000,
        total_shares: 19405918198,
        circulating_shares: 19405918198
      }
    });
  }),

  // GET /market/stocks -// 获取股票列表
  http.get('/api/market/stocks', ({ request }) => {
    const url = new URL(request.url);
    const market = url.searchParams.get('market');
    const industry = url.searchParams.get('industry');
    const page = parseInt(url.searchParams.get('page') || '1');
    const page_size = parseInt(url.searchParams.get('page_size') || '20');
    
    if (market === 'invalid') {
      return HttpResponse.json({
        success: false,
        message: "无效的市场参数",
        error_code: "INVALID_MARKET"
      }, { status: 400 });
    }
    
    const mockStocks = [
      {
        symbol: '000001',
        name: '平安银行',
        market: 'SZ',
        industry: '银行',
        list_date: '1991-04-03'
      },
      {
        symbol: '000002',
        name: '万科A',
        market: 'SZ',
        industry: '房地产开发',
        list_date: '1991-01-29'
      },
      {
        symbol: '600000',
        name: '浦发银行',
        market: 'SH',
        industry: '银行',
        list_date: '1999-11-10'
      }
    ];
    
    // 根据筛选条件过滤
    let filteredStocks = mockStocks;
    if (market) {
      filteredStocks = filteredStocks.filter(stock => stock.market === market);
    }
    if (industry) {
      filteredStocks = filteredStocks.filter(stock => stock.industry === industry);
    }
    
    const total = filteredStocks.length;
    const start = (page - 1) * page_size;
    const end = start + page_size;
    const paginatedStocks = filteredStocks.slice(start, end);
    
    return HttpResponse.json({
      success: true,
      message: "获取股票列表成功",
      data: paginatedStocks,
      total: total,
      page: page,
      page_size: page_size,
      total_pages: Math.ceil(total / page_size)
    });
  })
];