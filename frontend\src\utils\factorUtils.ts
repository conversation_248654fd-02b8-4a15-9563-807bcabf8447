/**
 * 因子处理相关的工具函数
 * 用于在 StrategyWorkshop 和 StrategyFormDialog 之间共享逻辑
 */

import type { FactorParameter } from '@/types'

/**
 * 通过 class_name 映射到友好名称；若找不到则回退为 class_name
 */
export const getFriendlyFactorName = (factor: any): string => {
  const className = factor?.class_name || factor?.class || factor?.name
  return className || '未命名因子'
}

/**
 * 统一获取参数对象（兼容不同字段命名）
 */
export const getFactorParameters = (factor: any): Record<string, any> => {
  const params = factor?.parameters || factor?.param || factor?.kwargs || {}
  if (params && typeof params === 'object' && !Array.isArray(params)) return params
  return {}
}

/**
 * 参数值格式化显示
 */
export const formatParameterValue = (val: any): string => {
  if (val === null || val === undefined) return '-'
  if (typeof val === 'boolean') return val ? '是' : '否'
  if (Array.isArray(val)) return val.join(', ')
  if (typeof val === 'object') return JSON.stringify(val)
  return String(val)
}

/**
 * 将因子参数对象转换为标准化的参数列表
 */
export const normalizeFactorParameters = (parameters: any): FactorParameter[] => {
  if (!parameters) return []
  if (Array.isArray(parameters)) return parameters as FactorParameter[]
  if (typeof parameters === 'object') {
    return Object.entries(parameters)
      .filter(([key]) => !['capital', 'kl_pd', 'combine_kl_pd', 'benchmark'].includes(key))
      .map(([key, param]: [string, any]) => ({
        name: key,
        label: param.comment || key,
        description: param.comment || key,
        type: param.type === 'any' ? 'string' : param.type,
        default: param.default,
        default_value: param.default,
        options: param.options,
        min_value: param.min_value,
        max_value: param.max_value,
        step: param.step,
        precision: param.precision,
        active_text: param.active_text,
        inactive_text: param.inactive_text,
        required: param.required,
        comment: param.comment,
      }))
  }
  return []
}

/**
 * 验证必填参数是否已填写
 */
export const validateRequiredParameters = (
  parameters: FactorParameter[], 
  formData: Record<string, any>
): boolean => {
  return !parameters.some(param => 
    param.required && 
    (formData[param.name] === undefined || 
     formData[param.name] === null || 
     formData[param.name] === '')
  )
}

/**
 * 初始化表单数据，设置默认值
 */
export const initializeFormData = (
  parameters: FactorParameter[], 
  existingData?: Record<string, any>
): Record<string, any> => {
  const formData: Record<string, any> = {}
  parameters.forEach(param => {
    if (existingData && Object.prototype.hasOwnProperty.call(existingData, param.name)) {
      formData[param.name] = existingData[param.name]
    } else {
      formData[param.name] = param.default || param.default_value
    }
  })
  return formData
}

/**
 * 检查因子是否有参数
 */
export const hasParameters = (factor: any): boolean => {
  const params = getFactorParameters(factor)
  return Object.keys(params).length > 0
}
