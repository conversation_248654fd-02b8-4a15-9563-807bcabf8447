// Dashboard组件扩展测试
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import Dashboard from '../../src/views/Dashboard.vue';
import { useBacktestStore } from '../../src/stores/backtest';
import { useDashboardStore } from '../../src/stores/dashboard';

describe('Dashboard Extended Tests', () => {
  let wrapper: any;
  let backtestStore: any;
  let dashboardStore: any;

  beforeEach(() => {
    setActivePinia(createPinia());
    backtestStore = useBacktestStore();
    dashboardStore = useDashboardStore();
    
    wrapper = mount(Dashboard, {
      global: {
        stubs: {
          'router-link': true,
          'router-view': true
        }
      }
    });
  });

  describe('数据加载状态测试', () => {
    it('应该显示加载状态', async () => {
      // 模拟加载状态
      dashboardStore.loading = true;
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="dashboard-content"]').exists()).toBe(false);
    });

    it('应该在数据加载完成后隐藏加载状态', async () => {
      dashboardStore.loading = false;
      dashboardStore.data = {
        totalStrategies: 10,
        activeBacktests: 3,
        completedBacktests: 25
      };
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="dashboard-content"]').exists()).toBe(true);
    });

    it('应该显示错误状态', async () => {
      dashboardStore.error = '数据加载失败';
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="error-message"]').text()).toContain('数据加载失败');
    });
  });

  describe('用户交互测试', () => {
    it('应该响应刷新按钮点击', async () => {
      const refreshSpy = vi.spyOn(dashboardStore, 'refreshData');
      
      await wrapper.find('[data-testid="refresh-button"]').trigger('click');
      
      expect(refreshSpy).toHaveBeenCalled();
    });

    it('应该响应快速操作按钮', async () => {
      const createStrategySpy = vi.spyOn(wrapper.vm, 'navigateToCreateStrategy');
      
      await wrapper.find('[data-testid="create-strategy-btn"]').trigger('click');
      
      expect(createStrategySpy).toHaveBeenCalled();
    });

    it('应该支持键盘快捷键', async () => {
      const refreshSpy = vi.spyOn(dashboardStore, 'refreshData');
      
      // 模拟Ctrl+R快捷键
      await wrapper.trigger('keydown', { key: 'r', ctrlKey: true });
      
      expect(refreshSpy).toHaveBeenCalled();
    });
  });

  describe('响应式布局测试', () => {
    it('应该在小屏幕上调整布局', async () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      });
      
      window.dispatchEvent(new Event('resize'));
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="mobile-layout"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="desktop-layout"]').exists()).toBe(false);
    });

    it('应该在大屏幕上显示完整布局', async () => {
      // 模拟大屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200
      });
      
      window.dispatchEvent(new Event('resize'));
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="desktop-layout"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="mobile-layout"]').exists()).toBe(false);
    });
  });

  describe('数据展示测试', () => {
    it('应该正确显示统计数据', async () => {
      dashboardStore.data = {
        totalStrategies: 15,
        activeBacktests: 5,
        completedBacktests: 30,
        totalProfit: 125000
      };
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="total-strategies"]').text()).toContain('15');
      expect(wrapper.find('[data-testid="active-backtests"]').text()).toContain('5');
      expect(wrapper.find('[data-testid="completed-backtests"]').text()).toContain('30');
      expect(wrapper.find('[data-testid="total-profit"]').text()).toContain('125,000');
    });

    it('应该正确格式化数字显示', async () => {
      dashboardStore.data = {
        totalProfit: 1234567.89
      };
      await wrapper.vm.$nextTick();
      
      const profitText = wrapper.find('[data-testid="total-profit"]').text();
      expect(profitText).toMatch(/1,234,567\.89|1,234,567/);
    });

    it('应该处理空数据状态', async () => {
      dashboardStore.data = {
        totalStrategies: 0,
        activeBacktests: 0,
        completedBacktests: 0
      };
      await wrapper.vm.$nextTick();
      
      expect(wrapper.find('[data-testid="empty-state"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="empty-state"]').text()).toContain('暂无数据');
    });
  });

  describe('实时更新测试', () => {
    it('应该定期刷新数据', async () => {
      const refreshSpy = vi.spyOn(dashboardStore, 'refreshData');
      
      // 启用自动刷新
      wrapper.vm.enableAutoRefresh(5000); // 5秒刷新
      
      // 快进时间
      vi.advanceTimersByTime(5000);
      
      expect(refreshSpy).toHaveBeenCalled();
    });

    it('应该在组件销毁时清理定时器', async () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      wrapper.vm.enableAutoRefresh(1000);
      wrapper.unmount();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });

  describe('性能优化测试', () => {
    it('应该防抖处理频繁的刷新请求', async () => {
      const refreshSpy = vi.spyOn(dashboardStore, 'refreshData');
      
      // 快速连续点击刷新按钮
      const refreshBtn = wrapper.find('[data-testid="refresh-button"]');
      await refreshBtn.trigger('click');
      await refreshBtn.trigger('click');
      await refreshBtn.trigger('click');
      
      // 等待防抖延迟
      vi.advanceTimersByTime(300);
      
      expect(refreshSpy).toHaveBeenCalledTimes(1);
    });

    it('应该缓存数据避免重复请求', async () => {
      const fetchSpy = vi.spyOn(dashboardStore, 'fetchData');
      
      // 第一次加载
      await wrapper.vm.loadData();
      // 第二次加载（应该使用缓存）
      await wrapper.vm.loadData();
      
      expect(fetchSpy).toHaveBeenCalledTimes(1);
    });
  });
});