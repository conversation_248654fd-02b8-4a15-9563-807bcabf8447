"""代码质量检查工具

提供代码质量检查、最佳实践验证和代码度量功能。
"""

import ast
import os
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from collections import defaultdict

@dataclass
class QualityIssue:
    """代码质量问题"""
    file_path: str
    line_number: int
    issue_type: str
    severity: str  # 'error', 'warning', 'info'
    message: str
    suggestion: Optional[str] = None

@dataclass
class CodeMetrics:
    """代码度量指标"""
    lines_of_code: int = 0
    comment_lines: int = 0
    blank_lines: int = 0
    function_count: int = 0
    class_count: int = 0
    complexity_score: int = 0
    test_coverage: float = 0.0

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.issues: List[QualityIssue] = []
        self.metrics: Dict[str, CodeMetrics] = {}
        
        # 配置检查规则
        self.max_line_length = 120
        self.max_function_length = 50
        self.max_class_length = 500
        self.max_complexity = 10
        
        # 命名规则
        self.snake_case_pattern = re.compile(r'^[a-z_][a-z0-9_]*$')
        self.pascal_case_pattern = re.compile(r'^[A-Z][a-zA-Z0-9]*$')
        self.constant_pattern = re.compile(r'^[A-Z_][A-Z0-9_]*$')
    
    def check_file(self, file_path: str) -> List[QualityIssue]:
        """检查单个文件的代码质量"""
        if not file_path.endswith('.py'):
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            # 解析AST
            tree = ast.parse(content, filename=file_path)
            
            # 执行各种检查
            file_issues = []
            file_issues.extend(self._check_line_length(file_path, lines))
            file_issues.extend(self._check_imports(file_path, tree))
            file_issues.extend(self._check_naming_conventions(file_path, tree))
            file_issues.extend(self._check_function_complexity(file_path, tree))
            file_issues.extend(self._check_docstrings(file_path, tree))
            file_issues.extend(self._check_code_smells(file_path, tree, lines))
            
            # 计算度量指标
            self.metrics[file_path] = self._calculate_metrics(content, tree)
            
            self.issues.extend(file_issues)
            return file_issues
            
        except Exception as e:
            logging.error(f"Failed to check file {file_path}: {e}")
            return []
    
    def check_directory(self, directory: str, exclude_patterns: List[str] = None) -> Dict[str, List[QualityIssue]]:
        """检查目录下所有Python文件"""
        exclude_patterns = exclude_patterns or ['__pycache__', '.git', 'venv', '.venv']
        results = {}
        
        for root, dirs, files in os.walk(directory):
            # 排除指定目录
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_patterns)]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    issues = self.check_file(file_path)
                    if issues:
                        results[file_path] = issues
        
        return results
    
    def _check_line_length(self, file_path: str, lines: List[str]) -> List[QualityIssue]:
        """检查行长度"""
        issues = []
        for i, line in enumerate(lines, 1):
            if len(line) > self.max_line_length:
                issues.append(QualityIssue(
                    file_path=file_path,
                    line_number=i,
                    issue_type="line_length",
                    severity="warning",
                    message=f"Line too long ({len(line)} > {self.max_line_length} characters)",
                    suggestion="Consider breaking the line or using shorter variable names"
                ))
        return issues
    
    def _check_imports(self, file_path: str, tree: ast.AST) -> List[QualityIssue]:
        """检查导入语句"""
        issues = []
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                imports.append(node)
        
        # 检查导入顺序和分组
        stdlib_imports = []
        third_party_imports = []
        local_imports = []
        
        for imp in imports:
            if isinstance(imp, ast.Import):
                for alias in imp.names:
                    if self._is_stdlib_module(alias.name):
                        stdlib_imports.append((imp.lineno, alias.name))
                    elif self._is_local_module(alias.name):
                        local_imports.append((imp.lineno, alias.name))
                    else:
                        third_party_imports.append((imp.lineno, alias.name))
            elif isinstance(imp, ast.ImportFrom):
                module = imp.module or ''
                if self._is_stdlib_module(module):
                    stdlib_imports.append((imp.lineno, module))
                elif self._is_local_module(module):
                    local_imports.append((imp.lineno, module))
                else:
                    third_party_imports.append((imp.lineno, module))
        
        # 检查是否按推荐顺序排列
        all_imports = stdlib_imports + third_party_imports + local_imports
        if len(all_imports) > 1:
            for i in range(len(all_imports) - 1):
                if all_imports[i][0] > all_imports[i + 1][0]:
                    # 简化检查，只检查明显的顺序问题
                    pass
        
        return issues
    
    def _check_naming_conventions(self, file_path: str, tree: ast.AST) -> List[QualityIssue]:
        """检查命名约定"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not self.snake_case_pattern.match(node.name) and not node.name.startswith('_'):
                    issues.append(QualityIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type="naming",
                        severity="warning",
                        message=f"Function '{node.name}' should use snake_case",
                        suggestion="Use lowercase with underscores: my_function"
                    ))
            
            elif isinstance(node, ast.ClassDef):
                if not self.pascal_case_pattern.match(node.name):
                    issues.append(QualityIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type="naming",
                        severity="warning",
                        message=f"Class '{node.name}' should use PascalCase",
                        suggestion="Use capitalized words: MyClass"
                    ))
            
            elif isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        name = target.id
                        # 检查常量命名（全大写变量）
                        if name.isupper() and not self.constant_pattern.match(name):
                            issues.append(QualityIssue(
                                file_path=file_path,
                                line_number=node.lineno,
                                issue_type="naming",
                                severity="info",
                                message=f"Constant '{name}' should use UPPER_CASE",
                                suggestion="Use uppercase with underscores: MY_CONSTANT"
                            ))
        
        return issues
    
    def _check_function_complexity(self, file_path: str, tree: ast.AST) -> List[QualityIssue]:
        """检查函数复杂度"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_cyclomatic_complexity(node)
                if complexity > self.max_complexity:
                    issues.append(QualityIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type="complexity",
                        severity="warning",
                        message=f"Function '{node.name}' has high complexity ({complexity})",
                        suggestion="Consider breaking into smaller functions"
                    ))
                
                # 检查函数长度
                func_length = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 0
                if func_length > self.max_function_length:
                    issues.append(QualityIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type="length",
                        severity="info",
                        message=f"Function '{node.name}' is too long ({func_length} lines)",
                        suggestion="Consider breaking into smaller functions"
                    ))
        
        return issues
    
    def _check_docstrings(self, file_path: str, tree: ast.AST) -> List[QualityIssue]:
        """检查文档字符串"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if not ast.get_docstring(node):
                    node_type = "Function" if isinstance(node, ast.FunctionDef) else "Class"
                    issues.append(QualityIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type="documentation",
                        severity="info",
                        message=f"{node_type} '{node.name}' missing docstring",
                        suggestion="Add a descriptive docstring"
                    ))
        
        return issues
    
    def _check_code_smells(self, file_path: str, tree: ast.AST, lines: List[str]) -> List[QualityIssue]:
        """检查代码异味"""
        issues = []
        
        # 检查TODO/FIXME注释
        for i, line in enumerate(lines, 1):
            if 'TODO' in line or 'FIXME' in line:
                issues.append(QualityIssue(
                    file_path=file_path,
                    line_number=i,
                    issue_type="todo",
                    severity="info",
                    message="TODO/FIXME comment found",
                    suggestion="Consider creating a task or fixing the issue"
                ))
        
        # 检查过长的参数列表
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                param_count = len(node.args.args)
                if param_count > 5:
                    issues.append(QualityIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type="parameters",
                        severity="warning",
                        message=f"Function '{node.name}' has too many parameters ({param_count})",
                        suggestion="Consider using a configuration object or breaking the function"
                    ))
        
        return issues
    
    def _calculate_cyclomatic_complexity(self, node: ast.FunctionDef) -> int:
        """计算圈复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _calculate_metrics(self, content: str, tree: ast.AST) -> CodeMetrics:
        """计算代码度量指标"""
        lines = content.splitlines()
        
        metrics = CodeMetrics()
        metrics.lines_of_code = len([line for line in lines if line.strip()])
        metrics.comment_lines = len([line for line in lines if line.strip().startswith('#')])
        metrics.blank_lines = len([line for line in lines if not line.strip()])
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                metrics.function_count += 1
                metrics.complexity_score += self._calculate_cyclomatic_complexity(node)
            elif isinstance(node, ast.ClassDef):
                metrics.class_count += 1
        
        return metrics
    
    def _is_stdlib_module(self, module_name: str) -> bool:
        """检查是否为标准库模块"""
        stdlib_modules = {
            'os', 'sys', 'json', 'datetime', 'time', 'logging', 'typing',
            'collections', 'functools', 'itertools', 'pathlib', 'math',
            'random', 're', 'urllib', 'http', 'email', 'xml'
        }
        return module_name.split('.')[0] in stdlib_modules
    
    def _is_local_module(self, module_name: str) -> bool:
        """检查是否为本地模块"""
        return module_name.startswith('backend.') or module_name.startswith('.')
    
    def get_summary_report(self) -> Dict[str, Any]:
        """生成质量检查摘要报告"""
        issue_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        
        for issue in self.issues:
            issue_counts[issue.issue_type] += 1
            severity_counts[issue.severity] += 1
        
        total_metrics = CodeMetrics()
        for metrics in self.metrics.values():
            total_metrics.lines_of_code += metrics.lines_of_code
            total_metrics.comment_lines += metrics.comment_lines
            total_metrics.blank_lines += metrics.blank_lines
            total_metrics.function_count += metrics.function_count
            total_metrics.class_count += metrics.class_count
            total_metrics.complexity_score += metrics.complexity_score
        
        return {
            'total_issues': len(self.issues),
            'issues_by_type': dict(issue_counts),
            'issues_by_severity': dict(severity_counts),
            'files_checked': len(self.metrics),
            'total_metrics': {
                'lines_of_code': total_metrics.lines_of_code,
                'comment_lines': total_metrics.comment_lines,
                'blank_lines': total_metrics.blank_lines,
                'function_count': total_metrics.function_count,
                'class_count': total_metrics.class_count,
                'average_complexity': total_metrics.complexity_score / max(total_metrics.function_count, 1)
            }
        }

# 便捷函数
def check_code_quality(directory: str, exclude_patterns: List[str] = None) -> Dict[str, Any]:
    """检查代码质量的便捷函数"""
    checker = CodeQualityChecker()
    results = checker.check_directory(directory, exclude_patterns)
    summary = checker.get_summary_report()
    
    return {
        'summary': summary,
        'detailed_results': results,
        'issues': checker.issues
    }