import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { setActivePinia, createPinia } from 'pinia';
import { nextTick } from 'vue';
import { flushPromises } from '@vue/test-utils';
import { getFactors, getFactor, createCustomFactor, testFactor } from '@/api/factors';
import { useAppStore } from '@/stores/app';
import { handlers } from '../mocks/handlers';
import {
  FactorCategory,
  FactorDataType,
  type Factor,
  type CustomFactorDefinition,
  type FactorTestRequest,
  type FactorTestResult,
  type FactorQueryParams,
  type FactorsResponse,
  type FactorResponse,
  type FactorCreationResponse,
  type FactorTestResponse
} from '@/api/types/factors';

// Mock数据
const mockFactor: Factor = {
  id: 'factor-1',
  name: 'RSI',
  description: '相对强弱指数，用于衡量价格变动的速度和变化',
  category: 'technical' as any,
  data_type: 'numeric' as any,
  formula: 'RSI = 100 - (100 / (1 + RS))',
  parameters: [
    {
      name: 'period',
      type: 'number',
      default_value: 14,
      description: '计算周期',
      required: true,
      min_value: 1,
      max_value: 100
    }
  ],
  dependencies: ['close_price'],
  frequency: 'daily',
  lookback_period: 14,
  is_builtin: true,
  is_active: true,
  created_at: '2024-01-01T10:00:00Z',
  updated_at: '2024-01-01T10:00:00Z',
  usage_count: 150,
  rating: 4.5
};

const mockFactors: Factor[] = [
  mockFactor,
  {
    ...mockFactor,
    id: 'factor-2',
    name: 'MACD',
    description: '移动平均收敛散度指标',
    category: 'technical' as any
  },
  {
    ...mockFactor,
    id: 'factor-3',
    name: 'PE_Ratio',
    description: '市盈率',
    category: 'fundamental' as any
  }
];

const mockCustomFactorDefinition: CustomFactorDefinition = {
  name: 'Custom_Momentum',
  description: '自定义动量因子',
  category: 'custom' as any,
  data_type: 'numeric' as any,
  formula: 'return (close / close.shift(20) - 1) * 100',
  parameters: [
    {
      name: 'lookback_days',
      type: 'number',
      default_value: 20,
      description: '回看天数',
      required: true,
      min_value: 1,
      max_value: 252
    }
  ]
};

const mockFactorTestResult: FactorTestResult = {
  factor_id: 'factor-1',
  test_period: {
    start_date: '2024-01-01',
    end_date: '2024-12-31'
  },
  statistics: {
    mean: 0.05,
    std: 0.15,
    min: -0.5,
    max: 0.8,
    skewness: 0.2,
    kurtosis: 3.1,
    sharpe_ratio: 0.33,
    information_ratio: 0.25
  },
  correlation_with_returns: 0.15,
  ic_analysis: {
    ic_mean: 0.08,
    ic_std: 0.12,
    ic_ir: 0.67,
    ic_win_rate: 0.55
  },
  factor_values: [
    {
      factor_id: 'factor-1',
      symbol: '000001.SZ',
      date: '2024-01-01',
      value: 65.5,
      percentile: 0.75,
      z_score: 1.2
    }
  ],
  performance_metrics: {
    annual_return: 0.12,
    volatility: 0.18,
    max_drawdown: -0.08
  }
};

// 设置MSW服务器
const server = setupServer(...handlers);

describe('Factors API', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen();
  });

  afterEach(async () => {
    vi.restoreAllMocks();
    server.resetHandlers();
    await flushPromises(); // 等待所有异步操作完成
    // 重置Store状态
    const store = useAppStore();
    store.$reset();
  });

  afterAll(() => {
    server.close();
  });

  describe('getFactors', () => {
    it('should return paginated factor list with default parameters', async () => {
      const response = await getFactors();
      expect(response).toMatchObject({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            name: expect.any(String),
            category: expect.any(String),
            data_type: expect.any(String),
            created_at: expect.any(String),
            updated_at: expect.any(String),
            is_builtin: expect.any(Boolean),
            is_active: expect.any(Boolean)
          })
        ]),
        total: expect.any(Number),
        page: expect.any(Number),
        page_size: expect.any(Number)
      });
      expect(response.data.length).toBeGreaterThan(0);
    });

    it('should filter factors by technical category when category parameter provided', async () => {
      const params: FactorQueryParams = {
        category: FactorCategory.TECHNICAL
      };
      const response = await getFactors(params);
      expect(response.success).toBe(true);
      expect(response.data.every((factor: Factor) => factor.category === FactorCategory.TECHNICAL)).toBe(true);
    });

    it('should filter factors by fundamental category when category parameter provided', async () => {
      const params: FactorQueryParams = {
        category: FactorCategory.FUNDAMENTAL
      };
      const response = await getFactors(params);
      expect(response.success).toBe(true);
      expect(response.data.every((factor: Factor) => factor.category === FactorCategory.FUNDAMENTAL)).toBe(true);
    });

    it('should filter factors by macro category when category parameter provided', async () => {
      const params: FactorQueryParams = {
        category: FactorCategory.MACRO
      };
      const response = await getFactors(params);
      expect(response.success).toBe(true);
      expect(response.data.every((factor: Factor) => factor.category === FactorCategory.MACRO)).toBe(true);
    });

    it('should search factors by name when search parameter provided', async () => {
      const params: FactorQueryParams = {
        search: 'RSI'
      };
      const response = await getFactors(params);
      expect(response.success).toBe(true);
      expect(response.data.some((factor: Factor) => factor.name.includes('RSI'))).toBe(true);
    });

    it('should return paginated results when page and page_size parameters provided', async () => {
      const params: FactorQueryParams = {
        page: 2,
        page_size: 10
      };
      const response = await getFactors(params);
      expect(response.success).toBe(true);
      expect(response.page).toBe(2);
      expect(response.page_size).toBe(10);
    });

    it('should handle empty results when no factors match criteria', async () => {
      const result = await getFactors({ search: 'nonexistent_factor_name' });
      expect(result).toMatchObject({
        data: [],
        total: 0,
        page: 1,
        page_size: expect.any(Number)
      });
    });

    it('should validate page size limits', async () => {
      const result = await getFactors({ page_size: 100 });
      expect(result.page_size).toBeLessThanOrEqual(50); // Assuming max page size is 50
    });

    it('should handle multiple filter combinations', async () => {
      const result = await getFactors({
        category: FactorCategory.TECHNICAL,
        data_type: FactorDataType.NUMERIC,
        search: 'momentum'
      });
      expect(result.success).toBe(true);
      expect(result.data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            category: FactorCategory.TECHNICAL,
            data_type: FactorDataType.NUMERIC
          })
        ])
      );
    });

    it('should sort factors by created_at in descending order when sort parameters provided', { timeout: 5000 }, async () => {
      const params: FactorQueryParams = {
        sort_by: 'created_at',
        sort_order: 'desc'
      };
      const response = await getFactors(params);
      expect(response.success).toBe(true);
      // 验证排序逻辑
      for (let i = 1; i < response.data.length; i++) {
        expect(new Date(response.data[i-1].created_at).getTime()).toBeGreaterThanOrEqual(new Date(response.data[i].created_at).getTime());
      }
    });

    it('should manage loading state correctly during API calls', async () => {
      const store = useAppStore();
      const promise = getFactors();
      await nextTick();
      await flushPromises();
      expect(store.isLoading).toBe(true);
      await promise;
      await flushPromises();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('getFactor', () => {
    it('should return factor details and calculation logic when valid factor ID provided', async () => {
      const response = await getFactor('factor-1');
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: 'factor-1',
          name: expect.any(String),
          description: expect.any(String),
          category: expect.any(String),
          data_type: expect.any(String),
          formula: expect.any(String),
          parameters: expect.arrayContaining([
            expect.objectContaining({
              name: expect.any(String),
              type: expect.any(String),
              default_value: expect.anything(),
              required: expect.any(Boolean)
            })
          ]),
          created_at: expect.any(String),
          updated_at: expect.any(String),
          is_builtin: expect.any(Boolean),
          is_active: expect.any(Boolean)
        })
      });
    });

    it('should throw 404 error when factor does not exist', async () => {
      await expect(getFactor('non-existent')).rejects.toMatchObject({
        status: 404,
        message: expect.stringContaining('Factor not found')
      });
    });

    it('should manage loading state correctly during API calls', async () => {
      const store = useAppStore();
      const promise = getFactor('factor-1');
      await nextTick();
      await flushPromises();
      expect(store.isLoading).toBe(true);
      await promise;
      await flushPromises();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('createCustomFactor', () => {
    it('should create custom factor and return factor ID when valid definition provided', async () => {
      const response = await createCustomFactor(mockCustomFactorDefinition);
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: expect.any(String),
          name: mockCustomFactorDefinition.name,
          description: mockCustomFactorDefinition.description,
          category: FactorCategory.CUSTOM,
          data_type: mockCustomFactorDefinition.data_type,
          formula: mockCustomFactorDefinition.formula,
          parameters: expect.arrayContaining(mockCustomFactorDefinition.parameters),
          is_builtin: false,
          is_active: expect.any(Boolean),
          created_at: expect.any(String),
          updated_at: expect.any(String)
        })
      });
    });

    it('should throw 400 error when factor definition syntax is invalid', async () => {
      const invalidDefinition = {
        ...mockCustomFactorDefinition,
        formula: 'invalid python syntax !!!'
      };

      await expect(createCustomFactor(invalidDefinition)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid factor definition syntax')
      });
    });

    it('should validate required fields in factor definition', async () => {
      const incompleteDefinition = {
        name: 'Test Factor',
        // Missing required fields: description, formula, category, data_type
      };

      await expect(createCustomFactor(incompleteDefinition as any)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Missing required fields')
      });
    });

    it('should validate factor name uniqueness', async () => {
      const duplicateDefinition = {
        ...mockCustomFactorDefinition,
        name: 'Existing Factor Name'
      };

      await expect(createCustomFactor(duplicateDefinition)).rejects.toMatchObject({
        status: 409,
        message: expect.stringContaining('Factor name already exists')
      });
    });

    it('should validate formula complexity limits', async () => {
      const complexDefinition = {
        ...mockCustomFactorDefinition,
        formula: 'a' + 'very_long_formula_that_exceeds_limits'.repeat(100)
      };

      await expect(createCustomFactor(complexDefinition)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Formula too complex')
      });
    });

    it('should manage loading state correctly during API calls', async () => {
      const store = useAppStore();
      const promise = createCustomFactor(mockCustomFactorDefinition);
      await nextTick();
      await flushPromises();
      expect(store.isLoading).toBe(true);
      await promise;
      await flushPromises();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('testFactor', () => {
    const mockTestRequest: FactorTestRequest = {
      factor_id: 'factor-1',
      symbols: ['AAPL', 'MSFT'],
      start_date: '2023-01-01',
      end_date: '2023-01-31',
      parameters: { lookback_days: 20 }
    };

    it('should execute factor calculation test and return results when valid request provided', async () => {
      const response = await testFactor(mockTestRequest);
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          factor_id: 'factor-1',
          test_period: expect.objectContaining({
            start_date: expect.any(String),
            end_date: expect.any(String)
          }),
          statistics: expect.objectContaining({
            mean: expect.any(Number),
            std: expect.any(Number),
            min: expect.any(Number),
            max: expect.any(Number),
            skewness: expect.any(Number),
            kurtosis: expect.any(Number)
          }),
          factor_values: expect.arrayContaining([
            expect.objectContaining({
              factor_id: expect.any(String),
              symbol: expect.any(String),
              date: expect.any(String),
              value: expect.any(Number)
            })
          ])
        })
      });
      expect(response.data.factor_values.length).toBeGreaterThan(0);
    });

    it('should throw 400 error when data format is invalid', async () => {
      const invalidRequest = {
        ...mockTestRequest,
        test_data: {
          ...mockTestRequest,
          start_date: 'invalid-date'
        }
      };

      await expect(testFactor(invalidRequest)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid data format')
      });
    });

    it('should validate date range in test data', async () => {
      const invalidDateRangeRequest = {
        ...mockTestRequest,
        test_data: {
          ...mockTestRequest,
          start_date: '2023-12-31',
          end_date: '2023-01-01' // end_date before start_date
        }
      };

      await expect(testFactor(invalidDateRangeRequest)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid date range')
      });
    });

    it('should handle timeout during factor testing', async () => {
      const timeoutRequest = {
        ...mockTestRequest,
        factor_definition: {
          // 移除对不存在的 factor_definition 属性的引用
          formula: 'time.sleep(1000)' // Simulating long-running operation
        }
      };

      await expect(testFactor(timeoutRequest)).rejects.toMatchObject({
        status: 408,
        message: expect.stringContaining('Request timeout')
      });
    });

    it('should validate minimum data requirements', async () => {
      const insufficientDataRequest = {
        ...mockTestRequest,
        test_data: {
          ...mockTestRequest,
          start_date: '2023-12-30',
          end_date: '2023-12-31' // Only 1 day of data
        }
      };

      await expect(testFactor(insufficientDataRequest)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Insufficient data')
      });
    });

    it('should validate factor formula dependencies', async () => {
      const invalidDependencyRequest = {
        ...mockTestRequest,
        factor_definition: {
          // 移除对不存在的factor_definition属性的展开,因为FactorTestRequest类型中没有该属性
          formula: 'undefined_variable * 2' // Using undefined variable
        }
      };

      await expect(testFactor(invalidDependencyRequest)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Undefined variable')
      });
    });

    it('should manage loading state correctly during API calls', async () => {
      const store = useAppStore();
      const promise = testFactor(mockTestRequest);
      await nextTick();
      await flushPromises();
      expect(store.isLoading).toBe(true);
      await promise;
      await flushPromises();
      expect(store.isLoading).toBe(false);
    });
  });
});