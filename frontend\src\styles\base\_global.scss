// ===== 全局基础样式 =====

// 应用全局背景色系统
html, body {
  background-color: var(--bg-color-base);
  color: var(--text-color-primary);
  transition: background-color 0.3s ease;
}

// 主内容区域背景
#app {
  min-height: 100vh;
  background-color: var(--bg-color-base);
}

// 滚动条样式优化
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color-container);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color-base);
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: var(--text-color-placeholder);
  }
}

// 选择文本样式
::selection {
  background-color: rgba(24, 144, 255, 0.2);
  color: var(--text-color-primary);
}

// 焦点样式
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// 禁用状态
.disabled,
[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

// 隐藏元素
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 文本截断
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 响应式图片
.img-responsive {
  max-width: 100%;
  height: auto;
}

// 容器
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-md;
  
  @media (min-width: $container-sm) {
    max-width: $container-sm;
  }
  
  @media (min-width: $container-md) {
    max-width: $container-md;
  }
  
  @media (min-width: $container-lg) {
    max-width: $container-lg;
  }
  
  @media (min-width: $container-xl) {
    max-width: $container-xl;
  }
  
  @media (min-width: $container-xxl) {
    max-width: $container-xxl;
  }
}

// 加载状态
.loading {
  position: relative;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid $border-color-light;
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// ===== 统一卡片布局规范 =====
.el-card {
  background-color: var(--bg-color-card);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  
  .el-card__header {
    padding: 16px 20px;
    background-color: var(--bg-color-card);
    border-bottom: 1px solid var(--border-color-split);
    border-radius: 8px 8px 0 0;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      color: var(--text-color-primary);
    }
  }
  
  .el-card__body {
    padding: 20px;
    background-color: var(--bg-color-card);
  }
  
  &.is-hover-shadow:hover {
    box-shadow: var(--shadow-elevated);
    transform: translateY(-2px);
  }
  
  // 特殊卡片样式
  &.profit-card {
    border-left: 4px solid var(--color-profit);
    background-color: var(--bg-color-profit-light);
  }
  
  &.loss-card {
    border-left: 4px solid var(--color-loss);
    background-color: var(--bg-color-loss-light);
  }
  
  &.neutral-card {
    border-left: 4px solid var(--color-neutral);
  }
}

// 统一表单布局规范
.el-form {
  .el-form-item {
    margin-bottom: var(--space-lg, 24px);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .el-form-item__label {
    font-weight: 500;
    color: var(--text-color-primary, #262626);
  }
  
  .el-form-item__content {
    .el-input,
    .el-select,
    .el-textarea {
      width: 100%;
    }
  }
}

// 页面标题规范
.el-page-header {
  padding: var(--space-lg, 24px) 0;
  border-bottom: 1px solid var(--border-color-light, #f0f0f0);
  margin-bottom: var(--space-lg, 24px);
  
  .el-page-header__title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color-primary, #262626);
  }
  
  .el-page-header__content {
    color: var(--text-color-secondary, #595959);
  }
}

// 两栏布局规范
.two-column-layout {
  display: flex;
  height: 100%;
  gap: var(--space-md, 16px);
  
  .left-panel {
    flex: 0 0 300px;
    overflow-y: auto;
    border-right: 1px solid var(--border-color-light, #f0f0f0);
    padding-right: var(--space-md, 16px);
  }
  
  .right-panel {
    flex: 1;
    overflow-y: auto;
    padding-left: var(--space-md, 16px);
  }
}

// 统一间距工具类
.p-xs { padding: var(--space-xs, 4px) !important; }
.p-sm { padding: var(--space-sm, 8px) !important; }
.p-md { padding: var(--space-md, 16px) !important; }
.p-lg { padding: var(--space-lg, 24px) !important; }
.p-xl { padding: var(--space-xl, 32px) !important; }

.m-xs { margin: var(--space-xs, 4px) !important; }
.m-sm { margin: var(--space-sm, 8px) !important; }
.m-md { margin: var(--space-md, 16px) !important; }
.m-lg { margin: var(--space-lg, 24px) !important; }
.m-xl { margin: var(--space-xl, 32px) !important; }

.mt-xs { margin-top: var(--space-xs, 4px) !important; }
.mt-sm { margin-top: var(--space-sm, 8px) !important; }
.mt-md { margin-top: var(--space-md, 16px) !important; }
.mt-lg { margin-top: var(--space-lg, 24px) !important; }
.mt-xl { margin-top: var(--space-xl, 32px) !important; }

.mb-xs { margin-bottom: var(--space-xs, 4px) !important; }
.mb-sm { margin-bottom: var(--space-sm, 8px) !important; }
.mb-md { margin-bottom: var(--space-md, 16px) !important; }
.mb-lg { margin-bottom: var(--space-lg, 24px) !important; }
.mb-xl { margin-bottom: var(--space-xl, 32px) !important; }