import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
import pytest

# 确保从正确的路径导入
from backend.app.services.market.kline_provider import KlineProvider
from backend.app.schemas.market import KlineData, KlineItem
from backend.app.core.exceptions import SymbolError, ValidationError, DataNotFoundError, ExternalAPIError


class TestKlineProvider(unittest.TestCase):

    @patch('backend.app.services.market.kline_provider.KlineProvider.convert_to_tushare_symbol', return_value='600519.SH')
    @patch('backend.app.services.market.kline_provider.KlineProvider.get_stock_kline_from_tushare')
    def test_get_kline_data_success_from_tushare(self, mock_get_stock_kline, mock_convert_symbol):
        """测试从Tushare成功获取K线数据"""
        # 模拟 tushare 返回的数据模型
        mock_kline_data = KlineData(
            symbol='600519.SH',
            name='贵州茅台',
            market='CN',
            period='daily',
            data=[
                KlineItem(date='2023-01-03', open=10.0, high=10.5, low=9.8, close=10.2, volume=10000),
                KlineItem(date='2023-01-04', open=11.0, high=11.5, low=10.8, close=11.2, volume=12000)
            ],
            latest_date='2023-01-04'
        )
        mock_get_stock_kline.return_value = mock_kline_data

        # 调用被测试方法
        # 注意：我们直接测试 get_kline_data，它内部会调用 get_stock_kline_from_tushare
        result = KlineProvider.get_kline_data(symbol='sh600519', data_source='tushare')

        # 断言结果
        self.assertIsInstance(result, KlineData)
        # 修正：使用 .data 属性
        self.assertEqual(len(result.data), 2)
        self.assertEqual(result.data[0].date, '2023-01-03')
        self.assertEqual(result.data[0].close, 10.2)
        mock_get_stock_kline.assert_called_once()


    def test_get_kline_data_invalid_symbol(self):
        """测试无效的股票代码"""
        with self.assertRaises(SymbolError):
            KlineProvider.get_kline_data(symbol=None)

        with self.assertRaises(SymbolError):
            KlineProvider.get_kline_data(symbol='')
        
        # 修正：SymbolAdapter.validate_symbol 会处理这个
        # 我们假设 validate_symbol 会对 'invalid-symbol' 抛出 SymbolError
        with patch('backend.app.abupy_adapter.symbol_adapter.SymbolAdapter.validate_symbol', side_effect=SymbolError(message="invalid")):
             with self.assertRaises(SymbolError):
                KlineProvider.get_kline_data(symbol='invalid-symbol')


    def test_get_kline_data_invalid_period(self):
        """测试无效的周期参数"""
        with self.assertRaises(ValidationError):
            KlineProvider.get_kline_data(symbol='sh600519', period='invalid_period')

    @patch('backend.app.services.market.kline_provider.KlineProvider.get_stock_kline_from_tushare', side_effect=ExternalAPIError(message="API Error"))
    def test_get_kline_data_tushare_api_error(self, mock_get_stock_kline):
        """测试Tushare API调用失败"""
        with self.assertRaises(ExternalAPIError):
            KlineProvider.get_kline_data(symbol='sh600519', data_source='tushare')

    @patch('pandas.HDFStore')
    def test_get_kline_from_local_h5_success(self, mock_hdfstore):
        """测试从本地HDF5文件成功获取数据"""
        # 本地HDF5返回的DataFrame通常没有索引名，且日期是列
        mock_df = pd.DataFrame({
            'trade_date': pd.to_datetime(['2023-01-03', '2023-01-04']),
            'open': [10.0, 11.0],
            'high': [10.5, 11.5],
            'low': [9.8, 10.8],
            'close': [10.2, 11.2],
            'vol': [10000, 12000] # 注意 kline_provider 使用 vol
        })

        # 模拟HDFStore的行为
        mock_store_instance = MagicMock()
        # 模拟 with 语句上下文
        mock_context = mock_store_instance.__enter__.return_value
        mock_context.keys.return_value = ['/600519']
        mock_context.__getitem__.return_value = mock_df
        mock_hdfstore.return_value = mock_store_instance
        
        # 模拟 settings
        with patch('backend.app.services.market.kline_provider.settings.LOCAL_DATA_PATH', 'dummy/path.h5'):
            result = KlineProvider.get_kline_from_local_h5(
                symbol='600519.SH', 
                start_date='20230101', 
                end_date='20230105',
                period='daily'
            )

        self.assertIsInstance(result, KlineData)
        # 修正：使用 .data 属性
        self.assertEqual(len(result.data), 2)
        self.assertEqual(result.data[0].date, '2023-01-03')


    @patch('pandas.HDFStore')
    @patch('backend.app.services.market.kline_provider.KlineProvider.get_stock_kline_from_tushare')
    @patch('backend.app.services.market.kline_provider.KlineProvider.save_kline_to_local_h5')
    def test_get_kline_from_local_h5_fallback_to_tushare(self, mock_save, mock_get_from_tushare, mock_hdfstore):
        """测试本地无数据时回退到Tushare"""
        # 模拟本地HDFStore中没有数据
        mock_store_instance = MagicMock()
        mock_context = mock_store_instance.__enter__.return_value
        mock_context.keys.return_value = []
        mock_hdfstore.return_value = mock_store_instance

        # 修正：创建合法的模拟KlineData对象
        mock_kline_data = KlineData(
            symbol='600519.SH', 
            name='贵州茅台',
            market='CN',
            period='daily',
            data=[],  # 使用正确的 'data' 字段
            latest_date=''
        )
        mock_get_from_tushare.return_value = mock_kline_data

        with patch('backend.app.services.market.kline_provider.settings.LOCAL_DATA_PATH', 'dummy/path.h5'):
            result = KlineProvider.get_kline_from_local_h5(
                symbol='600519.SH', 
                start_date='20230101', 
                end_date='20230105',
                period='daily'
            )
            # 验证回退逻辑被触发
            mock_get_from_tushare.assert_called_once()
            # 验证保存函数被调用
            mock_save.assert_called_once_with('600519.SH', mock_kline_data)
            # 验证返回结果
            self.assertEqual(result, mock_kline_data)


if __name__ == '__main__':
    unittest.main()