"""错误处理中间件和工具

提供统一的错误处理、日志记录和错误响应格式化功能。
"""

import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from backend.app.constants.messages import SystemMessages, get_error_message
from backend.app.core.exceptions import (
    AdapterError, ParameterError, FactorError, 
    SymbolError, DataNotFoundError
)

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """全局错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except HTTPException:
            # FastAPI的HTTPException直接抛出，不需要处理
            raise
        except Exception as exc:
            # 记录详细错误信息
            error_id = self._log_error(exc, request)
            
            # 根据异常类型返回适当的响应
            return self._create_error_response(exc, error_id)
    
    def _log_error(self, exc: Exception, request: Request) -> str:
        """记录错误信息并返回错误ID"""
        import uuid
        error_id = str(uuid.uuid4())[:8]
        
        logging.error(
            f"Error ID: {error_id} | "
            f"Path: {request.url.path} | "
            f"Method: {request.method} | "
            f"Exception: {type(exc).__name__} | "
            f"Message: {str(exc)}",
            exc_info=True
        )
        
        return error_id
    
    def _create_error_response(self, exc: Exception, error_id: str) -> JSONResponse:
        """根据异常类型创建错误响应"""
        if isinstance(exc, (AdapterError, ParameterError, FactorError)):
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": str(exc),
                    "error_id": error_id,
                    "error_type": type(exc).__name__
                }
            )
        elif isinstance(exc, (SymbolError, DataNotFoundError)):
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "message": str(exc),
                    "error_id": error_id,
                    "error_type": type(exc).__name__
                }
            )
        else:
            # 未知错误，返回通用错误信息
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "message": SystemMessages.INTERNAL_ERROR,
                    "error_id": error_id,
                    "error_type": "InternalServerError"
                }
            )

class ErrorLogger:
    """错误日志记录工具"""
    
    @staticmethod
    def log_strategy_error(strategy_name: str, error: Exception, context: Dict[str, Any] = None):
        """记录策略相关错误"""
        context_str = f" | Context: {context}" if context else ""
        logging.error(
            f"Strategy Error | Name: {strategy_name} | "
            f"Exception: {type(error).__name__} | "
            f"Message: {str(error)}{context_str}",
            exc_info=True
        )
    
    @staticmethod
    def log_market_data_error(symbol: str, error: Exception, context: Dict[str, Any] = None):
        """记录市场数据相关错误"""
        context_str = f" | Context: {context}" if context else ""
        logging.error(
            f"Market Data Error | Symbol: {symbol} | "
            f"Exception: {type(error).__name__} | "
            f"Message: {str(error)}{context_str}",
            exc_info=True
        )
    
    @staticmethod
    def log_database_error(operation: str, error: Exception, context: Dict[str, Any] = None):
        """记录数据库相关错误"""
        context_str = f" | Context: {context}" if context else ""
        logging.error(
            f"Database Error | Operation: {operation} | "
            f"Exception: {type(error).__name__} | "
            f"Message: {str(error)}{context_str}",
            exc_info=True
        )

class ValidationHelper:
    """数据验证辅助工具"""
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> bool:
        """验证日期范围"""
        try:
            from datetime import datetime
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            return start < end
        except ValueError:
            return False
    
    @staticmethod
    def validate_symbol_format(symbol: str) -> bool:
        """验证股票代码格式"""
        if not symbol or len(symbol) < 6:
            return False
        # 简单的A股代码验证
        return symbol.isdigit() or '.' in symbol
    
    @staticmethod
    def validate_capital_amount(capital: float) -> bool:
        """验证资金数额"""
        return capital > 0 and capital <= 1e10  # 最大100亿

def create_standardized_error(error_type: str, details: str = None, 
                            suggestions: Optional[str] = None) -> Dict[str, Any]:
    """创建标准化的错误响应"""
    error_response = {
        "success": False,
        "error_type": error_type,
        "message": details or SystemMessages.INTERNAL_ERROR,
        "timestamp": str(__import__('datetime').datetime.now())
    }
    
    if suggestions:
        error_response["suggestions"] = suggestions
    
    return error_response

def handle_abupy_exception(exc: Exception) -> AdapterError:
    """处理AbuPy相关异常"""
    error_msg = str(exc)
    
    # 根据错误消息类型进行分类
    if "module" in error_msg.lower():
        return AdapterError(message=get_error_message('strategy', 'factor_conversion_failed', error_msg))
    elif "data" in error_msg.lower():
        return AdapterError(message=get_error_message('market_data', 'data_load_failed', error_msg))
    else:
        return AdapterError(message=get_error_message('strategy', 'abupy_core_error', error_msg))