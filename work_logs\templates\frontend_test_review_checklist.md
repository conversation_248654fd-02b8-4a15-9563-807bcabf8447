# 前端测试脚本审查清单 (Frontend Test QA Checklist)

当您拿到测试AI生成的测试脚本（例如strategy.test.ts、components.test.ts）后，请打开它，并带着以下三层审查结构去审阅：

## 第一层：宏观审查 —— "它测试了正确的东西吗？" (25分)

### 1. ✅ 用户故事覆盖了吗？ (Does it cover the User Story?) - 10分
**对照**： 将测试脚本与我们下达给测试AI的"用户故事"或任务清单进行比对。  
**检查**： 测试用例（describe/it块）是否覆盖了我们要求的所有核心场景？例如，我们要求测试getStrategies的"成功"和"500错误"场景，这两个it块都存在吗？  
**目的**： 确保测试没有偏离我们的业务需求。

### 2. ✅ 文件结构和命名规范吗？ (Is the structure and naming conventional?) - 8分
**检查**：
- 文件名是否遵循了[name].test.ts的格式？
- 测试描述（describe和it里的字符串）是否清晰、易懂，能用自然语言描述出测试的目的？（例如，describe('getStrategies', ...) 和 it('should return an array of strategies on success', ...)）  
**目的**： 保证测试代码的可读性和可维护性。

### 3. ✅ 业务复杂度匹配吗？ (Does complexity match business requirements?) - 7分
**检查**：
- 对于复杂业务(如backtest)，是否有30+测试用例？
- 是否覆盖了领域特定的边界条件？
- 测试深度是否与业务重要性匹配？  
**目的**： 确保测试投入与业务价值成正比。

## 第二层：微观审查 —— "它测试的方式正确吗？" (53分)

### 1. ✅ Mock策略合理吗？ (Is the mocking strategy sound?) - 12分
**检查**：
- 测试是否正确地使用了MSW (Mock Service Worker)来模拟后端的API响应？
- 对于成功场景，它模拟返回的数据结构，是否与我们后端API的真实响应结构完全一致？（这是关键！）
- 对于失败场景，它是否正确地模拟了不同的HTTP状态码（如404, 500）？
- **警惕**： 测试是否Mock了"过多"或"过少"的东西？它应该只Mock掉与被测系统直接交互的"边界"（即HTTP请求），而不是去Mock被测函数内部的实现细节。

### 2. ✅ 断言（Assertions）有意义吗？ (Are the assertions meaningful?) - 10分
**检查**：
- expect(...)语句是否足够严格？
- **反例（弱断言）**： `expect(result).toBeDefined();` (只检查了有返回值，但没检查返回的是什么)
- **正例（强断言）**： `expect(result).toEqual([{ id: '1', name: 'Strategy A' }]);` (精确地检查了返回值的结构和内容)
- 是否检查了所有重要的"副作用"？例如，调用一个action后，不仅要检查最终的state，还要检查loading状态是否按预期的true -> false变化了。
- **错误断言要求**：
  ```javascript
  // 正例（强错误断言）：
  await expect(runBacktest(invalidConfig)).rejects.toMatchObject({
    status: 400,
    message: expect.stringContaining('Invalid configuration')
  });
  
  // 反例（弱错误断言）：
  await expect(runBacktest(invalidConfig)).rejects.toThrow();
  ```

### 3. ✅ 异步处理正确吗？ (Is async handling correct?) - 12分
**检查**：
- 所有与异步操作（如API调用）相关的测试，是否都正确地使用了async/await？
- **Vue组件异步验证模式**：
  ```javascript
  // Vue组件prop/emit测试
  await wrapper.setProps({ loading: true });
  await nextTick();
  expect(wrapper.find('[data-testid="spinner"]').exists()).toBe(true);
  
  // 事件触发测试
  await wrapper.find('button').trigger('click');
  await nextTick();
  expect(wrapper.emitted()).toHaveProperty('submit');
  ```
- **Vue状态管理异步验证模式**：
  ```javascript
  // 正确的异步验证顺序：
  const promise = store.someAction(); // 1. 启动异步操作
  await nextTick(); // 2. 等待Vue响应式更新
  await flushPromises(); // 3. 等待所有Promise完成
  expect(store.loading).toBe(false); // 4. 验证状态
  ```
- loading状态检查是否有 `await flushPromises()`？
- Store状态重置是否在afterEach中正确执行？  
**目的**： 避免因为异步处理不当，导致测试结果不稳定（有时通过，有时失败）。

### 4. ✅ 测试是否是独立的？ (Are the tests independent?) - 11分
**检查**：
- 是否在beforeEach或afterEach中，正确地重置了所有的mock和状态？
- **测试隔离完整性**：
  - afterEach中是否包含 `store.$reset()`？
  - 是否有 `await flushPromises()` 等待异步完成？
  - 能否连续运行25次且通过率100%？
- 一个测试用例的执行，是否会影响到另一个测试用例的结果？（理想情况下，任意改变测试用例的执行顺序，结果都应该是一样的）
- **异步操作泄漏检查**：确保没有未完成的Promise影响后续测试。

### 5. ✅ Vue组件测试完整吗？ (Is Vue component testing complete?) - 8分
**检查**：
- 组件渲染测试：关键UI元素是否正确渲染？
- Props验证：必需props和默认props是否测试？
- 事件测试：用户交互和自定义事件是否验证？
- 插槽测试：slot内容是否正确显示？
- 条件渲染：v-if/v-show逻辑是否验证？
- **组件测试示例**：
  ```javascript
  it('should render with required props', () => {
    const wrapper = mount(Component, {
      props: { title: 'Test Title', required: true }
    });
    expect(wrapper.find('[data-testid="title"]').text()).toBe('Test Title');
    expect(wrapper.find('.required-indicator').exists()).toBe(true);
  });
  
  it('should emit events correctly', async () => {
    await wrapper.find('button').trigger('click');
    expect(wrapper.emitted().submit[0]).toEqual([{ id: 1, name: 'test' }]);
  });
  ```

## 第三层：高级审查 —— "它能应对复杂挑战吗？" (22分)

### 1. ✅ Mock架构设计合理吗？ (Is the Mock architecture well-designed?) - 8分
**检查**：
- 是否过度依赖测试内部Mock而非handlers.ts？
- 是否有大量重复的server.use()调用？
- Mock逻辑是否与handlers.ts保持一致？
- 是否遵循了"单一Mock源"原则？
- **反例（架构混乱）**：
  ```javascript
  // 在测试内部重复定义Mock
  server.use(
    rest.get('/api/backtest', (req, res, ctx) => {
      return res(ctx.json({ success: true }));
    })
  );
  ```
- **正例（架构清晰）**：依赖handlers.ts统一Mock系统

### 2. ✅ 业务场景覆盖是否全面？ (Is business scenario coverage comprehensive?) - 8分
**检查**：
- **权限控制场景**(403)是否覆盖？
- **并发限制场景**(429)是否验证？
- **数据验证边界条件**是否完整？
- **状态转换逻辑**是否正确验证？
- **前端业务特殊要求**：
  - 表单验证：必填字段、格式验证、实时校验
  - 列表管理：分页、搜索、排序、筛选
  - 状态管理：loading、error、success状态转换
  - 用户交互：按钮禁用、消息提示、确认对话框
  - 路由导航：页面跳转、参数传递、权限检查

### 3. ✅ 测试稳定性是否可靠？ (Is test stability reliable?) - 6分
**检查**：
- 是否能在CI/CD环境稳定运行？
- 是否有随机失败的风险？
- 超时设置是否合理？
- 错误处理是否覆盖网络异常？
- **稳定性验证要求**：
  - 连续运行50次，通过率应达到100%
  - 不同执行顺序下结果一致
  - 内存使用稳定，无泄漏风险
  - 执行时间在合理范围内（单个测试<5秒）

---

# 审查评分与判断

## 📊 评分标准
- **90-100分**: 优秀 ✅ - 可直接交付实现
- **80-89分**: 良好 ⚠️ - 需要小幅优化
- **70-79分**: 及格 🔴 - 需要重要改进
- **<70分**: 不合格 ❌ - 需要重新设计

## 🎯 决策流程

### ✅ 如果评分≥90分
**结论**: 测试脚本质量优秀，可以充满信心地交给实现者AI。

### ⚠️ 如果评分80-89分
**行动**: 提出具体的优化建议，例如：
- "测试覆盖很好，但请为createStrategy补充一个'验证失败（400错误）'的测试用例。"
- "断言需要加强，请将`expect(result).toBeDefined()`改为`expect(result).toEqual(expectedData)`。"

### 🔴 如果评分70-79分
**行动**: 要求重要改进，例如：
- "异步处理有问题，请按照Vue状态管理异步验证模式重新实现。"
- "Mock架构混乱，请统一使用handlers.ts而非测试内部Mock。"

### ❌ 如果评分<70分
**行动**: 要求重新设计，明确指出核心问题和改进方向。

## 特殊场景处理

### 🔥 复杂业务测试 (如backtest.test.ts)
- 必须达到85分以上才能通过
- 重点检查第三层高级审查要点
- 特别关注业务场景覆盖的完整性

### ⚡ 简单API测试 (如基础CRUD)
- 80分以上即可通过
- 重点检查第一、二层基础要点
- 确保Mock策略和断言质量

### 🖥️ Vue组件测试 (如UI组件)
- 85分以上才能通过
- 重点检查第二层第5点Vue组件测试要点
- 特别关注组件渲染、事件处理、响应式更新

---

通过这个**三层审查体系**，可以从源头上保证前端代码的质量，特别是应对现代前端测试的复杂挑战：**Vue组件测试**、**异步状态管理**和**Mock架构设计**。