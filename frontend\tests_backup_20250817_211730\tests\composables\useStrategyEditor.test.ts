import { describe, it, expect, beforeEach } from 'vitest'
import { useStrategyEditor, strategyUtils } from '@/composables/useStrategyEditor'
import type { Strategy } from '@/api/types'

describe('useStrategyEditor', () => {
  let strategy: Strategy
  
  beforeEach(() => {
    strategy = {
      id: '1',
      name: '测试策略',
      description: '测试描述',
      author: '测试用户',
      create_time: '2023-01-01T00:00:00Z',
      is_public: false,
      buy_factors: [
        {
          class_name: 'AbuFactorBuyBreak',
          parameters: { period: 20 }
        }
      ],
      sell_factors: [],
      parameters: {}
    }
  })

  describe('基础状态管理', () => {
    it('应该正确初始化状态', () => {
      const { editingStrategy, isDirty, isSaving, errors, canSave } = useStrategyEditor()

      expect(editingStrategy.value).toBeNull()
      expect(isDirty.value).toBe(false)
      expect(isSaving.value).toBe(false)
      expect(errors.value).toEqual([])
      expect(canSave.value).toBe(false)
    })

    it('应该能够更新策略', () => {
      const { updateStrategy, editingStrategy } = useStrategyEditor()

      updateStrategy(strategy)

      expect(editingStrategy.value).toEqual(strategy)
    })

    it('应该正确检测策略变更', () => {
      const { updateStrategy, editingStrategy, isDirty } = useStrategyEditor()

      // 首次设置不应该标记为dirty
      updateStrategy(strategy)
      expect(isDirty.value).toBe(false)

      // 修改策略应该标记为dirty
      const modifiedStrategy = { ...strategy, name: '修改后的策略' }
      updateStrategy(modifiedStrategy)
      expect(isDirty.value).toBe(true)
    })
  })

  describe('策略验证', () => {
    it('应该验证策略名称为必填', () => {
      const { updateStrategy, canSave, errors } = useStrategyEditor()
      
      const invalidStrategy = { ...strategy, name: '' }
      updateStrategy(invalidStrategy)

      expect(canSave.value).toBe(false)
      expect(errors.value).toContainEqual({
        field: 'name',
        message: '策略名称不能为空'
      })
    })

    it('应该验证至少有一个买入因子', () => {
      const { updateStrategy, canSave, errors } = useStrategyEditor()
      
      const invalidStrategy = { ...strategy, buy_factors: [] }
      updateStrategy(invalidStrategy)

      expect(canSave.value).toBe(false)
      expect(errors.value).toContainEqual({
        field: 'buy_factors',
        message: '至少需要添加一个买入因子'
      })
    })

    it('应该在策略有效时允许保存', () => {
      const { updateStrategy, canSave, errors } = useStrategyEditor()
      
      updateStrategy(strategy)

      expect(canSave.value).toBe(true)
      expect(errors.value).toHaveLength(0)
    })
  })

  describe('保存功能', () => {
    it('应该能够保存有效策略', async () => {
      const { updateStrategy, saveStrategy, isDirty, isSaving } = useStrategyEditor()
      
      updateStrategy(strategy)
      
      // 修改策略使其变为dirty
      const modifiedStrategy = { ...strategy, name: '修改后的策略' }
      updateStrategy(modifiedStrategy)
      expect(isDirty.value).toBe(true)

      // 保存策略
      await saveStrategy()

      expect(isDirty.value).toBe(false)
      expect(isSaving.value).toBe(false)
    })

    it('应该在保存无效策略时抛出错误', async () => {
      const { updateStrategy, saveStrategy } = useStrategyEditor()
      
      const invalidStrategy = { ...strategy, name: '' }
      updateStrategy(invalidStrategy)

      await expect(saveStrategy()).rejects.toThrow('无法保存策略：验证失败')
    })

    it('应该在保存过程中设置loading状态', async () => {
      const { updateStrategy, saveStrategy, isSaving } = useStrategyEditor()
      
      updateStrategy(strategy)
      
      // 验证初始状态
      expect(isSaving.value).toBe(false)
      
      // 由于saveStrategy内部已经处理了loading状态的设置和重置
      // 我们只需要验证保存操作能够成功完成
      await saveStrategy()
      
      // 保存完成后loading状态应该重置为false
      expect(isSaving.value).toBe(false)
    })
  })

  describe('新策略检测', () => {
    it('应该正确识别新策略', () => {
      const { updateStrategy, isNewStrategy } = useStrategyEditor()
      
      const newStrategy = { ...strategy, id: undefined }
      updateStrategy(newStrategy)

      expect(isNewStrategy.value).toBe(true)
    })

    it('应该正确识别临时策略', () => {
      const { updateStrategy, isNewStrategy } = useStrategyEditor()
      
      const tempStrategy = { ...strategy, id: 'temp-123' }
      updateStrategy(tempStrategy)

      expect(isNewStrategy.value).toBe(true)
    })

    it('应该正确识别现有策略', () => {
      const { updateStrategy, isNewStrategy } = useStrategyEditor()
      
      updateStrategy(strategy)

      expect(isNewStrategy.value).toBe(false)
    })
  })

  describe('重置功能', () => {
    it('应该能够重置更改', () => {
      const { updateStrategy, resetChanges, editingStrategy, isDirty } = useStrategyEditor()
      
      // 设置原始策略
      updateStrategy(strategy)
      
      // 修改策略
      const modifiedStrategy = { ...strategy, name: '修改后的策略' }
      updateStrategy(modifiedStrategy)
      expect(isDirty.value).toBe(true)
      
      // 重置更改
      resetChanges()
      
      expect(editingStrategy.value).toEqual(strategy)
      expect(isDirty.value).toBe(false)
    })

    it('应该能够清空编辑器', () => {
      const { updateStrategy, clearEditor, editingStrategy, isDirty } = useStrategyEditor()
      
      updateStrategy(strategy)
      
      clearEditor()

      expect(editingStrategy.value).toBeNull()
      expect(isDirty.value).toBe(false)
    })
  })
})

describe('strategyUtils', () => {
  let strategy: Strategy

  beforeEach(() => {
    strategy = {
      id: '1',
      name: '测试策略',
      description: '测试描述',
      author: '测试用户',
      create_time: '2023-01-01T00:00:00Z',
      is_public: false,
      buy_factors: [
        {
          class_name: 'AbuFactorBuyBreak',
          description: '移动平均线突破',
          parameters: { period: 20 }
        }
      ],
      sell_factors: [
        {
          class_name: 'AbuFactorSellXd',
          description: '止损',
          parameters: { xd: 0.1 }
        }
      ],
      parameters: {}
    }
  })

  describe('isNewStrategy', () => {
    it('应该正确识别新策略', () => {
      expect(strategyUtils.isNewStrategy(null)).toBe(true)
      expect(strategyUtils.isNewStrategy({ ...strategy, id: undefined })).toBe(true)
      expect(strategyUtils.isNewStrategy({ ...strategy, id: 'temp-123' })).toBe(true)
      expect(strategyUtils.isNewStrategy(strategy)).toBe(false)
    })
  })

  describe('generateDescription', () => {
    it('应该生成包含买入和卖出规则的描述', () => {
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toContain('买入规则：移动平均线突破')
      expect(description).toContain('卖出规则：止损')
    })

    it('应该处理只有买入因子的情况', () => {
      const strategyWithBuyOnly = { ...strategy, sell_factors: [] }
      const description = strategyUtils.generateDescription(strategyWithBuyOnly)
      
      expect(description).toContain('买入规则：移动平均线突破')
      expect(description).not.toContain('卖出规则')
    })

    it('应该处理没有因子的情况', () => {
      const strategyWithoutFactors = { ...strategy, buy_factors: [], sell_factors: [] }
      const description = strategyUtils.generateDescription(strategyWithoutFactors)
      
      expect(description).toBe('请添加买入和卖出因子来完善此策略。')
    })

    it('应该使用class_name作为备用描述', () => {
      const strategyWithoutDesc = {
        ...strategy,
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', parameters: {} }]
      }
      const description = strategyUtils.generateDescription(strategyWithoutDesc)
      
      expect(description).toContain('AbuFactorBuyBreak')
    })
  })

  describe('handleError', () => {
    it('应该正确处理错误', () => {
      const error = new Error('测试错误')
      const result = strategyUtils.handleError(error, '保存策略')
      
      expect(result).toEqual({
        message: '测试错误',
        context: '保存策略'
      })
    })

    it('应该处理没有message的错误', () => {
      const error = new Error()
      error.message = ''
      const result = strategyUtils.handleError(error, '测试上下文')
      
      expect(result).toEqual({
        message: '操作失败',
        context: '测试上下文'
      })
    })
  })
})
