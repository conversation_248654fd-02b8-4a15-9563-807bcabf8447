#!/usr/bin/env python3
"""
代码复杂度分析工具
分析项目中最需要重构的文件
"""

import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class FileMetrics:
    """文件指标"""
    path: str
    lines_of_code: int
    functions_count: int
    classes_count: int
    max_function_complexity: int
    avg_function_complexity: float
    imports_count: int
    duplicated_patterns: int
    long_functions: int  # 超过50行的函数数量
    nested_depth: int    # 最大嵌套深度
    refactor_score: float  # 重构紧迫度评分

class ComplexityAnalyzer(ast.NodeVisitor):
    """AST复杂度分析器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.functions = []
        self.classes = []
        self.imports = []
        self.current_function = None
        self.current_complexity = 0
        self.current_depth = 0
        self.max_depth = 0
        self.current_function_lines = 0
        
    def visit_FunctionDef(self, node):
        # 保存当前状态
        old_function = self.current_function
        old_complexity = self.current_complexity
        old_depth = self.current_depth
        old_lines = self.current_function_lines
        
        # 设置新函数状态
        self.current_function = node.name
        self.current_complexity = 1  # 基础复杂度
        self.current_depth = 0
        self.current_function_lines = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 0
        
        # 访问函数体
        self.generic_visit(node)
        
        # 记录函数信息
        self.functions.append({
            'name': node.name,
            'complexity': self.current_complexity,
            'lines': self.current_function_lines,
            'max_depth': self.max_depth
        })
        
        # 恢复状态
        self.current_function = old_function
        self.current_complexity = old_complexity
        self.current_depth = old_depth
        self.current_function_lines = old_lines
    
    def visit_ClassDef(self, node):
        self.classes.append({
            'name': node.name,
            'methods': len([n for n in node.body if isinstance(n, ast.FunctionDef)])
        })
        self.generic_visit(node)
    
    def visit_Import(self, node):
        self.imports.extend([alias.name for alias in node.names])
    
    def visit_ImportFrom(self, node):
        if node.module:
            self.imports.append(node.module)
    
    def visit_If(self, node):
        self.current_complexity += 1
        self._visit_with_depth(node)
    
    def visit_For(self, node):
        self.current_complexity += 1
        self._visit_with_depth(node)
    
    def visit_While(self, node):
        self.current_complexity += 1
        self._visit_with_depth(node)
    
    def visit_Try(self, node):
        self.current_complexity += 1
        self._visit_with_depth(node)
    
    def visit_With(self, node):
        self.current_complexity += 1
        self._visit_with_depth(node)
    
    def _visit_with_depth(self, node):
        self.current_depth += 1
        self.max_depth = max(self.max_depth, self.current_depth)
        self.generic_visit(node)
        self.current_depth -= 1

def analyze_file(file_path: str) -> FileMetrics:
    """分析单个文件的复杂度"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 基本指标
        lines = content.split('\n')
        lines_of_code = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        
        # AST分析
        try:
            tree = ast.parse(content)
            analyzer = ComplexityAnalyzer()
            analyzer.visit(tree)
            
            functions_count = len(analyzer.functions)
            classes_count = len(analyzer.classes)
            imports_count = len(set(analyzer.imports))
            
            if analyzer.functions:
                complexities = [f['complexity'] for f in analyzer.functions]
                max_function_complexity = max(complexities)
                avg_function_complexity = sum(complexities) / len(complexities)
                long_functions = len([f for f in analyzer.functions if f['lines'] > 50])
                nested_depth = max([f['max_depth'] for f in analyzer.functions], default=0)
            else:
                max_function_complexity = 0
                avg_function_complexity = 0
                long_functions = 0
                nested_depth = 0
                
        except SyntaxError:
            # 如果AST解析失败，使用简单的启发式方法
            functions_count = len(re.findall(r'^\s*def\s+', content, re.MULTILINE))
            classes_count = len(re.findall(r'^\s*class\s+', content, re.MULTILINE))
            imports_count = len(re.findall(r'^\s*(?:import|from)\s+', content, re.MULTILINE))
            max_function_complexity = 0
            avg_function_complexity = 0
            long_functions = 0
            nested_depth = 0
        
        # 重复模式检测（简单版本）
        duplicated_patterns = detect_duplicated_patterns(content)
        
        # 计算重构紧迫度评分
        refactor_score = calculate_refactor_score(
            lines_of_code, functions_count, classes_count, max_function_complexity,
            avg_function_complexity, long_functions, nested_depth, duplicated_patterns
        )
        
        return FileMetrics(
            path=file_path,
            lines_of_code=lines_of_code,
            functions_count=functions_count,
            classes_count=classes_count,
            max_function_complexity=max_function_complexity,
            avg_function_complexity=avg_function_complexity,
            imports_count=imports_count,
            duplicated_patterns=duplicated_patterns,
            long_functions=long_functions,
            nested_depth=nested_depth,
            refactor_score=refactor_score
        )
        
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")
        return FileMetrics(
            path=file_path, lines_of_code=0, functions_count=0, classes_count=0,
            max_function_complexity=0, avg_function_complexity=0, imports_count=0,
            duplicated_patterns=0, long_functions=0, nested_depth=0, refactor_score=0
        )

def detect_duplicated_patterns(content: str) -> int:
    """检测重复代码模式"""
    lines = [line.strip() for line in content.split('\n') if line.strip()]
    duplicates = 0
    
    # 检测重复的代码块（简单版本）
    line_counts = defaultdict(int)
    for line in lines:
        if len(line) > 20 and not line.startswith('#'):  # 忽略短行和注释
            line_counts[line] += 1
    
    for count in line_counts.values():
        if count > 1:
            duplicates += count - 1
    
    return duplicates

def calculate_refactor_score(lines_of_code: int, functions_count: int, classes_count: int,
                           max_function_complexity: int, avg_function_complexity: float,
                           long_functions: int, nested_depth: int, duplicated_patterns: int) -> float:
    """计算重构紧迫度评分（0-100，越高越需要重构）"""
    score = 0
    
    # 文件大小评分 (0-25分)
    if lines_of_code > 1000:
        score += 25
    elif lines_of_code > 500:
        score += 15
    elif lines_of_code > 300:
        score += 10
    
    # 函数复杂度评分 (0-25分)
    if max_function_complexity > 20:
        score += 25
    elif max_function_complexity > 15:
        score += 20
    elif max_function_complexity > 10:
        score += 15
    elif avg_function_complexity > 8:
        score += 10
    
    # 长函数评分 (0-20分)
    if long_functions > 5:
        score += 20
    elif long_functions > 3:
        score += 15
    elif long_functions > 1:
        score += 10
    
    # 嵌套深度评分 (0-15分)
    if nested_depth > 6:
        score += 15
    elif nested_depth > 4:
        score += 10
    elif nested_depth > 3:
        score += 5
    
    # 重复代码评分 (0-15分)
    if duplicated_patterns > 20:
        score += 15
    elif duplicated_patterns > 10:
        score += 10
    elif duplicated_patterns > 5:
        score += 5
    
    return min(score, 100)

def analyze_project(project_path: str) -> List[FileMetrics]:
    """分析整个项目"""
    metrics = []
    
    # 遍历所有Python文件
    for root, dirs, files in os.walk(project_path):
        # 跳过一些目录
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                metrics.append(analyze_file(file_path))
    
    return metrics

if __name__ == "__main__":
    # 分析backend目录
    backend_path = "backend/app"
    if os.path.exists(backend_path):
        print("正在分析项目代码复杂度...")
        metrics = analyze_project(backend_path)
        
        # 按重构紧迫度排序
        metrics.sort(key=lambda x: x.refactor_score, reverse=True)
        
        print("\n=== 重构紧迫度排名 (前10名) ===")
        print(f"{'文件路径':<60} {'行数':<8} {'函数':<6} {'最大复杂度':<10} {'长函数':<8} {'重构评分':<8}")
        print("-" * 110)
        
        for i, metric in enumerate(metrics[:10]):
            relative_path = metric.path.replace("backend/app/", "")
            print(f"{relative_path:<60} {metric.lines_of_code:<8} {metric.functions_count:<6} "
                  f"{metric.max_function_complexity:<10} {metric.long_functions:<8} {metric.refactor_score:<8.1f}")
        
        print(f"\n总共分析了 {len(metrics)} 个文件")
        
        # 详细分析前3名
        print("\n=== 详细分析 (前3名) ===")
        for i, metric in enumerate(metrics[:3]):
            print(f"\n{i+1}. {metric.path}")
            print(f"   - 代码行数: {metric.lines_of_code}")
            print(f"   - 函数数量: {metric.functions_count}")
            print(f"   - 类数量: {metric.classes_count}")
            print(f"   - 最大函数复杂度: {metric.max_function_complexity}")
            print(f"   - 平均函数复杂度: {metric.avg_function_complexity:.1f}")
            print(f"   - 长函数数量: {metric.long_functions}")
            print(f"   - 最大嵌套深度: {metric.nested_depth}")
            print(f"   - 重复代码模式: {metric.duplicated_patterns}")
            print(f"   - 重构紧迫度评分: {metric.refactor_score:.1f}/100")
    else:
        print(f"目录 {backend_path} 不存在")
