import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import BacktestForm from '@/components/BacktestForm.vue'

// 中等难度测试套件 - TDD扩展阶段
describe('BacktestForm 扩展功能测试', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(BacktestForm)
  })

  describe('表单验证测试', () => {
    it('应该验证策略名称不能为空', async () => {
      const strategyInput = wrapper.find('[data-testid="strategy-input"]')
      await strategyInput.setValue('')
      await strategyInput.trigger('blur')
      await nextTick()

      expect(wrapper.vm.isFormValid).toBe(false)
      const errorMessage = wrapper.find('[data-testid="strategy-error"]')
      expect(errorMessage.exists()).toBe(true)
    })

    it('应该验证股票代码格式', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      // 测试无效格式
      await symbolInput.setValue('INVALID')
      await symbolInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      
      // 测试有效格式
      await symbolInput.setValue('000001.SZ')
      await symbolInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.formData.symbol).toBe('000001.SZ')
    })

    it('应该验证初始资金必须为正数', async () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      
      // 测试负数
      await capitalInput.setValue('-1000')
      await capitalInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      
      // 测试零
      await capitalInput.setValue('0')
      await capitalInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      
      // 测试正数
      await capitalInput.setValue('100000')
      await capitalInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.formData.initial_capital).toBe(100000)
    })

    it('应该验证日期范围的有效性', async () => {
      const startDateInput = wrapper.find('[data-testid="start-date-input"]')
      const endDateInput = wrapper.find('[data-testid="end-date-input"]')
      
      // 设置开始日期晚于结束日期
      await startDateInput.setValue('2023-12-31')
      await endDateInput.setValue('2023-01-01')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      
      // 设置正确的日期范围
      await startDateInput.setValue('2023-01-01')
      await endDateInput.setValue('2023-12-31')
      await nextTick()
      
      expect(wrapper.vm.formData.start_date).toBe('2023-01-01')
      expect(wrapper.vm.formData.end_date).toBe('2023-12-31')
    })
  })

  describe('表单字段测试', () => {
    it('应该正确处理策略名称字段', async () => {
      const strategyInput = wrapper.find('[data-testid="strategy-input"]')
      
      await strategyInput.setValue('移动平均策略')
      await nextTick()
      
      expect(wrapper.vm.formData.strategy_name).toBe('移动平均策略')
      expect(strategyInput.element.value).toBe('移动平均策略')
    })

    it('应该正确处理股票代码字段', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      await symbolInput.setValue('000001.SZ')
      await nextTick()
      
      expect(wrapper.vm.formData.symbol).toBe('000001.SZ')
      expect(symbolInput.element.value).toBe('000001.SZ')
    })

    it('应该正确处理日期字段', async () => {
      const startDateInput = wrapper.find('[data-testid="start-date-input"]')
      const endDateInput = wrapper.find('[data-testid="end-date-input"]')
      
      await startDateInput.setValue('2023-01-01')
      await endDateInput.setValue('2023-12-31')
      await nextTick()
      
      expect(wrapper.vm.formData.start_date).toBe('2023-01-01')
      expect(wrapper.vm.formData.end_date).toBe('2023-12-31')
    })

    it('应该正确处理资金字段', async () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      
      await capitalInput.setValue('100000')
      await nextTick()
      
      expect(wrapper.vm.formData.initial_capital).toBe(100000)
      expect(capitalInput.element.value).toBe('100000')
    })
  })

  describe('回测业务特有验证逻辑测试', () => {
    it('应该验证回测时间范围不能超过5年', async () => {
      const startDateInput = wrapper.find('[data-testid="start-date-input"]')
      const endDateInput = wrapper.find('[data-testid="end-date-input"]')
      
      // 设置超过5年的时间范围
      await startDateInput.setValue('2018-01-01')
      await endDateInput.setValue('2024-01-01')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      const errorMessage = wrapper.find('[data-testid="date-range-error"]')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('回测时间范围不能超过5年')
    })

    it('应该验证股票代码是否在支持列表中', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      // 输入不支持的股票代码
      await symbolInput.setValue('999999.SZ')
      await symbolInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      const errorMessage = wrapper.find('[data-testid="symbol-error"]')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('股票代码不在支持列表中')
    })

    it('应该根据股票类型调整最小资金要求', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      
      // 选择主板股票
      await symbolInput.setValue('000001.SZ')
      await capitalInput.setValue('5000')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      
      // 选择创业板股票（更高资金要求）
      await symbolInput.setValue('300001.SZ')
      await capitalInput.setValue('10000')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
    })

    it('应该验证回测开始日期不能是未来时间', async () => {
      const startDateInput = wrapper.find('[data-testid="start-date-input"]')
      
      // 设置未来日期
      const futureDate = new Date()
      futureDate.setFullYear(futureDate.getFullYear() + 1)
      const futureDateStr = futureDate.toISOString().split('T')[0]
      
      await startDateInput.setValue(futureDateStr)
      await startDateInput.trigger('blur')
      await nextTick()
      
      expect(wrapper.vm.isFormValid).toBe(false)
      const errorMessage = wrapper.find('[data-testid="start-date-error"]')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('开始日期不能是未来时间')
    })
  })

  describe('数据类型和格式处理测试', () => {
    it('应该正确处理数字格式化', async () => {
      const capitalInput = wrapper.find('[data-testid="capital-input"]')
      
      // 输入带逗号的数字
      await capitalInput.setValue('1,000,000')
      await capitalInput.trigger('blur')
      await nextTick()
      
      // 验证数字被正确解析
      expect(wrapper.vm.formData.initial_capital).toBe(1000000)
    })

    it('应该正确处理日期格式', async () => {
      const startDateInput = wrapper.find('[data-testid="start-date-input"]')
      
      // 输入不同格式的日期
      await startDateInput.setValue('2023/01/01')
      await startDateInput.trigger('blur')
      await nextTick()
      
      // 验证日期被标准化
      expect(wrapper.vm.formData.start_date).toBe('2023-01-01')
    })

    it('应该正确处理股票代码格式化', async () => {
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      // 输入小写股票代码
      await symbolInput.setValue('000001.sz')
      await symbolInput.trigger('blur')
      await nextTick()
      
      // 验证代码被转换为大写
      expect(wrapper.vm.formData.symbol).toBe('000001.SZ')
    })

    it('应该正确处理策略名称的空白字符', async () => {
      const strategyInput = wrapper.find('[data-testid="strategy-input"]')
      
      // 输入包含前后空格的策略名
      await strategyInput.setValue('  测试策略  ')
      await strategyInput.trigger('blur')
      await nextTick()
      
      // 验证空格被去除
      expect(wrapper.vm.formData.strategy_name).toBe('测试策略')
    })
  })

  describe('错误处理和用户反馈测试', () => {
    it('应该显示字段级别的错误信息', async () => {
      // 输入无效数据
      await wrapper.find('[data-testid="capital-input"]').setValue('-1000')
      await wrapper.find('[data-testid="capital-input"]').trigger('blur')
      await nextTick()

      // 验证错误信息显示
      const errorMessage = wrapper.find('[data-testid="capital-error"]')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('初始资金必须大于')
    })

    it('应该显示表单级别的错误信息', async () => {
      // 填写冲突的数据
      await wrapper.find('[data-testid="start-date-input"]').setValue('2023-12-31')
      await wrapper.find('[data-testid="end-date-input"]').setValue('2023-01-01')
      await nextTick()

      // 验证表单错误信息
      const formError = wrapper.find('[data-testid="form-error"]')
      expect(formError.exists()).toBe(true)
      expect(formError.text()).toContain('开始日期不能晚于结束日期')
    })

    it('应该正确处理网络错误', async () => {
      // 模拟网络错误
      const wrapperWithError = mount(BacktestForm, {
        props: {
          error: '网络连接失败，请检查网络设置'
        }
      })

      const errorAlert = wrapperWithError.find('[data-testid="error-alert"]')
      expect(errorAlert.exists()).toBe(true)
      expect(errorAlert.text()).toContain('网络连接失败')
      
      wrapperWithError.unmount()
    })
  })
})