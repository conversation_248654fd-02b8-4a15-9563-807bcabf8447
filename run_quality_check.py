#!/usr/bin/env python3
"""代码质量检查主脚本

运行完整的代码质量分析，包括：
- 代码质量检查
- 测试覆盖率分析
- 文档覆盖率检查
- 重构机会识别
- 性能监控
- 生成详细报告

使用方法:
    python run_quality_check.py
    python run_quality_check.py --html-only
    python run_quality_check.py --config config.json
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from backend.app.utils.quality_manager import run_quality_analysis, QualityManager
except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed and the project structure is correct.")
        print("\n💡 Tip: You can use 'python run_simple_quality_check.py' for basic quality analysis.")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Run comprehensive code quality analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_quality_check.py                    # Run full analysis
  python run_quality_check.py --html-only        # Generate HTML report only
  python run_quality_check.py --config my.json   # Use custom config
  python run_quality_check.py --no-html          # Skip HTML generation
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--html-only',
        action='store_true',
        help='Only generate HTML report from existing data'
    )
    
    parser.add_argument(
        '--no-html',
        action='store_true',
        help='Skip HTML report generation'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output directory for reports'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    try:
        print("🚀 Starting Code Quality Analysis...")
        print(f"📁 Project Root: {project_root}")
        
        if args.html_only:
            # 只生成HTML报告
            manager = QualityManager(str(project_root), args.config)
            
            # 检查是否有历史数据
            if not manager.history:
                print("❌ No historical data found. Run full analysis first.")
                return 1
            
            # 使用最新的历史数据生成报告
            latest_data = manager.history[-1]
            from backend.app.utils.quality_manager import QualityReport, QualityMetrics
            
            # 重建报告对象
            metrics_data = latest_data['metrics']
            metrics = QualityMetrics(**metrics_data)
            
            report = QualityReport(
                timestamp=latest_data['timestamp'],
                project_path=str(project_root),
                metrics=metrics,
                recommendations=["Report generated from historical data"],
                action_items=[]
            )
            
            html_file = manager.generate_html_report(report)
            print(f"✅ HTML report generated: {html_file}")
            
        else:
            # 运行完整分析
            generate_html = not args.no_html
            report = run_quality_analysis(str(project_root), generate_html)
            
            if args.verbose:
                print("\n📊 Detailed Metrics:")
                print(f"  Code Quality Score: {report.metrics.code_quality_score}")
                print(f"  Test Coverage: {report.metrics.test_coverage_percentage}%")
                print(f"  Documentation Coverage: {report.metrics.documentation_coverage}%")
                print(f"  Maintainability Index: {report.metrics.maintainability_index}")
                print(f"  Technical Debt: {report.metrics.technical_debt_hours} hours")
                
                print("\n📋 All Action Items:")
                for i, item in enumerate(report.action_items, 1):
                    # action_items 是字典列表，可以使用 .get() 方法
                    print(f"  {i}. [{item.get('priority', 'medium').upper()}] {item.get('title')}")
                    print(f"     Effort: {item.get('estimated_effort')}")
                    if item.get('file'):
                        print(f"     File: {item.get('file')}")
                    print()
        
        print("\n🎉 Quality analysis completed successfully!")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())