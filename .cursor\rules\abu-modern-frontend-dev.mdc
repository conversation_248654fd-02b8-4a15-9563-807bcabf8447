---
alwaysApply: true
---
1. 当前项目的技术架构是FastAPI + Vue3
2. 在D:\智能投顾\量化相关\abu_modern/.venv中已经创建了后端使用的虚拟环境，这是你在运行后端项目时需要激活的环境。
3. 当前项目的工作日志保存在work_logs中，其中
    实现AI的工作日志保存在work_logs/implementation_logs中，
    评审AI的工作日志保存在work_logs/review_logs中，
    勘探AI的工作日志保存在work_logs/explorer_logs中,
    测试AI的工作日志保存在work_logs\testing_logs中，
    保存工作日志时均应采用文件夹中原有工作日志的格式和命名格式。
4. 请在frontend目录中运行npx vitest run <测试文件路径> 来运行测试。