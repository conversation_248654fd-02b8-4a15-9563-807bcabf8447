import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { setActivePinia, createPinia } from 'pinia';
import { apiClient } from '@/api/client';
import { useAppStore } from '@/stores/app';
import { handlers } from '../mocks/handlers';

// 设置MSW服务器
const server = setupServer(...handlers);

describe('API Client Infrastructure', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('请求拦截器', () => {
    it('应在请求前将加载状态设置为true，在请求后设置为false', async () => {
      const appStore = useAppStore();
      expect(appStore.isLoading).toBe(false);

      const requestPromise = apiClient.get('/api/test-success');

      // 请求发送后，加载状态应立即变为true。
      // 由于异步执行流程，可能需要稍微等待一下以确保状态更新。
      await vi.dynamicImportSettled();
      expect(appStore.isLoading).toBe(true);

      await requestPromise;

      expect(appStore.isLoading).toBe(false);
    });
  });

  describe('响应拦截器', () => {
    it('应格式化成功的响应数据', async () => {
      const response = await apiClient.get('/api/test-success');
      expect(response).toEqual({ message: 'Success' });
    });


  });

  describe('错误处理', () => {
    it('应处理404未找到错误并将加载状态设置为false', async () => {
      const appStore = useAppStore();
      await expect(apiClient.get('/api/test-notfound')).rejects.toThrow();
      expect(appStore.isLoading).toBe(false);
    });

    it('应处理500内部服务器错误并将加载状态设置为false', async () => {
      const appStore = useAppStore();
      await expect(apiClient.get('/api/test-servererror')).rejects.toThrow();
      expect(appStore.isLoading).toBe(false);
    });

    it('应处理网络错误并将加载状态设置为false', async () => {
        const appStore = useAppStore();
        await expect(apiClient.get('/api/network-error')).rejects.toThrow();
        expect(appStore.isLoading).toBe(false);
    });
  });
});