import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import Dashboard from '../../src/views/Dashboard.vue';
import { useDashboardStore } from '../../src/stores/useDashboardStore';
import type { DashboardSummary } from '@/api/dashboard';

/**
 * Dashboard组件完整测试套件
 * 基于abu_modern量化应用的"四大核心信息象限"设计理念
 * 设计哲学：专业、直观、流程化的仪表板测试
 * 核心定位：应用的"脸面"，量化生态的现代化升级
 */

// Mock echarts - 专业图表库
const mockChart = {
  setOption: vi.fn(),
  resize: vi.fn(),
  dispose: vi.fn()
};

vi.mock('echarts', () => ({
  init: vi.fn(() => mockChart)
}));

// Mock量化数据
const mockDashboardSummary: DashboardSummary = {
  today_gain: 1500.50,      // 今日涨跌 - 绩效概览象限
  active_strategies: 5,      // 活跃策略数 - 策略健康度象限
  total_turnover_wan: 125.8, // 总成交额 - 市场脉搏象限
  signals_count: 12,         // 信号数量 - 信号与机会象限
  market_performance: {
    date: ['2023-01-01', '2023-01-02', '2023-01-03'],
    value: [100, 102, 105]
  }
};

describe('Dashboard.vue - 量化应用仪表板', () => {
  let wrapper: VueWrapper<any>;
  let pinia: any;
  let dashboardStore: any;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    dashboardStore = useDashboardStore();
    vi.resetAllMocks();
    
    // Mock图表容器
    const mockElement = document.createElement('div');
    mockElement.id = 'main-chart';
    vi.spyOn(document, 'getElementById').mockReturnValue(mockElement);
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
    vi.restoreAllMocks();
  });

  describe('四大核心信息象限 - 布局渲染测试', () => {
    it('应该渲染完整的仪表板布局（应用脸面）', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      // 验证主要布局结构
      expect(wrapper.find('.el-row').exists()).toBe(true);
      expect(wrapper.findAll('.el-col')).toHaveLength(5); // 4个象限 + 1个图表
      expect(wrapper.findAll('.el-card')).toHaveLength(5); // 4个统计卡片 + 1个图表卡片
    });

    it('应该渲染四大核心信息象限', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const statistics = wrapper.findAll('.el-statistic');
      expect(statistics).toHaveLength(4);
      
      // 验证四大象限标题
      const titles = statistics.map(stat => {
        const titleAttr = stat.find('[title]');
        return titleAttr ? titleAttr.attributes('title') : stat.text();
      });
      
      expect(titles).toContain('今日涨跌');      // 绩效概览象限
      expect(titles).toContain('活跃策略数');    // 策略健康度象限
      expect(titles).toContain('总成交额 (万)'); // 市场脉搏象限
      expect(titles).toContain('信号数量');      // 信号与机会象限
    });

    it('应该渲染市场表现图表（市场脉搏可视化）', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const chartContainer = wrapper.find('#main-chart');
      expect(chartContainer.exists()).toBe(true);
      expect(chartContainer.attributes('style')).toContain('height: 400px');
    });
  });

  describe('专业量化数据展示', () => {
    beforeEach(() => {
      dashboardStore.summary = mockDashboardSummary;
      dashboardStore.loading = false;
    });

    it('应该正确显示绩效概览象限 - 今日涨跌', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const todayGainStat = wrapper.findAll('.el-statistic')[0];
      expect(todayGainStat.text()).toContain('1500.5');
      expect(todayGainStat.text()).toContain('¥');
    });

    it('应该正确显示策略健康度象限 - 活跃策略数', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const activeStrategiesStat = wrapper.findAll('.el-statistic')[1];
      expect(activeStrategiesStat.text()).toContain('5');
    });

    it('应该正确显示市场脉搏象限 - 总成交额', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const turnoverStat = wrapper.findAll('.el-statistic')[2];
      expect(turnoverStat.text()).toContain('125.8');
    });

    it('应该正确显示信号与机会象限 - 信号数量', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const signalsStat = wrapper.findAll('.el-statistic')[3];
      expect(signalsStat.text()).toContain('12');
    });
  });

  describe('量化应用加载状态管理', () => {
    it('应该在数据加载时显示专业loading状态', async () => {
      dashboardStore.loading = true;
      
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('[v-loading="true"]').exists()).toBe(true);
    });

    it('应该在数据加载完成后隐藏loading状态', async () => {
      dashboardStore.loading = false;
      dashboardStore.summary = mockDashboardSummary;
      
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('.el-loading-mask').exists()).toBe(false);
    });
  });

  describe('量化数据容错处理', () => {
    it('应该在数据为空时显示默认值（系统稳定性）', () => {
      dashboardStore.summary = null;
      
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const statistics = wrapper.findAll('.el-statistic');
      statistics.forEach(stat => {
        expect(stat.text()).toContain('0');
      });
    });

    it('应该处理部分数据缺失（健壮性）', () => {
      dashboardStore.summary = {
        today_gain: 100,
        // 其他字段缺失
      } as any;
      
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const statistics = wrapper.findAll('.el-statistic');
      expect(statistics[0].text()).toContain('100'); // today_gain
      expect(statistics[1].text()).toContain('0');   // active_strategies
      expect(statistics[2].text()).toContain('0');   // total_turnover_wan
      expect(statistics[3].text()).toContain('0');   // signals_count
    });
  });

  describe('专业图表集成 - 市场脉搏可视化', () => {
    it('应该在组件挂载时初始化专业图表', () => {
      const echarts = require('echarts');
      
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      expect(echarts.init).toHaveBeenCalledWith(expect.any(HTMLElement));
      expect(mockChart.setOption).toHaveBeenCalled();
    });

    it('应该在市场数据更新时更新图表', async () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      mockChart.setOption.mockClear();

      // 模拟市场数据更新
      dashboardStore.summary = mockDashboardSummary;
      await wrapper.vm.$nextTick();

      expect(mockChart.setOption).toHaveBeenCalledWith({
        xAxis: {
          type: 'category',
          data: mockDashboardSummary.market_performance.date
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: mockDashboardSummary.market_performance.value,
          type: 'line'
        }]
      });
    });

    it('应该处理图表容器异常（容错性）', () => {
      vi.spyOn(document, 'getElementById').mockReturnValue(null);
      
      expect(() => {
        wrapper = mount(Dashboard, {
          global: {
            plugins: [pinia]
          }
        });
      }).not.toThrow();
    });
  });

  describe('量化应用生命周期管理', () => {
    it('应该在组件挂载时获取仪表板数据', () => {
      const fetchSpy = vi.spyOn(dashboardStore, 'fetchDashboardSummary');
      
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      expect(fetchSpy).toHaveBeenCalledOnce();
    });
  });

  describe('实时数据监听 - 量化应用核心', () => {
    it('应该监听市场数据变化并实时更新图表', async () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      mockChart.setOption.mockClear();

      // 模拟实时市场数据变化
      const newSummary = {
        ...mockDashboardSummary,
        market_performance: {
          date: ['2023-01-04', '2023-01-05'],
          value: [110, 115]
        }
      };

      dashboardStore.summary = newSummary;
      await wrapper.vm.$nextTick();

      expect(mockChart.setOption).toHaveBeenCalledWith({
        xAxis: {
          type: 'category',
          data: newSummary.market_performance.date
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: newSummary.market_performance.value,
          type: 'line'
        }]
      });
    });

    it('应该处理市场数据缺失情况', async () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      mockChart.setOption.mockClear();

      // 设置没有market_performance的数据
      dashboardStore.summary = {
        today_gain: 100,
        active_strategies: 5,
        total_turnover_wan: 125.8,
        signals_count: 12
      } as any;
      
      await wrapper.vm.$nextTick();

      // 图表不应该被更新
      expect(mockChart.setOption).not.toHaveBeenCalled();
    });
  });

  describe('专业UI布局标准', () => {
    it('应该应用专业量化应用样式', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      expect(wrapper.find('.el-statistic').exists()).toBe(true);
      expect(wrapper.find('.el-statistic').classes()).toContain('el-statistic');
    });

    it('应该设置专业栅格布局（四象限设计）', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const cols = wrapper.findAll('.el-col');
      // 前4个统计卡片应该是span=6（四等分布局）
      for (let i = 0; i < 4; i++) {
        expect(cols[i].classes()).toContain('el-col-6');
      }
      // 图表容器应该是span=24（全宽显示）
      expect(cols[4].classes()).toContain('el-col-24');
    });

    it('应该设置合适的间距（专业视觉效果）', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      const rows = wrapper.findAll('.el-row');
      expect(rows[0].classes()).toContain('el-row--20');
    });
  });

  describe('量化应用异常处理', () => {
    it('应该处理图表初始化失败', () => {
      const echarts = require('echarts');
      echarts.init.mockImplementation(() => {
        throw new Error('Chart initialization failed');
      });

      expect(() => {
        wrapper = mount(Dashboard, {
          global: {
            plugins: [pinia]
          }
        });
      }).not.toThrow();
    });

    it('应该处理异常数据格式（系统稳定性）', async () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      // 设置异常格式的数据
      dashboardStore.summary = {
        market_performance: {
          date: null,
          value: undefined
        }
      } as any;

      await wrapper.vm.$nextTick();

      // 组件应该能正常处理而不崩溃
      expect(wrapper.exists()).toBe(true);
    });
  });

  describe('量化生态集成准备', () => {
    it('应该为未来的abupy模块集成做好准备', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      // 验证组件结构适合集成更多量化模块
      expect(wrapper.findAll('.el-card')).toHaveLength(5);
      expect(wrapper.find('#main-chart').exists()).toBe(true);
    });

    it('应该支持扩展更多信息象限', () => {
      wrapper = mount(Dashboard, {
        global: {
          plugins: [pinia]
        }
      });

      // 验证布局可以支持更多象限
      const rowStructure = wrapper.find('.el-row');
      expect(rowStructure.exists()).toBe(true);
    });
  });
});