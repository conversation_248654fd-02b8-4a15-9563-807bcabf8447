"""
Benchmark 模块测试
"""
import pytest
from unittest.mock import patch, MagicMock

from app.abupy_adapter.benchmark import SimpleBenchmark, create_benchmark
from app.abupy_adapter.exceptions import ParameterError # Updated import

class TestBenchmark:
    """Benchmark 测试类"""
    
    def test_simple_benchmark_init(self):
        """测试 SimpleBenchmark 初始化"""
        benchmark = SimpleBenchmark(symbol="sh000300", n_folds=3)
        
        assert benchmark.symbol == "sh000300"
        assert benchmark.n_folds == 3
    
    def test_simple_benchmark_str(self):
        """测试 SimpleBenchmark 字符串表示"""
        benchmark = SimpleBenchmark(symbol="sh000300", n_folds=2)
        
        assert str(benchmark) == "SimpleBenchmark(symbol=sh000300, n_folds=2)"
    
    def test_simple_benchmark_repr(self):
        """测试 SimpleBenchmark repr 表示"""
        benchmark = SimpleBenchmark(symbol="sh000300", n_folds=2)
        
        assert repr(benchmark) == "SimpleBenchmark(symbol=sh000300, n_folds=2)"
    
    def test_create_benchmark_success(self):
        """测试成功创建基准"""
        # 调用创建基准函数
        benchmark = create_benchmark("sh000300")
        
        # 验证结果
        assert benchmark.symbol == "sh000300"
        assert benchmark.n_folds == 2
    
    def test_create_benchmark_failure(self):
        """测试创建基准失败的情况"""
        # 直接调用创建基准函数，不会抛出异常，因为简化实现不做验证
        benchmark = create_benchmark("invalid_symbol")
        
        # 验证结果
        assert benchmark.symbol == "invalid_symbol"
        assert benchmark.n_folds == 2
    
    def test_create_benchmark_with_custom_n_folds(self):
        """测试使用自定义折数创建基准"""
        # 调用创建基准函数
        benchmark = create_benchmark("custom_symbol", n_folds=5)
        
        # 验证结果
        assert benchmark.symbol == "custom_symbol"
        assert benchmark.n_folds == 5
    
    def test_create_benchmark_invalid_symbol_raises_error(self):
        """测试 create_benchmark 使用无效符号 (None, 空字符串, 非字符串) 时抛出 ParameterError"""
        with pytest.raises(ParameterError, match="基准符号 \\(benchmark_symbol\\) 不能为空 \\(None\\)。"):
            create_benchmark(None)
            
        with pytest.raises(ParameterError, match="基准符号 \\(benchmark_symbol\\) 不能为空字符串。"):
            create_benchmark("   ")

        with pytest.raises(ParameterError, match="基准符号 \\(benchmark_symbol\\) 不能为空字符串。"):
            create_benchmark("")

        with pytest.raises(ParameterError, match="基准符号 \\(benchmark_symbol\\) 必须是字符串，但收到了 <class 'int'> 类型。"):
            create_benchmark(123)
    

