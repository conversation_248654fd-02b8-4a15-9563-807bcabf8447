import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { runBacktest, getBacktestResults, getBacktestHistory, stopBacktest } from '../../../src/api/backtest';
import type { BacktestConfig, BacktestResult, BacktestTask } from '../../../src/api/types/backtest';
import { BacktestStatus } from '../../../src/api/types/backtest';
import { SimpleBacktestDataFactory } from '../../factories/SimpleBacktestDataFactory';
import { backtestHandlers } from '../../mocks/simple/backtestHandlers';

const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 回测API测试套件 - TDD版本
 * 
 * 本测试文件专注于核心功能的TDD测试，包括：
 * - 基本的HTTP请求处理
 * - 核心错误处理机制
 * - 基础的响应数据验证
 */

// 使用工厂类生成Mock数据
const mockBacktestConfig: BacktestConfig = SimpleBacktestDataFactory.createSimpleConfig({
  strategy_id: 'strategy-1',
  symbol: 'AAPL',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  capital: 100000,
  commission: 0.001,
  slippage: 0.001,
  benchmark: 'SPY'
});

const mockBacktestResult: BacktestResult = SimpleBacktestDataFactory.createSimpleResult('backtest-1');

const mockBacktestHistory: BacktestTask[] = SimpleBacktestDataFactory.createSimpleHistory(2);

// 使用外部handlers
const server = setupServer(...backtestHandlers);

describe('Backtest API - TDD', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('runBacktest', () => {
    it('should successfully start backtest task', async () => {
      const response = await runBacktest(mockBacktestConfig);
      expect(response.success).toBe(true);
      expect(response.data.id).toBeDefined();
      expect(response.data.status).toBe('pending');
      expect(response.data.strategy_id).toBe(mockBacktestConfig.strategy_id);
      expect(response.data.symbol).toBe(mockBacktestConfig.symbol);
    });

    it('should handle invalid configuration parameters error (400)', async () => {
      const invalidConfig = {
        ...mockBacktestConfig,
        start_date: 'invalid-date'
      };

      await expect(runBacktest(invalidConfig)).rejects.toMatchObject({
        status: 400,
        message: expect.stringContaining('Invalid configuration parameters')
      });
    });
  });

  describe('getBacktestResults', () => {
    it('should successfully get complete backtest report', async () => {
      const response = await getBacktestResults('backtest-1');
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          task_id: 'backtest-1',
          metrics: expect.any(Object),
          trades: expect.any(Array),
          equity_curve: expect.any(Array)
        })
      });
    });

    it('should handle backtest task not found error (404)', async () => {
      await expect(getBacktestResults('non-existent')).rejects.toMatchObject({
        status: 404,
        message: expect.stringContaining('Backtest task not found')
      });
    });
  });

  describe('getBacktestHistory', () => {
    it('should successfully get backtest history list', async () => {
      const response = await getBacktestHistory();
      expect(response).toMatchObject({
        success: true,
        data: expect.any(Array),
        total: expect.any(Number),
        page: expect.any(Number),
        page_size: expect.any(Number)
      });
      expect(response.total).toBeGreaterThan(0);
    });

    it('should support pagination parameters', async () => {
      const response = await getBacktestHistory({ page: 2, page_size: 10 });
      expect(response).toMatchObject({
        success: true,
        page: 2,
        page_size: 10,
        data: expect.any(Array)
      });
    });
  });

  describe('stopBacktest', () => {
    it('should successfully stop running backtest', async () => {
      const response = await stopBacktest('running-backtest');
      expect(response).toMatchObject({
        success: true,
        data: expect.objectContaining({
          status: BacktestStatus.STOPPED
        })
      });
    });

    it('should handle backtest task not found error (404)', async () => {
      await expect(stopBacktest('non-existent')).rejects.toMatchObject({
        status: 404,
        message: expect.stringContaining('Backtest task not found')
      });
    });
  });
});