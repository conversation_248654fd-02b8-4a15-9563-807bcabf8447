// Factors数据工厂
// 用于因子相关测试数据生成

import type {
  Factor,
  FactorParameter,
  CustomFactorDefinition,
  FactorTestRequest,
  FactorTestResult,
  FactorCalculationRequest,
  FactorsResponse,
  FactorResponse,
  FactorTestResponse,
  FactorValuesResponse,
  FactorCreationResponse,
  FactorValidationResponse,
  FactorCategory,
  FactorDataType
} from '../../src/api/types/factors';

/**
 * Factors数据工厂类
 * 提供类型安全的因子mock数据生成方法
 */
export class FactorsDataFactory {
  /**
   * 创建基础因子数据
   */
  static createFactor(overrides: Partial<Factor> = {}): Factor {
    return {
      id: 'factor-001',
      name: 'RSI相对强弱指标',
      description: '相对强弱指标，用于判断股票的超买超卖状态',
      category: 'technical',
      data_type: 'numeric',
      formula: '100 - (100 / (1 + RS))',
      parameters: [
        {
          name: 'period',
          type: 'integer',
          default_value: 14,
          min_value: 2,
          max_value: 100,
          description: 'RSI计算周期'
        }
      ],
      dependencies: ['close'],
      frequency: 'daily',
      lookback_period: 14,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      is_builtin: true,
      is_active: true,
      usage_count: 150,
      rating: 4.5,
      tags: ['技术指标', 'RSI', '超买超卖'],
      author: 'system',
      version: '1.0.0',
      ...overrides
    };
  }

  /**
   * 创建自定义因子定义
   */
  static createCustomFactorDefinition(overrides: Partial<CustomFactorDefinition> = {}): CustomFactorDefinition {
    return {
      name: '自定义动量因子',
      description: '基于价格动量的自定义因子',
      category: 'custom',
      data_type: 'numeric',
      formula: '(close / close.shift(20) - 1) * 100',
      parameters: [
        {
          name: 'lookback',
          type: 'integer',
          default_value: 20,
          min_value: 1,
          max_value: 252,
          description: '回看周期'
        }
      ],
      dependencies: ['close'],
      frequency: 'daily',
      lookback_period: 20,
      tags: ['动量', '自定义'],
      ...overrides
    };
  }

  /**
   * 创建因子测试请求
   */
  static createFactorTestRequest(overrides: Partial<FactorTestRequest> = {}): FactorTestRequest {
    return {
      factor_id: 'factor-001',
      symbols: ['000001.SZ', '000002.SZ'],
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      parameters: {
        period: 14
      },
      ...overrides
    };
  }

  /**
   * 创建因子测试结果
   */
  static createFactorTestResult(overrides: Partial<FactorTestResult> = {}): FactorTestResult {
    return {
      factor_id: 'factor-001',
      test_period: {
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      },
      statistics: {
        mean: 50.25,
        std: 15.8,
        min: 10.5,
        max: 89.7,
        skewness: 0.15,
        kurtosis: 2.8,
        sharpe_ratio: 1.25,
        information_ratio: 0.85
      },
      performance_metrics: {
        ic_mean: 0.045,
        ic_std: 0.12,
        ic_ir: 0.375,
        rank_ic_mean: 0.038,
        rank_ic_std: 0.11,
        rank_ic_ir: 0.345
      },
      factor_values: [
        {
          date: '2023-01-01',
          symbol: '000001.SZ',
          value: 45.2
        },
        {
          date: '2023-01-01',
          symbol: '000002.SZ',
          value: 52.8
        }
      ],
      ...overrides
    };
  }

  /**
   * 创建多个因子数据
   */
  static createFactors(count: number = 5): Factor[] {
    const categories: FactorCategory[] = ['technical', 'fundamental', 'macro', 'sentiment', 'custom'];
    const names = ['RSI', 'MACD', 'PE比率', '市场情绪', '自定义因子'];
    
    return Array.from({ length: count }, (_, index) => 
      this.createFactor({
        id: `factor-${String(index + 1).padStart(3, '0')}`,
        name: names[index] || `因子${index + 1}`,
        category: categories[index % categories.length],
        usage_count: Math.floor(Math.random() * 200),
        rating: 3 + Math.random() * 2
      })
    );
  }

  /**
   * 创建特定的测试因子列表
   * 用于策略工坊测试
   */
  static createSpecificFactors(): Factor[] {
    return [
      {
         id: 'factor_1',
         name: 'AbuDoubleMaBuy',
         description: '双均线交叉策略',
         category: 'technical' as const,
         data_type: 'boolean' as const,
         factor_type: 'buy',
         class_name: 'AbuDoubleMaBuy',
         parameters: [
           { name: 'fast_ma', label: '短期均线', type: 'number', default: 5 },
           { name: 'slow_ma', label: '长期均线', type: 'number', default: 20 }
         ],
         formula: 'fast_ma > slow_ma',
         dependencies: ['close'],
         frequency: 'daily',
         lookback_period: 20,
         created_at: '2023-01-01T00:00:00Z',
         updated_at: '2023-01-01T00:00:00Z',
         is_builtin: true,
         is_active: true,
         usage_count: 15,
         rating: 4.5,
         tags: ['技术指标', '均线'],
         author: 'system',
         version: '1.0.0'
       },
      {
          id: 'factor_2',
          name: 'AbuRSISell',
          description: 'RSI超买卖出策略',
          category: 'technical' as const,
          data_type: 'boolean' as const,
          factor_type: 'sell',
          class_name: 'AbuRSISell',
          parameters: [],
          formula: 'rsi > threshold',
          dependencies: ['close'],
          frequency: 'daily',
          lookback_period: 14,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          is_builtin: true,
          is_active: true,
          usage_count: 8,
          rating: 4.2,
          tags: ['技术指标', 'RSI'],
          author: 'system',
          version: '1.0.0'
        },
      {
         id: 'factor_3',
         name: 'AbuMACDBuy',
         description: 'MACD金叉买入策略',
         category: 'technical' as const,
         data_type: 'boolean' as const,
         factor_type: 'buy',
         class_name: 'AbuMACDBuy',
         parameters: [], // 明确提供空数组
         formula: 'macd_signal',
         dependencies: ['close'],
         frequency: 'daily',
         lookback_period: 1,
         created_at: '2023-01-01T00:00:00Z',
         updated_at: '2023-01-01T00:00:00Z',
         is_builtin: true,
         is_active: true,
         usage_count: 12,
         rating: 4.0,
         tags: ['技术指标', 'MACD'],
         author: 'system',
         version: '1.0.0'
       }
    ];
  }

  /**
   * 创建API响应格式的因子列表
   */
  static createFactorsResponse(factors?: Factor[]): FactorsResponse {
    const data = factors || this.createFactors();
    return {
      success: true,
      data,
      total: data.length,
      page: 1,
      page_size: 20,
      message: 'Success'
    };
  }

  /**
   * 创建单个因子响应
   */
  static createFactorResponse(factor?: Factor): FactorResponse {
    return {
      success: true,
      data: factor || this.createFactor(),
      message: 'Success'
    };
  }

  /**
   * 创建因子测试响应
   */
  static createFactorTestResponse(result?: FactorTestResult): FactorTestResponse {
    return {
      success: true,
      data: result || this.createFactorTestResult(),
      message: 'Test completed successfully'
    };
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(message: string = 'Internal Server Error'): {
    success: boolean;
    data: null;
    message: string;
  } {
    return {
      success: false,
      data: null,
      message
    };
  }

  /**
   * 验证因子数据的API契约
   */
  static validateFactorContract(factor: Factor): boolean {
    const requiredFields = ['id', 'name', 'description', 'category', 'data_type', 'parameters', 'is_builtin', 'is_active'];
    
    for (const field of requiredFields) {
      if (!(field in factor)) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // 验证参数数组
    if (!Array.isArray(factor.parameters)) {
      console.error('parameters must be an array');
      return false;
    }

    return true;
  }
}

// 导出便捷方法
export const {
  createFactor,
  createCustomFactorDefinition,
  createFactorTestRequest,
  createFactorTestResult,
  createFactors,
  createSpecificFactors,
  createFactorsResponse,
  createFactorResponse,
  createFactorTestResponse,
  createErrorResponse,
  validateFactorContract
} = FactorsDataFactory;