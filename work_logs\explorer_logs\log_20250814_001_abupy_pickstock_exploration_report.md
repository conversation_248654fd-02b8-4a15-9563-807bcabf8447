# abupy PickStockBu模块精准勘探报告

**勘探日期**: 2025年8月14日  
**勘探目标**: 为"选股器"页面设计提供核心技术依据  
**勘探状态**: 精准勘探完成 ✅  
**验证方法**: 基于abupy源码深度分析  
**勘探文件**: `abupy_pickstock_exploration.py`

---

## 执行摘要

本报告通过对abupy框架PickStockBu模块的精准勘探，确定了所有开箱即用的选股因子类及其详细参数配置。主要发现包括：

1. **选股因子总数**: 5个开箱即用的选股因子类
2. **基类参数**: 3个通用参数(reversed, xd, min_xd)
3. **组合逻辑**: 多个选股因子采用'与'逻辑组合
4. **配置格式**: 基于字典的因子配置格式，必须包含'class'键
5. **应用场景**: 覆盖趋势、价格、相似度、波动等多种选股策略

---

## 1. 选股因子完整清单

### 1.1 模块概览

**模块路径**: `abupy/PickStockBu`  
**基类**: `AbuPickStockBase`  
**可用选股因子总数**: 5个

### 1.2 所有可用选股因子

| 序号 | 类名 | 友好名称 | 功能分类 | 推荐度 |
|------|------|----------|----------|--------|
| 1 | `AbuPickRegressAngMinMax` | 价格拟合角度选股因子 | 趋势分析 | ⭐⭐⭐ |
| 2 | `AbuPickStockPriceMinMax` | 价格区间选股因子 | 价格筛选 | ⭐⭐⭐ |
| 3 | `AbuPickSimilarNTop` | 相似度选股因子 | 相似度分析 | ⭐⭐ |
| 4 | `AbuPickStockShiftDistance` | 位移路程比选股因子 | 波动分析 | ⭐ |
| 5 | `AbuPickStockNTop` | 涨跌幅排名选股因子 | 排名筛选 | ⭐⭐⭐ |

---

## 2. 基类通用参数

### 2.1 AbuPickStockBase基类参数

所有选股因子都继承自`AbuPickStockBase`，支持以下通用参数：

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `reversed` | `bool` | `False` | 是否反转选股结果，True表示选择不符合条件的股票 |
| `xd` | `int` | `252` | 选股分析周期，即分析多少个交易日的数据 |
| `min_xd` | `int` | `xd/2` | 最小选股周期，数据不足时的最小要求 |

---

## 3. 各选股因子详细参数

### 3.1 AbuPickRegressAngMinMax (价格拟合角度选股因子) ⭐⭐⭐

**功能描述**: 基于价格走势的线性拟合角度进行选股，适用于趋势分析  
**文件位置**: `abupy/PickStockBu/ABuPickRegressAngMinMax.py`  
**应用场景**: 选择上升趋势或下降趋势的股票

#### 特定参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `threshold_ang_min` | `float` | `-np.inf` | 最小角度阈值，角度大于此值才被选中 |
| `threshold_ang_max` | `float` | `np.inf` | 最大角度阈值，角度小于此值才被选中 |

#### 配置示例

```python
{
    'class': AbuPickRegressAngMinMax,
    'threshold_ang_min': 0.0,      # 选择上升趋势
    'threshold_ang_max': 45.0,     # 角度不超过45度
    'reversed': False
}
```

### 3.2 AbuPickStockPriceMinMax (价格区间选股因子) ⭐⭐⭐

**功能描述**: 基于股票价格区间进行选股，过滤价格过高或过低的股票  
**文件位置**: `abupy/PickStockBu/ABuPickStockPriceMinMax.py`  
**应用场景**: 选择特定价格区间的股票，避免仙股或高价股

#### 特定参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `threshold_price_min` | `float` | `-np.inf` | 最小价格阈值，股票最低价必须大于此值 |
| `threshold_price_max` | `float` | `np.inf` | 最大价格阈值，股票最高价必须小于此值 |

#### 配置示例

```python
{
    'class': AbuPickStockPriceMinMax,
    'threshold_price_min': 10.0,   # 最低价格10元
    'threshold_price_max': 200.0,  # 最高价格200元
    'reversed': False
}
```

### 3.3 AbuPickSimilarNTop (相似度选股因子) ⭐⭐

**功能描述**: 基于与目标股票的相似度进行选股，选择走势相似的股票  
**文件位置**: `abupy/PickStockBu/ABuPickSimilarNTop.py`  
**应用场景**: 选择与某只标杆股票走势相似的股票

#### 特定参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `similar_stock` | `str` | **必需** | 目标相似度股票代码，作为比较基准 |
| `n_top` | `int` | `100` | 选取相似度最高的前n_top个股票 |
| `threshold_similar_min` | `float` | `-np.inf` | 最小相似度阈值 |
| `threshold_similar_max` | `float` | `np.inf` | 最大相似度阈值 |
| `rolling` | `bool` | `False` | 是否使用时间加权计算相似度 |
| `corr_type` | `ECoreCorrType` | `E_CORE_TYPE_PEARS` | 相似度计算算法类型 |

#### 配置示例

```python
{
    'class': AbuPickSimilarNTop,
    'similar_stock': 'usAAPL',     # 以AAPL为基准
    'n_top': 50,                   # 选择前50个最相似的
    'rolling': False
}
```

### 3.4 AbuPickStockShiftDistance (位移路程比选股因子) ⭐

**功能描述**: 基于价格位移路程比进行选股，分析价格波动特征  
**文件位置**: `abupy/PickStockBu/ABuPickStockDemo.py`  
**应用场景**: 选择价格波动特征符合要求的股票

#### 特定参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `threshold_sd` | `float` | `2.0` | 位移路程比阈值 |
| `threshold_max_cnt` | `int` | `4` | 超过阈值的最大次数 |
| `threshold_min_cnt` | `int` | `1` | 超过阈值的最小次数 |

#### 配置示例

```python
{
    'class': AbuPickStockShiftDistance,
    'threshold_sd': 2.5,           # 位移路程比阈值
    'threshold_max_cnt': 3,        # 最大次数
    'threshold_min_cnt': 1         # 最小次数
}
```

### 3.5 AbuPickStockNTop (涨跌幅排名选股因子) ⭐⭐⭐

**功能描述**: 基于一段时间内的涨跌幅排名进行选股，选择表现最好或最差的股票  
**文件位置**: `abupy/PickStockBu/ABuPickStockDemo.py`  
**应用场景**: 选择涨幅或跌幅排名靠前的股票，适用于动量或反转策略

#### 特定参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `symbol_pool` | `List[str]` | `[]` | 参与排名比较的股票池 |
| `n_top` | `int` | `3` | 选取排名前n_top的股票数量 |
| `direction_top` | `int` | `1` | 选择方向，1选涨幅最大，-1选跌幅最大 |

#### 配置示例

```python
{
    'class': AbuPickStockNTop,
    'symbol_pool': ['usTSLA', 'usAAPL', 'usGOOG', 'usMSFT'],
    'n_top': 2,                    # 选择前2名
    'direction_top': 1             # 选择涨幅最大的
}
```

---

## 4. 完整的stock_picks配置示例

### 4.1 示例1: 趋势+价格双重筛选

**策略描述**: 选择上升趋势且价格在合理区间的股票

```python
stock_picks = [
    {
        'class': AbuPickRegressAngMinMax,
        'threshold_ang_min': 0.0,
        'threshold_ang_max': 45.0,
        'reversed': False
    },
    {
        'class': AbuPickStockPriceMinMax,
        'threshold_price_min': 10.0,
        'threshold_price_max': 200.0,
        'reversed': False
    }
]
```

### 4.2 示例2: 相似度+涨幅排名组合

**策略描述**: 选择与AAPL相似且涨幅排名靠前的股票

```python
stock_picks = [
    {
        'class': AbuPickSimilarNTop,
        'similar_stock': 'usAAPL',
        'n_top': 30,
        'rolling': False
    },
    {
        'class': AbuPickStockNTop,
        'symbol_pool': ['usTSLA', 'usAAPL', 'usGOOG', 'usMSFT', 'usAMZN'],
        'n_top': 3,
        'direction_top': 1,
        'xd': 60
    }
]
```

### 4.3 示例3: 反转策略选股

**策略描述**: 选择下跌趋势的股票，用于反转策略

```python
stock_picks = [
    {
        'class': AbuPickRegressAngMinMax,
        'threshold_ang_min': -45.0,
        'threshold_ang_max': 0.0,
        'reversed': False
    },
    {
        'class': AbuPickStockNTop,
        'symbol_pool': ['002230', '300104', '300059', '601766', '600085'],
        'n_top': 2,
        'direction_top': -1,  # 选择跌幅最大的
        'xd': 30
    }
]
```

### 4.4 示例4: 波动特征筛选

**策略描述**: 基于价格波动特征和位移路程比进行选股

```python
stock_picks = [
    {
        'class': AbuPickStockShiftDistance,
        'threshold_sd': 2.5,
        'threshold_max_cnt': 3,
        'threshold_min_cnt': 1,
        'xd': 120
    },
    {
        'class': AbuPickStockPriceMinMax,
        'threshold_price_min': 5.0,
        'threshold_price_max': 100.0,
        'reversed': False
    }
]
```

---

## 5. 使用指南

### 5.1 基础使用方式

```python
import abupy
from abupy import AbuPickRegressAngMinMax, AbuPickStockPriceMinMax

# 配置选股因子
stock_picks = [
    {
        'class': AbuPickRegressAngMinMax,
        'threshold_ang_min': 0.0,
        'reversed': False
    },
    {
        'class': AbuPickStockPriceMinMax,
        'threshold_price_min': 10.0,
        'threshold_price_max': 200.0,
        'reversed': False
    }
]

# 在回测中使用
abu_result_tuple, kl_pd_manager = abupy.run_loop_back(
    read_cash=1000000,
    buy_factors=buy_factors,
    sell_factors=sell_factors,
    stock_picks=stock_picks,  # 选股因子配置
    choice_symbols=None       # None表示全市场选股
)
```

### 5.2 高级使用技巧

- **组合逻辑**: 多个选股因子采用'与'逻辑，即股票必须通过所有选股因子的筛选
- **反转选股**: 使用`reversed=True`可以反转选股结果，选择不符合条件的股票
- **周期控制**: `xd`参数控制选股分析的时间周期，不同策略可能需要不同周期
- **动态选股**: 选股因子可以与买入因子的`stock_pickers`参数配合使用，实现动态选股
- **性能考虑**: 相似度选股因子计算量较大，建议在股票池较小时使用

### 5.3 性能优化建议

- 选股因子的执行顺序会影响性能，建议将筛选力度大的因子放在前面
- 使用`AbuPickStockMaster.do_pick_stock_with_process()`进行并行选股
- 相似度计算和位移路程比计算较为耗时，谨慎使用
- 合理设置`xd`和`min_xd`参数，避免数据不足导致的选股失败

---

## 6. 对"选股器"页面设计的建议

### 6.1 功能分类展示

建议按以下分类组织选股因子：

- **趋势分析类**: AbuPickRegressAngMinMax
- **价格筛选类**: AbuPickStockPriceMinMax  
- **相似度分析类**: AbuPickSimilarNTop
- **波动分析类**: AbuPickStockShiftDistance
- **排名筛选类**: AbuPickStockNTop

### 6.2 用户界面设计

- **参数配置界面**: 为每个因子提供直观的参数配置界面
- **说明文档**: 提供详细的参数说明和使用示例
- **多因子组合**: 支持多因子组合配置和预览功能
- **配置模板**: 提供常用配置模板和示例
- **参数验证**: 添加参数验证和性能提示功能

### 6.3 推荐配置

- **新手推荐**: AbuPickRegressAngMinMax + AbuPickStockPriceMinMax
- **进阶配置**: 添加AbuPickStockNTop进行排名筛选
- **高级策略**: 结合AbuPickSimilarNTop进行相似度分析

---

## 7. 结论

### 7.1 关键发现

1. ✅ **abupy提供5个开箱即用的选股因子**
2. ✅ **所有选股因子都支持3个基础参数**(reversed, xd, min_xd)
3. ✅ **选股因子采用'与'逻辑组合**，必须全部通过
4. ✅ **支持趋势、价格、相似度、波动等多种选股策略**
5. ✅ **提供了4个完整的多因子组合配置示例**

### 7.2 技术可行性

**完全可行** - 所有选股因子都有明确的参数定义和使用方式，可以直接用于"选股器"页面的设计和实现。

### 7.3 实施建议

本报告为abu_modern项目的"选股器"页面设计提供了完整的技术依据，建议：

1. **按功能分类展示选股因子**，提高用户体验
2. **提供参数配置界面和说明文档**，降低使用门槛  
3. **支持多因子组合配置**，满足不同策略需求
4. **提供常用配置模板**，快速上手使用
5. **添加性能提示功能**，优化用户体验
