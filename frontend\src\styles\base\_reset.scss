// CSS重置样式

// 基础重置
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// HTML和body
html {
  font-size: 16px;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: $font-family-base;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-base;
  color: $text-color-primary;
  background-color: $bg-color-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 标题重置
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: $font-weight-medium;
  line-height: $line-height-sm;
}

// 段落重置
p {
  margin: 0;
}

// 列表重置
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// 链接重置
a {
  color: $primary-color;
  text-decoration: none;
  background-color: transparent;
  transition: color 0.3s;
  
  &:hover {
    color: $primary-light;
  }
  
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// 按钮重置
button {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  background: transparent;
  border: 0;
  cursor: pointer;
  
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// 表单元素重置
input, textarea, select {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border: $border-width-thin solid $border-color;
  border-radius: $border-radius-base;
  
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
    border-color: $primary-color;
  }
}

// 图片重置
img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

// 表格重置
table {
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  padding: 0;
  text-align: left;
}