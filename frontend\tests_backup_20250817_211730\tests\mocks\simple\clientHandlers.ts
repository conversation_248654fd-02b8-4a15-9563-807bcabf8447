// 通用客户端API Mock处理器 - 严格按照API契约
// 保留一些通用的测试和健康检查端点

import { http, HttpResponse } from 'msw';

export const clientHandlers = [
  // 健康检查端点（如果后端有的话）
  http.get('/health', () => {
    return HttpResponse.json({
      success: true,
      message: "服务正常运行",
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    });
  }),

  // 通用测试端点（仅用于测试，实际API可能不存在）
  http.get('/test/success', () => {
    return HttpResponse.json({
      success: true,
      message: "测试成功",
      data: { test: true }
    });
  }),

  // 错误响应测试
  http.get('/test/error', () => {
    return HttpResponse.json({
      success: false,
      message: "测试错误",
      error_code: "TEST_ERROR"
    }, { status: 500 });
  }),

  // 网络错误测试
  http.get('/test/network-error', () => {
    return HttpResponse.error();
  })
];