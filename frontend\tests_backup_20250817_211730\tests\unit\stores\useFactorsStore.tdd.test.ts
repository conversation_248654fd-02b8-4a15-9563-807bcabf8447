import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useFactorsStore } from '../../../src/stores/useFactorsStore';
import * as factorsApi from '@/api/factors';

// TDD专用测试文件 - useFactorsStore
// 专注于因子数据获取操作的快速验证

// 契约 1: 外部依赖必须被完全模拟
vi.mock('@/api/factors');

// 契约 2: 定义标准的模拟数据
const mockBuyFactors = [
  {
    id: 'buy-factor-1',
    name: 'AbuDoubleMaBuy',
    description: '双均线交叉买入策略',
    factor_type: 'buy',
    class_name: 'AbuDoubleMaBuy',
    parameters: { fast_ma: 5, slow_ma: 20 }
  },
  {
    id: 'buy-factor-2', 
    name: 'AbuRSIBuy',
    description: 'RSI超卖买入策略',
    factor_type: 'buy',
    class_name: 'AbuRSIBuy',
    parameters: { rsi_period: 14, oversold_threshold: 30 }
  }
];

const mockSellFactors = [
  {
    id: 'sell-factor-1',
    name: 'AbuRSISell',
    description: 'RSI超买卖出策略', 
    factor_type: 'sell',
    class_name: 'AbuRSISell',
    parameters: { rsi_period: 14, overbought_threshold: 70 }
  }
];

const mockFactorsResponse = {
  buy_factors: mockBuyFactors,
  sell_factors: mockSellFactors
};

describe('useFactorsStore - TDD专用测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态 - TDD', () => {
      const store = useFactorsStore();
      expect(store.factors).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });
  });

  describe('契约 B: fetchFactors action - 成功场景', () => {
    it('成功获取因子数据后，应正确合并买入和卖出因子并更新store状态 - TDD', async () => {
      const store = useFactorsStore();
      
      // Mock API调用成功
      vi.mocked(factorsApi.getFactors).mockResolvedValue(mockFactorsResponse);
      
      // 验证初始状态
      expect(store.factors).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
      
      // 调用fetchFactors
      await store.fetchFactors();
      
      // 验证最终状态
      const expectedFactors = [...mockBuyFactors, ...mockSellFactors];
      expect(store.factors).toEqual(expectedFactors);
      expect(store.factors.length).toBe(3); // 2个买入因子 + 1个卖出因子
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
      
      // 验证API被调用
      expect(factorsApi.getFactors).toHaveBeenCalledTimes(1);
    });

    it('API返回不完整数据时，应能容错处理并设置空数组 - TDD', async () => {
      const store = useFactorsStore();
      
      // Mock API返回不完整数据
      const incompleteResponse = {
        buy_factors: null, // 买入因子为null
        sell_factors: undefined // 卖出因子为undefined  
      };
      vi.mocked(factorsApi.getFactors).mockResolvedValue(incompleteResponse as any);
      
      await store.fetchFactors();
      
      // 验证容错处理：应该设置为空数组而不是报错
      expect(store.factors).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
    });
  });

  describe('契约 C: fetchFactors action - 失败场景', () => {
    it('API调用失败时，应设置错误状态并保持factors为空数组 - TDD', async () => {
      const store = useFactorsStore();
      const errorMessage = '网络连接失败';
      
      // Mock API调用失败
      vi.mocked(factorsApi.getFactors).mockRejectedValue(new Error(errorMessage));
      
      // 验证初始状态
      expect(store.factors).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeNull();
      
      // 调用fetchFactors
      await store.fetchFactors();
      
      // 验证失败后的状态
      expect(store.factors).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBe(errorMessage);
      
      // 验证API被调用
      expect(factorsApi.getFactors).toHaveBeenCalledTimes(1);
    });

    it('非Error对象异常时，应设置通用错误信息 - TDD', async () => {
      const store = useFactorsStore();
      
      // Mock非Error对象的异常
      vi.mocked(factorsApi.getFactors).mockRejectedValue('某种非Error异常');
      
      await store.fetchFactors();
      
      // 验证错误处理
      expect(store.factors).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBe('网络请求失败');
    });
  });

  describe('契约 D: 加载状态管理', () => {
    it('fetchFactors执行期间，isLoading应正确设置为true - TDD', async () => {
      const store = useFactorsStore();
      
      // 创建一个延迟的Promise来模拟异步操作
      let resolvePromise: () => void;
      const delayedPromise = new Promise<typeof mockFactorsResponse>((resolve) => {
        resolvePromise = () => resolve(mockFactorsResponse);
      });
      
      vi.mocked(factorsApi.getFactors).mockReturnValue(delayedPromise);
      
      // 开始异步操作
      const fetchPromise = store.fetchFactors();
      
      // 验证加载状态已设置
      expect(store.isLoading).toBe(true);
      expect(store.error).toBeNull();
      
      // 完成异步操作
      resolvePromise!();
      await fetchPromise;
      
      // 验证加载状态已清除
      expect(store.isLoading).toBe(false);
    });
  });
});
