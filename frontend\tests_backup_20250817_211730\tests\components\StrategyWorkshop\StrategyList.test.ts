import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import StrategyList from '@/components/StrategyWorkshop/StrategyList.vue'
import { SimpleStrategyDataFactory } from '../../factories/SimpleStrategyDataFactory'
import type { Strategy } from '@/api/types'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElButton: {
    name: 'ElButton',
    props: ['type', 'size'],
    template: '<button class="el-button"><slot /></button>'
  },
  ElSkeleton: {
    name: 'ElSkeleton',
    props: ['rows', 'animated'],
    template: '<div class="el-skeleton"></div>'
  },
  ElAlert: {
    name: '<PERSON><PERSON>ler<PERSON>',
    props: ['type', 'title', 'showIcon'],
    template: '<div class="el-alert">{{ title }}</div>'
  },
  ElCard: {
    name: '<PERSON>Card',
    props: ['shadow'],
    emits: ['click'],
    template: '<div class="el-card" @click="$emit(\'click\')"><slot /></div>'
  },
  ElTag: {
    name: 'ElTag',
    props: ['type', 'size'],
    template: '<span class="el-tag"><slot /></span>'
  }
}))

// Mock 日期工具函数
vi.mock('@/utils/dateUtils', () => ({
  formatDate: vi.fn((date) => date ? '2023-01-01' : '未知时间')
}))

describe('StrategyList.vue', () => {
  let wrapper: VueWrapper<any>
  let mockStrategies: Strategy[]

  beforeEach(() => {
    mockStrategies = SimpleStrategyDataFactory.createStrategies(3)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  describe('基础渲染', () => {
    it('应该正确渲染标题和新建按钮', () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: [],
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      expect(wrapper.find('h3').text()).toBe('我的策略')
      expect(wrapper.find('.new-strategy-btn').exists()).toBe(true)
    })

    it('应该在点击新建按钮时发出create-strategy事件', async () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: [],
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      const createBtn = wrapper.find('.new-strategy-btn')
      await createBtn.trigger('click')

      expect(wrapper.emitted('create-strategy')).toHaveLength(1)
    })
  })

  describe('加载状态', () => {
    it('应该在加载时显示骨架屏', () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: [],
          currentStrategy: null,
          loading: true,
          error: null
        }
      })

      expect(wrapper.find('[data-testid="loading-indicator"]').exists()).toBe(true)
      expect(wrapper.find('.el-skeleton').exists()).toBe(true)
      expect(wrapper.find('[data-testid="strategy-table"]').exists()).toBe(false)
    })

    it('应该在出错时显示错误信息', () => {
      const errorMessage = '网络连接失败'
      wrapper = mount(StrategyList, {
        props: {
          strategies: [],
          currentStrategy: null,
          loading: false,
          error: errorMessage
        }
      })

      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
      expect(wrapper.find('.el-alert').text()).toBe(errorMessage)
      expect(wrapper.find('[data-testid="strategy-table"]').exists()).toBe(false)
    })
  })

  describe('策略列表', () => {
    it('应该正确渲染策略卡片', () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: mockStrategies,
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      expect(wrapper.find('[data-testid="strategy-table"]').exists()).toBe(true)
      
      const strategyCards = wrapper.findAll('[data-testid="strategy-row"]')
      expect(strategyCards).toHaveLength(mockStrategies.length)

      // 检查第一个策略卡片的内容
      const firstCard = strategyCards[0]
      expect(firstCard.find('[data-testid="strategy-name"]').text()).toBe(mockStrategies[0].name)
      expect(firstCard.find('.strategy-description').text()).toBe(mockStrategies[0].description)
      expect(firstCard.find('.strategy-author').text()).toContain(mockStrategies[0].author)
    })

    it('应该正确标记当前选中的策略', () => {
      const selectedStrategy = mockStrategies[0]
      
      wrapper = mount(StrategyList, {
        props: {
          strategies: mockStrategies,
          currentStrategy: selectedStrategy,
          loading: false,
          error: null
        }
      })

      const strategyCards = wrapper.findAll('.strategy-card')
      expect(strategyCards[0].classes()).toContain('is-active')
      expect(strategyCards[1].classes()).not.toContain('is-active')
    })

    it('应该在点击策略卡片时发出select-strategy事件', async () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: mockStrategies,
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      const firstCard = wrapper.find('[data-testid="strategy-row"]')
      await firstCard.trigger('click')

      expect(wrapper.emitted('select-strategy')).toHaveLength(1)
      expect(wrapper.emitted('select-strategy')?.[0][0]).toEqual(mockStrategies[0])
    })
  })

  describe('策略标签显示', () => {
    it('应该正确显示公开/私有标签', () => {
      const strategies = [
        { ...mockStrategies[0], is_public: true },
        { ...mockStrategies[1], is_public: false }
      ]

      wrapper = mount(StrategyList, {
        props: {
          strategies,
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      const tags = wrapper.findAll('.el-tag')
      expect(tags[0].text()).toBe('公开策略')
      expect(tags[1].text()).toBe('私有策略')
    })
  })

  describe('空状态处理', () => {
    it('应该处理空策略列表', () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: [],
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      expect(wrapper.find('[data-testid="strategy-table"]').exists()).toBe(true)
      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(0)
    })

    it('应该处理缺少描述的策略', () => {
      const strategiesWithoutDesc = [{
        ...mockStrategies[0],
        description: ''
      }]

      wrapper = mount(StrategyList, {
        props: {
          strategies: strategiesWithoutDesc,
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      expect(wrapper.find('.strategy-description').text()).toBe('暂无描述')
    })
  })

  describe('响应式行为', () => {
    it('应该在策略列表更新时重新渲染', async () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: [mockStrategies[0]],
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(1)

      // 更新props
      await wrapper.setProps({
        strategies: mockStrategies
      })

      expect(wrapper.findAll('[data-testid="strategy-row"]')).toHaveLength(mockStrategies.length)
    })

    it('应该在当前策略变化时更新高亮状态', async () => {
      wrapper = mount(StrategyList, {
        props: {
          strategies: mockStrategies,
          currentStrategy: null,
          loading: false,
          error: null
        }
      })

      // 初始状态：没有高亮
      expect(wrapper.find('.is-active').exists()).toBe(false)

      // 设置当前策略
      await wrapper.setProps({
        currentStrategy: mockStrategies[0]
      })

      // 验证第一个卡片被高亮
      const firstCard = wrapper.findAll('.strategy-card')[0]
      expect(firstCard.classes()).toContain('is-active')
    })
  })
})
