"""
策略服务单元测试模块

测试策略服务(StrategyService)的各项功能，包括创建、查询、更新和删除策略。
使用SQLite内存数据库进行测试，以确保测试的独立性和速度。
"""

import unittest
import pytest
from datetime import datetime
from typing import List
from sqlmodel import Session, SQLModel, select
from sqlalchemy import text
from unittest.mock import patch, Mock

from backend.app.schemas.strategy import BuyFactor, SellFactor, Strategy, StrategyCreate, StrategyUpdate
from backend.app.services.strategy_service import StrategyService
from backend.app.core.database import get_test_engine, create_test_db_and_tables
from backend.app.models.strategy_model import StrategyModel
from backend.app.core.exceptions import DataNotFoundError, AdapterError, ParameterError, FactorError


class TestStrategyService(unittest.TestCase):
    """测试策略服务类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用的内存数据库引擎
        self.engine = get_test_engine()
        
        # 创建数据库表
        create_test_db_and_tables(self.engine)
        
        # 创建数据库会话
        self.session = Session(self.engine)
        
        # 创建策略服务实例
        self.service = StrategyService(self.session)
        
        # 创建测试用的策略创建对象
        self.strategy_create = StrategyCreate(
            name="测试策略",
            description="这是一个测试策略",
            is_public=True,
            buy_factors=[
                BuyFactor(
                    name="测试买入因子",
                    description="测试用的买入因子",
                    factor_class="TestBuyFactor",
                    parameters={"p1": 1, "p2": 2}
                )
            ],
            sell_factors=[
                SellFactor(
                    name="测试卖出因子",
                    description="测试用的卖出因子",
                    factor_class="TestSellFactor",
                    parameters={"p1": 3, "p2": 4}
                )
            ],
            parameters={"test_param": "value"},
            tags=["测试", "示例"]
        )
    
    def tearDown(self):
        """测试后清理"""
        # 关闭会话
        self.session.close()
        
        # 删除所有表
        SQLModel.metadata.drop_all(self.engine)
    
    def test_create_strategy(self):
        """测试创建策略功能"""
        # 创建策略
        strategy = self.service.create_strategy(self.strategy_create)
        
        # 验证策略ID已生成
        self.assertIsNotNone(strategy.id)
        self.assertTrue(len(strategy.id) > 0)
        
        # 验证策略内容正确
        self.assertEqual(strategy.name, "测试策略")
        self.assertEqual(strategy.description, "这是一个测试策略")
        self.assertEqual(strategy.is_public, True)
        self.assertEqual(len(strategy.buy_factors), 1)
        self.assertEqual(len(strategy.sell_factors), 1)
        self.assertEqual(strategy.buy_factors[0].name, "测试买入因子")
        self.assertEqual(strategy.sell_factors[0].name, "测试卖出因子")
        self.assertEqual(strategy.parameters, {"test_param": "value"})
        self.assertEqual(strategy.tags, ["测试", "示例"])
        
        # 验证时间戳已设置
        self.assertIsNotNone(strategy.create_time)
        self.assertIsNotNone(strategy.update_time)
        
        # 验证策略已存储在数据库中
        # 查询数据库验证 - 使用ORM方式
        db_strategies = self.session.exec(select(StrategyModel)).all()
        self.assertEqual(len(db_strategies), 1)
    
    def test_get_strategy_by_id(self):
        """测试通过ID获取策略功能"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 通过ID获取策略
        retrieved_strategy = self.service.get_strategy_by_id(created_strategy.id)
        
        # 验证获取的策略正确
        self.assertIsNotNone(retrieved_strategy)
        self.assertEqual(retrieved_strategy.id, created_strategy.id)
        self.assertEqual(retrieved_strategy.name, created_strategy.name)
        
        # 测试获取不存在的策略
        with pytest.raises(DataNotFoundError, match=r"未找到ID为 non_existent_id 的策略"):
            self.service.get_strategy_by_id("non_existent_id")
    
    def test_get_strategies(self):
        """测试获取策略列表功能"""
        # 创建多个策略
        strategies = []
        for i in range(5):
            strategy_create = StrategyCreate(
                name=f"测试策略{i}",
                description=f"这是测试策略{i}",
                is_public=True if i % 2 == 0 else False,
                buy_factors=[],
                sell_factors=[],
                parameters={},
                tags=[]
            )
            strategy = self.service.create_strategy(strategy_create)
            strategies.append(strategy)
        
        # 获取所有策略
        all_strategies = self.service.get_strategies()
        self.assertEqual(len(all_strategies), 5)
        
        # 测试分页功能
        paged_strategies, _ = self.service.get_all_strategies_paginated(skip=1, limit=2)
        self.assertEqual(len(paged_strategies), 2)
        
        # 验证分页结果正确 - 注意：数据库查询可能会按不同顺序返回结果，这里我们只验证数量
    
    def test_update_strategy(self):
        """测试更新策略功能"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 创建更新数据
        update_data = StrategyUpdate(
            name="更新后的策略名称",
            description="更新后的描述",
            is_public=False,
            parameters={"updated_param": "new_value"}
        )
        
        # 更新策略
        updated_strategy = self.service.update_strategy(created_strategy.id, update_data)
        
        # 验证更新成功
        self.assertIsNotNone(updated_strategy)
        self.assertEqual(updated_strategy.id, created_strategy.id)
        self.assertEqual(updated_strategy.name, "更新后的策略名称")
        self.assertEqual(updated_strategy.description, "更新后的描述")
        self.assertEqual(updated_strategy.is_public, False)
        self.assertEqual(updated_strategy.parameters, {"updated_param": "new_value"})
        
        # 验证未更新的字段保持不变
        self.assertEqual(len(updated_strategy.buy_factors), 1)
        self.assertEqual(len(updated_strategy.sell_factors), 1)
        self.assertEqual(updated_strategy.tags, ["测试", "示例"])
        
        # 验证更新时间已更新
        self.assertNotEqual(updated_strategy.update_time, created_strategy.update_time)
        
        # 测试更新不存在的策略
        with pytest.raises(DataNotFoundError, match=r"未找到ID为 non_existent_id 的策略，无法更新"):
            self.service.update_strategy("non_existent_id", update_data)
    
    def test_delete_strategy(self):
        """测试删除策略功能"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 确认策略已创建
        db_strategies = self.session.exec(select(StrategyModel)).all()
        self.assertEqual(len(db_strategies), 1)
        
        # 删除策略
        result = self.service.delete_strategy(created_strategy.id)
        
        # 验证删除成功
        self.assertTrue(result)
        db_strategies = self.session.exec(select(StrategyModel)).all()
        self.assertEqual(len(db_strategies), 0)
        
        # 测试删除不存在的策略
        with pytest.raises(DataNotFoundError, match=r"未找到ID为 non_existent_id 的策略，无法删除"):
            self.service.delete_strategy("non_existent_id")
            
    @patch('backend.app.services.strategy_service.StrategyExecutor')
    def test_execute_strategy_success(self, mock_executor):
        """测试策略执行功能 - 正常情况"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 模拟策略执行器返回值
        expected_result = {
            "metrics": {
                "ann_return": 0.15,
                "sharpe": 1.2,
                "max_drawdown": -0.25
            },
            "orders": [
                {"symbol": "SZ000001", "action": "buy", "date": "2022-01-10", "price": 10.5},
                {"symbol": "SZ000001", "action": "sell", "date": "2022-06-15", "price": 12.5}
            ]
        }
        mock_executor.execute_strategy.return_value = expected_result
        
        # 准备市场数据
        market_data = {
            "choice_symbols": ["SZ000001", "SH600000"],
            "start_date": "2022-01-01",
            "end_date": "2022-12-31",
            "data_source": "tushare"
        }
        
        # 调用执行策略方法
        result = self.service.execute_strategy(created_strategy.id, market_data)
        
        # 验证结果正确
        self.assertEqual(result, expected_result)
        
        # 验证调用正确
        mock_executor.execute_strategy.assert_called_once()
        args, kwargs = mock_executor.execute_strategy.call_args
        self.assertEqual(args[0].id, created_strategy.id)  # 第一个参数应该是策略对象
        self.assertEqual(args[1], market_data)  # 第二个参数应该是市场数据
    
    @patch('backend.app.services.strategy_service.StrategyExecutor')
    def test_execute_strategy_not_found(self, mock_executor):
        """测试执行不存在的策略"""
        # 准备市场数据
        market_data = {
            "choice_symbols": ["SZ000001"],
            "start_date": "2022-01-01",
            "end_date": "2022-12-31",
            "data_source": "tushare"
        }
        
        # 测试执行不存在的策略
        with pytest.raises(DataNotFoundError, match=r"未找到ID为 non_existent_id 的策略"):
            self.service.execute_strategy("non_existent_id", market_data)
        
        # 验证策略执行器没有被调用
        mock_executor.execute_strategy.assert_not_called()
    
    @patch('backend.app.services.strategy_service.StrategyExecutor')
    def test_execute_strategy_parameter_error(self, mock_executor):
        """测试执行策略时参数错误"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 模拟执行器抛出ParameterError异常
        mock_executor.execute_strategy.side_effect = ParameterError(
            message="缺少必要参数: choice_symbols", 
            error_code="MISSING_PARAMETER"
        )
        
        # 准备缺少必要参数的市场数据
        market_data = {
            "start_date": "2022-01-01",
            "end_date": "2022-12-31",
            "data_source": "tushare"
        }
        
        # 测试异常捕获
        with pytest.raises(ParameterError, match=r"缺少必要参数: choice_symbols"):
            self.service.execute_strategy(created_strategy.id, market_data)
    
    @patch('backend.app.services.strategy_service.StrategyExecutor')
    def test_execute_strategy_factor_error(self, mock_executor):
        """测试执行策略时因子错误"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 模拟执行器抛出FactorError异常
        mock_executor.execute_strategy.side_effect = FactorError(
            message="因子类 'TestBuyFactor' 未在系统中注册", 
            error_code="FACTOR_NOT_REGISTERED"
        )
        
        # 准备市场数据
        market_data = {
            "choice_symbols": ["SZ000001"],
            "start_date": "2022-01-01",
            "end_date": "2022-12-31",
            "data_source": "tushare"
        }
        
        # 测试异常捕获
        with pytest.raises(FactorError, match=r"因子类 'TestBuyFactor' 未在系统中注册"):
            self.service.execute_strategy(created_strategy.id, market_data)
    
    @patch('backend.app.services.strategy_service.StrategyExecutor')
    def test_execute_strategy_unexpected_error(self, mock_executor):
        """测试执行策略时发生意外异常"""
        # 创建策略
        created_strategy = self.service.create_strategy(self.strategy_create)
        
        # 模拟执行器抛出意外异常
        mock_executor.execute_strategy.side_effect = ValueError("执行过程中出现意外错误")
        
        # 准备市场数据
        market_data = {
            "choice_symbols": ["SZ000001"],
            "start_date": "2022-01-01",
            "end_date": "2022-12-31",
            "data_source": "tushare"
        }
        
        # 测试异常捕获和包装
        with pytest.raises(AdapterError, match=r"执行策略时发生未预期的错误"):
            self.service.execute_strategy(created_strategy.id, market_data)


if __name__ == "__main__":
    unittest.main()
