# -*- coding: utf-8 -*-
"""
市场数据相关的Pydantic模型
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel
from datetime import datetime


class StockInfo(BaseModel):
    """股票信息"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型 CN/US/HK
    industry: Optional[str] = None  # 行业
    list_date: Optional[str] = None  # 上市日期


class StockBasic(BaseModel):
    """股票基础信息"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型 CN/US/HK
    industry: Optional[str] = None  # 行业
    list_date: Optional[str] = None  # 上市日期
    is_hs: Optional[str] = None  # 是否沪深300
    is_sz: Optional[str] = None  # 是否深证成指
    is_zz: Optional[str] = None  # 是否中证500
    is_st: Optional[bool] = None  # 是否ST股


class KlineItem(BaseModel):
    """K线数据项"""
    date: str  # 日期
    open: float  # 开盘价
    high: float  # 最高价
    low: float  # 最低价
    close: float  # 收盘价
    volume: float  # 成交量
    amount: Optional[float] = None  # 成交额
    turnover_rate: Optional[float] = None  # 换手率
    change_rate: Optional[float] = None  # 涨跌幅


class KlineData(BaseModel):
    """K线数据集合"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型
    period: str  # 周期类型
    data: List[KlineItem]  # K线数据列表
    latest_date: str  # 最新交易日期
    indicators: Optional[Dict[str, List[Optional[float]]]] = None  # 技术指标数据


class StockFundamental(BaseModel):
    """股票基本面数据"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型
    date: str  # 日期
    
    # 基本财务数据
    pe: Optional[float] = None  # 市盈率
    pe_ttm: Optional[float] = None  # 市盈率TTM
    pb: Optional[float] = None  # 市净率
    ps: Optional[float] = None  # 市销率
    ps_ttm: Optional[float] = None  # 市销率TTM
    dv_ratio: Optional[float] = None  # 股息率
    dv_ttm: Optional[float] = None  # 股息率TTM
    total_mv: Optional[float] = None  # 总市值
    circ_mv: Optional[float] = None  # 流通市值
    
    # 扩展字段，可根据需求添加
    extra_data: Optional[Dict[str, Any]] = None


class FundamentalData(BaseModel):
    """基本面数据"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    industry: Optional[str] = None  # 行业
    area: Optional[str] = None  # 地区
    market: str  # 市场类型
    listing_date: Optional[str] = None  # 上市日期
    report_date: Optional[str] = None  # 报告日期
    
    # 估值指标
    pe_ratio: Optional[float] = None  # 市盈率
    pe_ttm: Optional[float] = None  # 市盈率TTM
    pb_ratio: Optional[float] = None  # 市净率
    ps_ratio: Optional[float] = None  # 市销率
    ps_ttm: Optional[float] = None  # 市销率TTM
    dividend_yield: Optional[float] = None  # 股息率
    
    # 市值指标
    market_cap: Optional[float] = None  # 总市值
    circulating_cap: Optional[float] = None  # 流通市值
    
    # 盈利能力指标
    roe: Optional[float] = None  # 净资产收益率
    roa: Optional[float] = None  # 总资产收益率
    net_profit_margin: Optional[float] = None  # 净利润率
    gross_profit_margin: Optional[float] = None  # 毛利润率
    
    # 偿债能力指标
    debt_to_asset_ratio: Optional[float] = None  # 资产负债率
    current_ratio: Optional[float] = None  # 流动比率
    quick_ratio: Optional[float] = None  # 速动比率
    
    # 扩展字段
    extra_data: Optional[Dict[str, Any]] = None


class KlineRequest(BaseModel):
    """K线数据请求"""
    symbol: str  # 股票代码
    period: str = "1d"  # 周期类型
    start_date: Optional[str] = None  # 开始日期
    end_date: Optional[str] = None  # 结束日期
    limit: Optional[int] = 100  # 数据条数限制


class KlineResponse(BaseModel):
    """K线数据响应"""
    data: KlineData  # K线数据
    success: bool = True  # 请求是否成功
    message: Optional[str] = None  # 响应消息


class FundamentalResponse(BaseModel):
    """基本面数据响应"""
    data: FundamentalData  # 基本面数据
    success: bool = True  # 请求是否成功
    message: Optional[str] = None  # 响应消息


class StockItem(BaseModel):
    """股票项目"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型
    industry: Optional[str] = None  # 行业
    list_date: Optional[str] = None  # 上市日期


class StockListRequest(BaseModel):
    """股票列表请求"""
    market: str = "CN"  # 市场类型
    name_filter: Optional[str] = None  # 名称过滤
    limit: int = 100  # 数据条数限制


class StockListResponse(BaseModel):
    """股票列表响应"""
    data: List[StockItem]  # 股票列表
    total: int  # 总数
    success: bool = True  # 请求是否成功
    message: Optional[str] = None  # 响应消息
