# -*- coding: utf-8 -*-
"""
市场数据相关的Pydantic模型
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel
from datetime import datetime


class StockBasic(BaseModel):
    """股票基础信息"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型 CN/US/HK
    industry: Optional[str] = None  # 行业
    list_date: Optional[str] = None  # 上市日期
    is_hs: Optional[str] = None  # 是否沪深300
    is_sz: Optional[str] = None  # 是否深证成指
    is_zz: Optional[str] = None  # 是否中证500
    is_st: Optional[bool] = None  # 是否ST股


class KlineItem(BaseModel):
    """K线数据项"""
    date: str  # 日期
    open: float  # 开盘价
    high: float  # 最高价
    low: float  # 最低价
    close: float  # 收盘价
    volume: float  # 成交量
    amount: Optional[float] = None  # 成交额
    turnover_rate: Optional[float] = None  # 换手率
    change_rate: Optional[float] = None  # 涨跌幅


class KlineData(BaseModel):
    """K线数据集合"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型
    period: str  # 周期类型
    data: List[KlineItem]  # K线数据列表
    latest_date: str  # 最新交易日期
    indicators: Optional[Dict[str, List[Optional[float]]]] = None  # 技术指标数据


class StockFundamental(BaseModel):
    """股票基本面数据"""
    symbol: str  # 股票代码
    name: str  # 股票名称
    market: str  # 市场类型
    date: str  # 日期
    
    # 基本财务数据
    pe: Optional[float] = None  # 市盈率
    pe_ttm: Optional[float] = None  # 市盈率TTM
    pb: Optional[float] = None  # 市净率
    ps: Optional[float] = None  # 市销率
    ps_ttm: Optional[float] = None  # 市销率TTM
    dv_ratio: Optional[float] = None  # 股息率
    dv_ttm: Optional[float] = None  # 股息率TTM
    total_mv: Optional[float] = None  # 总市值
    circ_mv: Optional[float] = None  # 流通市值
    
    # 扩展字段，可根据需求添加
    extra_data: Optional[Dict[str, Any]] = None
