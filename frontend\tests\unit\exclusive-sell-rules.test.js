/**
 * 专属卖出规则功能测试套件
 * TDD 红阶段 - 测试用例定义
 * 
 * 测试目标：为买入因子配置专属卖出规则的完整功能流程
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElDialog, ElTag, ElButton } from 'element-plus'
import { createPinia, setActivePinia } from 'pinia'

// 组件导入
import BuyFactorCard from '@/components/strategy/BuyFactorCard.vue'
import ExclusiveSellRulesDialog from '@/components/strategy/ExclusiveSellRulesDialog.vue'
import StrategyFormDialog from '@/components/forms/StrategyFormDialog.vue'

// Store导入
import { useStrategyStore } from '@/stores/modules/useStrategyStore'

describe('专属卖出规则功能测试', () => {
  let wrapper
  let strategyStore
  
  // 模拟买入因子数据
  const mockBuyFactor = {
    id: 'buy-factor-1',
    name: '双均线交叉策略',
    type: 'technical',
    params: {
      short_period: 5,
      long_period: 20
    },
    sell_factors: [] // 初始为空，用于测试专属卖出规则添加
  }

  beforeEach(() => {
    // 设置 Pinia
    setActivePinia(createPinia())
    strategyStore = useStrategyStore()
    
    // 初始化store状态
    strategyStore.currentSelectedStrategy = {
      id: 'test-strategy',
      name: '测试策略',
      buy_factors: [mockBuyFactor],
      sell_factors: []
    }
  })

  describe('入口点测试', () => {
    beforeEach(() => {
      wrapper = mount(BuyFactorCard, {
        props: {
          buyFactor: mockBuyFactor
        },
        global: {
          plugins: [createPinia()],
          components: {
            ElDialog,
            ElTag,
            ElButton
          }
        }
      })
    })

    it('应该在买入因子卡片上显示专属卖出规则按钮', () => {
      // 验证按钮存在
      const exclusiveRuleBtn = wrapper.find('[data-testid="exclusive-sell-rule-btn"]')
      expect(exclusiveRuleBtn.exists()).toBe(true)
      
      // 验证按钮在正确的容器内
      const buyFactorCard = wrapper.find('.buy-factor-card')
      expect(buyFactorCard.exists()).toBe(true)
      expect(buyFactorCard.find('[data-testid="exclusive-sell-rule-btn"]').exists()).toBe(true)
    })

    it('点击专属卖出规则按钮应该打开对话框', async () => {
      // 查找并点击按钮
      const exclusiveRuleBtn = wrapper.find('[data-testid="exclusive-sell-rule-btn"]')
      await exclusiveRuleBtn.trigger('click')

      // 验证对话框被打开
      const dialog = wrapper.findComponent(ExclusiveSellRulesDialog)
      expect(dialog.exists()).toBe(true)
      expect(dialog.props('visible')).toBe(true)
    })

    it('对话框标题应该包含当前买入因子的名称', async () => {
      // 点击打开对话框
      const exclusiveRuleBtn = wrapper.find('[data-testid="exclusive-sell-rule-btn"]')
      await exclusiveRuleBtn.trigger('click')

      // 验证对话框标题
      const dialog = wrapper.findComponent(ExclusiveSellRulesDialog)
      const expectedTitle = `为'${mockBuyFactor.name}'配置专属卖出规则`
      expect(dialog.props('title')).toBe(expectedTitle)
    })
  })

  describe('对话框内部逻辑测试', () => {
    let dialogWrapper

    beforeEach(() => {
      dialogWrapper = mount(ExclusiveSellRulesDialog, {
        props: {
          visible: true,
          buyFactor: mockBuyFactor,
          title: `为'${mockBuyFactor.name}'配置专属卖出规则`
        },
        global: {
          plugins: [createPinia()],
          components: {
            ElDialog,
            ElTag,
            ElButton,
            StrategyFormDialog
          }
        }
      })
    })

    it('应该显示添加卖出因子按钮', () => {
      const addSellFactorBtn = dialogWrapper.find('[data-testid="add-sell-factor-btn"]')
      expect(addSellFactorBtn.exists()).toBe(true)
      expect(addSellFactorBtn.text()).toContain('添加卖出因子')
    })

    it('点击添加卖出因子按钮应该打开因子选择对话框', async () => {
      const addSellFactorBtn = dialogWrapper.find('[data-testid="add-sell-factor-btn"]')
      await addSellFactorBtn.trigger('click')

      // 验证StrategyFormDialog被打开
      const strategyFormDialog = dialogWrapper.findComponent(StrategyFormDialog)
      expect(strategyFormDialog.exists()).toBe(true)
      expect(strategyFormDialog.props('modelValue')).toBe(true)
      expect(strategyFormDialog.props('currentFactorType')).toBe('sell') // 应该是卖出模式
    })

    it('应该能够在因子选择对话框中选择卖出因子', async () => {
      // 模拟打开因子选择对话框
      const addSellFactorBtn = dialogWrapper.find('[data-testid="add-sell-factor-btn"]')
      await addSellFactorBtn.trigger('click')

      const strategyFormDialog = dialogWrapper.findComponent(StrategyFormDialog)
      
      // 模拟选择一个卖出因子
      const mockSellFactor = {
        id: 'sell-factor-1',
        name: 'RSI超买',
        type: 'technical',
        params: {
          threshold: 70,
          period: 14
        }
      }

      // 触发因子选择确认事件
      await strategyFormDialog.vm.$emit('save', mockSellFactor)
      
      // 等待组件状态更新
      await dialogWrapper.vm.$nextTick()
      await dialogWrapper.vm.$nextTick() // 双重等待确保状态更新
      
      // 验证因子被添加到对话框内的列表中
      const factorList = dialogWrapper.find('[data-testid="exclusive-sell-factors-list"]')
      expect(factorList.exists()).toBe(true)
      
      // 查找卖出因子卡片而不是直接查找 .el-tag
      const sellFactorCards = factorList.findAll('.sell-factor-card')
      expect(sellFactorCards.length).toBeGreaterThan(0)
      
      // 验证卖出因子名称出现在某个卡片中
      const cardTexts = sellFactorCards.map(card => card.text())
      expect(cardTexts.some(text => text.includes(mockSellFactor.name))).toBe(true)
    })
  })

  describe('数据流与状态更新测试（集成测试核心）', () => {
    let mainWrapper

    beforeEach(() => {
      // 重新设置 Pinia 和 store，确保状态干净
      setActivePinia(createPinia())
      strategyStore = useStrategyStore()
      
      // 初始化store状态
      strategyStore.currentSelectedStrategy = {
        id: 'test-strategy',
        name: '测试策略',
        buy_factors: [mockBuyFactor],
        sell_factors: []
      }

      // 挂载包含BuyFactorCard的主组件
      mainWrapper = mount({
        template: `
          <div>
            <BuyFactorCard 
              :buy-factor="buyFactor" 
              @update:exclusive-sell-rules="handleExclusiveSellRulesUpdate"
            />
          </div>
        `,
        components: {
          BuyFactorCard
        },
        data() {
          return {
            buyFactor: mockBuyFactor
          }
        },
        methods: {
          handleExclusiveSellRulesUpdate(buyFactorId, sellFactors) {
            // 模拟更新store
            strategyStore.updateBuyFactorSellRules(buyFactorId, sellFactors)
          }
        }
      }, {
        global: {
          components: {
            ElDialog,
            ElTag,
            ElButton
          }
        }
      })
    })

    it('添加专属卖出因子后应该更新store中的数据', async () => {
      // 模拟添加一个新的专属卖出因子
      const newSellFactor = {
        id: 'exclusive-sell-1',
        name: 'RSI超买',
        type: 'technical', 
        params: {
          threshold: 70,
          period: 14
        },
        exclusive_to_buy_factor: mockBuyFactor.id
      }

      // 直接调用store方法模拟添加过程
      strategyStore.addExclusiveSellFactor(mockBuyFactor.id, newSellFactor)

      // 验证store状态被正确更新
      const updatedBuyFactor = strategyStore.currentSelectedStrategy.buy_factors.find(
        factor => factor.id === mockBuyFactor.id
      )
      
      expect(updatedBuyFactor.sell_factors).toHaveLength(1)
      expect(updatedBuyFactor.sell_factors[0]).toEqual(newSellFactor)
    })

    it('应该支持为同一个买入因子添加多个专属卖出规则', async () => {
      const sellFactor1 = {
        id: 'exclusive-sell-1',
        name: 'RSI超买',
        type: 'technical',
        params: { threshold: 70 },
        exclusive_to_buy_factor: mockBuyFactor.id
      }

      const sellFactor2 = {
        id: 'exclusive-sell-2', 
        name: '止损规则',
        type: 'risk_control',
        params: { stop_loss_pct: 0.05 },
        exclusive_to_buy_factor: mockBuyFactor.id
      }

      // 添加两个卖出因子
      strategyStore.addExclusiveSellFactor(mockBuyFactor.id, sellFactor1)
      strategyStore.addExclusiveSellFactor(mockBuyFactor.id, sellFactor2)

      // 验证两个因子都被正确添加
      const updatedBuyFactor = strategyStore.currentSelectedStrategy.buy_factors.find(
        factor => factor.id === mockBuyFactor.id
      )
      
      expect(updatedBuyFactor.sell_factors).toHaveLength(2)
      
      // 验证第一个卖出因子的核心属性
      const storedFactor1 = updatedBuyFactor.sell_factors.find(f => f.id === 'exclusive-sell-1')
      expect(storedFactor1).toBeDefined()
      expect(storedFactor1.name).toBe(sellFactor1.name)
      expect(storedFactor1.type).toBe(sellFactor1.type)
      expect(storedFactor1.exclusive_to_buy_factor).toBe(sellFactor1.exclusive_to_buy_factor)
      expect(storedFactor1.params.threshold).toBe(sellFactor1.params.threshold)
      
      // 验证第二个卖出因子的核心属性
      const storedFactor2 = updatedBuyFactor.sell_factors.find(f => f.id === 'exclusive-sell-2')
      expect(storedFactor2).toBeDefined()
      expect(storedFactor2.name).toBe(sellFactor2.name)
      expect(storedFactor2.type).toBe(sellFactor2.type)
      expect(storedFactor2.exclusive_to_buy_factor).toBe(sellFactor2.exclusive_to_buy_factor)
      expect(storedFactor2.params.stop_loss_pct).toBe(sellFactor2.params.stop_loss_pct)
    })
  })

  describe('UI反馈测试', () => {
    let wrapper

    beforeEach(() => {
      // 创建包含专属卖出规则的买入因子
      const buyFactorWithSellRules = {
        ...mockBuyFactor,
        sell_factors: [
          {
            id: 'exclusive-sell-1',
            name: 'RSI超买',
            type: 'technical',
            params: { threshold: 70 },
            exclusive_to_buy_factor: mockBuyFactor.id
          },
          {
            id: 'exclusive-sell-2',
            name: '止损规则', 
            type: 'risk_control',
            params: { stop_loss_pct: 0.05 },
            exclusive_to_buy_factor: mockBuyFactor.id
          }
        ]
      }

      wrapper = mount(BuyFactorCard, {
        props: {
          buyFactor: buyFactorWithSellRules
        },
        global: {
          plugins: [createPinia()],
          components: {
            ElDialog,
            ElTag,
            ElButton
          }
        }
      })
    })

    it('应该在买入因子卡片下方显示专属卖出规则', () => {
      // 验证专属规则显示区域存在
      const exclusiveRulesDisplay = wrapper.find('[data-testid="exclusive-rules-display"]')
      expect(exclusiveRulesDisplay.exists()).toBe(true)
      
      // 验证显示区域在正确的容器内
      const buyFactorCard = wrapper.find('.buy-factor-card')
      expect(buyFactorCard.find('[data-testid="exclusive-rules-display"]').exists()).toBe(true)
    })

    it('应该为每个专属卖出规则渲染ElTag组件', () => {
      const exclusiveRulesDisplay = wrapper.find('[data-testid="exclusive-rules-display"]')
      const elTags = exclusiveRulesDisplay.findAllComponents(ElTag)
      
      // 验证有两个ElTag组件
      expect(elTags).toHaveLength(2)
      
      // 验证ElTag显示正确的规则名称
      expect(elTags[0].text()).toContain('RSI超买')
      expect(elTags[1].text()).toContain('止损规则')
    })

    it('ElTag应该有正确的样式和属性', () => {
      const exclusiveRulesDisplay = wrapper.find('[data-testid="exclusive-rules-display"]')
      const elTags = exclusiveRulesDisplay.findAllComponents(ElTag)
      
      elTags.forEach(tag => {
        // 验证ElTag的基本属性
        expect(tag.props('type')).toBe('info') // 或其他预期的类型
        expect(tag.props('size')).toBe('small') // 或其他预期的大小
        expect(tag.props('closable')).toBe(true) // 如果支持删除功能
      })
    })

    it('点击ElTag的删除按钮应该移除对应的专属卖出规则', async () => {
      const exclusiveRulesDisplay = wrapper.find('[data-testid="exclusive-rules-display"]')
      const firstTag = exclusiveRulesDisplay.findComponent(ElTag)
      
      // 模拟点击删除按钮
      await firstTag.vm.$emit('close')
      
      // 验证删除事件被正确处理（这里需要根据实际实现来验证）
      // 例如验证store中的数据被更新，或者组件发出了删除事件
      expect(wrapper.emitted('remove-exclusive-sell-rule')).toBeTruthy()
      expect(wrapper.emitted('remove-exclusive-sell-rule')[0]).toEqual(['exclusive-sell-1'])
    })

    it('当没有专属卖出规则时不应该显示规则显示区域', () => {
      // 创建一个明确没有卖出规则的买入因子
      const buyFactorWithoutSellRules = {
        id: 'buy-factor-empty',
        name: '空的买入因子',
        type: 'technical',
        params: {
          short_period: 5,
          long_period: 20
        }
        // 注意：没有 sell_factors 属性
      }

      // 重新挂载组件，使用没有卖出规则的买入因子
      wrapper = mount(BuyFactorCard, {
        props: {
          buyFactor: buyFactorWithoutSellRules
        },
        global: {
          plugins: [createPinia()],
          components: {
            ElDialog,
            ElTag,
            ElButton
          }
        }
      })

      const exclusiveRulesDisplay = wrapper.find('[data-testid="exclusive-rules-display"]')
      expect(exclusiveRulesDisplay.exists()).toBe(false)
    })
  })

  describe('边界情况和错误处理', () => {
    beforeEach(() => {
      // 确保每个测试都有干净的 store 状态
      setActivePinia(createPinia())
      strategyStore = useStrategyStore()
      
      // 初始化store状态
      strategyStore.currentSelectedStrategy = {
        id: 'test-strategy',
        name: '测试策略',
        buy_factors: [mockBuyFactor],
        sell_factors: []
      }
    })

    it('应该处理添加专属卖出规则时的错误情况', async () => {
      // 模拟网络错误或其他错误情况
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 尝试添加一个无效的卖出因子
      const invalidSellFactor = null
      
      try {
        strategyStore.addExclusiveSellFactor(mockBuyFactor.id, invalidSellFactor)
      } catch (error) {
        expect(error).toBeDefined()
      }
      
      consoleSpy.mockRestore()
    })

    it('应该验证买入因子ID的有效性', () => {
      const validSellFactor = {
        id: 'sell-1',
        name: 'Test Sell Factor',
        type: 'technical',
        params: {}
      }

      // 尝试为不存在的买入因子添加卖出规则
      expect(() => {
        strategyStore.addExclusiveSellFactor('non-existent-id', validSellFactor)
      }).toThrow()
    })

    it('应该防止重复添加相同ID的专属卖出规则', () => {
      const sellFactor = {
        id: 'sell-1',
        name: 'Test Sell Factor',
        type: 'technical',
        params: {},
        exclusive_to_buy_factor: mockBuyFactor.id
      }

      // 第一次添加应该成功
      strategyStore.addExclusiveSellFactor(mockBuyFactor.id, sellFactor)
      
      // 第二次添加相同ID的规则应该失败或被忽略
      const initialLength = strategyStore.currentSelectedStrategy.buy_factors
        .find(f => f.id === mockBuyFactor.id).sell_factors.length

      strategyStore.addExclusiveSellFactor(mockBuyFactor.id, sellFactor)
      
      const finalLength = strategyStore.currentSelectedStrategy.buy_factors
        .find(f => f.id === mockBuyFactor.id).sell_factors.length

      expect(finalLength).toBe(initialLength) // 长度不应该增加
    })
  })
})

/**
 * Store方法的期望实现（TDD红阶段的接口定义）
 * 这些方法在实际实现时需要在useStrategyStore中创建
 */

/*
// 期望的Store方法签名：

// 为指定买入因子添加专属卖出规则
strategyStore.addExclusiveSellFactor(buyFactorId: string, sellFactor: SellFactor): void

// 移除指定买入因子的专属卖出规则  
strategyStore.removeExclusiveSellFactor(buyFactorId: string, sellFactorId: string): void

// 更新买入因子的卖出规则列表
strategyStore.updateBuyFactorSellRules(buyFactorId: string, sellFactors: SellFactor[]): void

// 获取指定买入因子的专属卖出规则
strategyStore.getExclusiveSellFactors(buyFactorId: string): SellFactor[]
*/
