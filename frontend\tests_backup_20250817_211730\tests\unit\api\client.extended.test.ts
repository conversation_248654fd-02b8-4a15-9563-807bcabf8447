// Client API Extended 测试 - 扩展单元测试
// 测试复杂场景、边界条件和性能

import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { setActivePinia, createPinia } from 'pinia';
import { apiClient } from '../../../src/api/client';
import { useAppStore } from '../../../src/stores/app';
import { SimpleClientDataFactory } from '../../factories/SimpleClientDataFactory';

// 创建MSW服务器
const server = setupServer();

describe('Client API Extended Tests', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterEach(() => {
    vi.restoreAllMocks();
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('HTTP方法扩展测试', () => {
    it('应该正确处理POST请求', async () => {
      const requestData = { name: 'test', value: 123 };
      const responseData = { id: 1, ...requestData };

      server.use(
        http.post('/api/test', async ({ request }) => {
          const body = await request.json();
          expect(body).toEqual(requestData);
          return HttpResponse.json(responseData);
        })
      );

      const result = await apiClient.post('/api/test', requestData);
      expect(result).toEqual(responseData);
    });

    it('应该正确处理PUT请求', async () => {
      const updateData = { id: 1, name: 'updated', value: 456 };

      server.use(
        http.put('/api/test/1', async ({ request }) => {
          const body = await request.json();
          expect(body).toEqual(updateData);
          return HttpResponse.json(updateData);
        })
      );

      const result = await apiClient.put('/api/test/1', updateData);
      expect(result).toEqual(updateData);
    });

    it('应该正确处理DELETE请求', async () => {
      server.use(
        http.delete('/api/test/1', () => {
          return HttpResponse.json({ message: 'Deleted successfully' });
        })
      );

      const result = await apiClient.delete('/api/test/1');
      expect(result).toEqual({ message: 'Deleted successfully' });
    });
  });

  describe('请求配置扩展测试', () => {
    it('应该支持自定义请求头', async () => {
      const customHeaders = {
        'X-Custom-Header': 'custom-value',
        'Authorization': 'Bearer token123'
      };

      server.use(
        http.get('/api/test', ({ request }) => {
          expect(request.headers.get('X-Custom-Header')).toBe('custom-value');
          expect(request.headers.get('Authorization')).toBe('Bearer token123');
          return HttpResponse.json({ message: 'Success' });
        })
      );

      await apiClient.get('/api/test', { headers: customHeaders });
    });

    it('应该支持查询参数', async () => {
      const params = { page: 1, size: 20, filter: 'active' };

      server.use(
        http.get('/api/test', ({ request }) => {
          const url = new URL(request.url);
          expect(url.searchParams.get('page')).toBe('1');
          expect(url.searchParams.get('size')).toBe('20');
          expect(url.searchParams.get('filter')).toBe('active');
          return HttpResponse.json({ message: 'Success' });
        })
      );

      await apiClient.get('/api/test', { params });
    });

    it('应该支持请求超时配置', async () => {
      server.use(
        http.get('/api/slow', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json({ message: 'Slow response' }));
            }, 2000);
          });
        })
      );

      // 测试短超时
      await expect(
        apiClient.get('/api/slow', { timeout: 1000 })
      ).rejects.toThrow();
    });
  });

  describe('错误处理扩展测试', () => {
    it('应该处理不同的HTTP错误状态码', async () => {
      const errorCodes = [400, 401, 403, 404, 422, 500, 502, 503];

      for (const code of errorCodes) {
        server.use(
          http.get(`/api/error/${code}`, () => {
            return HttpResponse.json(
              { error: `Error ${code}` },
              { status: code }
            );
          })
        );

        await expect(
          apiClient.get(`/api/error/${code}`)
        ).rejects.toThrow();
      }
    });

    it('应该处理JSON解析错误', async () => {
      server.use(
        http.get('/api/invalid-json', () => {
          return new Response('Invalid JSON{', {
            headers: { 'Content-Type': 'application/json' }
          });
        })
      );

      await expect(
        apiClient.get('/api/invalid-json')
      ).rejects.toThrow();
    });

    it('应该处理网络连接错误', async () => {
      server.use(
        http.get('/api/network-error', () => {
          return HttpResponse.error();
        })
      );

      await expect(
        apiClient.get('/api/network-error')
      ).rejects.toThrow();
    });
  });

  describe('加载状态管理扩展测试', () => {
    it('应该在并发请求中正确管理加载状态', async () => {
      const appStore = useAppStore();
      
      server.use(
        http.get('/api/concurrent/1', () => {
          return HttpResponse.json({ id: 1 });
        }),
        http.get('/api/concurrent/2', () => {
          return HttpResponse.json({ id: 2 });
        }),
        http.get('/api/concurrent/3', () => {
          return HttpResponse.json({ id: 3 });
        })
      );

      expect(appStore.isLoading).toBe(false);

      // 并发请求
      const promises = [
        apiClient.get('/api/concurrent/1'),
        apiClient.get('/api/concurrent/2'),
        apiClient.get('/api/concurrent/3')
      ];

      // 请求期间应该是加载状态
      await vi.dynamicImportSettled();
      expect(appStore.isLoading).toBe(true);

      await Promise.all(promises);

      // 所有请求完成后应该停止加载
      expect(appStore.isLoading).toBe(false);
    });

    it('应该在请求失败时正确重置加载状态', async () => {
      const appStore = useAppStore();
      
      server.use(
        http.get('/api/error', () => {
          return HttpResponse.json(
            { error: 'Server error' },
            { status: 500 }
          );
        })
      );

      expect(appStore.isLoading).toBe(false);

      await expect(
        apiClient.get('/api/error')
      ).rejects.toThrow();

      expect(appStore.isLoading).toBe(false);
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内处理大量数据', async () => {
      const largeData = SimpleClientDataFactory.createLargeDataResponse(1000);
      
      server.use(
        http.get('/api/large-data', () => {
          return HttpResponse.json(largeData.data);
        })
      );

      const startTime = Date.now();
      const result = await apiClient.get('/api/large-data');
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(5000); // 5秒内完成
      expect(result.data).toHaveLength(1000);
    });

    it('应该支持请求取消', async () => {
      const controller = new AbortController();
      
      server.use(
        http.get('/api/slow', () => {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(HttpResponse.json({ message: 'Slow response' }));
            }, 5000);
          });
        })
      );

      const requestPromise = apiClient.get('/api/slow', {
        signal: controller.signal
      });

      // 1秒后取消请求
      setTimeout(() => {
        controller.abort();
      }, 1000);

      await expect(requestPromise).rejects.toThrow();
    });
  });

  describe('数据验证测试', () => {
    it('应该验证请求配置的完整性', () => {
      const validConfig = SimpleClientDataFactory.createRequestConfig({
        url: '/api/test',
        method: 'GET'
      });
      
      expect(SimpleClientDataFactory.validateRequestConfig(validConfig)).toBe(true);

      const invalidConfig = { method: 'GET' }; // 缺少URL
      expect(SimpleClientDataFactory.validateRequestConfig(invalidConfig as any)).toBe(false);
    });

    it('应该正确处理不同的内容类型', async () => {
      // JSON数据
      server.use(
        http.post('/api/json', async ({ request }) => {
          expect(request.headers.get('content-type')).toContain('application/json');
          return HttpResponse.json({ message: 'JSON received' });
        })
      );

      await apiClient.post('/api/json', { data: 'test' });

      // 表单数据
      const formData = SimpleClientDataFactory.createFormData({
        name: 'test',
        value: '123'
      });

      server.use(
        http.post('/api/form', async ({ request }) => {
          expect(request.headers.get('content-type')).toContain('multipart/form-data');
          return HttpResponse.json({ message: 'Form received' });
        })
      );

      await apiClient.post('/api/form', formData);
    });
  });

  describe('边界条件测试', () => {
    it('应该处理空响应', async () => {
      server.use(
        http.get('/api/empty', () => {
          return new Response(null, { status: 204 });
        })
      );

      const result = await apiClient.get('/api/empty');
      expect(result).toBeNull();
    });

    it('应该处理非常长的URL', async () => {
      const longPath = '/api/' + 'a'.repeat(1000);
      
      server.use(
        http.get(longPath, () => {
          return HttpResponse.json({ message: 'Long URL handled' });
        })
      );

      const result = await apiClient.get(longPath);
      expect(result.message).toBe('Long URL handled');
    });

    it('应该处理特殊字符的URL编码', async () => {
      const specialChars = '/api/test with spaces & symbols';
      
      server.use(
        http.get('/api/test%20with%20spaces%20&%20symbols', () => {
          return HttpResponse.json({ message: 'Special chars handled' });
        })
      );

      const result = await apiClient.get(specialChars);
      expect(result.message).toBe('Special chars handled');
    });
  });
});