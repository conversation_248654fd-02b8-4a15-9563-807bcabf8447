// 简化Mock处理器统一导出 - 严格按照API契约

export { clientHandlers } from './clientHandlers';
export { marketHandlers } from './marketHandlers';
export { strategyHandlers } from './strategyHandlers';
export { factorsHandlers } from './factorsHandlers';
export { backtestHandlers } from './backtestHandlers';
export { metricsHandlers } from './metricsHandlers';
export { gridSearchHandlers } from './gridSearchHandlers';
export { dashboardHandlers } from './dashboardHandlers';
export { optionsHandlers } from './optionsHandlers';
export { umpireHandlers } from './umpireHandlers';

// 合并所有处理器
import { clientHandlers } from './clientHandlers';
import { marketHandlers } from './marketHandlers';
import { strategyHandlers } from './strategyHandlers';
import { factorsHandlers } from './factorsHandlers';
import { backtestHandlers } from './backtestHandlers';
import { metricsHandlers } from './metricsHandlers';
import { gridSearchHandlers } from './gridSearchHandlers';
import { dashboardHandlers } from './dashboardHandlers';
import { optionsHandlers } from './optionsHandlers';
import { umpireHandlers } from './umpireHandlers';

export const simpleHandlers = [
  ...clientHandlers,
  ...marketHandlers,
  ...strategyHandlers,
  ...factorsHandlers,
  ...backtestHandlers,
  ...metricsHandlers,
  ...gridSearchHandlers,
  ...dashboardHandlers,
  ...optionsHandlers,
  ...umpireHandlers
];