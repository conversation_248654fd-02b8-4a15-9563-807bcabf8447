# -*- coding: utf-8 -*-
"""
市场数据服务门面层，整合各个数据提供者的功能
"""
import logging
from typing import List, Optional

import tushare as ts
from abupy.CoreBu import ABuEnv
from backend.app.abupy_adapter.data_cache_adapter import AbuDataCache

from backend.app.core.config import settings
from backend.app.core.exceptions import ValidationError, ExternalAPIError
from backend.app.schemas.market import StockBasic, StockFundamental, KlineData

from .kline_provider import KlineProvider
from .fundamental_provider import FundamentalProvider
from .symbol_provider import SymbolProvider


class MarketService:
    """市场数据服务门面，整合各个数据提供者的功能"""
    
    def __init__(self):
        """初始化，设置tushare token和环境配置"""
        # 初始化tushare
        try:
            if not settings.TUSHARE_TOKEN:
                raise ValidationError(
                    message="缺失Tushare API Token",
                    error_code="MISSING_API_TOKEN"
                )
            ts.set_token(settings.TUSHARE_TOKEN)
            self.pro = ts.pro_api()
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            logging.error(f"初始化Tushare API失败: {str(e)}", exc_info=True)
            raise ExternalAPIError(
                message=f"初始化Tushare API失败: {str(e)}",
                data={"token_provided": bool(settings.TUSHARE_TOKEN)}
            )
        
        # 初始化abu环境
        ABuEnv.g_data_cache_type = ABuEnv.EDataCacheType.E_DATA_CACHE_CSV

        # 初始化A股指数代码集合
        self.a_share_index_codes = {
            "000001.SH", "399001.SZ", "000300.SH", "399005.SZ", 
            "399006.SZ", "000905.SH", "000016.SH", "000688.SH"
        }
        
        # 配置abu数据缓存
        if settings.ENABLE_CACHE:
            # 启用abu数据缓存
            AbuDataCache.disable_cache = False
            AbuDataCache.cache_expiry_days = settings.CACHE_EXPIRE_DAYS
        else:
            AbuDataCache.disable_cache = True
    
    def get_stock_list(self, market: Optional[str] = None, 
                      industry: Optional[str] = None, 
                      name: Optional[str] = None) -> List[StockBasic]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 CN(A股)/US(美股)/HK(港股)
            industry: 行业类型
            name: 股票名称(模糊查询)
            
        Returns:
            List[StockBasic]: 股票列表
            
        Raises:
            ValidationError: 当市场类型无效时
            ExternalAPIError: 当外部API调用失败。
            DataNotFoundError: 当没有找到匹配的股票数据。
        """
        # 调用SymbolProvider获取股票列表
        return SymbolProvider.get_stock_list(self.pro, market, industry, name)
    
    def get_kline_data(self, symbol: str, start_date: Optional[str] = None, 
                       end_date: Optional[str] = None, 
                       period: str = "daily", data_source: str = 'tushare') -> KlineData:
        """
        获取K线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            period: 周期类型，daily/weekly/monthly
            data_source: 数据源，tushare/local
            
        Returns:
            KlineData: K线数据
            
        Raises:
            SymbolError: 当股票代码格式无效时
            ValidationError: 当参数无效时
            DataNotFoundError: 当没有找到K线数据时
            ExternalAPIError: 当外部API调用失败。
        """
        # 调用KlineProvider获取K线数据
        return KlineProvider.get_kline_data(
            symbol=symbol, 
            start_date=start_date, 
            end_date=end_date, 
            period=period, 
            data_source=data_source,
            pro_api=self.pro
        )
    
    def get_fundamental_data(self, symbol: str, date: Optional[str] = None) -> StockFundamental:
        """
        获取基本面数据
        
        Args:
            symbol: 股票代码
            date: 日期，格式YYYYMMDD
            
        Returns:
            StockFundamental: 基本面数据
        """
        # 调用FundamentalProvider获取基本面数据
        return FundamentalProvider.get_fundamental_data(self.pro, symbol, date)
