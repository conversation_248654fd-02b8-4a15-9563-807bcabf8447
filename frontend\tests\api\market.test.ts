import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as marketApi from '../../src/api/modules/market'
import { apiClient } from '../../src/api/client'
import type { Symbol, KlineData, SymbolsResponse, KlineDataResponse } from '../../src/api/types'
import { SimpleMarketDataFactory } from '../factories/SimpleMarketDataFactory'

// Mock API client
vi.mock('../../src/api/client', () => ({
  apiClient: {
    get: vi.fn()
  }
}))

const mockApiClient = vi.mocked(apiClient)

describe('Market API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getStockCodes', () => {
    it('应该能够获取所有可用股票代码', async () => {
      // Arrange
      const expectedSymbols: Symbol[] = [
        SimpleMarketDataFactory.createSymbol(),
        SimpleMarketDataFactory.createSymbol(),
        SimpleMarketDataFactory.createSymbol()
      ]
      const expectedResponse: SymbolsResponse = SimpleMarketDataFactory.createSymbolsResponse(expectedSymbols)
      
      mockApiClient.get.mockResolvedValue(expectedResponse)

      // Act
      const result = await marketApi.getSymbols()

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith('/market/symbols')
      expect(result).toEqual(expectedResponse)
    })

    it('应该处理获取股票代码失败的情况', async () => {
      // Arrange
      const error = new Error('获取股票代码失败')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(marketApi.getSymbols()).rejects.toThrow('获取股票代码失败')
    })
  })

  describe('getKLineData', () => {
    it('应该能够获取特定股票的K线数据', async () => {
      // Arrange
      const symbol = '000001.SZ'
      const startDate = '2023-01-01'
      const endDate = '2023-12-31'
      const expectedKLineData: KlineDataResponse = SimpleMarketDataFactory.createKlineDataResponse('000001.SZ', '1d')
      
      mockApiClient.get.mockResolvedValue(expectedKLineData)

      // Act
      const result = await marketApi.getKlineData(symbol, '1d')

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/market/kline/${symbol}`,
        {
          params: {
            period: '1d'
          }
        }
      )
      expect(result).toEqual(expectedKLineData)
    })

    it('应该处理无效股票代码的情况', async () => {
      // Arrange
      const symbol = 'INVALID.CODE'
      const startDate = '2023-01-01'
      const endDate = '2023-12-31'
      const error = new Error('无效的股票代码')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(
        marketApi.getKlineData(symbol, '1d')
      ).rejects.toThrow('无效的股票代码')
    })

    it('应该处理无效日期范围的情况', async () => {
      // Arrange
      const symbol = '000001.SZ'
      const startDate = '2023-12-31'
      const endDate = '2023-01-01' // 结束日期早于开始日期
      const error = new Error('无效的日期范围')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(
        marketApi.getKlineData(symbol, '1d')
      ).rejects.toThrow('无效的日期范围')
    })

    it('应该处理数据不存在的情况', async () => {
      // Arrange
      const symbol = '000001.SZ'
      const startDate = '1990-01-01'
      const endDate = '1990-12-31'
      const error = new Error('指定日期范围内无数据')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(
        marketApi.getKlineData(symbol, '1d')
      ).rejects.toThrow('指定日期范围内无数据')
    })
  })

  describe('getKLineData with optional parameters', () => {
    it('应该能够处理不传入日期参数的情况', async () => {
      // Arrange
      const symbol = '000001.SZ'
      const expectedKLineData: KlineDataResponse = SimpleMarketDataFactory.createKlineDataResponse('000001.SZ', '1d')
      
      mockApiClient.get.mockResolvedValue(expectedKLineData)

      // Act
      const result = await marketApi.getKlineData(symbol, '1d')

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/market/kline/${symbol}`,
        {
          params: {
            period: '1d'
          }
        }
      )
      expect(result).toEqual(expectedKLineData)
    })

    it('应该能够只传入开始日期', async () => {
      // Arrange
      const symbol = '000001.SZ'
      const startDate = '2023-01-01'
      const expectedKLineData: KlineDataResponse = SimpleMarketDataFactory.createKlineDataResponse('000001.SZ', '1d')
      
      mockApiClient.get.mockResolvedValue(expectedKLineData)

      // Act
      const result = await marketApi.getKlineData(symbol, '1d')

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(
        `/market/kline/${symbol}`,
        {
          params: {
            period: '1d'
          }
        }
      )
      expect(result).toEqual(expectedKLineData)
    })
  })
})