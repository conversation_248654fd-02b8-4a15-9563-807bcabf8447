#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Tushare数据下载脚本

此脚本用于从Tushare下载股票/指数的日线数据，并将其保存为标准化的HDF5格式，
用于abu_modern项目的测试和开发。
"""

import os
import sys
import time
from pathlib import Path
import pandas as pd
import tushare as ts
from dotenv import load_dotenv

# 使用更健壮的方式加载.env文件，无论从哪里运行脚本都能找到
# 获取脚本所在目录 -> 获取项目根目录 -> 拼接.env文件路径
try:
    project_root = Path(__file__).parent.parent
    dotenv_path = project_root / 'backend' / '.env'
    load_dotenv(dotenv_path=dotenv_path)
    print(f"成功从 {dotenv_path} 加载环境变量。")
except NameError:
    # 在交互式环境（如Jupyter）中__file__可能不存在，使用备用方案
    load_dotenv(dotenv_path="backend/.env")
    print("在交互式环境中运行，尝试从 'backend/.env' 加载环境变量。")


# ===================== 配置区 =====================
# Tushare API Token将从.env文件中加载
TUSHARE_TOKEN = os.getenv("TUSHARE_TOKEN")

# 需要下载的股票/指数列表
SYMBOLS_TO_DOWNLOAD = [
    '000001.SZ', '600000.SH', '000300.SH', '000858.SZ', # 原有
    '600519.SH', '300750.SZ', '601318.SH' # 新增
]

# --- 新增配置：用于识别哪些是指数 ---
# 将列表中的指数代码放在这里
INDEX_SYMBOLS = ['000300.SH']

# 数据日期范围
START_DATE = '20200101'
END_DATE = '20231231'

# 输出目录和文件名
# 直接指向最终的数据文件路径
OUTPUT_DIR = './data/'
OUTPUT_FILE = 'market_data.h5'
# ================================================

def check_token():
    """检查Tushare Token是否已设置"""
    if not TUSHARE_TOKEN:
        print("错误: TUSHARE_TOKEN 未设置!")
        print("请确保项目 'backend' 目录下有 '.env' 文件，并且其中包含 TUSHARE_TOKEN='你的token'。")
        sys.exit(1)
    print("Tushare Token 已成功加载。")

def init_tushare_api():
    """初始化Tushare API"""
    print("正在初始化Tushare API...")
    try:
        pro = ts.pro_api(TUSHARE_TOKEN, timeout=120)
        # 测试API连通性
        pro.trade_cal(exchange='', start_date='20200101', end_date='20200101')
        return pro
    except Exception as e:
        print(f"初始化Tushare API失败，请检查Token是否有效或网络连接: {e}")
        sys.exit(1)

def clean_symbol(symbol):
    """清理股票代码，去除后缀"""
    return symbol.split('.')[0]

def download_and_save_data():
    """下载并保存数据"""
    # 确保输出目录存在
    output_path = Path(OUTPUT_DIR)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 输出文件的完整路径
    output_file_path = output_path / OUTPUT_FILE
    abs_output_path = output_file_path.absolute()
    
    print(f"数据将被保存到: {abs_output_path}")
    
    # 初始化Tushare API
    pro = init_tushare_api()
    
    # 如果文件已存在，先删除，确保每次运行都是全新的
    if os.path.exists(output_file_path):
        os.remove(output_file_path)
        print(f"已删除旧的数据文件: {output_file_path}")
    
    # 遍历并下载每个股票/指数的数据
    for symbol in SYMBOLS_TO_DOWNLOAD:
        print("-" * 30)
        print(f"正在处理 {symbol} 的数据 ({START_DATE} - {END_DATE})...")
        
        try:
            df = None  # 初始化DataFrame
            
            # =================== 核心修改逻辑 ===================
            if symbol in INDEX_SYMBOLS:
                # 如果是指数，调用 index_daily 接口
                print(f"检测到 {symbol} 为指数，使用 'index_daily' 接口。")
                df = pro.index_daily(
                    ts_code=symbol,
                    start_date=START_DATE,
                    end_date=END_DATE
                )
            else:
                # 如果是股票，调用 pro_bar 接口获取后复权数据
                print(f"检测到 {symbol} 为股票，使用 'pro_bar' 接口 (后复权)。")
                df = ts.pro_bar(
                    ts_code=symbol,
                    start_date=START_DATE,
                    end_date=END_DATE,
                    adj='hfq',
                    freq='D'  # 日线数据
                )
            # ====================================================

            # 检查是否成功获取数据
            if df is None or df.empty:
                print(f"警告: 未能获取 {symbol} 的数据，可能权限不足或代码有误，已跳过。")
                continue

            # --- 核心数据格式化：设置datetime索引 ---
            if 'trade_date' in df.columns:
                df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
                df.set_index('trade_date', inplace=True)
                df.sort_index(inplace=True) # 确保数据按时间排序
                print(f"已为 {symbol} 设置datetime索引。")
            else:
                print(f"警告: {symbol} 的数据中未找到 'trade_date' 列，无法设置索引。")
                continue
            # -----------------------------------------
            
            # 标准化列名（可选，根据项目需求调整）
            # 这里保持Tushare的原始列名
            
            # 保存到HDF5文件
            clean_sym = clean_symbol(symbol)
            # 使用 'a' 模式，如果文件不存在会自动创建
            # 在循环开始前，我们已经删除了旧文件，所以这里总是安全的
            with pd.HDFStore(output_file_path, 'a') as store:
                # 写入前先尝试删除旧的key，防止重复写入
                if f'/{clean_sym}' in store.keys():
                    store.remove(clean_sym)
                    print(f"已从HDF5文件中删除旧的key: {clean_sym}")
                
                # 写入新的DataFrame
                store.put(clean_sym, df, format='table', data_columns=True)

            
            print(f"已成功将 {clean_sym} 的数据保存到 {OUTPUT_FILE}")
            
            # 添加短暂延迟，避免频繁请求
            time.sleep(1)
            
        except Exception as e:
            print(f"下载或保存 {symbol} 数据时出错: {e}")
    
    print("=" * 50)
    print("\n所有数据下载完成！")
    print(f"数据已保存到: {abs_output_path}")

def main():
    """主函数"""
    print("=" * 50)
    print("Tushare数据下载工具 (智能版)")
    print("=" * 50)
    
    # 检查Token
    check_token()
    
    # 下载并保存数据
    download_and_save_data()

if __name__ == "__main__":
    main()