# UmpBu (裁判系统) 技术实现指南 V2.0

**版本说明:** V2.0版是在V1.0的基础上，结合了项目在集成过程中遇到的实际问题（`RuntimeError: you must first fit orders...`）以及成功的诊断（`log_20250628_004`）后，进行的重大更新。本版的核心是补充了“集成注意事项”并提供了更符合工程实践的代码示例，旨在指导实现者AI进行正确、健壮的集成。

## 1. 核心工作原理 (无变化)

UmpBu（Umpire System，裁判系统）是abupy中用于风险控制的核心模块。其主要思想是利用机器学习模型，通过分析历史交易数据，学习到可能导致亏损的交易模式（特征），并在未来的回测或实盘中，对符合这些模式的交易信号进行拦截，从而避免潜在的亏损，提高策略的胜率和稳定性。

**核心流程如下：**

1.  **特征工程**: 从历史交易订单（`orders_pd`）中提取与交易行为相关的特征。`ABuMLFeature` 模块负责此工作。
2.  **模型训练 (fit)**: 将提取的特征和交易结果（盈利/亏损）作为训练数据，训练一个或多个分类模型。训练完成后，将学习到的模型**持久化为文件**。
3.  **模型加载与裁判介入 (predict)**: 在新的回测任务中，系统从文件加载已经训练好的模型。当一个买入或卖出信号产生后，系统先将当前的行情数据和交易信号转换成与训练时相同的特征向量。
4.  **决策判断**: 将特征向量输入到加载好的模型中进行预测。如果模型预测该笔交易属于“高风险”模式，则“裁判”会“否决”这笔交易。

## 2. “裁判系统”在回测流程中的作用 (无变化)

“裁判系统”在回测流程中的干预点位于**买入或卖出信号已经产生，但尚未生成最终交易订单**的环节。

## 3. ABuUmpManager (裁判管理器) (无变化)

`ABuUmpManager` 是裁判系统的核心调度器，负责持有、管理、筛选和调用所有已配置的“裁判”实例。

## 4. “裁判”的种类与区别 (无变化)

abupy提供了两大类裁判：**主裁判 (Main Umpire)** 和 **边裁判 (Edge Umpire)**，其核心思想和区别详见V1.0文档。

| 特性 | 主裁判 (Main Umpire) | 边裁判 (Edge Umpire) |
| :--- | :--- | :--- |
| **关注点** | 大概率亏损的普遍模式 | 极端盈利/亏损的特殊模式 |
| **决策模型** | 聚类 + 分类簇命中 | K-近邻 + 相似度投票 |
| **决策逻辑** | 落入“危险区域”即拦截 | 与“极端亏损”模式高度相似才拦截 |
| **作用** | 过滤掉常见的、可识别的亏损陷阱 | 防范“黑天鹅”式的极端亏损事件 |

---

## 5. 【V2.0 新增】集成注意事项与常见陷阱

**警告：直接使用V1.0中的代码示例会在实际工程中导致失败。** UmpBu的集成必须遵循其内在的机器学习生命周期，并正确配置其运行环境。以下是集成的关键要点和必须避免的陷阱：

### 陷阱1：环境路径未配置

*   **问题描述**: 如果不显式设置abupy的环境路径，它会试图在操作系统的用户家目录下（如 `C:\Users\<USER>\abu\data`）寻找或创建模型文件。这会导致模型文件与项目本身脱离，造成管理混乱和部署失败。
*   **解决方案**: **必须在任何abupy操作之前**，设置其全局项目根目录。这会强制abupy将所有数据（包括缓存、模型文件等）都存放在项目指定的路径下。

    ```python
    from abupy import ABuEnv
    # 这里的 "d:/path/to/your/abu_modern" 应该由项目的配置模块提供
    ABuEnv.g_project_root = "d:/path/to/your/abu_modern"
    ```

### 陷阱2：混淆训练与预测流程

*   **问题描述**: UmpBu的运作严格遵循“先训练(fit)，后预测(predict)”的模式。**不能在一个流程中同时进行训练和预测。** 原始指南中的示例代码具有误导性，因为它在同一个脚本内生成模型并立即使用，掩盖了这两个阶段必须分离的事实。
*   **解决方案**: 必须将模型训练和模型应用（回测）设计为两个独立的、解耦的流程。
    1.  **训练流程 (Train Flow)**: 这是一个独立的任务，其唯一目的是生成模型文件。它需要一次不带裁判的回测来产生高质量的交易订单（`orders_pd`），然后调用 `ump_main_clf_dump` 和 `ump_edge_clf_dump` 将训练好的模型保存到磁盘。
    2.  **预测流程 (Predict Flow / Backtest Flow)**: 这是常规的回测任务。它假定模型文件**已经存在**。在回测开始前，它会实例化裁判（`predict=True`），并加载训练流程中生成的模型文件，用于在回测中拦截高风险信号。

### 结论：正确的集成模式

正确的集成模式是将UmpBu的功能API化，提供一个独立的 `POST /api/umpires/train` 接口来执行训练流程，而现有的 `POST /api/strategies/{id}/execute` 接口则只负责预测流程。

## 6. 【V2.0 更新】配置与使用（正确流程示例）

以下伪代码展示了如何正确地分离“训练”和“预测”两个流程，这对于实现者AI具有直接的指导价值。

### 流程一：训练并保存裁判模型 (对应 `Train API`)

```python
# a_training_script.py

from abupy import abu, ABuEnv, AbuUmpMainBase, AbuUmpEdgeBase, ABuMetricsBase

def train_and_dump_umpire_models(project_root, cash, buy_factors, sell_factors, choice_symbols, market_name):
    """
    执行一个“香草”回测以生成训练数据，然后训练并保存裁判模型。
    这是一个独立的、一次性的操作，用于生成模型文件。
    """
    # 关键步骤1：设置项目环境，确保模型保存在正确的位置
    ABuEnv.g_project_root = project_root

    # 关键步骤2：运行一次不带裁判的回测，目的是获取高质量的 orders_pd
    print(f"为市场'{market_name}'生成训练数据中...")
    # 注意：这里不配置任何裁判
    abu_result_tuple, _ = abu.run_loop_back(cash, buy_factors, sell_factors, choice_symbols=choice_symbols)

    if abu_result_tuple is None:
        print("训练数据生成失败，回测未产生任何交易。")
        return

    # 关键步骤3：从回测结果中准备训练数据
    metrics = ABuMetricsBase(*abu_result_tuple)
    metrics.fit_metrics_order()
    orders_pd_for_training = metrics.orders_pd

    if orders_pd_for_training.empty:
        print("训练数据为空，无法训练模型。")
        return

    # 关键步骤4：训练并序列化（dump）主裁判和边裁判模型
    print(f"开始训练主裁判模型并保存为 '{market_name}'...")
    AbuUmpMainBase.ump_main_clf_dump(orders_pd_for_training, market_name=market_name, show_info=True)
    print("主裁判模型训练完成。")

    print(f"开始训练边裁判模型并保存为 '{market_name}'...")
    AbuUmpEdgeBase.ump_edge_clf_dump(orders_pd_for_training, market_name=market_name, show_info=True)
    print("边裁判模型训练完成。")

# --- 使用示例 ---
# if __name__ == '__main__':
#     # 定义回测参数以生成训练数据
#     read_cash = 1000000
#     stock_pool = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU']
#     buy_factors = [{'xd': 60, 'class': 'AbuFactorBuyBreak'}]
#     sell_factors = [{'stop_loss_n': 1.0, 'class': 'AbuFactorAtrNStop'}]
#     
#     # 执行训练
#     train_and_dump_umpire_models(
#         project_root="d:/path/to/your/abu_modern",
#         cash=read_cash,
#         buy_factors=buy_factors,
#         sell_factors=sell_factors,
#         choice_symbols=stock_pool,
#         market_name='us_market' # 为这批模型命名
#     )

```

### 流程二：在回测中使用已训练的裁判 (对应 `Execute API`)

```python
# b_backtest_with_umpire.py

from abupy import abu, ABuEnv, ABuUmpManager, AbuUmpMainDeg, AbuUmpEdgeDeg

def run_backtest_with_umpire(project_root, cash, buy_factors, sell_factors, choice_symbols, market_name):
    """
    在回测中使用已经训练好的、名为market_name的裁判模型。
    """
    # 关键步骤1：同样需要设置项目环境，以确保能找到模型文件
    ABuEnv.g_project_root = project_root

    # 关键步骤2：配置并启用裁判系统
    print("配置裁判系统中...")
    ABuUmpManager.g_enable_user_ump = True
    ABuEnv.g_enable_ml_feature = True # 裁判系统依赖此功能
    ABuUmpManager.clear_user_ump() # (最佳实践) 每次运行前清空

    # 关键步骤3：实例化裁判，并指定加载哪个已训练好的模型
    # predict=True 表明用于预测
    # market_name='us_market' 告诉裁判加载之前保存的名为 'us_market' 的模型
    try:
        ump_main = AbuUmpMainDeg(predict=True, market_name=market_name)
        ump_edge = AbuUmpEdgeDeg(predict=True, market_name=market_name)
    except Exception as e:
        print(f"加载裁判模型 '{market_name}' 失败！请先执行训练流程。错误: {e}")
        # 关闭开关并退出
        ABuUmpManager.g_enable_user_ump = False
        ABuEnv.g_enable_ml_feature = False
        return

    # 关键步骤4：将裁判实例添加到管理器中
    ABuUmpManager.append_user_ump(ump_main)
    ABuUmpManager.append_user_ump(ump_edge)
    print(f"已加载 '{market_name}' 市场的裁判模型，开始执行回测...")

    # 关键步骤5：执行带裁判的回测
    abu_result_tuple, _ = abu.run_loop_back(cash, buy_factors, sell_factors, choice_symbols=choice_symbols)

    # (最佳实践) 回测结束后，关闭开关
    ABuUmpManager.g_enable_user_ump = False
    ABuEnv.g_enable_ml_feature = False
    print("\n回测完成，已关闭裁判系统开关。")

    return abu_result_tuple

# --- 使用示例 ---
# if __name__ == '__main__':
#     # 定义回测参数
#     read_cash = 1000000
#     stock_pool = ['usAAPL', 'usGOOG', 'usWUBA', 'usVIPS'] # 可以是不同的股票
#     buy_factors = [{'xd': 42, 'class': 'AbuFactorBuyBreak'}]
#     sell_factors = [{'class': 'AbuFactorPreAtrNStop'}]
#
#     # 执行带裁判的回测，使用之前训练好的 'us_market' 模型
#     run_backtest_with_umpire(
#         project_root="d:/path/to/your/abu_modern",
#         cash=read_cash,
#         buy_factors=buy_factors,
#         sell_factors=sell_factors,
#         choice_symbols=stock_pool,
#         market_name='us_market'
#     )
```