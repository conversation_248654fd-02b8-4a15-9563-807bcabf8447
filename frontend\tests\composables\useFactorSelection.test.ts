import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFactorSelection, factorUtils } from '@/composables/useFactorSelection'
import type { Strategy } from '@/api/types'
import type { Factor } from '@/types'

// Mock useFactorsStore
const mockFactorsStore = {
  fetchFactors: vi.fn()
}

vi.mock('@/stores', () => ({
  useFactorsStore: () => mockFactorsStore
}))

describe('useFactorSelection', () => {
  let factorSelection: ReturnType<typeof useFactorSelection>
  
  beforeEach(() => {
    vi.clearAllMocks()
    factorSelection = useFactorSelection()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(factorSelection.isDialogVisible.value).toBe(false)
      expect(factorSelection.currentFactorType.value).toBeNull()
      expect(factorSelection.editingType.value).toBeNull()
      expect(factorSelection.editingIndex.value).toBeNull()
      expect(factorSelection.selectedFactor.value).toBeNull()
      expect(factorSelection.searchKeyword.value).toBe('')
      expect(factorSelection.buyFactorsForDialog.value).toEqual([])
      expect(factorSelection.sellFactorsForDialog.value).toEqual([])
    })

    it('应该暴露factorsStore实例', () => {
      expect(factorSelection.factorsStore).toBe(mockFactorsStore)
    })
  })

  describe('openDialog', () => {
    it('应该打开买入因子选择对话框', () => {
      factorSelection.openDialog('buy')
      
      expect(factorSelection.isDialogVisible.value).toBe(true)
      expect(factorSelection.currentFactorType.value).toBe('buy')
      expect(factorSelection.editingType.value).toBeNull()
      expect(factorSelection.editingIndex.value).toBeNull()
    })

    it('应该打开卖出因子选择对话框', () => {
      factorSelection.openDialog('sell')
      
      expect(factorSelection.isDialogVisible.value).toBe(true)
      expect(factorSelection.currentFactorType.value).toBe('sell')
      expect(factorSelection.editingType.value).toBeNull()
      expect(factorSelection.editingIndex.value).toBeNull()
    })

    it('应该重置编辑状态', () => {
      // 先设置一些编辑状态
      factorSelection.editFactor('buy', 0, {})
      
      // 然后打开新的对话框
      factorSelection.openDialog('sell')
      
      expect(factorSelection.editingType.value).toBeNull()
      expect(factorSelection.editingIndex.value).toBeNull()
    })
  })

  describe('editFactor', () => {
    it('应该设置编辑模式状态', () => {
      const factor = { name: 'TestFactor' }
      
      factorSelection.editFactor('buy', 1, factor)
      
      expect(factorSelection.isDialogVisible.value).toBe(true)
      expect(factorSelection.currentFactorType.value).toBe('buy')
      expect(factorSelection.editingType.value).toBe('buy')
      expect(factorSelection.editingIndex.value).toBe(1)
    })

    it('应该支持编辑卖出因子', () => {
      const factor = { name: 'SellFactor' }
      
      factorSelection.editFactor('sell', 2, factor)
      
      expect(factorSelection.currentFactorType.value).toBe('sell')
      expect(factorSelection.editingType.value).toBe('sell')
      expect(factorSelection.editingIndex.value).toBe(2)
    })
  })

  describe('deleteFactor', () => {
    const mockStrategy: Strategy = {
      id: '1',
      name: 'TestStrategy',
      buy_factors: [
        { name: 'BuyFactor1' },
        { name: 'BuyFactor2' },
        { name: 'BuyFactor3' }
      ],
      sell_factors: [
        { name: 'SellFactor1' },
        { name: 'SellFactor2' }
      ]
    } as Strategy

    it('应该删除指定的买入因子', () => {
      const result = factorSelection.deleteFactor(mockStrategy, 'buy', 1)
      
      expect(result.buy_factors).toHaveLength(2)
      expect(result.buy_factors).toEqual([
        { name: 'BuyFactor1' },
        { name: 'BuyFactor3' }
      ])
    })

    it('应该删除指定的卖出因子', () => {
      const result = factorSelection.deleteFactor(mockStrategy, 'sell', 0)
      
      expect(result.sell_factors).toHaveLength(1)
      expect(result.sell_factors).toEqual([
        { name: 'SellFactor2' }
      ])
    })

    it('应该处理无效的索引', () => {
      const result = factorSelection.deleteFactor(mockStrategy, 'buy', 10)
      
      // 无效索引不应该改变数组
      expect(result.buy_factors).toHaveLength(3)
      expect(result.buy_factors).toEqual(mockStrategy.buy_factors)
    })

    it('应该处理负数索引', () => {
      const result = factorSelection.deleteFactor(mockStrategy, 'buy', -1)
      
      expect(result.buy_factors).toHaveLength(3)
      expect(result.buy_factors).toEqual(mockStrategy.buy_factors)
    })

    it('应该处理空因子列表', () => {
      const emptyStrategy: Strategy = {
        id: '1',
        name: 'EmptyStrategy',
        buy_factors: [],
        sell_factors: []
      } as Strategy
      
      const result = factorSelection.deleteFactor(emptyStrategy, 'buy', 0)
      
      expect(result.buy_factors).toEqual([])
    })

    it('应该处理undefined的因子列表', () => {
      const strategyWithoutFactors: Strategy = {
        id: '1',
        name: 'NoFactorsStrategy'
      } as Strategy
      
      const result = factorSelection.deleteFactor(strategyWithoutFactors, 'buy', 0)
      
      expect(result.buy_factors).toEqual([])
    })
  })

  describe('saveFactor', () => {
    const mockStrategy: Strategy = {
      id: '1',
      name: 'TestStrategy',
      buy_factors: [
        { name: 'BuyFactor1' },
        { name: 'BuyFactor2' }
      ],
      sell_factors: [
        { name: 'SellFactor1' }
      ]
    } as Strategy

    it('应该添加新的买入因子', () => {
      const newFactor = { name: 'NewBuyFactor' }
      
      const result = factorSelection.saveFactor(mockStrategy, 'buy', newFactor)
      
      expect(result.buy_factors).toHaveLength(3)
      expect(result.buy_factors?.[2]).toEqual(newFactor)
    })

    it('应该添加新的卖出因子', () => {
      const newFactor = { name: 'NewSellFactor' }
      
      const result = factorSelection.saveFactor(mockStrategy, 'sell', newFactor)
      
      expect(result.sell_factors).toHaveLength(2)
      expect(result.sell_factors?.[1]).toEqual(newFactor)
    })

    it('应该更新现有的买入因子', () => {
      const updatedFactor = { name: 'UpdatedBuyFactor' }
      
      const result = factorSelection.saveFactor(mockStrategy, 'buy', updatedFactor, 1)
      
      expect(result.buy_factors).toHaveLength(2)
      expect(result.buy_factors?.[1]).toEqual(updatedFactor)
      expect(result.buy_factors?.[0]).toEqual({ name: 'BuyFactor1' }) // 其他因子不变
    })

    it('应该更新现有的卖出因子', () => {
      const updatedFactor = { name: 'UpdatedSellFactor' }
      
      const result = factorSelection.saveFactor(mockStrategy, 'sell', updatedFactor, 0)
      
      expect(result.sell_factors).toHaveLength(1)
      expect(result.sell_factors?.[0]).toEqual(updatedFactor)
    })

    it('应该处理无效的编辑索引', () => {
      const newFactor = { name: 'NewFactor' }
      
      // 无效索引会扩展数组
      const result = factorSelection.saveFactor(mockStrategy, 'buy', newFactor, 10)
      
      expect(result.buy_factors).toHaveLength(11)
      expect(result.buy_factors?.[10]).toEqual(newFactor)
    })

    it('应该处理undefined的因子列表', () => {
      const strategyWithoutFactors: Strategy = {
        id: '1',
        name: 'NoFactorsStrategy'
      } as Strategy
      
      const newFactor = { name: 'FirstFactor' }
      
      const result = factorSelection.saveFactor(strategyWithoutFactors, 'buy', newFactor)
      
      expect(result.buy_factors).toHaveLength(1)
      expect(result.buy_factors?.[0]).toEqual(newFactor)
    })
  })

  describe('selectFactor', () => {
    it('应该选择因子', () => {
      const factor: Factor = {
        id: '1',
        name: 'TestFactor',
        class_name: 'TestFactorClass'
      } as Factor
      
      factorSelection.selectFactor(factor)
      
      expect(factorSelection.selectedFactor.value).toStrictEqual(factor)
    })

    it('应该更新选择的因子', () => {
      const factor1: Factor = { id: '1', name: 'Factor1' } as Factor
      const factor2: Factor = { id: '2', name: 'Factor2' } as Factor
      
      factorSelection.selectFactor(factor1)
      expect(factorSelection.selectedFactor.value).toStrictEqual(factor1)
      
      factorSelection.selectFactor(factor2)
      expect(factorSelection.selectedFactor.value).toStrictEqual(factor2)
    })
  })

  describe('clearSelection', () => {
    it('应该清除因子选择', () => {
      const factor: Factor = { id: '1', name: 'TestFactor' } as Factor
      
      factorSelection.selectFactor(factor)
      expect(factorSelection.selectedFactor.value).toStrictEqual(factor)
      
      factorSelection.clearSelection()
      expect(factorSelection.selectedFactor.value).toBeNull()
    })
  })

  describe('reset', () => {
    it('应该重置所有状态', () => {
      // 设置一些状态
      factorSelection.selectFactor({ id: '1', name: 'TestFactor' } as Factor)
      factorSelection.searchKeyword.value = 'search term'
      factorSelection.openDialog('buy')
      factorSelection.editFactor('sell', 1, {})
      
      // 重置
      factorSelection.reset()
      
      expect(factorSelection.selectedFactor.value).toBeNull()
      expect(factorSelection.searchKeyword.value).toBe('')
      expect(factorSelection.isDialogVisible.value).toBe(false)
      expect(factorSelection.currentFactorType.value).toBeNull()
      expect(factorSelection.editingType.value).toBeNull()
      expect(factorSelection.editingIndex.value).toBeNull()
    })
  })

  describe('initialize', () => {
    it('应该初始化因子选择', async () => {
      mockFactorsStore.fetchFactors.mockResolvedValue(undefined)
      
      await factorSelection.initialize()
      
      expect(mockFactorsStore.fetchFactors).toHaveBeenCalled()
    })

    it('应该处理初始化错误', async () => {
      const error = new Error('Failed to fetch factors')
      mockFactorsStore.fetchFactors.mockRejectedValue(error)
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      await factorSelection.initialize()
      
      expect(consoleSpy).toHaveBeenCalledWith('Failed to initialize factor selection:', error)
      
      consoleSpy.mockRestore()
    })
  })

  describe('closeDialog', () => {
    it('应该关闭对话框并重置相关状态', () => {
      // 设置对话框状态
      factorSelection.openDialog('buy')
      factorSelection.editFactor('sell', 1, {})
      
      // 关闭对话框
      factorSelection.closeDialog()
      
      expect(factorSelection.isDialogVisible.value).toBe(false)
      expect(factorSelection.currentFactorType.value).toBeNull()
      expect(factorSelection.editingType.value).toBeNull()
      expect(factorSelection.editingIndex.value).toBeNull()
    })
  })

  describe('响应式状态', () => {
    it('应该正确暴露所有响应式状态', () => {
      const states = [
        'isDialogVisible',
        'currentFactorType',
        'editingType',
        'editingIndex',
        'selectedFactor',
        'searchKeyword',
        'buyFactorsForDialog',
        'sellFactorsForDialog'
      ]
      
      states.forEach(state => {
        expect(factorSelection).toHaveProperty(state)
        expect(factorSelection[state as keyof typeof factorSelection]).toBeDefined()
      })
    })

    it('应该正确暴露所有方法', () => {
      const methods = [
        'openDialog',
        'editFactor',
        'deleteFactor',
        'saveFactor',
        'selectFactor',
        'clearSelection',
        'reset',
        'initialize',
        'closeDialog'
      ]
      
      methods.forEach(method => {
        expect(factorSelection).toHaveProperty(method)
        expect(typeof factorSelection[method as keyof typeof factorSelection]).toBe('function')
      })
    })
  })
})

describe('factorUtils', () => {
  describe('validateFactor', () => {
    it('应该验证有效的因子', () => {
      const factor = {
        class_name: 'MovingAverage',
        factor_type: 'buy',
        name: '移动平均线',
        parameters: { period: 20 }
      }
      
      const result = factorUtils.validateFactor(factor)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it('应该检测缺少class_name的因子', () => {
      const factor = {
        name: '移动平均线',
        parameters: { period: 20 }
      }
      
      const result = factorUtils.validateFactor(factor)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('因子类名不能为空')
    })

    it('应该检测空class_name的因子', () => {
      const factor = {
        class_name: '',
        name: '移动平均线'
      }
      
      const result = factorUtils.validateFactor(factor)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('因子类名不能为空')
    })

    it('应该处理null和undefined', () => {
      expect(factorUtils.validateFactor(null).isValid).toBe(false)
      expect(factorUtils.validateFactor(undefined).isValid).toBe(false)
      expect(factorUtils.validateFactor({}).isValid).toBe(false)
    })
  })

  describe('formatFactorForDisplay', () => {
    it('应该格式化完整的因子信息', () => {
      const factor = {
        name: '移动平均线',
        class_name: 'MovingAverage',
        description: '计算移动平均值',
        parameters: { period: 20 },
        factor_type: 'technical'
      }
      
      const result = factorUtils.formatFactorForDisplay(factor)
      
      expect(result).toEqual({
        name: '移动平均线',
        description: '计算移动平均值',
        parameters: { period: 20 },
        type: 'technical'
      })
    })

    it('应该使用class_name作为默认名称', () => {
      const factor = {
        class_name: 'MovingAverage',
        description: '计算移动平均值'
      }
      
      const result = factorUtils.formatFactorForDisplay(factor)
      
      expect(result.name).toBe('MovingAverage')
    })

    it('应该使用默认值填充缺失字段', () => {
      const factor = {
        class_name: 'MovingAverage'
      }
      
      const result = factorUtils.formatFactorForDisplay(factor)
      
      expect(result).toEqual({
        name: 'MovingAverage',
        description: '无描述',
        parameters: {},
        type: 'unknown'
      })
    })

    it('应该处理空名称和class_name', () => {
      const factor = {
        name: '',
        class_name: ''
      }
      
      const result = factorUtils.formatFactorForDisplay(factor)
      
      expect(result.name).toBe('未命名因子')
    })

    it('应该处理null和undefined', () => {
      const result1 = factorUtils.formatFactorForDisplay(null)
      const result2 = factorUtils.formatFactorForDisplay(undefined)
      const result3 = factorUtils.formatFactorForDisplay({})
      
      expect(result1.name).toBe('未命名因子')
      expect(result2.name).toBe('未命名因子')
      expect(result3.name).toBe('未命名因子')
    })
  })

  describe('cloneFactor', () => {
    it('应该创建因子的深拷贝', () => {
      const factor = {
        name: '移动平均线',
        class_name: 'MovingAverage',
        parameters: {
          period: 20,
          config: {
            nested: true
          }
        }
      }
      
      const cloned = factorUtils.cloneFactor(factor)
      
      expect(cloned).toEqual(factor)
      expect(cloned).not.toBe(factor)
      expect(cloned.parameters).not.toBe(factor.parameters)
      expect(cloned.parameters.config).not.toBe(factor.parameters.config)
    })

    it('应该处理简单值', () => {
      const factor = {
        name: 'SimpleFactor',
        value: 42
      }
      
      const cloned = factorUtils.cloneFactor(factor)
      
      expect(cloned).toEqual(factor)
      expect(cloned).not.toBe(factor)
    })

    it('应该处理数组', () => {
      const factor = {
        name: 'ArrayFactor',
        values: [1, 2, { nested: true }]
      }
      
      const cloned = factorUtils.cloneFactor(factor)
      
      expect(cloned).toEqual(factor)
      expect(cloned.values).not.toBe(factor.values)
      expect(cloned.values[2]).not.toBe(factor.values[2])
    })

    it('应该处理null和undefined', () => {
      expect(factorUtils.cloneFactor(null)).toBeNull()
      expect(factorUtils.cloneFactor(undefined)).toBeUndefined()
    })

    it('应该处理循环引用', () => {
      const factor: any = {
        name: 'CircularFactor'
      }
      factor.self = factor
      
      // JSON.parse/stringify 会抛出错误处理循环引用
      expect(() => {
        factorUtils.cloneFactor(factor)
      }).toThrow()
    })
  })

  describe('边界情况', () => {
    let factorSelection: ReturnType<typeof useFactorSelection>

    beforeEach(() => {
      factorSelection = useFactorSelection()
    })

    it('应该处理空的搜索关键词', () => {
      factorSelection.searchKeyword.value = ''
      expect(factorSelection.searchKeyword.value).toBe('')
    })

    it('应该处理非常长的搜索关键词', () => {
      const longKeyword = 'a'.repeat(1000)
      factorSelection.searchKeyword.value = longKeyword
      expect(factorSelection.searchKeyword.value).toBe(longKeyword)
    })

    it('应该处理特殊字符的搜索关键词', () => {
      const specialKeyword = '!@#$%^&*()_+-=[]{}|;:",./<>?'
      factorSelection.searchKeyword.value = specialKeyword
      expect(factorSelection.searchKeyword.value).toBe(specialKeyword)
    })
  })
})