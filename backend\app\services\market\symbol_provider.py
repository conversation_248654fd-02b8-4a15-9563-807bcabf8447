"""股票/指数列表提供者模块

该模块负责获取和处理股票/指数列表数据，支持A股、美股和港股市场。
"""

import logging
from typing import List, Optional
import pandas as pd

# 导入abu相关模块
try:
    from abupy.MarketBu import ABuSymbol as abSymbol
    from abupy import ABuSymbolPd
    abu_available = True
except ImportError:
    abSymbol = None
    ABuSymbolPd = None
    abu_available = False
    logging.warning("abupy模块导入失败，部分功能可能不可用")

# 导入tushare
try:
    import tushare as ts
    tushare_available = True
except ImportError:
    tushare_available = False
    logging.warning("tushare模块导入失败，A股列表功能可能不可用")

# 导入项目内部模块
from backend.app.schemas.market import StockInfo
# 最终修正：ValueError 是内置异常，永远不应从自定义文件中导入！
from backend.app.core.exceptions import ResourceNotFoundError, ExternalAPIError

class SymbolProvider:
    """股票/指数列表提供者"""
    
    _stock_list_cache = {}
    
    @classmethod
    def get_stock_list(cls, market: str, name_filter: Optional[str] = None, limit: int = 100) -> List[StockInfo]:
        """获取股票列表"""
        market = market.upper()
        if market not in ["CN", "US", "HK"]:
            # 直接使用内置的 ValueError，无需导入
            raise ValueError(f"不支持的市场类型: {market}")

        try:
            if not abu_available:
                raise ExternalAPIError("abupy模块不可用，无法获取股票列表")

            if market == "CN":
                return cls._get_cn_stock_list(name_filter, limit)
            elif market == "US":
                return cls._get_us_stock_list(name_filter, limit)
            elif market == "HK":
                return cls._get_hk_stock_list(name_filter, limit)
        except (ResourceNotFoundError, ExternalAPIError, ValueError):
            raise
        except Exception as e:
            logging.error(f"获取股票列表时发生未知错误: {str(e)}", exc_info=True)
            raise ExternalAPIError(f"获取股票列表时发生未知内部错误: {str(e)}")

    @classmethod
    def _get_cn_stock_list(cls, name_filter: Optional[str] = None, limit: int = 100) -> List[StockInfo]:
        """
        获取A股股票列表。
        修正了回退逻辑：优先尝试Tushare，如果Tushare因任何原因失败（不可用、API错误、无数据），
        则自动回退到abupy方法。
        """
        try:
            if not tushare_available:
                raise ExternalAPIError("Tushare模块不可用")
            logging.info("尝试从Tushare获取A股列表...")
            return cls._get_cn_stock_list_by_tushare(name_filter, limit)
        except Exception as e:
            logging.warning(f"从Tushare获取A股列表失败 ({type(e).__name__}: {e})，回退到abupy方法。")
            try:
                return cls._get_cn_stock_list_by_abu(name_filter, limit)
            except Exception as e2:
                logging.error(f"备用方法abupy也获取失败: {e2}", exc_info=True)
                raise ExternalAPIError(f"主方法(Tushare)和备用方法(abupy)均获取A股列表失败。")

    @classmethod
    def _get_cn_stock_list_by_tushare(cls, name_filter: Optional[str] = None, limit: int = 100) -> List[StockInfo]:
        """
        使用tushare获取A股列表。
        修正：移除了内部的try-except，让异常自然冒泡，由上层调用者决定如何处理。
        """
        pro = ts.pro_api()
        stock_list = pro.stock_basic(exchange='', list_status='L', 
                                    fields='ts_code,symbol,name,area,industry,market,list_date')
        
        if stock_list is None or stock_list.empty:
            raise ExternalAPIError("Tushare API未返回任何数据")
            
        if name_filter:
            stock_list = stock_list[stock_list['name'].str.contains(name_filter, na=False)]
            
        if stock_list.empty:
            raise ResourceNotFoundError("Tushare中未找到匹配的股票数据")
            
        result = []
        for _, row in stock_list.head(limit).iterrows():
            symbol = cls._tushare_to_abu_code(row['ts_code'])
            result.append(StockInfo(
                symbol=symbol, name=row['name'], market="CN",
                industry=row.get('industry', ''), area=row.get('area', ''),
                listing_date=row.get('list_date', '')
            ))
                
        if not result:
            raise ResourceNotFoundError("从Tushare找到数据但无法有效处理")
            
        return result

    @classmethod
    def _get_cn_stock_list_by_abu(cls, name_filter: Optional[str] = None, limit: int = 100) -> List[StockInfo]:
        """使用abu原有逻辑获取A股列表"""
        logging.info("执行abupy回退方法获取A股列表。")
        stock_df = abSymbol.get_a_stock_symbols()

        if name_filter:
            stock_df = stock_df[stock_df['name'].str.contains(name_filter, na=False)]

        if stock_df.empty:
            raise ResourceNotFoundError("abupy回退方法未找到匹配的A股数据")

        result = []
        for _, row in stock_df.head(limit).iterrows():
            result.append(StockInfo(
                symbol=row['symbol'], name=row['name'], market="CN"
            ))
        return result

    @classmethod
    def _get_us_stock_list(cls, name_filter: Optional[str] = None, limit: int = 100) -> List[StockInfo]:
        """获取美股股票列表"""
        stock_df = abSymbol.get_us_stock_symbols()
        if name_filter:
            stock_df = stock_df[stock_df['name'].str.contains(name_filter, case=False, na=False)]
        if stock_df.empty:
            raise ResourceNotFoundError("未找到匹配的美股数据")
        result = []
        for _, row in stock_df.head(limit).iterrows():
            result.append(StockInfo(
                symbol=f"us{row['symbol']}", name=row['name'], market="US",
                industry=row.get('industry', ''), sector=row.get('sector', '')
            ))
        return result

    @classmethod
    def _get_hk_stock_list(cls, name_filter: Optional[str] = None, limit: int = 100) -> List[StockInfo]:
        """获取港股股票列表"""
        stock_df = abSymbol.get_hk_stock_symbols()
        if name_filter:
            stock_df = stock_df[stock_df['name'].str.contains(name_filter, case=False, na=False)]
        if stock_df.empty:
            raise ResourceNotFoundError("未找到匹配的港股数据")
        result = []
        for _, row in stock_df.head(limit).iterrows():
            result.append(StockInfo(
                symbol=f"hk{row['symbol']}", name=row['name'], market="HK"
            ))
        return result

    @staticmethod
    def _tushare_to_abu_code(ts_code: str) -> str:
        """将tushare格式代码转换为abu格式代码"""
        if '.' not in ts_code:
            return ts_code
        code, market = ts_code.split('.')
        market = market.lower()
        return f"{market}{code}"