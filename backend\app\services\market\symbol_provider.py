# -*- coding: utf-8 -*-
"""
股票/指数列表提供者模块，负责获取和处理股票/指数列表数据
"""
import logging
from typing import List, Optional

import pandas as pd
import tushare as ts

from abupy.MarketBu import ABuMarket

from backend.app.abupy_adapter.symbol_adapter import SymbolAdapter
from backend.app.core.exceptions import DataNotFoundError, ExternalAPIError, ValidationError
from backend.app.schemas.market import StockBasic
from backend.app.core.config import settings


class SymbolProvider:
    """股票/指数列表提供者"""
    
    @staticmethod
    def get_stock_list(pro, market: Optional[str] = None, 
                      industry: Optional[str] = None, 
                      name: Optional[str] = None) -> List[StockBasic]:
        """
        获取股票列表
        
        Args:
            pro: tushare pro_api实例
            market: 市场类型 CN(A股)/US(美股)/HK(港股)
            industry: 行业类型
            name: 股票名称（模糊查询）
            
        Returns:
            List[StockBasic]: 股票列表
            
        Raises:
            ValidationError: 当市场类型无效时
            ExternalAPIError: 当外部API调用失败时
            DataNotFoundError: 当没有找到匹配的股票数据时
        """
        # 验证市场参数
        if market is not None and market not in ['CN', 'US', 'HK']:
            raise ValidationError(
                message=f"不支持的市场类型: {market}",
                data={"supported_markets": ['CN', 'US', 'HK']}
            )
            
        if market == 'CN' or market is None:
            try:
                # 使用tushare获取A股列表
                fields = 'ts_code,name,industry,list_date,market,is_hs,is_st'
                where_condition = ""
                
                if industry:
                    where_condition += f" AND industry='{industry}'"
                if name:
                    where_condition += f" AND name LIKE '%{name}%'"
                    
                try:
                    df = pro.stock_basic(exchange='', list_status='L',
                                          fields=fields)
                except Exception as e:
                    logging.error(f"Tushare API调用失败: {str(e)}")
                    raise ExternalAPIError(
                        message=f"Tushare API获取股票列表失败: {str(e)}",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                if industry or name:
                    # 过滤数据
                    if industry:
                        df = df[df['industry'] == industry]
                    if name:
                        df = df[df['name'].str.contains(name)]
                
                # 检查是否找到数据
                if df.empty:
                    raise DataNotFoundError(
                        message="未找到匹配的股票数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                # 转换为响应模型
                result = []
                for _, row in df.iterrows():
                    try:
                        # 从tushare代码(如000001.SZ)转换为abu格式(如sh000001)
                        symbol = SymbolProvider._convert_to_abu_symbol(row['ts_code'])
                        
                        stock = StockBasic(
                            symbol=symbol,
                            name=row['name'],
                            market='CN',
                            industry=row.get('industry'),
                            list_date=row.get('list_date'),
                            is_hs=row.get('is_hs'),
                            is_st=bool(row.get('is_st') == 1) if 'is_st' in row else None
                        )
                        result.append(stock)
                    except Exception as e:
                        # 记录错误但继续处理其他股票
                        logging.warning(f"处理股票 {row.get('ts_code', 'unknown')} 出错: {str(e)}")
                        continue
                
                if not result:
                    raise DataNotFoundError(
                        message="未找到有效的股票数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                    
                return result
            except Exception as e:
                if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError)):
                    raise
                logging.error(f"获取A股列表失败: {str(e)}")
                raise ExternalAPIError(
                    message=f"获取A股列表失败: {str(e)}",
                    data={"market": market, "industry": industry, "name": name}
                )
        
        elif market == 'US':
            try:
                # 使用abu原有逻辑获取美股列表
                us_symbols = ABuMarket.all_us_symbol()
                if not us_symbols:
                    raise DataNotFoundError(
                        message="未找到美股列表数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                    
                result = []
                # 限制返回数量，实际应用中可以分页
                count = 0
                for symbol in us_symbols:
                    if count >= 100:  # 限制返回数量
                        break
                        
                    try:
                        # 使用abu的函数获取股票名称
                        stock_name = SymbolAdapter.get_symbol_name(symbol)
                        
                        # 如果有名称过滤条件，检查是否匹配
                        if name and name.lower() not in stock_name.lower():
                            continue
                            
                        stock = StockBasic(
                            symbol=symbol,
                            name=stock_name,
                            market='US'
                        )
                        result.append(stock)
                        count += 1
                    except Exception as e:
                        logging.warning(f"处理美股 {symbol} 出错: {str(e)}")
                        continue
                
                if not result:
                    raise DataNotFoundError(
                        message="未找到匹配的美股数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                return result
                
            except Exception as e:
                if isinstance(e, DataNotFoundError):
                    raise
                logging.error(f"获取美股列表失败: {str(e)}")
                raise ExternalAPIError(
                    message=f"获取美股列表失败: {str(e)}",
                    data={"market": market, "industry": industry, "name": name}
                )
        
        elif market == 'HK':
            try:
                # 使用tushare获取港股列表
                fields = 'ts_code,name,industry,list_date,market'
                try:
                    df = pro.hk_basic(fields=fields)
                except Exception as e:
                    logging.error(f"Tushare API调用失败: {str(e)}")
                    raise ExternalAPIError(
                        message=f"Tushare API获取港股列表失败: {str(e)}",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                # 过滤数据
                if industry:
                    df = df[df['industry'] == industry]
                if name:
                    df = df[df['name'].str.contains(name)]
                
                # 检查是否找到数据
                if df.empty:
                    raise DataNotFoundError(
                        message="未找到匹配的港股数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                result = []
                for _, row in df.iterrows():
                    try:
                        symbol = SymbolProvider._convert_to_abu_symbol(row['ts_code'], market='HK')
                        
                        stock = StockBasic(
                            symbol=symbol,
                            name=row['name'],
                            market='HK',
                            industry=row.get('industry'),
                            list_date=row.get('list_date')
                        )
                        result.append(stock)
                    except Exception as e:
                        logging.warning(f"处理港股 {row.get('ts_code', 'unknown')} 出错: {str(e)}")
                        continue
                
                if not result:
                    raise DataNotFoundError(
                        message="未找到有效的港股数据",
                        data={"market": market, "industry": industry, "name": name}
                    )
                
                return result
                
            except Exception as e:
                if isinstance(e, (DataNotFoundError, ExternalAPIError)):
                    raise
                logging.error(f"获取港股列表失败: {str(e)}")
                raise ExternalAPIError(
                    message=f"获取港股列表失败: {str(e)}",
                    data={"market": market, "industry": industry, "name": name}
                )
        
        else:
            # 这部分代码应该永远不会执行，因为我们在函数开头已经验证了market参数
            # 但作为防御性编程，还是保留它
            raise ValidationError(
                message=f"不支持的市场类型: {market}",
                data={"supported_markets": ['CN', 'US', 'HK']}
            )
    
    @staticmethod
    def _convert_to_abu_symbol(ts_code: str, market: str = 'CN') -> str:
        """将tushare代码转换为abu格式代码"""
        if market == 'CN':
            code, exchange = ts_code.split('.')
            if exchange == 'SZ':
                return f"sz{code}"
            elif exchange == 'SH':
                return f"sh{code}"
            else:
                return ts_code
        elif market == 'HK':
            code = ts_code.split('.')[0]
            return f"hk{code}"
        else:
            # 其他市场简单返回
            return ts_code
