import { defineStore } from 'pinia';
import { getDashboardSummary, type DashboardSummary } from '@/api/dashboard';

interface DashboardState {
  summary: DashboardSummary | null;
  loading: boolean;
}

export const useDashboardStore = defineStore('dashboard', {
  state: (): DashboardState => ({
    summary: null,
    loading: false,
  }),
  actions: {
    async fetchDashboardSummary() {
      this.loading = true;
      try {
        const response = await getDashboardSummary();
        this.summary = response;
      } catch (error) {
        console.error('Failed to fetch dashboard summary:', error);
        // 这里可以添加错误处理逻辑，例如设置一个错误状态
      } finally {
        this.loading = false;
      }
    },
  },
});