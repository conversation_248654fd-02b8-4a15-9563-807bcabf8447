import pytest
from backend.app.services.market.kline_provider import KlineProvider

class TestKlineProvider:

    def setup_method(self):
        self.provider = KlineProvider()

    @pytest.mark.parametrize("symbol, expected_key", [
        ("sz000001", "/000001"),
        ("sh600000", "/600000"),
        ("000001.SZ", "/000001"),
        ("600000.SH", "/600000"),
        ("000001", "/000001"),
    ])
    def test_generate_hdf5_key_valid(self, symbol, expected_key):
        """测试 _generate_hdf5_key 方法能否正确处理各种有效的股票代码格式"""
        assert self.provider._generate_hdf5_key(symbol) == expected_key

    @pytest.mark.parametrize("invalid_symbol", [
        ("12345"),
        ("sz12345"),
        ("invalid_code"),
        (""),
    ])
    def test_generate_hdf5_key_invalid(self, invalid_symbol):
        """测试 _generate_hdf5_key 方法在遇到无效输入时是否会按预期抛出 KeyError"""
        with pytest.raises(KeyError, match=f"无法从符号 '{invalid_symbol}' 中提取出6位数字代码。"):
            self.provider._generate_hdf5_key(invalid_symbol)