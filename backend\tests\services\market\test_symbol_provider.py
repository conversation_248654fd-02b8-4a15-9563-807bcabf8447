import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
import sys
import os

# 确保能找到 backend 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
from backend.app.services.market.symbol_provider import SymbolProvider
from backend.app.schemas.market import StockInfo
from backend.app.core.exceptions import ResourceNotFoundError

# 最终解决方案：我们 patch 的目标是 symbol_provider 模块内部的对象
@patch('backend.app.services.market.symbol_provider.abSymbol', create=True)
@patch('backend.app.services.market.symbol_provider.tushare_available', True) # 这个patch不会注入参数
@patch('tushare.pro_api')
class TestSymbolProvider(unittest.TestCase):

    def setUp(self):
        SymbolProvider._stock_list_cache = {}

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_cn_stock_list_success(self, mock_pro_api, mock_abSymbol):
        """测试：当Tushare成功返回数据时，应正确处理"""
        mock_df = pd.DataFrame({
            'ts_code': ['600519.SH', '000001.SZ'], 'name': ['贵州茅台', '平安银行'],
            'industry': ['白酒', '银行'], 'area': ['贵州', '深圳'], 'list_date': ['20010827', '19910403']
        })
        mock_pro_api.return_value.stock_basic.return_value = mock_df
        
        result = SymbolProvider.get_stock_list(market='CN')
        
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].symbol, 'sh600519')
        mock_abSymbol.get_a_stock_symbols.assert_not_called()

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_cn_stock_list_tushare_fails_fallback_to_abu(self, mock_pro_api, mock_abSymbol):
        """测试：当Tushare API调用异常时，应成功回退到abupy"""
        mock_pro_api.return_value.stock_basic.side_effect = Exception("Tushare API Error")
        
        mock_abu_df = pd.DataFrame({'name': ['上证指数'], 'symbol': ['sh000001']})
        mock_abSymbol.get_a_stock_symbols.return_value = mock_abu_df

        result = SymbolProvider.get_stock_list(market='CN')
        
        mock_abSymbol.get_a_stock_symbols.assert_called_once()
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].name, '上证指数')

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_cn_stock_list_no_data_fallback_to_abu(self, mock_pro_api, mock_abSymbol):
        """测试：当Tushare返回空数据时，应成功回退到abupy"""
        mock_pro_api.return_value.stock_basic.return_value = pd.DataFrame()
        
        mock_abu_df = pd.DataFrame({'name': ['上证指数'], 'symbol': ['sh000001']})
        mock_abSymbol.get_a_stock_symbols.return_value = mock_abu_df

        result = SymbolProvider.get_stock_list(market='CN')
        
        mock_abSymbol.get_a_stock_symbols.assert_called_once()
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].name, '上证指数')

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_us_stock_list_success(self, mock_pro_api, mock_abSymbol):
        """测试：成功获取美股列表"""
        mock_us_df = pd.DataFrame({
            'name': ['Apple Inc.', 'Google Inc.'], 'symbol': ['AAPL', 'GOOGL'],
            'sector': ['Technology', 'Technology'], 'industry': ['Computers', 'Internet']
        })
        mock_abSymbol.get_us_stock_symbols.return_value = mock_us_df
        
        result = SymbolProvider.get_stock_list(market='US')
        
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].symbol, 'usAAPL')

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_hk_stock_list_success(self, mock_pro_api, mock_abSymbol):
        """测试：成功获取港股列表"""
        mock_hk_df = pd.DataFrame({'name': ['腾讯控股', '阿里巴巴'], 'symbol': ['00700', '09988']})
        mock_abSymbol.get_hk_stock_symbols.return_value = mock_hk_df
        
        result = SymbolProvider.get_stock_list(market='HK')
        
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].symbol, 'hk00700')

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_us_stock_list_with_name_filter(self, mock_pro_api, mock_abSymbol):
        """测试：获取美股列表时名称过滤正常工作"""
        mock_df = pd.DataFrame({
            'name': ['Apple Inc.', 'Google Inc.'], 'symbol': ['AAPL', 'GOOGL']
        })
        mock_abSymbol.get_us_stock_symbols.return_value = mock_df
        
        result = SymbolProvider.get_stock_list(market='US', name_filter='Apple')
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].name, 'Apple Inc.')

    # 修正：移除多余的 mock_ts_available 参数
    def test_get_stock_list_unsupported_market(self, mock_pro_api, mock_abSymbol):
        """测试：请求不支持的市场时应抛出ValueError"""
        with self.assertRaises(ValueError):
            SymbolProvider.get_stock_list(market='JP')