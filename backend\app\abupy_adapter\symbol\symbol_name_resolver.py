# -*- coding: utf-8 -*-
"""
符号名称解析器模块
"""
import threading
import logging
from typing import Dict

# 导入自定义异常
from app.core.exceptions import SymbolError

# 假设这些常量在其他地方定义
CN_INDEX_CODES = {
    'sh000001': '上证指数',
    'sh000300': '沪深300',
    'sz399001': '深证成指',
    'sz399006': '创业板指'
}

US_INDEX_CODES = {
    'us.dji': '道琼斯工业平均指数',
    'us.ixic': '纳斯达克综合指数',
    'us.inx': '标普500'
}

HK_INDEX_CODES = {
    'hkhsi': '恒生指数',
    'hkhscei': '国企指数',
    'hkhscci': '红筹指数'
}

class SymbolNameResolver:
    """解析股票名称"""
    _cache_lock = threading.RLock()
    _symbol_name_cache: Dict[str, str] = {}

    @classmethod
    def get_symbol_name(cls, symbol_str: str) -> str:
        """
        获取股票名称
        
        Args:
            symbol_str: 代码字符串
            
        Returns:
            str: 股票名称
        """
        with cls._cache_lock:
            if symbol_str in cls._symbol_name_cache:
                return cls._symbol_name_cache[symbol_str]

        # 检查是否为已知指数
        for index_dict in [CN_INDEX_CODES, US_INDEX_CODES, HK_INDEX_CODES]:
            if symbol_str.lower() in index_dict:
                name = index_dict[symbol_str.lower()]
                with cls._cache_lock:
                    cls._symbol_name_cache[symbol_str] = name
                return name

        # 尝试使用tushare或其他数据源获取名称
        try:
            name = cls._get_name_from_tushare(symbol_str)
            if name:
                with cls._cache_lock:
                    cls._symbol_name_cache[symbol_str] = name
                return name
        except Exception as e:
            logging.warning(f"从tushare获取名称失败: {e}")

        # 如果外部数据源不可用，则使用本地规则
        try:
            name = cls._get_name_from_local_rules(symbol_str)
            with cls._cache_lock:
                cls._symbol_name_cache[symbol_str] = name
            return name
        except Exception:
            return symbol_str

    @staticmethod
    def _get_name_from_tushare(symbol_str: str) -> str | None:
        """尝试从Tushare获取股票名称"""
        try:
            import tushare as ts
            from app.core.config import settings
            if not settings.TUSHARE_TOKEN:
                return None

            ts.set_token(settings.TUSHARE_TOKEN)
            pro = ts.pro_api()

            if symbol_str.startswith(('sh', 'sz')):
                ts_code = f"{symbol_str[2:]}.{symbol_str[:2].upper()}"
                df = pro.stock_basic(ts_code=ts_code, fields='ts_code,name')
                if not df.empty:
                    return df['name'].iloc[0]
            elif symbol_str.startswith('hk'):
                ts_code = f"{symbol_str[2:]}.HK"
                df = pro.hk_basic(ts_code=ts_code, fields='ts_code,name')
                if not df.empty:
                    return df['name'].iloc[0]
            elif '.' in symbol_str:
                code, suffix = symbol_str.split('.')
                ts_code = f"{code}.{suffix.upper()}"
                if ts_code.endswith(('.SH', '.SZ')):
                    df = pro.stock_basic(ts_code=ts_code, fields='ts_code,name')
                    if not df.empty:
                        return df['name'].iloc[0]
                elif ts_code.endswith('.HK'):
                    df = pro.hk_basic(ts_code=ts_code, fields='ts_code,name')
                    if not df.empty:
                        return df['name'].iloc[0]
            elif symbol_str.isdigit():
                if len(symbol_str) == 6:
                    prefix = 'SH' if symbol_str.startswith('6') else 'SZ'
                    ts_code = f"{symbol_str}.{prefix}"
                    df = pro.stock_basic(ts_code=ts_code, fields='ts_code,name')
                    if not df.empty:
                        return df['name'].iloc[0]
                elif len(symbol_str) in [4, 5]:
                    ts_code = f"{symbol_str}.HK"
                    df = pro.hk_basic(ts_code=ts_code, fields='ts_code,name')
                    if not df.empty:
                        return df['name'].iloc[0]

        except ImportError:
            logging.warning("Tushare未安装，无法获取股票名称")
        except Exception as e:
            logging.warning(f"使用tushare获取{symbol_str}的名称失败: {str(e)}")
        
        return None

    @staticmethod
    def _get_name_from_local_rules(symbol_str: str) -> str:
        """根据本地规则生成股票名称"""
        symbol_lower = symbol_str.lower()
        if symbol_lower.startswith('sh'):
            code = symbol_lower[2:]
            if code.startswith('60'): return f"{code}(沪A)"
            if code.startswith('688'): return f"{code}(科创板)"
            if code == '000001': return "上证指数"
            return f"{code}(上证)"
        elif symbol_lower.startswith('sz'):
            code = symbol_lower[2:]
            if code.startswith('00'): return f"{code}(深A)"
            if code.startswith('30'): return f"{code}(创业板)"
            if code == '399001': return "深证成指"
            return f"{code}(深证)"
        elif symbol_lower.startswith('us'):
            return f"{symbol_str[2:].upper()}(美股)"
        elif symbol_lower.startswith('hk'):
            return f"{symbol_lower[2:]}(港股)"
        elif symbol_str.isdigit():
            if len(symbol_str) == 6:
                if symbol_str.startswith('6'): return f"sh{symbol_str}(沪A)"
                if symbol_str.startswith(('0', '3')): return f"sz{symbol_str}(深A)"
            elif len(symbol_str) == 5: return f"hk{symbol_str}(港股)"
        return symbol_str