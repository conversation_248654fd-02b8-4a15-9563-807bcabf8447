import { describe, it, expect, beforeEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useAppStore } from '../../../src/stores/app';

// TDD专用测试文件 - useAppStore
// 专注于全局状态管理的核心功能

describe('useAppStore - TDD专用测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  describe('契约 A: 初始状态', () => {
    it('Store被创建时，必须处于一个明确的、干净的初始状态 - TDD', () => {
      const store = useAppStore();
      expect(store.isLoading).toBe(false);
    });
  });

  describe('契约 B: 加载状态管理 (setLoading)', () => {
    it('setLoading(true)应设置isLoading为true - TDD', () => {
      const store = useAppStore();
      store.setLoading(true);
      expect(store.isLoading).toBe(true);
    });

    it('setLoading(false)应设置isLoading为false - TDD', () => {
      const store = useAppStore();
      store.setLoading(true); // 先设置为true
      store.setLoading(false);
      expect(store.isLoading).toBe(false);
    });

    it('多次调用setLoading应正确更新状态 - TDD', () => {
      const store = useAppStore();
      
      store.setLoading(true);
      expect(store.isLoading).toBe(true);
      
      store.setLoading(false);
      expect(store.isLoading).toBe(false);
      
      store.setLoading(true);
      expect(store.isLoading).toBe(true);
    });
  });

  describe('契约 C: 状态一致性', () => {
    it('isLoading状态应始终反映最后一次setLoading调用的值 - TDD', () => {
      const store = useAppStore();
      
      // 初始状态
      expect(store.isLoading).toBe(false);
      
      // 连续调用
      store.setLoading(true);
      store.setLoading(true);
      expect(store.isLoading).toBe(true);
      
      store.setLoading(false);
      store.setLoading(false);
      expect(store.isLoading).toBe(false);
    });
  });
});