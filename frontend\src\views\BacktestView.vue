<template>
  <div class="backtest-view">
    <div class="backtest-header">
      <h1>回测分析</h1>
      <p class="subtitle">专业量化策略回测平台</p>
    </div>
    
    <div class="backtest-content">
      <!-- 回测表单 -->
      <BacktestForm 
        @submit="handleBacktestSubmit"
        @reset="handleFormReset"
        :loading="backtestStore.isBacktesting"
      />
      
      <!-- 回测进度 -->
      <div v-if="backtestStore.isBacktesting" class="progress-section">
        <ElProgress 
          :percentage="backtestStore.backtestProgress"
          class="progress-bar"
        />
      </div>
      
      <!-- 回测结果 -->
      <BacktestResults 
        :result="backtestStore.backtestResult"
        :loading="backtestStore.isBacktesting"
        :error="backtestStore.backtestError"
        @export="handleExportResults"
        @view-analysis="handleViewAnalysis"
      />
      

    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { ElProgress } from 'element-plus'
import { useBacktestStore, useStrategyStore } from '@/stores'
import BacktestForm from '@/components/backtest/BacktestForm.vue'
import BacktestResults from '@/components/backtest/BacktestResults.vue'

import type { BacktestConfig } from '@/api/types/backtest'

// 定义组件emit事件
const emit = defineEmits(['export-result', 'view-analysis'])

const backtestStore = useBacktestStore()
const strategyStore = useStrategyStore()

// 计算指标数据
const computedMetrics = computed(() => {
  if (!backtestStore.backtestResult) return null
  return {
    totalReturn: backtestStore.backtestResult.total_return,
    sharpeRatio: backtestStore.backtestResult.sharpe_ratio,
    maxDrawdown: backtestStore.backtestResult.max_drawdown
  }
})

// 处理回测提交
const handleBacktestSubmit = async (config: BacktestConfig) => {
  // 检查配置是否为空
  if (!config) {
    return
  }
  
  try {
    await backtestStore.startBacktest(config)
    ElMessage.success('回测启动成功')
  } catch (error) {
    console.error('回测执行失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '回测执行失败')
  }
}

// 处理表单重置
const handleFormReset = () => {
  backtestStore.resetBacktestState()
  backtestStore.clearError()
}

// 处理结果导出
const handleExportResults = () => {
  emit('export-result', backtestStore.backtestResult)
}

// 处理查看详细分析
const handleViewAnalysis = () => {
  emit('view-analysis', backtestStore.backtestResult)
}

// 组件挂载时初始化
onMounted(() => {
  // 获取策略列表
  strategyStore.fetchStrategies()
  // 获取回测历史
  backtestStore.fetchBacktestHistory()
})
</script>

<style scoped>
.backtest-view {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.backtest-header {
  margin-bottom: 32px;
  text-align: center;
}

.backtest-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 16px;
}

.backtest-content {
  margin-top: 24px;
}

.progress-section {
  margin: 24px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-bar {
  margin-bottom: 8px;
}
</style>