// 卡片和表单组件的统一布局规范

// ===== 卡片组件规范 =====

// 基础卡片样式
.el-card {
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color-light);
  box-shadow: var(--shadow-1);
  transition: var(--transition-base);
  
  // 卡片头部
  .el-card__header {
    padding: var(--card-header-padding);
    background-color: var(--bg-color-secondary);
    border-bottom: 1px solid var(--border-color-light);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    
    .card-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      margin: 0;
    }
    
    .card-subtitle {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      margin: var(--space-xs) 0 0 0;
    }
    
    .card-actions {
      display: flex;
      gap: var(--space-sm);
      align-items: center;
    }
  }
  
  // 卡片主体
  .el-card__body {
    padding: var(--card-body-padding);
    
    // 紧凑模式
    &.compact {
      padding: var(--space-md);
    }
    
    // 宽松模式
    &.spacious {
      padding: var(--space-xl);
    }
  }
  
  // 悬停效果
  &.is-hover-shadow:hover {
    box-shadow: var(--shadow-2);
    transform: translateY(-2px);
  }
  
  // 无阴影模式
  &.no-shadow {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
  
  // 无边框模式
  &.borderless {
    border: none;
    box-shadow: none;
  }
}

// 卡片变体样式
.card-primary {
  border-color: var(--color-primary);
  
  .el-card__header {
    background-color: var(--color-primary);
    color: white;
    border-bottom-color: var(--color-primary);
  }
}

.card-success {
  border-color: var(--color-success);
  
  .el-card__header {
    background-color: var(--color-success);
    color: white;
    border-bottom-color: var(--color-success);
  }
}

.card-warning {
  border-color: var(--color-warning);
  
  .el-card__header {
    background-color: var(--color-warning);
    color: white;
    border-bottom-color: var(--color-warning);
  }
}

.card-error {
  border-color: var(--color-error);
  
  .el-card__header {
    background-color: var(--color-error);
    color: white;
    border-bottom-color: var(--color-error);
  }
}

// 量化投资特定卡片样式
.card-profit {
  border-color: var(--color-profit);
  background-color: var(--bg-color-profit);
  
  .el-card__header {
    background-color: var(--color-profit);
    color: white;
  }
}

.card-loss {
  border-color: var(--color-loss);
  background-color: var(--bg-color-loss);
  
  .el-card__header {
    background-color: var(--color-loss);
    color: white;
  }
}

// ===== 表单组件规范 =====

// 基础表单样式
.el-form {
  // 表单项间距
  .el-form-item {
    margin-bottom: var(--space-lg);
    
    // 紧凑模式
    &.compact {
      margin-bottom: var(--space-md);
    }
    
    // 宽松模式
    &.spacious {
      margin-bottom: var(--space-xl);
    }
    
    // 最后一个表单项
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  // 表单标签
  .el-form-item__label {
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-base);
    
    // 必填标记
    &.is-required::before {
      color: var(--color-error);
    }
  }
  
  // 表单内容
  .el-form-item__content {
    // 输入框统一宽度
    .el-input,
    .el-select,
    .el-textarea,
    .el-date-picker,
    .el-time-picker {
      width: 100%;
    }
    
    // 输入框样式
    .el-input__wrapper {
      border-radius: var(--border-radius-base);
      transition: var(--transition-fast);
      
      &:hover {
        border-color: var(--color-primary-light);
      }
      
      &.is-focus {
        border-color: var(--color-primary);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
  
  // 错误状态
  .el-form-item.is-error {
    .el-input__wrapper {
      border-color: var(--color-error);
      
      &.is-focus {
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
      }
    }
  }
}

// 表单布局变体
.form-horizontal {
  .el-form-item {
    display: flex;
    align-items: center;
    
    .el-form-item__label {
      flex: 0 0 auto;
      margin-right: var(--space-md);
    }
    
    .el-form-item__content {
      flex: 1;
    }
  }
}

.form-inline {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
  
  .el-form-item {
    margin-bottom: 0;
    flex: 0 0 auto;
  }
}

// 表单组合样式
.form-group {
  padding: var(--space-lg);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-color-secondary);
  margin-bottom: var(--space-lg);
  
  .form-group-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid var(--border-color-light);
  }
}

// 表单按钮组
.form-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-color-light);
  margin-top: var(--space-lg);
  
  &.center {
    justify-content: center;
  }
  
  &.start {
    justify-content: flex-start;
  }
  
  &.between {
    justify-content: space-between;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-card {
    .el-card__header {
      padding: var(--space-md);
    }
    
    .el-card__body {
      padding: var(--space-md);
    }
  }
  
  .el-form {
    .el-form-item {
      margin-bottom: var(--space-md);
    }
  }
  
  .form-horizontal {
    .el-form-item {
      flex-direction: column;
      align-items: flex-start;
      
      .el-form-item__label {
        margin-right: 0;
        margin-bottom: var(--space-xs);
      }
    }
  }
  
  .form-inline {
    flex-direction: column;
    
    .el-form-item {
      width: 100%;
    }
  }
}