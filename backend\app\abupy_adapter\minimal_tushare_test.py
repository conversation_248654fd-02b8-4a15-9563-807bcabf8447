# D:\智能投顾\量化相关\abu_modern\backend\app\abupy_adapter\minimal_tushare_test.py
import sys
import os

# Try to set up sys.path to mimic the project environment
# Assumes the script is run from the project root 'D:\智能投顾\量化相关\abu_modern'
# Get the directory of the current script (abupy_adapter)
current_script_dir = os.path.dirname(os.path.abspath(__file__))
# Get the path to 'backend' (parent of 'app' directory)
backend_dir = os.path.abspath(os.path.join(current_script_dir, '..', '..'))
# Get the project root (parent of 'backend')
project_root_dir = os.path.abspath(os.path.join(backend_dir, '..'))

# Add backend_dir to sys.path so that 'from app.core.config import settings' works
# This makes the project's 'app' package importable as a top-level 'app'
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)
# Add project_root_dir as well, as pytest often does
if project_root_dir not in sys.path:
    sys.path.insert(0, project_root_dir)

# >>> NEW: Apply compatibility patches <<<
try:
    print("Attempting to apply compatibility patches...")
    from backend.app.abupy_adapter.compatibility_patch import apply_patches
    apply_patches()
    print("Compatibility patches applied successfully.")
except ImportError as e_patch_imp:
    print(f"ERROR: Failed to import apply_patches: {e_patch_imp}")
    print("Skipping patch application. ABuPy imports might fail.")
except Exception as e_patch_apply:
    print(f"ERROR: Failed during application of compatibility patches: {e_patch_apply}")
    print("ABuPy imports might fail.")
print("-" * 30)
# >>> END NEW <<<

print(f"Running from: {os.getcwd()}")
print(f"Current script directory: {current_script_dir}")
print(f"Backend directory (added to sys.path): {backend_dir}")
print(f"Project root directory (added to sys.path): {project_root_dir}")
print("Effective sys.path:")
for p in sys.path:
    print(f"  {p}")

print("-" * 30)
print("Before any app-specific import from project:")
print(f"Is 'app' in sys.modules? {'app' in sys.modules}")
if 'app' in sys.modules:
    print(f"sys.modules['app'] = {sys.modules['app']}")
print("-" * 30)

settings_imported = False
project_app_module = None
try:
    print("Attempting to import from project's app: from app.core.config import settings")
    from backend.app.core.config import settings
    settings_imported = True
    print("Successfully imported 'settings' from project's 'app.core.config'")
    print(f"Is 'app' in sys.modules after import? {'app' in sys.modules}")
    if 'app' in sys.modules:
        project_app_module = sys.modules['app']
        print(f"sys.modules['app'] = {project_app_module}")
        if hasattr(project_app_module, '__path__'):
            print(f"sys.modules['app'].__path__ = {project_app_module.__path__}")
        else:
            print(f"sys.modules['app'] is not a package or __path__ is not set.")

except ImportError as e:
    print(f"Failed to import from project's app: {e}")
    print("This test might not accurately reflect the environment if this fails.")
print("-" * 30)

# >>> SECTION: Import and initialize ABuEnv <<<
abu_env_imported = False
try:
    print("Attempting to import ABuEnv from abupy.CoreBu.ABuEnv...")
    from abupy.CoreBu.ABuEnv import EMarketSourceType, EMarketDataFetchMode
    import abupy.CoreBu.ABuEnv as EnvModule # Import the module itself for diagnostics
    abu_env_imported = True
    print("Successfully imported ABuEnv and related enums.")
    
    # Basic diagnostics from ABuEnv
    print(f"  ABU_PROJECT_DATA_DIR (before any change): {EnvModule.g_project_data_dir}")
    print(f"  Market Source (before any change): {EnvModule.g_market_source}")
    print(f"  Data Fetch Mode (before any change): {EnvModule.g_data_fetch_mode}")

except ImportError as e_abu:
    print(f"ERROR: Failed to import ABuEnv: {e_abu}")
    import traceback
    traceback.print_exc()
except Exception as e_abu_diag:
    print(f"ERROR: Error during ABuEnv import or diagnostics: {e_abu_diag}")
    import traceback
    traceback.print_exc()
print("-" * 30)
# >>> END SECTION <<<

if not abu_env_imported:
    print("Skipping Tushare import due to ABuEnv import failure.")
else:
    print("Attempting to import tushare...")
    try:
        import tushare as ts
        print("Tushare imported successfully.")
        
        tushare_token_to_use = None
        if settings_imported and hasattr(settings, 'TUSHARE_TOKEN'):
            tushare_token_to_use = settings.TUSHARE_TOKEN
            print(f"Using TUSHARE_TOKEN from settings: {bool(tushare_token_to_use)}")
        else:
            print("settings.TUSHARE_TOKEN not available.")

        if tushare_token_to_use:
            print(f"Calling ts.set_token() with token from settings.")
            ts.set_token(tushare_token_to_use)
        
        print("Calling ts.pro_api()...")
        pro = ts.pro_api() 
        print("Tushare pro_api() called successfully.")

    except ImportError as e_ts_imp:
        print(f"ERROR: Failed to import tushare: {e_ts_imp}")
        import traceback
        traceback.print_exc()
    except Exception as e_ts_op:
        print(f"ERROR: Error during tushare operations: {e_ts_op}")
        import traceback
        traceback.print_exc()
print("-" * 30)
