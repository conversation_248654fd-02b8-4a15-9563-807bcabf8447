# 工作日志：后端仪表盘API真实数据计算实现

**日期:** 2025年06月26日

**实现者:** 实现者AI

**分支:** `feature/frontend-mvp`

## 1. 任务描述

本次任务的核心目标是修改后端仪表盘（Dashboard）的聚合API (`/api/dashboard/summary`)，将其中硬编码的假数据替换为基于真实数据的计算逻辑，并明确标记出当前无法实现的技术债。

## 2. 实现细节

### 2.1. 文件修改

- **文件路径:** `backend/app/api/endpoints/dashboard.py`

### 2.2. 主要变更

1.  **注入 `MarketService`**:
    -   为了获取市场行情数据，在 `get_dashboard_summary` API端点中通过FastAPI的依赖注入系统注入了 `MarketService`。

2.  **实现 `today_gain` (今日涨跌) 计算**:
    -   **逻辑:** 通过调用 `market_service.get_kline_data('000300.SH', period='daily')` 获取沪深300指数最近的日K线数据。
    -   **计算:** 使用返回数据中的最新两个交易日的收盘价，计算涨跌幅 `(今日收盘价 - 昨日收盘价) / 昨日收盘价`。
    -   **健壮性:** 增加了异常处理逻辑，如果获取数据或计算过程中发生错误，将记录错误日志并默认 `today_gain` 为 `0.0`。

3.  **处理 `active_strategies` (活跃策略数)**:
    -   此项逻辑之前已正确实现，继续使用 `strategy_service.get_all_strategies()` 的返回结果计算策略数量。

4.  **明确技术债**:
    -   **`total_turnover_wan` (总成交额):** 由于该指标依赖于尚未实现的回测结果持久化功能，当前版本暂时硬编码为 `0.0`。在代码中添加了明确的 `TODO` 注释，说明了未来的实现方向。
        ```python
        # TODO: Implement real turnover calculation after backtest result persistence is done.
        total_turnover_wan = 0.0
        ```
    -   **`signals_count` (信号数量):** 该指标同样依赖于未来的信号生成或记录模块，因此也暂时硬编码为 `0`，并添加了 `TODO` 注释。
        ```python
        # TODO: Implement real signals count after signal generation module is done.
        signals_count = 0
        ```

## 3. 代码变更摘要

```python
# backend/app/api/endpoints/dashboard.py

# ... imports
from backend.app.services.market import MarketService

# ...

@router.get("/summary", response_model=ResponseSchema[DashboardSummary])
def get_dashboard_summary(
    db: Session = Depends(get_session), 
    strategy_service: StrategyService = Depends(get_strategy_service),
    market_service: MarketService = Depends(MarketService)  # 注入MarketService
):
    # ... (获取活跃策略数)

    # 计算今日涨跌 (以沪深300为例)
    today_gain = 0.0
    try:
        kline_data = market_service.get_kline_data('000300.SH', period='daily')
        if kline_data and len(kline_data.kl_data) >= 2:
            today_close = kline_data.kl_data[-1]['close']
            yesterday_close = kline_data.kl_data[-2]['close']
            if yesterday_close > 0:
                today_gain = (today_close - yesterday_close) / yesterday_close
    except Exception as e:
        logger.error(f"Failed to calculate today_gain: {e}", exc_info=True)

    # ... (技术债 TODO 注释)

    summary_data = DashboardSummary(
        today_gain=today_gain,
        active_strategies=strategies_count,
        total_turnover_wan=total_turnover_wan,
        signals_count=signals_count
    )
    return success(data=summary_data)
```

## 4. 预期结果

- 后端 `/api/dashboard/summary` 接口现在能够返回基于真实市场数据计算的 `today_gain` 和基于数据库的 `active_strategies`。
- 其余指标 (`total_turnover_wan`, `signals_count`) 暂时保持为 `0`，并有清晰的技术债注释。
- 前端仪表盘刷新后，“今日涨跌”和“活跃策略数”将展示真实数据。

## 5. 后续步骤

- 提交本次后端代码变更。
- 在未来的开发迭代中，根据 `TODO` 注释实现回测结果和信号系统的功能，并回来完善这些指标的计算。

## 6. 补充：前后端联调修复市场表现图表

在完成上述后端API改造后，前端仪表盘的“市场表现”图表仍无法显示数据。经过排查，发现问题涉及前后端数据结构的差异和后端的数据处理错误。以下是详细的修复过程：

### 6.1. 问题诊断与修复

1.  **前端数据结构不匹配**: 
    -   **问题**: 前端 `Dashboard.vue` 组件期望从API获取 `market_performance` 字段来渲染图表，但后端的 `DashboardSummary` schema中并未定义此字段。
    -   **修复**: 
        -   **后端**: 修改 `backend/app/schemas/dashboard.py`，为 `DashboardSummary` 添加 `market_performance` 字段，并定义了新的 `MarketPerformance` schema。
        -   **前端**: 对应修改 `frontend/src/api/dashboard.ts`，在 `DashboardSummary` 接口中添加 `market_performance` 字段。

2.  **后端API未返回图表数据**:
    -   **问题**: 即使schema更新后，后端的 `get_dashboard_summary` 函数也并未实际构造和返回 `market_performance` 数据。
    -   **修复**: 修改 `backend/app/api/endpoints/dashboard.py`，在函数中增加了数据处理逻辑：从K线数据中截取最近30天的数据，并构造成 `MarketPerformance` 对象，填充到返回的 `DashboardSummary` 中。

3.  **后端数据处理错误 (AttributeError)**:
    -   **问题**: 在修复上述问题后，后端API开始返回 `500 Internal Server Error`。日志显示 `AttributeError: 'str' object has no attribute 'strftime'`，原因是代码尝试对一个已经是字符串格式的日期 (`k.date`) 调用 `strftime` 方法。
    -   **修复**: 再次修改 `backend/app/api/endpoints/dashboard.py`，移除了不必要的 `.strftime('%Y-%m-%d')` 调用，直接使用 `k.date` 字符串。

### 6.2. 代码变更摘要 (修复部分)

```python
# backend/app/schemas/dashboard.py
class MarketPerformance(BaseModel):
    date: List[str]
    value: List[float]

class DashboardSummary(BaseModel):
    # ... other fields
    market_performance: MarketPerformance

# backend/app/api/endpoints/dashboard.py
@router.get("/summary", response_model=ResponseSchema[DashboardSummary])
def get_dashboard_summary(...):
    # ... (existing logic)

    # 获取最近30天的市场表现数据
    recent_kline = kline_data.kl_data[-30:]
    market_performance_data = {
        "date": [k.date for k in recent_kline],  # 修复点：直接使用字符串日期
        "value": [k.close for k in recent_kline]
    }

    summary_data = DashboardSummary(
        # ... other fields
        market_performance=MarketPerformance(**market_performance_data)
    )
    return success(data=summary_data)
```

### 6.3. 最终结果

- 经过上述修复，前后端数据结构完全对齐，后端API能够正确处理并返回包含最近30天市场表现的数据。
- 前端仪表盘的“市场表现”图表成功渲染，正确显示了动态数据，问题得到解决。