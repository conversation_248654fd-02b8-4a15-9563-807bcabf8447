# -*- coding: utf-8 -*-
"""
符号验证器模块
"""
import re
from backend.app.core.exceptions import SymbolError

class SymbolValidator:
    """验证股票代码格式"""

    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """
        验证股票代码格式是否有效。重构后的逻辑更清晰，能正确处理前缀和后缀带点的情况。

        Args:
            symbol: 股票代码

        Returns:
            bool: 格式是否有效

        Raises:
            SymbolError: 当代码格式无效时
        """
        if not symbol:
            raise SymbolError(message="股票代码不能为空")

        # 优先处理带'.'的格式，因为其意图最明确
        if '.' in symbol:
            parts = symbol.split('.')
            if len(parts) != 2:
                raise SymbolError(f"股票代码格式错误，包含多个点: {symbol}")

            part1, part2 = parts[0], parts[1]

            # 场景一: market_prefix.code 格式, e.g., 'us.TSLA'
            if part1.lower() in ['us', 'sh', 'sz', 'hk']:
                prefix = part1.lower()
                code = part2
                if prefix == 'us':
                    if not re.match(r'^[A-Za-z0-9\.-]+$', code) or not code:
                        raise SymbolError(f"美股代码部分格式错误: {symbol}")
                    return True
                elif prefix in ['sh', 'sz']:
                    if not (code.isdigit() and len(code) == 6):
                        raise SymbolError(f"A股代码部分必须为6位数字: {symbol}")
                    return True
                elif prefix == 'hk':
                    if not (code.isdigit() and len(code) in [4, 5]):
                        raise SymbolError(f"港股代码部分必须为4或5位数字: {symbol}")
                    return True

            # 场景二: code.market_suffix 格式, e.g., '600036.SH'
            else:
                code = part1
                suffix = part2.upper()
                if suffix in ['SH', 'SZ']:
                    if not (code.isdigit() and len(code) == 6):
                        raise SymbolError(f"A股代码必须为6位数字: {symbol}")
                    return True
                elif suffix == 'HK':
                    if not (code.isdigit() and len(code) in [4, 5]):
                        raise SymbolError(f"港股代码必须为4或5位数字: {symbol}")
                    return True
                else:
                    raise SymbolError(f"不支持的市场后缀: {suffix}", data={"supported_suffixes": ["SH", "SZ", "HK"]})
            
            # 如果带点，但两种格式都不匹配，则为错误
            raise SymbolError(f"无法识别的带点格式: {symbol}")

        # --- 以下处理不带'.'的格式 ---

        # 场景三: market_prefix + code 格式, e.g., 'sh600036'
        if symbol.lower().startswith(('sh', 'sz')):
            code = symbol[2:]
            if not (code.isdigit() and len(code) == 6):
                raise SymbolError(f"A股代码必须为sh/sz前缀+6位数字: {symbol}")
            return True
        
        if symbol.lower().startswith('hk'):
            code = symbol[2:]
            if code.lower() in ['hsi', 'hscei', 'hscci']: # 指数代码
                return True
            if not (code.isdigit() and len(code) in [4, 5]):
                raise SymbolError(f"港股代码必须为hk前缀+4/5位数字: {symbol}")
            return True

        if symbol.lower().startswith('us'):
            code = symbol[2:]
            if not re.match(r'^[A-Za-z0-9\.-]+$', code) or not code:
                raise SymbolError(f"美股代码格式错误: {symbol}")
            return True

        # 场景四: 纯数字代码, e.g., '600036'
        if symbol.isdigit():
            if len(symbol) == 6:  # A股
                return True
            if len(symbol) in [4, 5]:  # 港股
                return True
            raise SymbolError(f"纯数字代码长度必须为4, 5或6位: {symbol}")

        # 如果所有规则都不匹配，则格式无效
        raise SymbolError(
            message=f"无法识别的股票代码格式: {symbol}",
            data={"symbol": symbol}
        )