#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通用abupy参数提取器
自动检测abupy安装位置，支持：
1. pip安装的第三方库（site-packages中）
2. 源码目录安装
3. 开发环境中的源码
"""

import re
import os
import sys
import ast
import importlib
import inspect
from collections import defaultdict

class UniversalAbuFactorParamExtractor:
    """通用abupy因子参数提取器（环境自适应）"""
    
    # 系统参数，需要过滤
    SYSTEM_PARAMS = {
        'capital', 'kl_pd', 'combine_kl_pd', 'benchmark', 
        'self', 'kwargs', 'args', 'slippage', 'position',
        'stock_pickers', 'sell_factors'
    }
    
    def __init__(self):
        self.abupy_path = self._detect_abupy_path()
        self.abupy_available = self._check_abupy_import()
    
    def _detect_abupy_path(self):
        """自动检测abupy的安装路径"""
        try:
            import abupy
            abupy_path = os.path.dirname(abupy.__file__)
            print(f"✅ 检测到abupy安装路径: {abupy_path}")
            return abupy_path
        except ImportError:
            # 如果无法导入abupy，尝试在当前目录查找
            current_dir = os.path.abspath('.')
            if os.path.exists(os.path.join(current_dir, 'abupy')):
                abupy_path = os.path.join(current_dir, 'abupy')
                print(f"✅ 在当前目录找到abupy: {abupy_path}")
                return abupy_path
            else:
                print("❌ 无法找到abupy安装路径")
                return None
    
    def _check_abupy_import(self):
        """检查是否可以导入abupy模块"""
        try:
            import abupy
            return True
        except ImportError:
            print("⚠️ 无法导入abupy模块，将使用静态分析")
            return False
    
    def extract_complete_factor_structure(self):
        """提取完整的因子参数结构（环境自适应）"""
        
        if not self.abupy_path:
            print("❌ 无法找到abupy路径，无法提取参数")
            return {}
        
        result = {
            'buy_factors': self._extract_buy_factors(),
            'sell_factors': self._extract_sell_factors(),
            'position_classes': self._extract_position_classes(),
            'stock_pick_classes': self._extract_stock_pick_classes(),
            'umpire_params': self._extract_umpire_params(),
            'extraction_info': {
                'abupy_path': self.abupy_path,
                'abupy_importable': self.abupy_available,
                'extraction_method': 'dynamic_import' if self.abupy_available else 'static_analysis'
            }
        }
        
        return result
    
    def _extract_buy_factors(self):
        """提取买入因子参数（环境自适应）"""
        if self.abupy_available:
            return self._extract_factors_by_import('FactorBuyBu')
        else:
            return self._extract_factors_by_file_analysis('FactorBuyBu')
    
    def _extract_sell_factors(self):
        """提取卖出因子参数（环境自适应）"""
        if self.abupy_available:
            return self._extract_factors_by_import('FactorSellBu')
        else:
            return self._extract_factors_by_file_analysis('FactorSellBu')
    
    def _extract_factors_by_import(self, module_name):
        """通过动态导入提取因子参数"""
        factors = {}
        
        try:
            # 动态导入模块
            module_path = f'abupy.{module_name}'
            module = importlib.import_module(module_path)
            
            # 获取模块中的所有类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if name.startswith('Abu') and not name.endswith('Mixin') and not name.endswith('Base'):
                    try:
                        # 检查类是否有_init_self方法
                        if hasattr(obj, '_init_self'):
                            params = self._extract_params_from_class(obj)
                            if params:
                                factors[name] = {
                                    'module_path': module_path,
                                    'parameters': params
                                }
                    except Exception as e:
                        print(f"⚠️ 提取类 {name} 参数时出错: {e}")
        
        except ImportError as e:
            print(f"⚠️ 无法导入模块 {module_name}: {e}")
        
        return factors
    
    def _extract_factors_by_file_analysis(self, module_name):
        """通过文件分析提取因子参数"""
        factors = {}
        module_path = os.path.join(self.abupy_path, module_name)
        
        if not os.path.exists(module_path):
            return factors
        
        for file in os.listdir(module_path):
            if file.endswith('.py') and not file.startswith('_'):
                file_path = os.path.join(module_path, file)
                file_factors = self._extract_factors_from_file(file_path)
                factors.update(file_factors)
        
        return factors
    
    def _extract_params_from_class(self, cls):
        """从类对象中提取参数"""
        params = {}
        
        try:
            # 获取_init_self方法的源码
            if hasattr(cls, '_init_self'):
                source = inspect.getsource(cls._init_self)
                params = self._parse_init_self_source(source)
        except Exception as e:
            print(f"⚠️ 无法获取类 {cls.__name__} 的源码: {e}")
        
        return self._filter_user_params(params)
    
    def _extract_position_classes(self):
        """提取仓位管理类参数（基于验证结果）"""
        position_classes = {
            'AbuAtrPosition': {
                'location': 'abupy.BetaBu.ABuAtrPosition',
                'parameters': {
                    'atr_base_price': {
                        'type': 'number',
                        'required': False,
                        'default': 15,
                        'description': '常数价格设定'
                    },
                    'atr_pos_base': {
                        'type': 'number', 
                        'required': False,
                        'default': 0.1,
                        'description': '仓位基础配比'
                    },
                    'std_atr_threshold': {
                        'type': 'number',
                        'required': False, 
                        'default': 0.5,
                        'description': 'ATR阈值'
                    }
                }
            },
            'AbuKellyPosition': {
                'location': 'abupy.BetaBu.ABuKellyPosition',
                'parameters': {
                    'win_rate': {
                        'type': 'number',
                        'required': False,
                        'default': 0.50,
                        'description': 'Kelly仓位胜率'
                    },
                    'gains_mean': {
                        'type': 'number',
                        'required': False,
                        'default': 0.10,
                        'description': '平均获利期望'
                    },
                    'losses_mean': {
                        'type': 'number',
                        'required': False,
                        'default': 0.05,
                        'description': '平均亏损期望'
                    }
                }
            },
            'AbuPositionBase': {
                'location': 'abupy.BetaBu.ABuPositionBase',
                'parameters': {
                    'pos_max': {
                        'type': 'number',
                        'required': False,
                        'default': 1.0,
                        'description': '最大仓位限制'
                    }
                }
            }
        }
        
        # 如果可以导入abupy，尝试动态验证参数
        if self.abupy_available:
            position_classes = self._verify_position_classes(position_classes)
        
        return position_classes
    
    def _verify_position_classes(self, position_classes):
        """动态验证仓位管理类参数"""
        for class_name, class_info in position_classes.items():
            try:
                module_path, class_name_only = class_info['location'].rsplit('.', 1)
                module = importlib.import_module(module_path)
                cls = getattr(module, class_name_only)
                
                # 动态提取参数
                dynamic_params = self._extract_params_from_class(cls)
                if dynamic_params:
                    # 合并静态定义和动态提取的参数
                    for param_name, param_info in dynamic_params.items():
                        if param_name not in class_info['parameters']:
                            class_info['parameters'][param_name] = param_info
                
            except Exception as e:
                print(f"⚠️ 验证类 {class_name} 时出错: {e}")
        
        return position_classes
    
    def _extract_stock_pick_classes(self):
        """提取选股因子参数（基于验证结果）"""
        pick_classes = {
            'AbuPickRegressAngMinMax': {
                'location': 'abupy.PickStockBu.ABuPickRegressAngMinMax',
                'parameters': {
                    'threshold_ang_min': {
                        'type': 'float',
                        'required': False,
                        'default': 0.0,
                        'description': '最小角度阈值'
                    },
                    'threshold_ang_max': {
                        'type': 'float',
                        'required': False,
                        'default': 90.0,
                        'description': '最大角度阈值'
                    }
                }
            },
            'AbuPickStockBase': {
                'location': 'abupy.PickStockBu.ABuPickStockBase',
                'parameters': {
                    'reversed': {
                        'type': 'bool',
                        'required': False,
                        'default': False,
                        'description': '反向选择'
                    }
                }
            }
        }
        
        return pick_classes
    
    def _extract_umpire_params(self):
        """提取UmpBu裁判参数（环境自适应）"""
        umpire_params = {
            'global_switches': {
                'description': 'UmpBu全局开关参数',
                'location': 'abupy.CoreBu.ABuEnv',
                'parameters': [
                    'g_enable_ump_main_deg_block',
                    'g_enable_ump_main_jump_block', 
                    'g_enable_ump_main_price_block',
                    'g_enable_ump_main_wave_block',
                    'g_enable_ump_edge_deg_block',
                    'g_enable_ump_edge_full_block',
                    'g_enable_ump_edge_price_block',
                    'g_enable_ump_edge_wave_block'
                ]
            },
            'ml_features': {
                'description': 'UmpBu ML特征参数（动态生成）',
                'location': 'abupy.TradeBu.ABuMLFeature',
                'parameters': {
                    'deg_features': [
                        'buy_deg_ang21', 'buy_deg_ang42', 'buy_deg_ang60', 'buy_deg_ang252'
                    ],
                    'price_rank_features': [
                        'buy_price_rank60', 'buy_price_rank90', 'buy_price_rank120', 'buy_price_rank252'
                    ],
                    'wave_features': [
                        'buy_wave_score1', 'buy_wave_score2', 'buy_wave_score3'
                    ],
                    'atr_features': [
                        'buy_atr_std'
                    ],
                    'jump_features': [
                        'buy_jump_down_power', 'buy_diff_down_days', 'buy_jump_up_power', 'buy_diff_up_days'
                    ]
                }
            },
            'market_names': ['us', 'cn', 'hk'],
            'umpire_types': [
                'main_deg', 'main_jump', 'main_price', 'main_wave',
                'edge_deg', 'edge_full', 'edge_price', 'edge_wave'
            ]
        }
        
        # 如果可以导入abupy，尝试动态验证UmpBu参数
        if self.abupy_available:
            umpire_params = self._verify_umpire_params(umpire_params)
        
        return umpire_params
    
    def _verify_umpire_params(self, umpire_params):
        """动态验证UmpBu参数"""
        try:
            # 验证全局开关
            import abupy.CoreBu.ABuEnv as env
            verified_switches = []
            for switch in umpire_params['global_switches']['parameters']:
                if hasattr(env, switch):
                    verified_switches.append(switch)
            umpire_params['global_switches']['parameters'] = verified_switches
            
            # 验证ML特征（这些是动态生成的，基于预定义的周期）
            try:
                import abupy.TradeBu.ABuMLFeature as ml_feature
                # 可以进一步验证特征生成逻辑
            except ImportError:
                pass
                
        except Exception as e:
            print(f"⚠️ 验证UmpBu参数时出错: {e}")
        
        return umpire_params

    def _extract_factors_from_file(self, file_path):
        """从文件中提取因子类及其参数（静态分析）"""
        factors = {}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取所有类定义
            class_pattern = r'^class\s+(\w+)\s*\([^)]*\):'
            class_matches = re.finditer(class_pattern, content, re.MULTILINE)

            for match in class_matches:
                class_name = match.group(1)

                # 只分析Abu开头的类，排除Mixin和Base类
                if not class_name.startswith('Abu') or class_name.endswith('Mixin'):
                    continue

                # 提取该类的参数
                class_params = self._extract_class_params_from_content(content, class_name)
                if class_params:
                    factors[class_name] = {
                        'file_path': file_path,
                        'parameters': class_params
                    }

        except Exception as e:
            print(f"⚠️ 处理文件 {file_path} 时出错: {e}")

        return factors

    def _extract_class_params_from_content(self, content, class_name):
        """从文件内容中提取单个类的参数"""

        # 提取_init_self方法
        init_self_pattern = rf'class\s+{class_name}.*?def\s+_init_self\s*\([^)]*\):(.*?)(?=def\s+|\Z)'
        init_self_match = re.search(init_self_pattern, content, re.DOTALL)

        if not init_self_match:
            return {}

        init_self_body = init_self_match.group(1)
        return self._parse_init_self_source(init_self_body)

    def _parse_init_self_source(self, source_code):
        """解析_init_self方法源码"""

        params = {}
        lines = source_code.split('\n')

        # 收集条件参数
        conditionals = set()
        for line in lines:
            conditional_match = re.search(r"if\s+['\"](.*?)['\"]\s+in\s+kwargs:", line.strip())
            if conditional_match:
                conditionals.add(conditional_match.group(1))

        # 解析参数定义
        for line in lines:
            line = line.strip()

            if not line or line.startswith('#') or line.startswith('"""'):
                continue

            # kwargs['param'] - 必需参数
            required_match = re.search(r"self\.(\w+)\s*=\s*kwargs\[['\"](.*?)['\"]\]", line)
            if required_match and required_match.group(2) not in conditionals:
                param_name = required_match.group(2)
                params[param_name] = {
                    'type': self._infer_type_from_name(param_name),
                    'required': True,
                    'default': None
                }
                continue

            # kwargs.pop('param', default) - 可选参数
            pop_match = re.search(r"kwargs\.pop\(['\"](.*?)['\"]\s*,\s*(.*?)\)", line)
            if pop_match:
                param_name, default_value = pop_match.groups()

                default_val = self._parse_default_value(default_value)

                params[param_name] = {
                    'type': self._infer_type_from_default(default_value),
                    'required': False,
                    'default': default_val
                }
                continue

        # 处理条件参数
        for param_name in conditionals:
            if param_name not in params:
                params[param_name] = {
                    'type': self._infer_type_from_name(param_name),
                    'required': False,
                    'default': None
                }

        return self._filter_user_params(params)

    def _infer_type_from_name(self, param_name):
        """根据参数名推断类型"""
        param_name = param_name.lower()

        if param_name == 'xd' or 'period' in param_name or param_name in ['fast', 'slow']:
            return 'int'
        elif param_name.endswith('_n') or 'threshold' in param_name or 'rate' in param_name:
            return 'float'
        elif 'is_' in param_name or param_name.startswith('is_'):
            return 'bool'
        else:
            return 'str'

    def _infer_type_from_default(self, default_value):
        """根据默认值推断类型"""
        default_value = default_value.strip()

        try:
            parsed = ast.literal_eval(default_value)
            return type(parsed).__name__
        except:
            if default_value.isdigit():
                return 'int'
            elif '.' in default_value and default_value.replace('.', '').isdigit():
                return 'float'
            elif default_value.lower() in ['true', 'false']:
                return 'bool'
            else:
                return 'str'

    def _parse_default_value(self, default_value):
        """解析默认值"""
        if not default_value:
            return None

        default_value = default_value.strip()

        try:
            return ast.literal_eval(default_value)
        except:
            if default_value.lower() == 'true':
                return True
            elif default_value.lower() == 'false':
                return False
            elif default_value.lower() == 'none':
                return None
            else:
                return default_value

    def _filter_user_params(self, params):
        """过滤出用户可配置参数"""
        return {k: v for k, v in params.items() if k not in self.SYSTEM_PARAMS}

    def generate_api_schema(self, factor_structure):
        """生成API数据契约的JSON Schema（环境自适应）"""

        # 收集所有买入因子参数
        buy_factor_params = {}
        for factor_name, factor_info in factor_structure['buy_factors'].items():
            for param_name, param_info in factor_info['parameters'].items():
                if param_name not in buy_factor_params:
                    buy_factor_params[param_name] = {
                        "type": param_info['type'],
                        "description": f"Parameter for {factor_name}"
                    }
                    if param_info.get('default') is not None:
                        buy_factor_params[param_name]["default"] = param_info['default']

        # 收集所有卖出因子参数
        sell_factor_params = {}
        for factor_name, factor_info in factor_structure['sell_factors'].items():
            for param_name, param_info in factor_info['parameters'].items():
                if param_name not in sell_factor_params:
                    sell_factor_params[param_name] = {
                        "type": param_info['type'],
                        "description": f"Parameter for {factor_name}"
                    }
                    if param_info.get('default') is not None:
                        sell_factor_params[param_name]["default"] = param_info['default']

        # 构建仓位管理参数
        position_params = {}
        for pos_class, pos_info in factor_structure['position_classes'].items():
            for param_name, param_info in pos_info['parameters'].items():
                position_params[param_name] = {
                    "type": param_info['type'],
                    "description": param_info.get('description', f'Parameter for {pos_class}')
                }
                if param_info.get('default') is not None:
                    position_params[param_name]["default"] = param_info['default']

        # 构建完整的API Schema
        schema = {
            "StrategyCreate": {
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "策略名称"},
                    "description": {"type": "string", "description": "策略描述"},
                    "read_cash": {"type": "number", "description": "初始资金"},
                    "buy_factors": {
                        "type": "array",
                        "description": "买入因子配置",
                        "items": {
                            "type": "object",
                            "properties": {
                                "class_name": {
                                    "type": "string",
                                    "enum": list(factor_structure['buy_factors'].keys()),
                                    "description": "买入因子类名"
                                },
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        **buy_factor_params,
                                        "position": {
                                            "type": "object",
                                            "description": "仓位管理配置",
                                            "properties": {
                                                "class": {
                                                    "type": "string",
                                                    "enum": list(factor_structure['position_classes'].keys()),
                                                    "description": "仓位管理类名"
                                                },
                                                **position_params
                                            }
                                        }
                                    }
                                }
                            },
                            "required": ["class_name"]
                        }
                    },
                    "sell_factors": {
                        "type": "array",
                        "description": "卖出因子配置",
                        "items": {
                            "type": "object",
                            "properties": {
                                "class_name": {
                                    "type": "string",
                                    "enum": list(factor_structure['sell_factors'].keys()),
                                    "description": "卖出因子类名"
                                },
                                "parameters": {
                                    "type": "object",
                                    "properties": sell_factor_params
                                }
                            },
                            "required": ["class_name"]
                        }
                    },
                    "umpire_rules": {
                        "type": "array",
                        "description": "UmpBu裁判规则",
                        "items": {
                            "type": "string",
                            "enum": factor_structure['umpire_params']['global_switches']['parameters']
                        }
                    },
                    "umpire_market_name": {
                        "type": "string",
                        "enum": factor_structure['umpire_params']['market_names'],
                        "description": "裁判市场名称"
                    },
                    "ml_feature_dict": {
                        "type": "object",
                        "description": "机器学习特征字典",
                        "properties": self._generate_ml_feature_schema(factor_structure['umpire_params']['ml_features'])
                    }
                },
                "required": ["name", "read_cash"]
            }
        }

        return schema

    def _generate_ml_feature_schema(self, ml_features):
        """生成ML特征的Schema"""
        ml_schema = {}

        for feature_type, features in ml_features['parameters'].items():
            for feature in features:
                ml_schema[feature] = {
                    "type": "number",
                    "description": f"ML feature: {feature}"
                }

        return ml_schema

def main():
    """主函数"""
    extractor = UniversalAbuFactorParamExtractor()

    print("=== 通用abupy参数提取器（环境自适应）===\n")

    if not extractor.abupy_path:
        print("❌ 无法找到abupy安装路径，请确保abupy已正确安装")
        return

    # 提取完整的因子结构
    factor_structure = extractor.extract_complete_factor_structure()

    if not factor_structure:
        print("❌ 无法提取因子结构")
        return

    # 统计参数数量
    buy_factors_count = len(factor_structure['buy_factors'])
    sell_factors_count = len(factor_structure['sell_factors'])
    position_classes_count = len(factor_structure['position_classes'])
    stock_pick_count = len(factor_structure['stock_pick_classes'])

    umpire_switches = len(factor_structure['umpire_params']['global_switches']['parameters'])
    ml_features = sum(len(features) for features in factor_structure['umpire_params']['ml_features']['parameters'].values())

    # 打印统计信息
    print("📊 提取结果统计:")
    print(f"- abupy路径: {factor_structure['extraction_info']['abupy_path']}")
    print(f"- 提取方法: {factor_structure['extraction_info']['extraction_method']}")
    print(f"- 买入因子: {buy_factors_count}个")
    print(f"- 卖出因子: {sell_factors_count}个")
    print(f"- 仓位管理类: {position_classes_count}个")
    print(f"- 选股因子: {stock_pick_count}个")
    print(f"- UmpBu全局开关: {umpire_switches}个")
    print(f"- UmpBu ML特征: {ml_features}个")

    # 生成API Schema
    api_schema = extractor.generate_api_schema(factor_structure)

    # 保存结果
    import json
    with open('universal_factor_structure.json', 'w', encoding='utf-8') as f:
        json.dump(factor_structure, f, indent=2, ensure_ascii=False, default=str)

    with open('universal_api_schema.json', 'w', encoding='utf-8') as f:
        json.dump(api_schema, f, indent=2, ensure_ascii=False)

    print(f"\n✅ 结果已保存:")
    print(f"- universal_factor_structure.json: 通用因子参数结构")
    print(f"- universal_api_schema.json: 通用API数据契约JSON Schema")
    print(f"\n🎯 环境适应性:")
    print(f"- ✅ 支持pip安装的abupy（site-packages）")
    print(f"- ✅ 支持源码目录安装")
    print(f"- ✅ 自动检测安装路径")
    print(f"- ✅ 动态导入 + 静态分析双重保障")

if __name__ == '__main__':
    main()
