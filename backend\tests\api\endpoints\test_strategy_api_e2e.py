"""
策略管理API端点端到端集成测试

这个文件包含策略API的端到端集成测试，不再使用mock，而是通过API进行实际的数据准备与验证。
遵循"黑盒"测试原则，只关注API的外部行为和响应，不关心内部实现细节。
"""

import pytest
from fastapi.testclient import TestClient
import uuid
from typing import Dict, List, Any
import pandas as pd
import os
from sqlalchemy.orm import Session

from backend.main import app
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor, StrategyCreate

# -------------------------------------------------------
# 测试辅助函数
# -------------------------------------------------------

def generate_unique_name() -> str:
    """生成唯一的测试用名称，确保测试之间不会相互干扰"""
    return f"test_strategy_{uuid.uuid4().hex[:8]}"

def create_test_strategy_data() -> Dict[str, Any]:
    """创建用于测试的策略数据"""
    return {
        "name": generate_unique_name(),
        "description": "这是一个通过API测试创建的测试策略",
        "is_public": True,
        "buy_factors": [
            {
                "name": "测试买入因子",
                "description": "测试用买入因子",
                "factor_class": "FactorBuyBreak",
                "parameters": {"xd": 20}
            }
        ],
        "sell_factors": [
            {
                "name": "测试卖出因子",
                "description": "测试用卖出因子",
                "factor_class": "FactorSellNDay",
                "parameters": {"sell_n": 5}
            }
        ],
        "parameters": {"initial_capital": 100000},
        "tags": ["测试", "集成测试"]
    }

def create_test_market_data() -> Dict[str, Any]:
    """创建用于执行策略的市场数据"""
    return {
        "market": "CN",
        "data_source": "tushare",  # 必须使用有效的数据源（tushare或local）
        "choice_symbols": ["000001.SZ", "600000.SH"],
        "start_date": "2023-01-01",
        "end_date": "2023-03-31", 
        "benchmark_symbol": "000300.SH",
        "capital": 100000
    }

# -------------------------------------------------------
# 测试夹具 (Fixtures)
# -------------------------------------------------------

@pytest.fixture
def client() -> TestClient:
    """返回一个可用于测试的客户端"""
    return TestClient(app)

@pytest.fixture
def created_strategy(client: TestClient, session: Session) -> Dict[str, Any]:
    """创建一个测试策略并在测试后删除，确保测试环境的干净"""
    # 1. 创建测试策略
    strategy_data = create_test_strategy_data()
    response = client.post("/api/v1/strategy/", json=strategy_data)
    assert response.status_code == 201
    created = response.json()["data"]
    
    # 2. 返回创建的策略，供测试使用
    yield created
    
    # 3. 测试完成后清理 - 删除策略
    # 注意：即使删除失败，对其他测试影响很小，因为使用了唯一名称
    client.delete(f"/api/v1/strategy/{created['id']}")

# -------------------------------------------------------
# 端到端集成测试 
# -------------------------------------------------------

def test_create_and_get_strategy(client: TestClient, session: Session):
    """测试创建策略和获取策略 - 端到端集成测试"""
    # 1. Arrange: 准备测试数据
    strategy_data = create_test_strategy_data()
    
    # 2. Act: 创建策略
    create_response = client.post("/api/v1/strategy/", json=strategy_data)
    
    # 3. Assert: 验证创建结果
    assert create_response.status_code == 201
    created_data = create_response.json()["data"]
    assert created_data["name"] == strategy_data["name"]
    assert created_data["description"] == strategy_data["description"]
    assert len(created_data["buy_factors"]) == 1
    assert len(created_data["sell_factors"]) == 1
    
    # 4. Act: 获取创建的策略
    strategy_id = created_data["id"]
    get_response = client.get(f"/api/v1/strategy/{strategy_id}")
    
    # 5. Assert: 验证获取的策略与创建的一致
    assert get_response.status_code == 200
    get_data = get_response.json()["data"]
    assert get_data["id"] == strategy_id
    assert get_data["name"] == strategy_data["name"]
    
    # 6. 清理：删除测试创建的策略
    delete_response = client.delete(f"/api/v1/strategy/{strategy_id}")
    assert delete_response.status_code == 200
    
    # 7. 最终验证：确认策略已被删除
    get_deleted_response = client.get(f"/api/v1/strategy/{strategy_id}")
    assert get_deleted_response.status_code == 404

def test_get_strategies_list(client: TestClient, created_strategy: Dict[str, Any], session: Session):
    """测试获取策略列表 - 端到端集成测试"""
    # 1. Arrange: 已通过fixture创建了测试策略
    
    # 2. Act: 获取策略列表
    response = client.get("/api/v1/strategy/")
    
    # 3. Assert: 验证返回结果
    assert response.status_code == 200
    data = response.json()
    
    # 验证数据结构包含分页信息
    assert "data" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data
    
    # 验证创建的测试策略出现在列表中
    strategy_ids = [s["id"] for s in data["data"]]
    assert created_strategy["id"] in strategy_ids

def test_update_strategy(client: TestClient, created_strategy: Dict[str, Any], session: Session):
    """测试更新策略 - 端到端集成测试"""
    # 1. Arrange: 准备更新数据
    strategy_id = created_strategy["id"]
    update_data = {
        "name": f"更新后的策略名称-{uuid.uuid4().hex[:6]}",
        "description": "这是更新后的描述",
        "is_public": False
    }
    
    # 2. Act: 更新策略
    update_response = client.put(f"/api/v1/strategy/{strategy_id}", json=update_data)
    
    # 3. Assert: 验证更新结果
    assert update_response.status_code == 200
    updated_data = update_response.json()["data"]
    
    # 验证更新的字段已被修改
    assert updated_data["name"] == update_data["name"]
    assert updated_data["description"] == update_data["description"]
    assert updated_data["is_public"] == update_data["is_public"]
    
    # 验证其他字段保持不变
    assert updated_data["id"] == strategy_id
    assert len(updated_data["buy_factors"]) == len(created_strategy["buy_factors"])

def test_delete_strategy(client: TestClient, session: Session):
    """测试删除策略 - 端到端集成测试"""
    # 1. Arrange: 创建待删除的测试策略
    strategy_data = create_test_strategy_data()
    create_response = client.post("/api/v1/strategy/", json=strategy_data)
    strategy_id = create_response.json()["data"]["id"]
    
    # 2. Act: 删除策略
    delete_response = client.delete(f"/api/v1/strategy/{strategy_id}")
    
    # 3. Assert: 验证删除结果
    assert delete_response.status_code == 200
    delete_data = delete_response.json()
    assert delete_data["success"] is True
    assert "已成功删除" in delete_data["message"]
    
    # 4. 验证: 确认策略已被删除
    get_response = client.get(f"/api/v1/strategy/{strategy_id}")
    assert get_response.status_code == 404

def test_get_strategy_not_found(client: TestClient, session: Session):
    """测试获取不存在的策略 - 端到端集成测试"""
    # 1. Arrange: 生成一个几乎确定不存在的ID
    non_existent_id = f"non-existent-{uuid.uuid4()}"
    
    # 2. Act: 尝试获取不存在的策略
    response = client.get(f"/api/v1/strategy/{non_existent_id}")
    
    # 3. Assert: 验证返回404
    assert response.status_code == 404
    data = response.json()
    assert data["success"] is False
    assert "未找到" in data["message"]

def test_get_available_factors(client: TestClient, session: Session):
    """测试获取可用因子列表 - 端到端集成测试"""
    # 1. Act: 请求因子列表
    response = client.get("/api/v1/strategy/factors/")
    
    # 2. Assert: 验证返回结果
    assert response.status_code == 200
    data = response.json()
    
    # 验证数据结构
    assert "buy_factors" in data
    assert "sell_factors" in data
    
    # 验证因子列表非空
    assert len(data["buy_factors"]) > 0
    assert len(data["sell_factors"]) > 0
    
    # 验证因子数据结构
    if data["buy_factors"]:
        factor = data["buy_factors"][0]
        assert "name" in factor
        assert "factor_class" in factor
        assert "parameters" in factor

@pytest.mark.skipif(os.environ.get("CI") == "true", reason="策略执行测试在CI环境中跳过")
def test_execute_strategy(client: TestClient, created_strategy: Dict[str, Any], session: Session):
    """测试策略执行 - 端到端集成测试"""
    # 1. Arrange: 准备市场数据
    market_data = create_test_market_data()
    strategy_id = created_strategy["id"]
    
    # 2. Act: 执行策略
    response = client.post(f"/api/v1/strategy/{strategy_id}/execute", json=market_data)
    
    # 3. Assert: 验证执行结果
    assert response.status_code == 200, f"执行策略失败，响应: {response.text}"
    data = response.json()
    
    # 验证数据结构
    assert data["success"] is True
    assert "data" in data
    execution_result = data["data"]
    
    # 验证执行结果结构
    assert "status" in execution_result
    assert execution_result["status"] == "success"
    assert "execution_summary" in execution_result
    assert "results" in execution_result
    
    # 验证执行摘要
    summary = execution_result["execution_summary"]
    assert "initial_capital" in summary
    assert "final_capital" in summary
    assert "total_trades" in summary
