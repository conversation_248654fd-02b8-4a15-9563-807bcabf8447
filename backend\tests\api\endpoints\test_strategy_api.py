# backend/tests/api/endpoints/test_strategy_api.py (最终修正版)

import pytest
from unittest.mock import MagicMock
from fastapi.testclient import TestClient
from typing import Generator

# 导入应用实例和依赖项
from backend.main import app
from backend.app.api.endpoints.strategy import get_strategy_service, get_factor_service
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor, StrategyCreate
from backend.app.services.strategy_service import StrategyService
from backend.app.services.factor_service import FactorService

@pytest.fixture
def mock_strategy_service_fixture() -> MagicMock:
    """只创建一个 StrategyService 的模拟对象，不进行注入。"""
    return MagicMock(spec=StrategyService)

@pytest.fixture
def mock_factor_service_fixture() -> MagicMock:
    """只创建一个 FactorService 的模拟对象，不进行注入。"""
    mock_service = MagicMock(spec=FactorService)
    # 为因子服务设置一个默认的返回值，服务层现在返回一个对象列表
    mock_service.get_available_factors.return_value = [
        BuyFactor(name="Mock Buy Factor", class_name="FactorBuyBreak", factor_type="buy"),
        SellFactor(name="Mock Sell Factor", class_name="FactorSellBreak", factor_type="sell")
    ]
    return mock_service

@pytest.fixture
def client_with_mocks(
    mock_strategy_service_fixture: MagicMock, 
    mock_factor_service_fixture: MagicMock
) -> Generator[TestClient, None, None]:
    """
    创建一个 FastAPI TestClient，并将 service mocks 注入。
    这是最推荐的测试依赖注入方式。
    """
    app.dependency_overrides[get_strategy_service] = lambda: mock_strategy_service_fixture
    app.dependency_overrides[get_factor_service] = lambda: mock_factor_service_fixture
    
    with TestClient(app) as client:
        yield client
        
    # 测试结束后清理覆盖
    app.dependency_overrides.clear()

# --- 测试用例 ---

def test_get_strategies(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试获取策略列表API"""
    mock_db_data = [
        Strategy(id="s1", name="Strategy A", description="", buy_factors=[], sell_factors=[], parameters={}, is_public=True, tags=[], user_id="test_user"),
        Strategy(id="s2", name="Strategy B", description="", buy_factors=[], sell_factors=[], parameters={}, is_public=True, tags=[], user_id="test_user")
    ]
    mock_strategy_service_fixture.get_all_strategies_paginated.return_value = (mock_db_data, 2)

    response = client_with_mocks.get("/api/v1/strategy/?skip=0&limit=10")
    
    assert response.status_code == 200
    res_json = response.json()
    assert res_json["success"] is True
    assert res_json["total"] == 2
    assert len(res_json["data"]) == 2
    # 确保断言与端点传递给服务的所有参数匹配
    mock_strategy_service_fixture.get_all_strategies_paginated.assert_called_once_with(
        skip=0, limit=10, owner=None, is_public=None
    )

def test_get_strategy_not_found(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试获取不存在的策略"""
    from backend.app.core.exceptions import DataNotFoundError
    from backend.app.constants.messages import StrategyMessages
    mock_strategy_service_fixture.get_strategy_by_id.side_effect = DataNotFoundError(message=StrategyMessages.strategy_not_found("non-existent-id"), error_code="STRATEGY_NOT_FOUND")
    response = client_with_mocks.get("/api/v1/strategy/non-existent-id")
    assert response.status_code == 404

def test_get_strategy(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试获取单个策略"""
    test_id = "test-id"
    mock_strategy = Strategy(id=test_id, name="Found Strategy", description="", buy_factors=[], sell_factors=[], parameters={})
    mock_strategy_service_fixture.get_strategy_by_id.return_value = mock_strategy
    
    response = client_with_mocks.get(f"/api/v1/strategy/{test_id}")
    assert response.status_code == 200
    assert response.json()["data"]["id"] == test_id

def test_create_strategy(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试创建策略"""
    request_data = {
        "name": "New Strategy", 
        "description": "", 
        "buy_factors": [{
            "name": "f1", 
            "class_name": "FactorBuyBreak", 
            "factor_type": "buy",
            "parameters": {}
        }], 
        "sell_factors": [], 
        "parameters": {}
    }
    # 模拟服务层返回一个完整的Strategy对象
    # 注意：**request_data不能直接用于构造Strategy，因为有额外的字段
    created_strategy = Strategy(
        id="new-id-from-mock", 
        name=request_data['name'],
        description=request_data['description'],
        buy_factors=[BuyFactor(**f) for f in request_data['buy_factors']],
        sell_factors=[],
        parameters=request_data['parameters'],
        is_public=True, 
        tags=[], 
        user_id="test_user"
    ) 
    mock_strategy_service_fixture.create_strategy.return_value = created_strategy

    response = client_with_mocks.post("/api/v1/strategy/", json=request_data)
    
    assert response.status_code == 201
    mock_strategy_service_fixture.create_strategy.assert_called_once()
    assert response.json()["data"]["id"] == "new-id-from-mock"

def test_update_strategy(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试更新策略"""
    test_id = "test-id"
    update_data = {"name": "Updated Name"}
    # 模拟服务层返回更新后的完整Strategy对象
    updated_strategy = Strategy(
        id=test_id, 
        name="Updated Name", 
        description="", 
        buy_factors=[], 
        sell_factors=[], 
        parameters={},
        is_public=True,
        tags=[],
        user_id="test_user"
    )
    mock_strategy_service_fixture.update_strategy.return_value = updated_strategy

    response = client_with_mocks.put(f"/api/v1/strategy/{test_id}", json=update_data)
    
    assert response.status_code == 200
    mock_strategy_service_fixture.update_strategy.assert_called_once()
    assert response.json()["data"]["name"] == "Updated Name"

def test_update_strategy_not_found(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试更新不存在的策略"""
    from backend.app.core.exceptions import DataNotFoundError
    from backend.app.constants.messages import StrategyMessages
    mock_strategy_service_fixture.update_strategy.side_effect = DataNotFoundError(message=StrategyMessages.strategy_not_found("non-existent-id"), error_code="STRATEGY_NOT_FOUND")
    response = client_with_mocks.put("/api/v1/strategy/non-existent-id", json={"name": "any"})
    assert response.status_code == 404

def test_delete_strategy(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试删除策略"""
    test_id = "test-id"
    mock_strategy_service_fixture.delete_strategy.return_value = True
    response = client_with_mocks.delete(f"/api/v1/strategy/{test_id}")
    assert response.status_code == 200
    assert "成功删除" in response.json()["message"]

def test_delete_strategy_not_found(client_with_mocks: TestClient, mock_strategy_service_fixture: MagicMock):
    """测试删除不存在的策略"""
    from backend.app.core.exceptions import DataNotFoundError
    from backend.app.constants.messages import StrategyMessages
    mock_strategy_service_fixture.delete_strategy.side_effect = DataNotFoundError(message=StrategyMessages.strategy_not_found("non-existent-id"), error_code="STRATEGY_NOT_FOUND")
    response = client_with_mocks.delete("/api/v1/strategy/non-existent-id")
    assert response.status_code == 404

def test_get_available_factors(client_with_mocks: TestClient, mock_factor_service_fixture: MagicMock):
    """测试获取可用因子列表API"""
    response = client_with_mocks.get("/api/v1/strategy/factors/")

    assert response.status_code == 200
    mock_factor_service_fixture.get_available_factors.assert_called_once_with(factor_type=None)

    res_json = response.json()
    
    # 直接验证返回的字典结构
    assert "buy_factors" in res_json
    assert "sell_factors" in res_json
    assert len(res_json["buy_factors"]) == 1
    assert len(res_json["sell_factors"]) == 1
    assert res_json["buy_factors"][0]["name"] == "Mock Buy Factor"
    assert res_json["sell_factors"][0]["name"] == "Mock Sell Factor"