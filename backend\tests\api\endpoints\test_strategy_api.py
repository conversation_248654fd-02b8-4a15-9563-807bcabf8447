"""
策略管理API端点集成测试
"""

import json
from unittest.mock import patch, MagicMock, ANY
import pytest
from fastapi.testclient import TestClient

from backend.main import app
from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor, StrategyCreate
from backend.app.services.strategy_service import StrategyService
from backend.app.services.factor_service import FactorService


# 创建测试客户端
client = TestClient(app)


@pytest.fixture
def mock_strategy_service():
    """模拟策略服务"""
    mock_service_instance = MagicMock(spec=StrategyService)
    # 定义所有需要的方法
    mock_service_instance.get_strategies = MagicMock()
    mock_service_instance.get_all_strategies_paginated = MagicMock()
    mock_service_instance.create_strategy = MagicMock()
    mock_service_instance.get_strategy_by_id = MagicMock()
    mock_service_instance.update_strategy = MagicMock()
    mock_service_instance.delete_strategy = MagicMock()
    mock_service_instance.execute_strategy = MagicMock()
    with patch("app.api.endpoints.strategy.get_strategy_service", return_value=mock_service_instance) as mock_getter:
        yield mock_service_instance


@pytest.fixture
def mock_factor_service():
    """模拟因子服务"""
    mock_service_instance = MagicMock(spec=FactorService)
    mock_service_instance.get_available_factors = MagicMock()
    with patch("app.api.endpoints.strategy.get_factor_service", return_value=mock_service_instance) as mock_getter:
        yield mock_service_instance


@pytest.fixture
def real_strategy_service():
    """使用真实的策略服务而不是mock"""
    # 清空现有数据
    from backend.app.services.strategy_service import StrategyService
    StrategyService._strategies.clear()
    service = StrategyService()
    with patch("app.api.endpoints.strategy.get_strategy_service", return_value=service):
        yield service


@pytest.fixture
def real_factor_service():
    """使用真实的因子服务而不是mock"""
    from backend.app.services.factor_service import FactorService
    service = FactorService()
    with patch("app.api.endpoints.strategy.get_factor_service", return_value=service):
        yield service


@pytest.fixture
def sample_strategy():
    """创建示例策略"""
    return Strategy(
        id="test-strategy-id",
        name="测试策略",
        description="这是一个用于测试的策略",
        is_public=True,
        buy_factors=[
            BuyFactor(
                id="buy-factor-1",
                name="测试买入因子",
                description="测试用的买入因子",
                factor_class="AbuFactorBuyBreak",
                parameters={"xd": 20},
                factor_type="buy"
            )
        ],
        sell_factors=[
            SellFactor(
                id="sell-factor-1",
                name="测试卖出因子",
                description="测试用的卖出因子",
                factor_class="AbuFactorSellBreak",
                parameters={"xd": 15},
                factor_type="sell"
            )
        ],
        parameters={"init_cash": 100000},
        tags=["测试", "示例"]
    )


def test_get_strategies(real_strategy_service, sample_strategy):
    """测试获取策略列表API"""
    # 先创建一个策略
    from backend.app.schemas.strategy import StrategyCreate, BuyFactor, SellFactor
    created = real_strategy_service.create_strategy(
        StrategyCreate(
            name=sample_strategy.name,
            description=sample_strategy.description,
            is_public=sample_strategy.is_public,
            buy_factors=sample_strategy.buy_factors,
            sell_factors=sample_strategy.sell_factors,
            parameters=sample_strategy.parameters,
            tags=sample_strategy.tags
        )
    )
    
    # 发送GET请求
    response = client.get("/api/v1/strategy/")
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]) == 1
    assert data["data"][0]["name"] == sample_strategy.name


def test_get_strategy_not_found():
    """测试获取不存在的策略API"""
    # 发送GET请求访问不存在的ID
    response = client.get("/api/v1/strategy/non-existent-id")
    
    # 验证响应 - 应该是 404
    assert response.status_code == 404
    data = response.json()
    # 检查自定义错误响应格式
    assert data["success"] is False
    assert "未找到" in data["message"]


def test_get_strategy(real_strategy_service):
    """测试获取单个策略API"""
    # 先创建一个策略
    from backend.app.schemas.strategy import StrategyCreate, BuyFactor, SellFactor
    strategy_data = StrategyCreate(
        name="测试策略",
        description="这是一个测试策略",
        buy_factors=[
            BuyFactor(
                name="测试买入因子",
                description="测试用的买入因子",
                factor_class="AbuFactorBuyBreak",
                factor_type="buy",
                parameters={"xd": 20}
            )
        ],
        parameters={"initial_capital": 100000}
    )
    created = real_strategy_service.create_strategy(strategy_data)
    
    # 发送GET请求
    response = client.get(f"/api/v1/strategy/{created.id}")
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == created.id
    assert data["data"]["name"] == created.name


def test_get_strategy_success(real_strategy_service, sample_strategy):
    """测试成功获取单个策略API"""
    # 先创建策略
    created = real_strategy_service.create_strategy(
        StrategyCreate(
            name=sample_strategy.name,
            description=sample_strategy.description,
            is_public=sample_strategy.is_public,
            buy_factors=sample_strategy.buy_factors,
            sell_factors=sample_strategy.sell_factors,
            parameters=sample_strategy.parameters,
            tags=sample_strategy.tags
        )
    )
    
    # 发送GET请求
    response = client.get(f"/api/v1/strategy/{created.id}")
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == created.id
    assert data["data"]["name"] == created.name


def test_create_strategy():
    """测试创建策略API"""
    # 准备请求数据
    strategy_data = {
        "name": "新测试策略",
        "description": "这是一个新的测试策略",
        "is_public": True,
        "buy_factors": [
            {
                "name": "测试买入因子",
                "description": "测试用的买入因子",
                "factor_class": "AbuFactorBuyBreak",
                "parameters": {"xd": 20}
            }
        ],
        "sell_factors": [
            {
                "name": "测试卖出因子",
                "description": "测试用的卖出因子",
                "factor_class": "AbuFactorSellBreak",
                "parameters": {"xd": 15}
            }
        ],
        "parameters": {"init_cash": 100000},
        "tags": ["测试", "示例"]
    }
    
    # 发送POST请求
    response = client.post(
        "/api/v1/strategy/",
        json=strategy_data
    )
    
    # 验证响应
    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == strategy_data["name"]
    # 不检查具体的ID值，只检查是否存在
    assert "id" in data["data"]


def test_update_strategy(real_strategy_service):
    """测试更新策略API"""
    # 先创建一个策略
    from backend.app.schemas.strategy import StrategyCreate, BuyFactor, SellFactor
    strategy_data = StrategyCreate(
        name="原始策略",
        description="这是一个原始策略",
        buy_factors=[
            BuyFactor(
                name="测试买入因子",
                description="测试用的买入因子",
                factor_class="AbuFactorBuyBreak",
                factor_type="buy",
                parameters={"xd": 20}
            )
        ],
        parameters={"initial_capital": 100000}
    )
    created = real_strategy_service.create_strategy(strategy_data)
    
    # 准备请求数据
    update_data = {
        "name": "更新后的策略名称",
        "description": "这是更新后的描述",
        "is_public": False
    }
    
    # 发送PUT请求
    response = client.put(
        f"/api/v1/strategy/{created.id}",
        json=update_data
    )
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == created.id
    assert data["data"]["name"] == update_data["name"]


def test_update_strategy_not_found():
    """测试更新不存在的策略API"""
    # 准备请求数据
    update_data = {
        "name": "更新后的策略名称"
    }
    
    # 发送PUT请求到不存在的ID
    response = client.put(
        "/api/v1/strategy/non-existent-id",
        json=update_data
    )
    
    # 验证响应
    assert response.status_code == 404
    data = response.json()
    assert data["success"] is False
    assert "未找到" in data["message"]


def test_delete_strategy(real_strategy_service):
    """测试删除策略API"""
    # 先创建一个策略
    from backend.app.schemas.strategy import StrategyCreate, BuyFactor
    strategy_data = StrategyCreate(
        name="要删除的策略",
        description="这是一个要删除的策略",
        buy_factors=[
            BuyFactor(
                name="测试买入因子",
                description="测试用的买入因子",
                factor_class="AbuFactorBuyBreak",
                factor_type="buy",
                parameters={"xd": 20}
            )
        ],
        parameters={"initial_capital": 100000}
    )
    created = real_strategy_service.create_strategy(strategy_data)
    
    # 发送DELETE请求
    response = client.delete(f"/api/v1/strategy/{created.id}")
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "已成功删除" in data["message"]


def test_delete_strategy_not_found():
    """测试删除不存在的策略API"""
    # 发送DELETE请求到不存在的ID
    response = client.delete("/api/v1/strategy/non-existent-id")
    
    # 验证响应
    assert response.status_code == 404
    data = response.json()
    assert data["success"] is False
    assert "未找到" in data["message"]


# 已替换为下面的新版本 test_get_available_factors 方法


def test_get_available_factors():
    """测试获取可用因子列表API"""
    # 发送GET请求
    response = client.get("/api/v1/strategy/factors/")
    
    # 验证响应
    assert response.status_code == 200
    data = response.json()
    
    # 验证返回的是字典格式
    assert isinstance(data, dict)
    assert "buy_factors" in data
    assert "sell_factors" in data
    
    # 因子服务可能返回不同数量的因子，我们只验证数据格式
    # 验证因子结构
    if data["buy_factors"]:
        factor = data["buy_factors"][0]
        assert "id" in factor
        assert "name" in factor
        assert "factor_class" in factor