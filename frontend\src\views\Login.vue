<template>
  <div class="login-container">
    <el-card class="login-card">
      <template #header>
        <div class="login-header">
          <h2>Abu Modern</h2>
          <p>量化投资平台</p>
        </div>
      </template>
      <el-page-header title="用户登录" content="请输入您的账户信息" />
      <!-- 未来这个页面的登录表单将放在这里 -->
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 登录页面 - 占位符组件
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  box-shadow: var(--shadow-3);
  border-radius: var(--border-radius-lg);
}

.login-header {
  text-align: center;
}

.login-header h2 {
  margin: 0 0 var(--space-xs) 0;
  color: var(--text-color-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.login-header p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: var(--font-size-sm);
}
</style>