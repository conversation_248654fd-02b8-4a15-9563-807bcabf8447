"""
因子转换模块

负责将API中的因子模型转换为abupy框架可识别的因子对象
"""

import importlib
import logging
from typing import Any, Dict, List, Union

from backend.app.core.exceptions import AdapterError, FactorError
from backend.app.schemas.strategy import BuyFactor, SellFactor
from backend.app.abupy_adapter.position_adapter import create_position_manager

# 参数类型映射表 - 定义已知因子参数的期望类型
PARAM_TYPE_MAP = {
    'xd': int,
    'sell_n': int,
    'fast_ma': int,
    'slow_ma': int,
    'stop_loss_n': float,
    'stop_win_n': float,
    'n_folds': int,
    'past_factor': int,
    'atr_n': int,
    'stop_loss_pct': float,
    'stop_win_pct': float,
    'is_sell_today': bool,
    'trend_period': int,
    'golden_period': int,
    'ma_period': int,
    'threshold': float,
    # 'position': float,  # position参数需要特殊处理，不能简单转换为float
    'atr_pos_base': float,  # ATR仓位基础比例参数
    # 添加更多已知参数类型
}

class FactorsConverter:
    """
    将我们的API因子模型，转换为abupy引擎可识别的因子对象。
    核心是维护一个从API class_name到abupy真实类名的映射。
    """
    """
    将我们的API因子模型，转换为abupy引擎可识别的因子对象。
    核心是维护一个从API class_name到abupy真实类名的映射。
    """
    # 基础模块路径
    BUY_FACTOR_MODULE = "abupy.FactorBuyBu"
    SELL_FACTOR_MODULE = "abupy.FactorSellBu"

    # --- 核心：API到AbuPy的翻译词典 ---
    # key: 我们API中使用的 class_name (干净、清晰的名称)
    # value: abupy库中对应的真实类名
    FACTOR_CLASS_MAP = {
        # --- 买入因子 ---
        "FactorBuyBreak": "AbuFactorBuyBreak",
        "FactorBuyWD": "AbuFactorBuyWD",
        "FactorBuyMA": "AbuDoubleMaBuy",  # 我们的'MA'映射到abupy的'DoubleMa'
        "FactorBuyTrend": "AbuUpDownTrend",
        "FactorBuyGolden": "AbuUpDownGolden",
        "FactorBuyTD": "AbuFactorBuyTD", # 'TD'可能代表趋势或时序，也加入映射
        "TestBuyFactor": "AbuFactorBuyBreak",  # 测试用买入因子映射到突破因子
        "DemoFactorBuy": "AbuFactorBuyBreak",  # 演示用买入因子映射到突破因子
        "FactorBuyDemo": "AbuFactorBuyBreak",  # 演示用买入因子映射到突破因子
        "FactorBuyTest": "AbuFactorBuyBreak",  # 测试用买入因子映射到突破因子
        
        # --- 前端兼容性：支持完整的abupy类名 ---
        "AbuFactorBuyBreak": "AbuFactorBuyBreak",  # 前端发送的完整类名
        "AbuFactorBuyWD": "AbuFactorBuyWD",
        "AbuFactorBuyGp": "AbuFactorBuyGp",
        "AbuFactorBuyTD": "AbuFactorBuyTD",
        
        # --- BTC相关因子映射 ---
        "AbuBTCDayBuy": "AbuBTCDayBuy",  # BTC日买入因子
        "BTCDayBuy": "AbuBTCDayBuy",  # 简化的BTC日买入因子名称
        
        # --- 新增API返回的买入因子映射 ---
        "AbuDoubleMaBuy": "AbuDoubleMaBuy",  # 双均线买入因子
        "AbuDownUpTrend": "AbuDownUpTrend",  # 下跌上涨趋势买入因子
        "AbuFactorBuyBreakHitPredictDemo": "AbuFactorBuyBreakHitPredictDemo",  # 突破命中预测演示因子
        "AbuFactorBuyBreakReocrdHitDemo": "AbuFactorBuyBreakReocrdHitDemo",  # 突破记录命中演示因子
        "AbuFactorBuyBreakUmpDemo": "AbuFactorBuyBreakUmpDemo",  # 突破裁判演示因子
        "AbuFactorBuyPutBreak": "AbuFactorBuyPutBreak",  # 看跌突破买入因子
        "AbuFactorBuyPutXDBK": "AbuFactorBuyPutXDBK",  # 看跌XD突破买入因子
        "AbuFactorBuyXDBK": "AbuFactorBuyXDBK",  # XD突破买入因子
        "AbuSDBreak": "AbuSDBreak",  # SD突破买入因子
        "AbuTwoDayBuy": "AbuTwoDayBuy",  # 两日买入因子
        "AbuUpDownGolden": "AbuUpDownGolden",  # 上涨下跌黄金买入因子
        "AbuUpDownTrend": "AbuUpDownTrend",  # 上涨下跌趋势买入因子
        "AbuWeekMonthBuy": "AbuWeekMonthBuy",  # 周月买入因子
        
        # --- 卖出因子 (根据对应的sell __init__.py添加) ---
        "FactorSellNDay": "AbuFactorSellNDay",
        "FactorSellAtrStop": "AbuFactorAtrNStop",
        "FactorSellPreAtrStop": "AbuFactorSellPreAtrStop",
        "FactorSellLose": "AbuFactorSellLose",
        "FactorSellGain": "AbuFactorSellGain",
        "FactorSellBreak": "AbuFactorSellBreak",  # 添加缺失的卖出突破因子映射
        "TestSellFactor": "AbuFactorSellNDay",  # 测试用卖出因子映射到N日卖出因子
        "DemoFactorSell": "AbuFactorSellNDay",  # 演示用卖出因子映射到N日卖出因子
        "FactorSellDemo": "AbuFactorSellNDay",  # 演示用卖出因子映射到N日卖出因子
        "FactorSellTest": "AbuFactorSellNDay",  # 测试用卖出因子映射到N日卖出因子
        
        # --- 前端兼容性：支持完整的abupy卖出因子类名 ---
        "AbuFactorSellNDay": "AbuFactorSellNDay",
        "AbuFactorSellXd": "AbuFactorSellXd",  # 止损因子
        "AbuFactorSellBreak": "AbuFactorSellBreak",
        "AbuFactorSellLose": "AbuFactorSellLose",
        "AbuFactorSellGain": "AbuFactorSellGain",
        "AbuFactorAtrNStop": "AbuFactorAtrNStop",  # ATR止损
        "AbuFactorSellPreAtrStop": "AbuFactorSellPreAtrStop",  # 预测ATR止损
        "AbuFactorSellTrendMove": "AbuFactorSellTrendMove",  # 趋势移动卖出
        "AbuFactorSellCloseAbuMul": "AbuFactorSellCloseAbuMul",  # 收盘价倍数卖出
        
        # --- 新增API返回的卖出因子映射 ---
        "AbuDoubleMaSell": "AbuDoubleMaSell",  # 双均线卖出因子
        "AbuFactorCloseAtrNStop": "AbuFactorCloseAtrNStop",  # 收盘ATR止损因子
        "AbuFactorPreAtrNStop": "AbuFactorPreAtrNStop",  # 预测ATR止损因子
        "AbuFactorSellXD": "AbuFactorSellXD",  # XD卖出因子
        "AbuFactorSellXDBK": "AbuFactorSellXDBK",  # XD突破卖出因子
        
        # --- 添加更多可能的卖出因子变体 ---
        "FactorSellXd": "AbuFactorSellXd",  # 简化名称支持
        "FactorAtrNStop": "AbuFactorAtrNStop",
        "FactorSellPreAtrStop": "AbuFactorSellPreAtrStop"
    }

    @classmethod
    def validate_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]]) -> None:
        """
        校验因子列表中的所有因子class_name是否在FACTOR_CLASS_MAP中存在
        
        Args:
            factors_data: 要校验的因子列表
            
        Raises:
            ValueError: 如果发现任何无效的因子class_name
        """
        if not factors_data:
            return
            
        for factor_model in factors_data:
            class_name = factor_model.class_name
            if class_name not in cls.FACTOR_CLASS_MAP:
                raise ValueError(f"因子 '{class_name}' 是一个无效或不受支持的因子。")

    @classmethod
    def convert_to_abu_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]],
                             **kwargs) -> List[Dict[str, Any]]:
        """
        将Pydantic因子模型转换为abupy期望的因子描述字典列表。
        """
        if not factors_data:
            return []

        result_factors_desc = []
        for factor_model in factors_data:
            try:
                # 1. 获取因子类名和模块路径
                class_name_str = factor_model.class_name # 使用 class_name 字段
                abu_class_name = cls.FACTOR_CLASS_MAP.get(class_name_str, class_name_str)
                if abu_class_name == class_name_str:
                    logging.warning(f"因子类型 '{class_name_str}' 未在 FACTOR_CLASS_MAP 中找到，将直接尝试使用。")

                if factor_model.factor_type == "buy":
                    module_to_import = cls.BUY_FACTOR_MODULE
                elif factor_model.factor_type == "sell":
                    module_to_import = cls.SELL_FACTOR_MODULE
                else:
                    # 这个错误理论上应该由Pydantic的枚举验证捕获，但作为防御性编程，我们保留。
                    raise FactorError(f"未知因子类型: '{factor_model.factor_type}'")

                # 2. 动态导入模块和�?
                factor_package = importlib.import_module(module_to_import)
                factor_obj = getattr(factor_package, abu_class_name)

                # 3. 验证导入的是否为类
                if not isinstance(factor_obj, type):
                    raise FactorError(f"NotAClass")
                    # 原始错误信息：raise FactorError(f"'{abu_class_name}' from module '{module_to_import}' 不是一个有效的类。")

                # 4. 准备因子描述字典并进行参数类型转换
                factor_desc = {'class': factor_obj}
                if factor_model.parameters:
                    # ***** 核心修复逻辑：强制类型转换 *****
                    logging.debug(f"开始处理因子 '{factor_model.name}' 的参数类型转换，原始参数: {factor_model.parameters}")
                    abu_params = {}
                    for key, value in factor_model.parameters.items():
                        if key == 'position':
                            # 特殊处理position参数
                            if isinstance(value, dict):
                                try:
                                    position_config = create_position_manager(value)
                                    if position_config:
                                        abu_params[key] = position_config
                                        logging.debug(f"position参数转换成功: {position_config}")
                                    else:
                                        logging.warning(f"position参数转换失败，保持原样: {value}")
                                        abu_params[key] = value  # 保持原样，不跳过
                                except Exception as e:
                                    logging.error(f"position参数转换出错: {e}，保持原样")
                                    abu_params[key] = value  # 保持原样，不跳过
                            else:
                                logging.warning(f"position参数不是字典类型，保持原样: {value}")
                                abu_params[key] = value  # 保持原样，不跳过
                        elif key in PARAM_TYPE_MAP:
                            try:
                                # 根据映射表，强制进行类型转换
                                abu_params[key] = PARAM_TYPE_MAP[key](value)
                            except (ValueError, TypeError) as e:
                                logging.warning(f"参数 '{key}' 的值 '{value}' 无法转换为期望的类型 {PARAM_TYPE_MAP[key]}，将保持原样。错误: {e}")
                                abu_params[key] = value
                        else:
                            # 对于未在映射表中的参数，尝试智能转换
                            if isinstance(value, str):
                                # 处理布尔值字符串
                                if value.lower() in ('true', 'false'):
                                    abu_params[key] = value.lower() == 'true'
                                # 处理纯数字字符串
                                elif value.isdigit():
                                    abu_params[key] = int(value)
                                else:
                                    try:
                                        # 尝试转换为浮点数
                                        abu_params[key] = float(value)
                                    except ValueError:
                                        # 如果转换失败，则保持为字符串
                                        abu_params[key] = value
                            else:
                                abu_params[key] = value
                    
                    factor_desc.update(abu_params)
                    logging.debug(f"因子 '{factor_model.name}' 参数类型转换完成，转换后参数: {abu_params}")
                    # *********************************
                
                # 5. 添加到结果列表
                result_factors_desc.append(factor_desc)

            except Exception as e:
                # ★★★ 关键修改：捕获所有可能的异常。
                # 包装成一个统一的、可被测试的AdapterError
                error_message = f"因子 '{factor_model.name}' ('{factor_model.class_name}') 配置或实例化时发生致命错误: {e}"
                logging.error(error_message, exc_info=True)
                raise AdapterError(error_message) from e
        
        return result_factors_desc
