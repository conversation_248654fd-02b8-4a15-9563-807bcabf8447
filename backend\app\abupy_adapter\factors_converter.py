"""
因子转换模块

负责将API中的因子模型转换为abupy框架可识别的因子对象
"""

import importlib
import logging
from typing import Any, Dict, List, Union

from backend.app.core.exceptions import AdapterError, FactorError
from backend.app.schemas.strategy import BuyFactor, SellFactor

class FactorsConverter:
    """
    将我们的API因子模型，转换为abupy引擎可识别的因子对象。
    核心是维护一个从API class_name到abupy真实类名的映射。
    """
    # 基础模块路径
    BUY_FACTOR_MODULE = "abupy.FactorBuyBu"
    SELL_FACTOR_MODULE = "abupy.FactorSellBu"

    # --- 核心：API到AbuPy的翻译词典 ---
    # key: 我们API中使用的 factor_class (干净、清晰的名称)
    # value: abupy库中对应的真实类名
    FACTOR_CLASS_MAP = {
        # --- 买入因子 ---
        "FactorBuyBreak": "AbuFactorBuyBreak",
        "FactorBuyWD": "AbuFactorBuyWD",
        "FactorBuyMA": "AbuDoubleMaBuy",  # 我们的'MA'映射到abupy的'DoubleMa'
        "FactorBuyTrend": "AbuUpDownTrend",
        "FactorBuyGolden": "AbuUpDownGolden",
        "FactorBuyTD": "AbuFactorBuyTD", # 'TD'可能代表趋势或时序，也加入映射

        # --- 卖出因子 (根据对应的sell __init__.py添加) ---
        "FactorSellNDay": "AbuFactorSellNDay",
        "FactorSellAtrStop": "AbuFactorAtrNStop",
        "FactorSellPreAtrStop": "AbuFactorSellPreAtrStop",
        "FactorSellLose": "AbuFactorSellLose",
        "FactorSellGain": "AbuFactorSellGain",
    }

    @classmethod
    def convert_to_abu_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]],
                             **kwargs) -> List[Any]:
        """
        将Pydantic的因子模型列表转换为abu内部可识别的、已实例化的因子对象列表。
        """
        if not factors_data:
            return []

        result_factors = []
        factor_init_kwargs = kwargs

        for factor_model in factors_data:
            # 在循环的开始处初始化 abu_class_name
            abu_class_name = "UnknownFactor" 
            try:
                factor_class_str = factor_model.factor_class
                # 将 abu_class_name 的赋值提前
                abu_class_name = cls.FACTOR_CLASS_MAP.get(factor_class_str)
                if abu_class_name is None:
                    # 如果在映射中找不到，立即抛出明确的错误
                    raise FactorError(f"未知的因子类型 '{factor_class_str}'. 它没有在FactorsConverter.FACTOR_CLASS_MAP中定义。")
                # 1. 从API获取干净的"基础类名" (例如 "FactorBuyMA")
                base_class_name = factor_model.factor_class

                # 2. 使用"翻译词典"查找abupy中的真实类名
                if base_class_name not in cls.FACTOR_CLASS_MAP:
                    raise FactorError(f"未知的因子类型 '{base_class_name}'. 它没有在FactorsConverter.FACTOR_CLASS_MAP中定义。")
                
                abu_class_name = cls.FACTOR_CLASS_MAP[base_class_name]

                # 3. 确定要导入的"父级模块"路径
                if factor_model.factor_type == "buy":
                    module_to_import = cls.BUY_FACTOR_MODULE
                elif factor_model.factor_type == "sell":
                    module_to_import = cls.SELL_FACTOR_MODULE
                else:
                    raise FactorError(f"未知因子类型: '{factor_model.factor_type}'")

                # 4. 动态导入"父级模块"
                factor_package = importlib.import_module(module_to_import)

                # 5. 从父级模块中，获取我们想要的"类对象"
                factor_class_obj = getattr(factor_package, abu_class_name)

                # 6. 准备实例化参数 (修正：只使用因子自身的参数)
                instance_params = factor_model.parameters if factor_model.parameters else {}

                # 7. 实例化因子对象
                factor_instance = factor_class_obj(**instance_params)
                result_factors.append(factor_instance)

            except ImportError as e:
                raise FactorError(f"无法为因子 '{factor_model.name}' 导入基础模块 '{module_to_import}'.") from e
            except AttributeError as e:
                raise FactorError(f"在模块 '{module_to_import}' 中找不到类 '{abu_class_name}'. 这可能意味着 FACTOR_CLASS_MAP 中的映射已过时。") from e
            except Exception as e:
                raise AdapterError(f"实例化因子 '{abu_class_name}' 时发生错误: {e}") from e

        return result_factors
