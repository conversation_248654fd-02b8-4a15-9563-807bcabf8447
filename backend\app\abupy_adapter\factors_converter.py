"""
因子转换模块

负责将API中的因子模型转换为abupy框架可识别的因子对象
"""

import importlib
import logging
from typing import Any, Dict, List, Union

from app.core.exceptions import AdapterError, FactorError
from app.schemas.strategy import BuyFactor, SellFactor

class FactorsConverter:
    """
    将我们的API因子模型，转换为abupy引擎可识别的因子对象。
    核心是维护一个从API class_name到abupy真实类名的映射。
    """
    # 基础模块路径
    BUY_FACTOR_MODULE = "abupy.FactorBuyBu"
    SELL_FACTOR_MODULE = "abupy.FactorSellBu"

    # --- 核心：API到AbuPy的翻译词典 ---
    # key: 我们API中使用的 class_name (干净、清晰的名称)
    # value: abupy库中对应的真实类名
    FACTOR_CLASS_MAP = {
        # --- 买入因子 ---
        "FactorBuyBreak": "AbuFactorBuyBreak",
        "FactorBuyWD": "AbuFactorBuyWD",
        "FactorBuyMA": "AbuDoubleMaBuy",  # 我们的'MA'映射到abupy的'DoubleMa'
        "FactorBuyTrend": "AbuUpDownTrend",
        "FactorBuyGolden": "AbuUpDownGolden",
        "FactorBuyTD": "AbuFactorBuyTD", # 'TD'可能代表趋势或时序，也加入映射
        "TestBuyFactor": "AbuFactorBuyBreak",  # 测试用买入因子映射到突破因子
        "DemoFactorBuy": "AbuFactorBuyBreak",  # 演示用买入因子映射到突破因子
        "FactorBuyDemo": "AbuFactorBuyBreak",  # 演示用买入因子映射到突破因子
        "FactorBuyTest": "AbuFactorBuyBreak",  # 测试用买入因子映射到突破因子
        
        # --- 卖出因子 (根据对应的sell __init__.py添加) ---
        "FactorSellNDay": "AbuFactorSellNDay",
        "FactorSellAtrStop": "AbuFactorAtrNStop",
        "FactorSellPreAtrStop": "AbuFactorSellPreAtrStop",
        "FactorSellLose": "AbuFactorSellLose",
        "FactorSellGain": "AbuFactorSellGain",
        "FactorSellBreak": "AbuFactorSellBreak",  # 添加缺失的卖出突破因子映射
        "TestSellFactor": "AbuFactorSellNDay",  # 测试用卖出因子映射到N日卖出因子
        "DemoFactorSell": "AbuFactorSellNDay",  # 演示用卖出因子映射到N日卖出因子
        "FactorSellDemo": "AbuFactorSellNDay",  # 演示用卖出因子映射到N日卖出因子
        "FactorSellTest": "AbuFactorSellNDay",  # 测试用卖出因子映射到N日卖出因子
    }

    @classmethod
    def convert_to_abu_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]],
                             **kwargs) -> List[Dict[str, Any]]:
        """
        将Pydantic因子模型转换为abupy期望的因子描述字典列表。
        """
        if not factors_data:
            return []

        result_factors_desc = []
        for factor_model in factors_data:
            try:
                # 1. 获取因子类名和模块路径
                class_name_str = factor_model.class_name # 使用 class_name 字段
                abu_class_name = cls.FACTOR_CLASS_MAP.get(class_name_str, class_name_str)
                if abu_class_name == class_name_str:
                    logging.warning(f"因子类型 '{class_name_str}' 未在 FACTOR_CLASS_MAP 中找到，将直接尝试使用。")

                if factor_model.factor_type == "buy":
                    module_to_import = cls.BUY_FACTOR_MODULE
                elif factor_model.factor_type == "sell":
                    module_to_import = cls.SELL_FACTOR_MODULE
                else:
                    # 这个错误理论上应该由Pydantic的枚举验证捕获，但作为防御性编程，我们保留它
                    raise FactorError(f"未知因子类型: '{factor_model.factor_type}'")

                # 2. 动态导入模块和类
                factor_package = importlib.import_module(module_to_import)
                factor_obj = getattr(factor_package, abu_class_name)

                # 3. 验证导入的是否为类
                if not isinstance(factor_obj, type):
                    raise FactorError(f"'{abu_class_name}' from module '{module_to_import}' 不是一个有效的类。")

                # 4. 准备因子描述字典并实例化（abupy内部会做）
                factor_desc = {'class': factor_obj}
                if factor_model.parameters:
                    factor_desc.update(factor_model.parameters)
                
                # 5. 添加到结果列表
                result_factors_desc.append(factor_desc)

            except Exception as e:
                # ★★★ 关键修改：捕获所有可能的异常！
                # 包装成一个统一的、可被测试的AdapterError
                error_message = f"因子 '{factor_model.name}' ({factor_model.class_name}) 配置或实例化时发生致命错误: {e}"
                logging.error(error_message, exc_info=True)
                raise AdapterError(error_message) from e
        
        return result_factors_desc
