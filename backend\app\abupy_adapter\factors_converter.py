"""
因子转换模块

负责将API中的因子模型转换为abupy框架可识别的因子对象
"""

import importlib
import logging
from typing import Any, Dict, List, Union

from backend.app.core.exceptions import AdapterError, FactorError
from backend.app.schemas.strategy import BuyFactor, SellFactor

class FactorsConverter:
    """
    将我们的API因子模型，转换为abupy引擎可识别的因子对象。
    核心是维护一个从API class_name到abupy真实类名的映射。
    """
    # 基础模块路径
    BUY_FACTOR_MODULE = "abupy.FactorBuyBu"
    SELL_FACTOR_MODULE = "abupy.FactorSellBu"

    # --- 核心：API到AbuPy的翻译词典 ---
    # key: 我们API中使用的 factor_class (干净、清晰的名称)
    # value: abupy库中对应的真实类名
    FACTOR_CLASS_MAP = {
        # --- 买入因子 ---
        "FactorBuyBreak": "AbuFactorBuyBreak",
        "FactorBuyWD": "AbuFactorBuyWD",
        "FactorBuyMA": "AbuDoubleMaBuy",  # 我们的'MA'映射到abupy的'DoubleMa'
        "FactorBuyTrend": "AbuUpDownTrend",
        "FactorBuyGolden": "AbuUpDownGolden",
        "FactorBuyTD": "AbuFactorBuyTD", # 'TD'可能代表趋势或时序，也加入映射
        
        # --- 卖出因子 (根据对应的sell __init__.py添加) ---
        "FactorSellNDay": "AbuFactorSellNDay",
        "FactorSellAtrStop": "AbuFactorAtrNStop",
        "FactorSellPreAtrStop": "AbuFactorSellPreAtrStop",
        "FactorSellLose": "AbuFactorSellLose",
        "FactorSellGain": "AbuFactorSellGain",
    }

    @classmethod
    def convert_to_abu_factors(cls, factors_data: List[Union[BuyFactor, SellFactor]],
                            **kwargs) -> List[Dict[str, Any]]:
        """
        [最终修正版] 将Pydantic因子模型转换为abupy【期望的因子描述字典】列表。
        abupy的回测引擎期望的不是实例化对象，而是描述如何创建对象的字典。
        """
        if not factors_data:
            return []

        result_factors_desc = []
        for factor_model in factors_data:
            try:
                # 1. 查找因子对应的abupy内部类名
                factor_class_str = factor_model.factor_class
                abu_class_name = cls.FACTOR_CLASS_MAP.get(factor_class_str)
                if abu_class_name is None:
                    raise FactorError(f"未知的因子类型 '{factor_class_str}'.")

                # 2. 动态导入包含该类的模块
                if factor_model.factor_type == "buy":
                    module_to_import = cls.BUY_FACTOR_MODULE
                elif factor_model.factor_type == "sell":
                    module_to_import = cls.SELL_FACTOR_MODULE
                else:
                    raise FactorError(f"未知因子类型: '{factor_model.factor_type}'")
                
                factor_package = importlib.import_module(module_to_import)
                factor_class_obj = getattr(factor_package, abu_class_name)
                
                # 3. 构建abupy期望的描述字典
                factor_desc = {'class': factor_class_obj}
                if factor_model.parameters:
                    factor_desc.update(factor_model.parameters)
                
                result_factors_desc.append(factor_desc)

            except Exception as e:
                # 捕获所有可能的错误（导入、属性查找等）并包装
                raise AdapterError(f"转换因子 '{factor_model.name}' 时发生错误: {e}") from e
        
        return result_factors_desc
