<template>
  <div class="backtest-results">
    <div class="results-header">
      <h2>回测结果</h2>
      <div class="status-indicator" :class="statusClass">
        <span class="status-dot"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading loading-container" data-testid="loading-state">
      <div class="loading-spinner"></div>
      <p>正在计算回测结果...</p>
    </div>

    <!-- 结果展示 -->
    <div v-else-if="result" class="results-content">
      <!-- 核心指标卡片 -->
      <div class="metrics-grid">
        <div class="metric-card" :class="getReturnColorClass(result.total_return)" data-testid="total-return-card">
          <div class="metric-label">总收益率</div>
          <div class="metric-value" :class="returnClass">
            {{ formatPercentage(result.total_return) }}
          </div>
        </div>
        
        <div class="metric-card" data-testid="sharpe-ratio-card">
          <div class="metric-label">夏普比率</div>
          <div class="metric-value">
            {{ formatNumber(result.sharpe_ratio) }}
          </div>
        </div>
        
        <div class="metric-card" data-testid="max-drawdown-card">
          <div class="metric-label">最大回撤</div>
          <div class="metric-value negative">
            {{ formatPercentage(result.max_drawdown) }}
          </div>
        </div>
        
        <div class="metric-card" data-testid="final-capital-card">
          <div class="metric-label">期末资金</div>
          <div class="metric-value">
            {{ formatCurrency(result.final_capital) }}
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="details-section">
        <h3>详细信息</h3>
        <div class="details-grid">
          <div class="detail-item">
            <span class="detail-label">策略名称:</span>
            <span class="detail-value" data-testid="strategy-name">{{ result.strategy_name }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">交易标的:</span>
            <span class="detail-value" data-testid="symbol">{{ result.symbol }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">回测期间:</span>
            <span class="detail-value" data-testid="date-range">
              {{ result.start_date }} 至 {{ result.end_date }}
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">初始资金:</span>
            <span class="detail-value" data-testid="initial-capital">
              {{ formatCurrency(result.initial_capital) }}
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">交易次数:</span>
            <span class="detail-value" data-testid="trade-count">
              {{ result.trades?.length || 0 }} 次
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">任务ID:</span>
            <span class="detail-value task-id" data-testid="task-id">{{ result.task_id }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <button 
          @click="exportResults" 
          class="btn btn-secondary"
          data-testid="export-btn"
        >
          导出结果
        </button>
        <button 
          @click="viewAnalysis" 
          class="btn btn-primary"
          data-testid="analysis-btn"
        >
          查看分析
        </button>
      </div>
    </div>

    <!-- 无结果状态 -->
    <div v-else class="empty-state no-results" data-testid="no-results">
      <div class="no-results-icon">📊</div>
      <p>暂无回测结果</p>
      <p class="no-results-hint">请先配置并运行回测</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { BacktestResult } from '@/types/backtest'

interface Props {
  result?: BacktestResult | null
  loading?: boolean
}

interface Emits {
  export: [result: BacktestResult]
  'view-analysis': [result: BacktestResult]
}

const props = withDefaults(defineProps<Props>(), {
  result: null,
  loading: false
})

const emit = defineEmits<Emits>()

// 状态计算
const statusClass = computed(() => {
  if (props.loading) return 'status-loading'
  if (props.result?.status === 'running') return 'status-running'
  if (props.result) return 'status-completed'
  return 'status-pending'
})

const statusText = computed(() => {
  if (props.loading) return '计算中'
  if (props.result) return '已完成'
  return '待运行'
})

const returnClass = computed(() => {
  if (!props.result) return ''
  return props.result.total_return >= 0 ? 'positive' : 'negative'
})

// 获取收益率颜色类
const getReturnColorClass = (returnValue: number): string => {
  if (returnValue > 0) return 'positive'
  if (returnValue < 0) return 'negative'
  return 'neutral'
}

// 格式化函数
const formatPercentage = (value: number): string => {
  if (value === undefined || value === null || isNaN(value)) {
    return '0.00%'
  }
  return `${(value * 100).toFixed(2)}%`
}

const formatNumber = (value: number, decimals: number = 3): string => {
  if (value === undefined || value === null || isNaN(value)) {
    return '0.000'
  }
  return value.toFixed(decimals)
}

const formatCurrency = (value: number): string => {
  if (value === undefined || value === null || isNaN(value)) {
    return '¥0.00'
  }
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 事件处理
const exportResults = () => {
  if (props.result) {
    emit('export', props.result)
  }
}

const viewAnalysis = () => {
  if (props.result) {
    emit('view-analysis', props.result)
  }
}

// 暴露方法给测试使用
defineExpose({
  formatPercentage,
  formatNumber,
  formatCurrency
})
</script>

<style scoped>
.backtest-results {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.results-header h2 {
  margin: 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.status-pending {
  background: #f3f4f6;
  color: #6b7280;
}

.status-loading {
  background: #fef3c7;
  color: #d97706;
}

.status-running {
  background: #dbeafe;
  color: #2563eb;
}

.status-completed {
  background: #d1fae5;
  color: #059669;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.loading-container {
  text-align: center;
  padding: 48px 24px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.metric-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.metric-card.positive {
  border-color: #059669;
  background-color: #f0fdf4;
}

.metric-card.negative {
  border-color: #dc2626;
  background-color: #fef2f2;
}

.metric-card.neutral {
  border-color: #6b7280;
  background-color: #f9fafb;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
}

.metric-value.positive {
  color: #059669;
}

.metric-value.negative {
  color: #dc2626;
}

.details-section {
  margin-bottom: 32px;
}

.details-section h3 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: 600;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-label {
  font-weight: 500;
  color: #64748b;
}

.detail-value {
  color: #1e293b;
}

.task-id {
  font-family: monospace;
  font-size: 12px;
}

.actions-section {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.no-results {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-results-hint {
  font-size: 14px;
  margin-top: 8px;
}
</style>