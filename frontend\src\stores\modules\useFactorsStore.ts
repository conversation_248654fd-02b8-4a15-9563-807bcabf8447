import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getFactors } from '@/api/modules/factors'
import type { Factor } from '@/types'

export const useFactorsStore = defineStore('factors', () => {
  // 状态
  const factors = ref<Factor[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 获取因子列表
  const fetchFactors = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await getFactors()
      console.log('获取因子数据:', response)
      
      // 安全地处理响应数据，确保属性存在且为数组
      const buyFactors = Array.isArray(response?.buy_factors) ? response.buy_factors : []
      const sellFactors = Array.isArray(response?.sell_factors) ? response.sell_factors : []
      
      console.log('买入因子数量:', buyFactors.length)
      console.log('卖出因子数量:', sellFactors.length)
      
      // 合并买入和卖出因子
      factors.value = [...buyFactors, ...sellFactors]
      
      console.log('因子数据加载成功:', {
        buyFactorsCount: buyFactors.length,
        sellFactorsCount: sellFactors.length,
        totalFactors: factors.value.length
      })
      console.log('因子列表:', factors.value)
    } catch (err) {
      console.error('获取因子列表失败:', err)
      error.value = err instanceof Error ? err.message : '网络请求失败'
      // 确保在错误情况下也有一个空数组
      factors.value = []
    } finally {
      isLoading.value = false
    }
  }

  // 重置状态
  const resetState = () => {
    factors.value = []
    isLoading.value = false
    error.value = null
  }

  return {
    // 状态
    factors,
    isLoading,
    error,
    // 方法
    fetchFactors,
    resetState
  }
})