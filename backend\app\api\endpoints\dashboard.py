# -*- coding: utf-8 -*- 
"""
Dashboard API
"""
import logging
from fastapi import APIRouter, Depends
from sqlmodel import Session

from backend.app.schemas.dashboard import DashboardSummary
from backend.app.schemas.response import ResponseSchema, success
from backend.app.core.database import get_session
from backend.app.services.strategy_service import StrategyService, get_strategy_service
from backend.app.services.market import MarketService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/summary", response_model=ResponseSchema[DashboardSummary])
def get_dashboard_summary(
    db: Session = Depends(get_session), 
    strategy_service: StrategyService = Depends(get_strategy_service),
    market_service: MarketService = Depends(MarketService)  # 注入MarketService
):
    """
    获取仪表盘核心摘要数据
    """
    logger.info("Executing get_dashboard_summary...")
    
    # 1. 获取活跃策略数
    strategies = strategy_service.get_all_strategies()
    strategies_count = len(strategies) if strategies else 0
    logger.info(f"Found {strategies_count} strategies.")

    # 2. 计算今日涨跌 (以沪深300为例)
    today_gain = 0.0
    try:
        # 获取最近两个交易日的数据
        kline_data = market_service.get_kline_data('000300.SH', period='daily')
        if kline_data and kline_data.data and len(kline_data.data) >= 2:
            today_close = kline_data.data[-1].close
            yesterday_close = kline_data.data[-2].close
            if yesterday_close > 0:
                today_gain = (today_close - yesterday_close) / yesterday_close
    except Exception as e:
        logger.error(f"Failed to calculate today_gain: {e}", exc_info=True)

    # 3. 总成交额 (技术债)
    # TODO: Implement real turnover calculation after backtest result persistence is done.
    total_turnover_wan = 0.0

    # 4. 信号数量 (技术债)
    # TODO: Implement real signals count after signal generation module is done.
    signals_count = 0

    # 5. 构造市场表现数据
    market_performance_data = {"date": [], "value": []}
    if kline_data and kline_data.data:
        # 截取最近30天的数据
        recent_kline = kline_data.data[-30:]
        market_performance_data["date"] = [k.date for k in recent_kline]
        market_performance_data["value"] = [k.close for k in recent_kline]

    summary_data = DashboardSummary(
        today_gain=today_gain,
        active_strategies=strategies_count,
        total_turnover_wan=total_turnover_wan,
        signals_count=signals_count,
        market_performance=market_performance_data
    )
    return success(data=summary_data)