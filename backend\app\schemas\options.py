# -*- coding: utf-8 -*-
"""
选项相关的Pydantic模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel


class PositionOption(BaseModel):
    """仓位管理选项"""
    key: str  # 选项键
    name: str  # 选项名称
    description: Optional[str] = None  # 选项描述
    params: Optional[Dict[str, Any]] = None  # 参数配置


class PositionOptionResponse(BaseModel):
    """仓位管理选项响应"""
    options: List[PositionOption]  # 选项列表
    total: int  # 总数


class JudgeOption(BaseModel):
    """裁判规则选项"""
    key: str  # 选项键
    name: str  # 选项名称
    description: Optional[str] = None  # 选项描述
    params: Optional[Dict[str, Any]] = None  # 参数配置


class JudgeOptionResponse(BaseModel):
    """裁判规则选项响应"""
    options: List[JudgeOption]  # 选项列表
    total: int  # 总数


class FeatureFilterOption(BaseModel):
    """特征过滤选项"""
    key: str  # 选项键
    name: str  # 选项名称
    description: Optional[str] = None  # 选项描述
    params: Optional[Dict[str, Any]] = None  # 参数配置


class FeatureFilterResponse(BaseModel):
    """特征过滤选项响应"""
    filters: List[FeatureFilterOption]  # 过滤器列表
    total: int  # 总数