# -*- coding: utf-8 -*-
"""
选项服务模块

提供策略选项相关的服务，包括仓位管理策略、裁判规则和特征过滤器等。
"""

from typing import List, Optional
import logging

from backend.app.schemas.options import (
    PositionOption,
    JudgeOption,
    FeatureFilterOption
)
from backend.app.core.exceptions import ResourceNotFoundError, ValidationError

logger = logging.getLogger(__name__)


class OptionsService:
    """选项服务类"""
    
    def __init__(self):
        """初始化选项服务"""
        pass
    
    def get_position_options(self) -> List[PositionOption]:
        """获取所有支持的仓位管理策略及其参数信息
        
        Returns:
            List[PositionOption]: 仓位管理选项列表
        """
        try:
            # 返回预定义的仓位管理策略选项
            position_options = [
                PositionOption(
                    key="kelly",
                    name="凯利公式仓位管理",
                    description="基于凯利公式计算最优仓位大小",
                    params={
                        "win_rate": {"type": "float", "default": 0.6, "description": "胜率"},
                        "win_loss_ratio": {"type": "float", "default": 2.0, "description": "盈亏比"}
                    }
                ),
                PositionOption(
                    key="fixed_ratio",
                    name="固定比例仓位管理",
                    description="使用固定比例分配仓位",
                    params={
                        "ratio": {"type": "float", "default": 0.1, "description": "仓位比例"}
                    }
                ),
                PositionOption(
                    key="atr_based",
                    name="ATR波动率仓位管理",
                    description="基于ATR波动率调整仓位大小",
                    params={
                        "atr_period": {"type": "int", "default": 14, "description": "ATR计算周期"},
                        "risk_ratio": {"type": "float", "default": 0.02, "description": "风险比例"}
                    }
                )
            ]
            
            logger.info(f"获取到 {len(position_options)} 个仓位管理策略")
            return position_options
            
        except Exception as e:
            logger.error(f"获取仓位管理策略失败: {str(e)}")
            raise ResourceNotFoundError(f"获取仓位管理策略失败: {str(e)}")
    
    def get_judge_options(self) -> List[JudgeOption]:
        """获取所有支持的裁判规则及其参数信息
        
        Returns:
            List[JudgeOption]: 裁判规则选项列表
        """
        try:
            # 返回预定义的裁判规则选项
            judge_options = [
                JudgeOption(
                    key="stop_loss",
                    name="止损裁判",
                    description="基于价格或百分比的止损规则",
                    params={
                        "stop_loss_pct": {"type": "float", "default": 0.05, "description": "止损百分比"}
                    }
                ),
                JudgeOption(
                    key="take_profit",
                    name="止盈裁判",
                    description="基于价格或百分比的止盈规则",
                    params={
                        "take_profit_pct": {"type": "float", "default": 0.1, "description": "止盈百分比"}
                    }
                ),
                JudgeOption(
                    key="time_based",
                    name="时间裁判",
                    description="基于持仓时间的退出规则",
                    params={
                        "max_hold_days": {"type": "int", "default": 30, "description": "最大持仓天数"}
                    }
                ),
                JudgeOption(
                    key="trailing_stop",
                    name="移动止损裁判",
                    description="跟踪价格变动的移动止损规则",
                    params={
                        "trail_pct": {"type": "float", "default": 0.03, "description": "移动止损百分比"}
                    }
                )
            ]
            
            logger.info(f"获取到 {len(judge_options)} 个裁判规则")
            return judge_options
            
        except Exception as e:
            logger.error(f"获取裁判规则失败: {str(e)}")
            raise ResourceNotFoundError(f"获取裁判规则失败: {str(e)}")
    
    def get_feature_filters(self, model_name: Optional[str] = None) -> List[FeatureFilterOption]:
        """基于K线角度特征，过滤大概率亏损的交易模式
        
        Args:
            model_name: 已训练好的模型名称
            
        Returns:
            List[FeatureFilterOption]: 特征过滤器选项列表
        """
        try:
            # 返回预定义的特征过滤器选项
            feature_filters = [
                FeatureFilterOption(
                    key="volatility_filter",
                    name="波动率过滤器",
                    description="过滤高波动率时期的交易信号",
                    params={
                        "volatility_threshold": {"type": "float", "default": 0.02, "description": "波动率阈值"}
                    }
                ),
                FeatureFilterOption(
                    key="volume_filter",
                    name="成交量过滤器",
                    description="过滤低成交量的交易信号",
                    params={
                        "volume_ratio": {"type": "float", "default": 1.5, "description": "成交量比率"}
                    }
                ),
                FeatureFilterOption(
                    key="trend_filter",
                    name="趋势过滤器",
                    description="基于趋势强度过滤交易信号",
                    params={
                        "trend_period": {"type": "int", "default": 20, "description": "趋势计算周期"},
                        "trend_threshold": {"type": "float", "default": 0.1, "description": "趋势强度阈值"}
                    }
                )
            ]
            
            # 如果指定了模型名称，可以根据模型加载特定的过滤器
            if model_name:
                logger.info(f"使用模型 {model_name} 的特征过滤器")
                # 这里可以根据模型名称加载特定的过滤器配置
                # 目前返回默认配置
            
            logger.info(f"获取到 {len(feature_filters)} 个特征过滤器")
            return feature_filters
            
        except Exception as e:
            logger.error(f"获取特征过滤器失败: {str(e)}")
            raise ResourceNotFoundError(f"获取特征过滤器失败: {str(e)}")