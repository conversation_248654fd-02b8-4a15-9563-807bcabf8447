from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import List, Dict, Any
import pandas as pd
from backend.app.abupy_adapter.metrics_adapter import calculate_metrics

router = APIRouter()

class TradingOrder(BaseModel):
    buy_date: int
    sell_date: int
    buy_price: float
    sell_price: float
    symbol: str
    # ... 其他你认为需要从前端接收的订单字段

class Action(BaseModel):
    buy: List[Dict[str, Any]]
    sell: List[Dict[str, Any]]

class TradingResult(BaseModel):
    orders: List[TradingOrder]
    actions: Action
    start_cash: float

@router.post("/calculate")
def calculate(result: TradingResult):
    """
    接收前端传递的回测结果，计算并返回性能指标。
    """
    # 1. 将Pydantic模型转换为Pandas DataFrame
    # 注意：这里的字段名需要与abupy中AbuMetricsBase期望的列名匹配
    orders_df = pd.DataFrame([order.dict() for order in result.orders])
    # abupy期望的列名可能包含 'buy_date', 'sell_date', 'buy_price', 
    # 'sell_price', 'symbol', 'profit', 'profit_cg', 'result'等
    # 你可能需要在这里进行列名映射或数据转换
    # 例如: orders_df.rename(columns={'buy_date': 'buy_date_int'}, 
    # inplace=True)

    # 2. 转换action数据 (如果需要)
    # abupy的action_pd结构比较复杂，通常包含买卖信号的详细信息
    # 这里我们先用一个简化的示例，实际情况可能需要更复杂的转换
    buy_actions_df = pd.DataFrame(result.actions.buy)
    sell_actions_df = pd.DataFrame(result.actions.sell)
    # 假设action_pd需要合并买卖信息
    action_pd = pd.concat([buy_actions_df.assign(action='buy'), 
                           sell_actions_df.assign(action='sell')])

    # 3. 调用适配器函数
    metrics = calculate_metrics(
        capital=result.start_cash,
        orders_pd=orders_df,
        action_pd=action_pd,
        benchmark_pd=None # 基准数据暂时不处理
    )
    return metrics
