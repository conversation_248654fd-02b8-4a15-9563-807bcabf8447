<template>
  <div class="strategy-workshop">
    <!-- 固定的页面头部 -->
    <div class="workshop-header">
      <el-page-header :title="UI_TEXT.PAGE_TITLE" :content="UI_TEXT.PAGE_SUBTITLE" />
    </div>
    
    <!-- 可滚动的内容区域 -->
    <div class="workshop-content">
      <div class="workshop-layout">
        <div class="workshop-left-panel">
          <StrategyList
            data-testid="strategy-list"
            :strategies="strategyStore.strategies"
            :current-strategy="strategyStore.currentSelectedStrategy"
            :loading="strategyStore.isLoading"
            :error="strategyStore.error"
            @select-strategy="handleSelectStrategy"
            @create-strategy="handleCreateStrategy"
          />
        </div>
        
        <div class="workshop-right-panel">
          <StrategyEditor
            ref="strategyEditorRef"
            data-testid="strategy-editor"
            :strategy="strategyStore.currentSelectedStrategy"
            :loading="strategyStore.isLoading"
            @save-strategy="handleSaveStrategy"
            @update-strategy="handleUpdateStrategy"
            @create-strategy="handleCreateStrategy"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useStrategyStore, useFactorsStore } from '@/stores'
import { UI_TEXT, isNewStrategy } from '@/constants/strategy'
import StrategyList from '@/components/StrategyWorkshop/StrategyList.vue'
import StrategyEditor from '@/components/StrategyWorkshop/StrategyEditor.vue'
import type { Strategy } from '@/api/types'

const strategyStore = useStrategyStore()
const factorsStore = useFactorsStore()
const strategyEditorRef = ref<InstanceType<typeof StrategyEditor> | null>(null)

// 生命周期
onMounted(async () => {
  await Promise.all([
    strategyStore.fetchStrategies(),
    factorsStore.fetchFactors()
  ])
})

// 事件处理
const handleSelectStrategy = (strategy: Strategy) => {
  strategyStore.setCurrentSelectedStrategy(strategy)
}

const handleCreateStrategy = () => {
  strategyStore.startNewStrategyCreation()
}

const handleSaveStrategy = async (strategy: Strategy) => {
  try {
    const isNew = isNewStrategy(strategy)
    
    if (isNew) {
      // 创建新策略，并获取返回的策略对象（包含新的ID）
      const createdStrategy = await strategyStore.createStrategy(strategy)
      
      // 通知子组件保存成功
      strategyEditorRef.value?.onSaveComplete(true)
      
      // 刷新策略列表
      await strategyStore.fetchStrategies()
      
      // 保持当前策略的专属卖出规则不被覆盖
      const mergedStrategy = { ...createdStrategy }
      if (strategy.buy_factors) {
        mergedStrategy.buy_factors = strategy.buy_factors.map(localBuyFactor => {
          const backendBuyFactor = createdStrategy.buy_factors?.find(f => f.id === localBuyFactor.id)
          if (backendBuyFactor && localBuyFactor.sell_factors) {
            // 保持本地的专属卖出规则
            return { ...backendBuyFactor, sell_factors: localBuyFactor.sell_factors }
          }
          return backendBuyFactor || localBuyFactor
        })
      }
      
      // 设置创建的策略为当前选中策略，这样按钮文本会变为"保存策略"
      strategyStore.setCurrentSelectedStrategy(mergedStrategy)
    } else {
      // 更新现有策略
      const updatedStrategy = await strategyStore.updateStrategy(String(strategy.id), strategy)
      
      // 通知子组件保存成功
      strategyEditorRef.value?.onSaveComplete(true)
      
      // 刷新策略列表
      await strategyStore.fetchStrategies()
      
      // 保持当前策略的专属卖出规则不被覆盖
      // 如果后端返回的数据不包含专属卖出规则，则保持本地的数据
      const mergedStrategy = { ...updatedStrategy }
      if (strategy.buy_factors) {
        mergedStrategy.buy_factors = strategy.buy_factors.map(localBuyFactor => {
          const backendBuyFactor = updatedStrategy.buy_factors?.find(f => f.id === localBuyFactor.id)
          if (backendBuyFactor && localBuyFactor.sell_factors) {
            // 保持本地的专属卖出规则
            return { ...backendBuyFactor, sell_factors: localBuyFactor.sell_factors }
          }
          return backendBuyFactor || localBuyFactor
        })
      }
      
      // 更新当前选中策略
      strategyStore.setCurrentSelectedStrategy(mergedStrategy)
    }
  } catch (error) {
    console.error('保存策略失败:', error)
    
    // 通知子组件保存失败
    strategyEditorRef.value?.onSaveComplete(false, error.message || '保存失败')
    
    // 不再抛出错误，因为已经在子组件中处理了
  }
}

const handleUpdateStrategy = (strategy: Strategy) => {
  // 更新当前策略的数据，而不是替换整个策略对象
  const currentStrategy = strategyStore.currentSelectedStrategy
  if (currentStrategy) {
    // 合并更新的数据到当前策略
    const updatedStrategy = { ...currentStrategy, ...strategy }
    strategyStore.setCurrentSelectedStrategy(updatedStrategy)
    
    // 如果策略有ID，说明是现有策略，调用updateStrategy
    if (updatedStrategy.id && !isNewStrategy(updatedStrategy)) {
      strategyStore.updateStrategy(String(updatedStrategy.id), updatedStrategy)
    }
  } else {
    // 如果没有当前策略，直接设置
    strategyStore.setCurrentSelectedStrategy(strategy)
  }
}
</script>

<style scoped>
.strategy-workshop {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 固定的页面头部 */
.workshop-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 可滚动的内容区域 */
.workshop-content {
  flex: 1;
  overflow: hidden;
  margin: 0 0 -24px 0;
  padding: 24px 0 24px 0; /* 右侧padding改为0，让内容区卡片与顶部标题对齐 */
}

.workshop-layout {
  display: flex;
  height: 100%; /* 关键：撑满父容器的高度 */
  gap: var(--space-workshop-gap) !important; /* 栏间距 - 使用!important确保优先级 */
}

.workshop-left-panel {
  width: 320px; /* 固定宽度 */
  flex-shrink: 0;
  overflow-y: auto; /* 关键：左侧独立滚动 */
}

.workshop-right-panel {
  flex: 1; /* 占据剩余宽度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 关键：右侧本身不滚动 */
}
</style>