// src/api/types/strategy.ts

import type { BuyFactorInstance, SellFactorInstance } from './factors';

// 策略接口
export interface Strategy {
  id?: string;
  name: string;
  description?: string;
  create_time?: string | null;
  update_time?: string | null;
  author?: string;
  is_public: boolean;
  buy_factors: BuyFactorInstance[];
  sell_factors: SellFactorInstance[];
  parameters: Record<string, any>;
  tags?: string[];
  content?: string;
  umpire_rules?: any[];
}

// 创建策略请求接口
export interface CreateStrategyRequest {
  name: string;
  description?: string;
  is_public?: boolean;
  buy_factors?: BuyFactorInstance[];
  sell_factors?: SellFactorInstance[];
  parameters?: Record<string, any>;
  tags?: string[];
}

// 更新策略请求接口
export interface UpdateStrategyRequest extends CreateStrategyRequest {
  id?: string;
}

// 策略响应接口
export interface StrategyResponse {
  success: boolean;
  data: Strategy;
  message?: string;
}

export interface StrategiesResponse {
  success: boolean;
  data: Strategy[];
  total: number;
  page: number;
  page_size: number;
  message?: string;
}

// 策略查询参数
export interface StrategyQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  author?: string;
  is_public?: boolean;
  tags?: string[];
  sort_by?: 'name' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

// 策略删除响应
export interface StrategyDeleteResponse {
  success: boolean;
  message?: string;
}