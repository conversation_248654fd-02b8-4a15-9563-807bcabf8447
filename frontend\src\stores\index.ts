import { createPinia } from 'pinia'

// 导出所有Store模块
export { useAppStore } from './modules/useAppStore'
export { useBacktestStore } from './modules/useBacktestStore'
export { useDashboardStore } from './modules/useDashboardStore'
export { useFactorsStore } from './modules/useFactorsStore'
export { useStrategyEditorStore } from './modules/useStrategyEditorStore'
export { useStrategyStore } from './modules/useStrategyStore'

const pinia = createPinia()

export default pinia