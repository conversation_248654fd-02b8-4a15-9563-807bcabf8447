// Factors API Extended 测试 - 扩展业务逻辑测试
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { factorsApi } from '../../../src/api/factors';
import { FactorsDataFactory } from '../../factories/FactorsDataFactory';

const factory = new FactorsDataFactory();

// 扩展Mock处理器
const extendedHandlers = [
  // 分页测试处理器
  http.get('/api/factors', ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const category = url.searchParams.get('category');
    const search = url.searchParams.get('search');
    
    // 模拟大数据集
    const totalFactors = 150;
    const factors = Array.from({ length: limit }, (_, i) => 
      factory.createFactor({
        id: `factor-${page}-${i + 1}`,
        name: search ? `搜索因子-${search}-${i + 1}` : `因子-${page}-${i + 1}`,
        category: category || '买入因子'
      })
    );
    
    return HttpResponse.json({
      success: true,
      data: factors,
      total: totalFactors,
      page,
      limit,
      hasMore: page * limit < totalFactors
    });
  }),

  // 分类筛选处理器
  http.get('/api/factors/categories', () => {
    return HttpResponse.json({
      success: true,
      data: [
        { id: 'buy', name: '买入因子', count: 45 },
        { id: 'sell', name: '卖出因子', count: 38 },
        { id: 'select', name: '选股因子', count: 42 },
        { id: 'timing', name: '择时因子', count: 25 }
      ]
    });
  }),

  // 因子搜索处理器
  http.get('/api/factors/search', ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q');
    const category = url.searchParams.get('category');
    
    if (!query) {
      return HttpResponse.json(
        { success: false, error: '搜索关键词不能为空' },
        { status: 400 }
      );
    }
    
    const results = Array.from({ length: 5 }, (_, i) => 
      factory.createFactor({
        id: `search-${i + 1}`,
        name: `${query}相关因子-${i + 1}`,
        category: category || '买入因子'
      })
    );
    
    return HttpResponse.json({
      success: true,
      data: results,
      total: results.length,
      query,
      category
    });
  }),

  // 批量操作处理器
  http.post('/api/factors/batch', async ({ request }) => {
    const { action, factorIds } = await request.json() as {
      action: 'delete' | 'export' | 'copy';
      factorIds: string[];
    };
    
    if (!factorIds || factorIds.length === 0) {
      return HttpResponse.json(
        { success: false, error: '请选择要操作的因子' },
        { status: 400 }
      );
    }
    
    if (factorIds.length > 50) {
      return HttpResponse.json(
        { success: false, error: '批量操作最多支持50个因子' },
        { status: 400 }
      );
    }
    
    return HttpResponse.json({
      success: true,
      message: `成功${action === 'delete' ? '删除' : action === 'export' ? '导出' : '复制'}${factorIds.length}个因子`,
      processedCount: factorIds.length
    });
  }),

  // 因子验证处理器
  http.post('/api/factors/validate', async ({ request }) => {
    const factorData = await request.json();
    
    // 模拟验证延迟
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const validationErrors = [];
    
    if (!factorData.name || factorData.name.length < 2) {
      validationErrors.push('因子名称至少需要2个字符');
    }
    
    if (!factorData.parameters || Object.keys(factorData.parameters).length === 0) {
      validationErrors.push('因子参数不能为空');
    }
    
    if (factorData.name === '重复因子名称') {
      validationErrors.push('因子名称已存在');
    }
    
    return HttpResponse.json({
      success: validationErrors.length === 0,
      errors: validationErrors,
      warnings: validationErrors.length === 0 ? [] : ['建议优化参数配置']
    });
  })
];

const server = setupServer(...extendedHandlers);

describe('Factors API Extended Tests', () => {
  beforeEach(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('分页功能测试', () => {
    it('应该正确处理分页参数', async () => {
      const result = await factorsApi.getFactors({ page: 2, limit: 20 });
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(20);
      expect(result.page).toBe(2);
      expect(result.limit).toBe(20);
      expect(result.total).toBe(150);
      expect(result.hasMore).toBe(true);
    });

    it('应该正确计算是否有更多数据', async () => {
      const result = await factorsApi.getFactors({ page: 15, limit: 10 });
      
      expect(result.success).toBe(true);
      expect(result.hasMore).toBe(false); // 15 * 10 = 150, 已到最后一页
    });

    it('应该处理无效的分页参数', async () => {
      const result = await factorsApi.getFactors({ page: 0, limit: -1 });
      
      // API应该自动修正为默认值
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
    });
  });

  describe('分类筛选测试', () => {
    it('应该获取因子分类列表', async () => {
      const result = await factorsApi.getCategories();
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(4);
      expect(result.data[0]).toHaveProperty('id');
      expect(result.data[0]).toHaveProperty('name');
      expect(result.data[0]).toHaveProperty('count');
    });

    it('应该按分类筛选因子', async () => {
      const result = await factorsApi.getFactors({ category: 'buy' });
      
      expect(result.success).toBe(true);
      expect(result.data.every(factor => factor.category === '买入因子')).toBe(true);
    });
  });

  describe('搜索功能测试', () => {
    it('应该支持关键词搜索', async () => {
      const result = await factorsApi.searchFactors({ query: 'MA均线' });
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(5);
      expect(result.query).toBe('MA均线');
      expect(result.data.every(factor => factor.name.includes('MA均线'))).toBe(true);
    });

    it('应该支持分类+搜索组合', async () => {
      const result = await factorsApi.searchFactors({ 
        query: '突破', 
        category: 'buy' 
      });
      
      expect(result.success).toBe(true);
      expect(result.category).toBe('buy');
      expect(result.data.every(factor => 
        factor.name.includes('突破') && factor.category === '买入因子'
      )).toBe(true);
    });

    it('应该处理空搜索关键词', async () => {
      await expect(factorsApi.searchFactors({ query: '' }))
        .rejects.toThrow('搜索关键词不能为空');
    });
  });

  describe('批量操作测试', () => {
    it('应该支持批量删除', async () => {
      const factorIds = ['factor-1', 'factor-2', 'factor-3'];
      const result = await factorsApi.batchOperation({
        action: 'delete',
        factorIds
      });
      
      expect(result.success).toBe(true);
      expect(result.processedCount).toBe(3);
      expect(result.message).toContain('删除');
    });

    it('应该支持批量导出', async () => {
      const factorIds = ['factor-1', 'factor-2'];
      const result = await factorsApi.batchOperation({
        action: 'export',
        factorIds
      });
      
      expect(result.success).toBe(true);
      expect(result.processedCount).toBe(2);
      expect(result.message).toContain('导出');
    });

    it('应该限制批量操作数量', async () => {
      const factorIds = Array.from({ length: 51 }, (_, i) => `factor-${i}`);
      
      await expect(factorsApi.batchOperation({
        action: 'delete',
        factorIds
      })).rejects.toThrow('批量操作最多支持50个因子');
    });

    it('应该验证批量操作参数', async () => {
      await expect(factorsApi.batchOperation({
        action: 'delete',
        factorIds: []
      })).rejects.toThrow('请选择要操作的因子');
    });
  });

  describe('因子验证测试', () => {
    it('应该验证有效的因子数据', async () => {
      const factorData = factory.createFactorDefinition({
        name: '有效因子',
        parameters: { period: 20, threshold: 0.05 }
      });
      
      const result = await factorsApi.validateFactor(factorData);
      
      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测因子名称重复', async () => {
      const factorData = factory.createFactorDefinition({
        name: '重复因子名称',
        parameters: { period: 20 }
      });
      
      const result = await factorsApi.validateFactor(factorData);
      
      expect(result.success).toBe(false);
      expect(result.errors).toContain('因子名称已存在');
    });

    it('应该验证因子名称长度', async () => {
      const factorData = factory.createFactorDefinition({
        name: 'A', // 太短
        parameters: { period: 20 }
      });
      
      const result = await factorsApi.validateFactor(factorData);
      
      expect(result.success).toBe(false);
      expect(result.errors).toContain('因子名称至少需要2个字符');
    });

    it('应该验证因子参数完整性', async () => {
      const factorData = factory.createFactorDefinition({
        name: '测试因子',
        parameters: {} // 空参数
      });
      
      const result = await factorsApi.validateFactor(factorData);
      
      expect(result.success).toBe(false);
      expect(result.errors).toContain('因子参数不能为空');
    });
  });

  describe('性能和边界测试', () => {
    it('应该处理大量因子数据', async () => {
      const startTime = Date.now();
      const result = await factorsApi.getFactors({ limit: 100 });
      const endTime = Date.now();
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(1000); // 应在1秒内完成
    });

    it('应该处理网络超时', async () => {
      // 模拟超时
      server.use(
        http.get('/api/factors', async () => {
          await new Promise(resolve => setTimeout(resolve, 10000));
          return HttpResponse.json({ success: true, data: [] });
        })
      );
      
      await expect(factorsApi.getFactors({ timeout: 1000 }))
        .rejects.toThrow(/timeout|超时/);
    });

    it('应该处理并发请求', async () => {
      const promises = Array.from({ length: 5 }, (_, i) => 
        factorsApi.getFactors({ page: i + 1 })
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      expect(results.every(result => result.success)).toBe(true);
    });
  });

  describe('数据契约验证', () => {
    it('应该验证因子数据结构', async () => {
      const result = await factorsApi.getFactors();
      
      expect(result.success).toBe(true);
      expect(result.data).toBeInstanceOf(Array);
      
      if (result.data.length > 0) {
        const factor = result.data[0];
        expect(factor).toHaveProperty('id');
        expect(factor).toHaveProperty('name');
        expect(factor).toHaveProperty('category');
        expect(factor).toHaveProperty('description');
        expect(factor).toHaveProperty('parameters');
        expect(factor).toHaveProperty('created_at');
      }
    });

    it('应该验证分页响应结构', async () => {
      const result = await factorsApi.getFactors({ page: 1, limit: 10 });
      
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('total');
      expect(result).toHaveProperty('page');
      expect(result).toHaveProperty('limit');
      expect(result).toHaveProperty('hasMore');
      
      expect(typeof result.total).toBe('number');
      expect(typeof result.page).toBe('number');
      expect(typeof result.limit).toBe('number');
      expect(typeof result.hasMore).toBe('boolean');
    });
  });
});