<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="600px"
    @close="handleCloseDialog"
  >
    <div class="exclusive-sell-rules-content">
      <!-- 买入因子信息展示 -->
      <div v-if="buyFactor" class="buy-factor-info">
        <div class="factor-card">
          <div class="factor-header">
            <h4 class="factor-name">{{ buyFactor.name }}</h4>
            <el-tag type="success">买入因子</el-tag>
          </div>
          <div class="factor-description">
            为该买入因子配置专属的卖出规则，这些规则仅在通过此因子进入持仓时生效。
          </div>
        </div>
      </div>

      <!-- 专属卖出因子列表 -->
      <div class="exclusive-sell-factors-section">
        <div class="section-header">
          <h4>专属卖出规则</h4>
          <el-button 
            type="primary" 
            size="default"
            data-testid="add-sell-factor-btn"
            @click="handleAddSellFactor"
          >
            添加卖出因子
          </el-button>
        </div>
        
        <div 
          v-if="exclusiveSellFactors.length > 0" 
          class="sell-factors-list"
          data-testid="exclusive-sell-factors-list"
        >
          <el-card 
            v-for="(sellFactor, index) in exclusiveSellFactors"
            :key="sellFactor.id || index"
            class="sell-factor-card"
          >
            <div class="sell-factor-header">
              <span class="sell-factor-title">{{ sellFactor.name }}</span>
              <div class="sell-factor-actions">
                <el-button 
                  size="small" 
                  type="primary" 
                  plain
                  @click="handleEditSellFactor(index, sellFactor)"
                >
                  编辑
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  plain
                  @click="handleRemoveSellFactor(index)"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div class="sell-factor-params">
              <div v-if="hasSellFactorParameters(sellFactor)" class="param-list">
                <div 
                  v-for="[key, value] in Object.entries(getSellFactorParameters(sellFactor))" 
                  :key="key"
                  class="param-item"
                >
                  <span class="param-name">{{ key }}:</span>
                  <span class="param-value">{{ formatParameterValue(value) }}</span>
                </div>
              </div>
              <div v-else class="no-params">无参数</div>
            </div>
          </el-card>
        </div>
        
        <div v-else class="empty-sell-factors">
          <p>暂无专属卖出规则，点击上方按钮添加。</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" @click="handleSaveChanges">确定</el-button>
      </div>
    </template>

    <!-- 卖出因子选择对话框 -->
    <StrategyFormDialog
      v-model="isFactorSelectionDialogVisible"
      :current-factor-type="'sell'"
      :editing-type="editingType"
      :editing-index="editingIndex"
      @save="handleFactorSelected"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { formatParameterValue } from '@/utils/factorUtils'
import StrategyFormDialog from '@/components/forms/StrategyFormDialog.vue'

interface BuyFactor {
  id: string
  name: string
  type: string
  params: Record<string, any>
  sell_factors?: any[]
}

interface SellFactor {
  id: string
  name: string
  type: string
  params: Record<string, any>
  exclusive_to_buy_factor?: string
}

interface Props {
  visible: boolean
  buyFactor: BuyFactor
  title: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'update:exclusive-sell-rules', buyFactorId: string, sellFactors: SellFactor[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部状态
const exclusiveSellFactors = ref<SellFactor[]>([])
const isFactorSelectionDialogVisible = ref(false)
const editingType = ref<'add' | 'edit' | null>(null)
const editingIndex = ref<number | null>(null)

// 监听 props.buyFactor 变化并初始化数据

// 初始化专属卖出因子列表
const initializeExclusiveSellFactors = () => {
  exclusiveSellFactors.value = Array.isArray(props.buyFactor.sell_factors) 
    ? [...props.buyFactor.sell_factors] 
    : []
}

// 监听 buyFactor 变化，重新初始化
watch(() => props.buyFactor, (newBuyFactor) => {
  if (newBuyFactor) {
    initializeExclusiveSellFactors()
  }
}, { immediate: true })

// 监听 visible 变化，确保对话框打开时数据是最新的
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initializeExclusiveSellFactors()
  }
})

// 工具函数
const hasSellFactorParameters = (factor: SellFactor) => {
  return factor.params && Object.keys(factor.params).length > 0
}

const getSellFactorParameters = (factor: SellFactor) => {
  return factor.params || {}
}

// 事件处理
const handleCloseDialog = () => {
  emit('update:visible', false)
}

const handleAddSellFactor = () => {
  editingType.value = 'add'
  editingIndex.value = null
  isFactorSelectionDialogVisible.value = true
}

const handleEditSellFactor = (index: number, sellFactor: SellFactor) => {
  editingType.value = 'edit'
  editingIndex.value = index
  isFactorSelectionDialogVisible.value = true
}

const handleRemoveSellFactor = (index: number) => {
  exclusiveSellFactors.value.splice(index, 1)
}

const handleFactorSelected = (factorConfig: SellFactor) => {
  // 为卖出因子添加专属标记
  const sellFactorWithExclusiveMarker = {
    ...factorConfig,
    exclusive_to_buy_factor: props.buyFactor.id
  }

  if (editingType.value === 'edit' && editingIndex.value !== null) {
    // 编辑模式：替换现有因子
    exclusiveSellFactors.value[editingIndex.value] = sellFactorWithExclusiveMarker
  } else {
    // 添加模式：新增因子
    exclusiveSellFactors.value.push(sellFactorWithExclusiveMarker)
  }
  
  // 关闭因子选择对话框
  isFactorSelectionDialogVisible.value = false
  editingType.value = null
  editingIndex.value = null
}

const handleSaveChanges = () => {
  // 发出专属卖出规则更新事件
  emit('update:exclusive-sell-rules', props.buyFactor.id, exclusiveSellFactors.value)
  handleCloseDialog()
}
</script>

<style scoped>
.exclusive-sell-rules-content {
  padding: 10px 0;
}

.buy-factor-info {
  margin-bottom: 20px;
}

.factor-card {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.factor-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.factor-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.exclusive-sell-factors-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.sell-factors-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sell-factor-card {
  border: 1px solid #e4e7ed;
}

.sell-factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sell-factor-title {
  font-weight: 600;
  color: #303133;
}

.sell-factor-actions {
  display: flex;
  gap: 8px;
}

.sell-factor-params {
  padding: 8px 4px;
}

.param-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.param-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.param-name {
  color: #606266;
  font-weight: 500;
}

.param-value {
  color: #303133;
}

.no-params {
  color: #909399;
  font-size: 13px;
}

.empty-sell-factors {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
