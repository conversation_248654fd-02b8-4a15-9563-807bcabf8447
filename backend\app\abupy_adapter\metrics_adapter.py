from abupy.MetricsBu.ABuMetricsBase import AbuMetricsBase

def calculate_metrics(orders_pd, action_pd, capital, benchmark):
    """
    调用abupy的Metrics模块来计算回测结果的性能指标。

    :param orders_pd: 交易订单DataFrame
    :param action_pd: 交易行为DataFrame
    :param capital: 资金对象
    :param benchmark: 基准对象
    :return: 包含性能指标的字典
    """
    metrics = AbuMetricsBase(orders_pd, action_pd, capital, benchmark)
    metrics.fit_metrics()

    # 从metrics对象中提取关键指标并返回
    result = {
        "algorithm_period_returns": metrics.algorithm_period_returns,
        "benchmark_period_returns": metrics.benchmark_period_returns,
        "algorithm_annualized_returns": metrics.algorithm_annualized_returns,
        "benchmark_annualized_returns": metrics.benchmark_annualized_returns,
        "algorithm_sharpe": metrics.algorithm_sharpe,
        "benchmark_sharpe": metrics.benchmark_sharpe,
        "alpha": metrics.alpha,
        "beta": metrics.beta,
        "max_drawdown": metrics.max_drawdown,
        "cash_utilization": metrics.cash_utilization,
        # 更多指标可以根据需要添加
    }
    return result
