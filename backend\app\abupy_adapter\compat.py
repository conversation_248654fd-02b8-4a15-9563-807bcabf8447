# -*- coding: utf-8 -*-
"""
abu兼容性模块，处理abu原有代码与现代Python版本的兼容性问题
"""
import sys
import collections
import collections.abc
import logging
import importlib.util

# 设置日志格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 为collections模块打补丁，使较老版本的abu代码能在现代Python环境中运行
if not hasattr(collections, 'Iterable'):
    collections.Iterable = collections.abc.Iterable

# 添加其他常用的collections对象兼容性
if not hasattr(collections, 'Mapping'):
    collections.Mapping = collections.abc.Mapping
    
if not hasattr(collections, 'MutableMapping'):
    collections.MutableMapping = collections.abc.MutableMapping

# 模拟IPython模块
# 先尝试导入我们的mock模块
try:
    from .mock_ipython import display, clear_output
    # 将IPython模块加入到sys.modules中，这样当其他模块导入IPython时会使用我们的模拟版本
    sys.modules['IPython'] = type('IPython', (), {})
    sys.modules['IPython.display'] = type('display', (), {'display': display, 'clear_output': clear_output})
    logging.info("使用模拟IPython模块")
except ImportError:
    logging.warning("导入模拟IPython模块失败")

# 模拟ipywidgets模块
try:
    from .mock_widgets import FloatProgress, Text, Box, HBox, VBox, Button, Label, Layout
    # 创建模拟的ipywidgets模块
    mock_ipywidgets = type('ipywidgets', (), {
        'FloatProgress': FloatProgress,
        'Text': Text,
        'Box': Box,
        'HBox': HBox,
        'VBox': VBox,
        'Button': Button,
        'Label': Label,
        'Layout': Layout
    })
    # 将模拟模块加入到sys.modules中
    sys.modules['ipywidgets'] = mock_ipywidgets
    logging.info("使用模拟ipywidgets模块")
except ImportError:
    logging.warning("导入模拟ipywidgets模块失败")

# 导入并应用SciPy兼容性补丁
try:
    # 先检查原始SciPy是否存在interp
    import scipy
    has_old_interp = hasattr(scipy, 'interp')
    
    if not has_old_interp:
        # 引入我们的兼容性函数
        from .scipy_compat import interp
        # 添加到scipy模块
        scipy.interp = interp
        logging.info("已应用SciPy interp函数兼容性补丁")
except ImportError as e:
    logging.error(f"SciPy兼容性补丁应用失败: {str(e)}")

def apply_patches():
    """应用所有兼容性补丁"""
    # 在这里可以添加更多的补丁应用逻辑
    logging.info("已应用兼容性补丁")
    
    # 检查并输出已应用的补丁
    patches = []
    if 'IPython' in sys.modules:
        patches.append("IPython")
    if 'ipywidgets' in sys.modules:
        patches.append("ipywidgets")
    if hasattr(collections, 'Iterable') and collections.Iterable is collections.abc.Iterable:
        patches.append("collections.Iterable")
    
    # 检查scipy补丁
    try:
        import scipy
        if hasattr(scipy, 'interp'):
            patches.append("scipy.interp")
    except ImportError:
        pass
    
    logging.info(f"已应用的补丁: {', '.join(patches)}")
    return patches
