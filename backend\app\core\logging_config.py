"""日志配置模块"""
import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional


class LoggerConfig:
    """日志配置类"""
    
    def __init__(self, 
                 name: str = "abu_modern",
                 level: str = "INFO",
                 log_dir: Optional[str] = None,
                 max_bytes: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5):
        self.name = name
        self.level = getattr(logging, level.upper())
        self.log_dir = Path(log_dir) if log_dir else Path("logs")
        self.max_bytes = max_bytes
        self.backup_count = backup_count
        
        # 确保日志目录存在
        self.log_dir.mkdir(exist_ok=True)
    
    def setup_logger(self) -> logging.Logger:
        """设置并返回配置好的logger"""
        logger = logging.getLogger(self.name)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
            
        logger.setLevel(self.level)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器（应用日志）
        app_log_file = self.log_dir / "app.log"
        file_handler = RotatingFileHandler(
            app_log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(self.level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = self.log_dir / "error.log"
        error_handler = RotatingFileHandler(
            error_log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)
        
        return logger


# 全局logger实例
_logger_config = LoggerConfig()
app_logger = _logger_config.setup_logger()


def get_logger(name: str = None) -> logging.Logger:
    """获取logger实例"""
    if name:
        return logging.getLogger(f"{_logger_config.name}.{name}")
    return app_logger


def setup_service_logger(service_name: str) -> logging.Logger:
    """为服务设置专用logger"""
    logger_name = f"{_logger_config.name}.service.{service_name}"
    logger = logging.getLogger(logger_name)
    
    if not logger.handlers:
        logger.setLevel(_logger_config.level)
        
        # 继承父logger的配置
        logger.parent = app_logger
        
        # 服务专用日志文件
        service_log_file = _logger_config.log_dir / f"{service_name}.log"
        service_handler = RotatingFileHandler(
            service_log_file,
            maxBytes=_logger_config.max_bytes,
            backupCount=_logger_config.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        service_handler.setFormatter(formatter)
        logger.addHandler(service_handler)
    
    return logger