<template>
  <router-view />
</template>

<style>
/* 全局样式应用 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-color-base);
  color: var(--text-color-primary);
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  background-color: var(--bg-color-base);
}

/* 确保Element Plus组件使用我们的设计令牌 */
.el-main {
  background-color: var(--bg-color-base);
  padding: 20px;
}

.el-aside {
  background-color: var(--bg-color-card);
  border-right: 1px solid var(--border-color-split);
}

.el-header {
  background-color: var(--bg-color-card);
  border-bottom: 1px solid var(--border-color-split);
  box-shadow: var(--shadow-light);
}

.el-footer {
  background-color: var(--bg-color-card);
  border-top: 1px solid var(--border-color-split);
}
</style>
