    工作日志 #0 - 项目启动与初步方案确立
日志ID：
日期： 2025-05-22

项目名称： abu 量化投资框架现代化改造 (Vue3 + FastAPI)

总体目标： 将现有的本地 Python 量化投资框架 abu 改造为一个前后端分离的现代化 Web 应用，后端采用 FastAPI，前端采用 Vue3。

核心参与者/工具：
开发者： ccxx
主要辅助工具： Winsurf .智能编程助手 (及其他 AI 编程助手)
当前阶段： 项目启动与方案规划

已完成工作与讨论摘要：
项目背景与动机：
abu 是一个本地的 Python 量化投资框架，功能丰富但技术栈较旧，缺乏现代化的用户界面和 API 接口。
目标是利用 AI 辅助，将其改造为 Vue3 + FastAPI 架构，提升易用性、可维护性和扩展性。
AI 辅助策略选择：
经过讨论，决定采用借鉴现有开源项目 (abu) 并利用 AI 进行现代化改造的策略，而非完全从零开始由 AI 生成所有代码。
理由： 充分利用 abu 已有的核心量化逻辑沉淀，降低从零构建复杂金融系统的风险和难度，让 AI 更专注于其擅长的架构迁移、代码生成和 UI 实现等具体任务。可以更快获得可用原型 (MVP)。
初步改造方案（由 AI 分析 abu 后建议）：
后端 (FastAPI)：
保留 abu 核心量化算法、因子模块、交易模块等。
模块化封装核心功能，通过 abupy_adapter 与新 FastAPI 架构对接。
新增 tushare 作为主要数据源：设计数据源抽象层，优先使用 tushare（付费账户）作为标准化数据接口，同时保留 abu 原有数据获取能力作为备选。
规划了清晰的目录结构 (app/api/endpoints, app/core, app/models/schemas, app/services, app/utils, app/abupy_adapter, app/datasources)。
设计了核心 API 接口，包括：
/api/market (市场数据)
/api/strategy (策略管理)
/api/backtest (回测)
/api/portfolio (投资组合)
/api/analytics (分析)
/api/realtime (实时数据 - WebSocket)
前端 (Vue3)：
技术选型：Vue3 + TypeScript, Pinia, Vite, UI 组件库 (Element Plus/Naive UI), 图表库 (ECharts/TradingView)。
规划了清晰的目录结构 (src/api, src/components, src/layouts, src/router, src/stores, src/views)。
设计了核心页面，包括：仪表盘、市场数据、策略管理、回测分析、实时监控、投资组合。
前后端交互 API 设计样例：
提供了市场数据、策略管理、回测 API 的具体端点和方法 (GET, POST, PUT, DELETE)。
WebSocket 实时数据规划： /ws/market, /ws/signals。
提示词策略：
将大任务分解为小任务。
向 AI 提供清晰的上下文和目标。
引用之前 AI 的方案进行提问。
逐步迭代，对 AI 生成的代码进行审查和反馈。
在需要时提供 abu 的具体代码片段或函数签名信息。
下一步计划 (短期 - 即将开始)：
后端现代化 - 阶段一：环境准备与项目结构初始化
任务： 指导 AI新建abu_modern作为项目文件夹，在根目录下创建 backend 文件夹，并按照方案生成 FastAPI 的基础目录结构和初始文件 (如 main.py, app 子目录结构, requirements.txt 等)。

预期成果： 一个基本的、可运行（即使是空的）FastAPI 项目骨架。

数据源集成 - tushare实现：
任务：

设计数据源抽象接口(DataSourceInterface)
实现tushare数据源适配器(TushareDataSource)
实现abu原有数据源适配器(AbuDataSource)
创建数据源工厂(DataSourceFactory)实现数据源的动态选择
设计配置系统允许用户选择默认数据源
预期成果： 一个灵活的数据获取系统，能够优先使用tushare作为数据源，同时保留abu原有功能作为备选。

后端现代化 - 阶段一：核心功能模块改造 (以市场数据为例)
任务：

指导 AI 在 backend/app/services/ 中创建 market_service.py，使用新设计的数据源抽象层获取市场数据
指导 AI 在 backend/app/schemas/ (或 models/) 中创建对应的 Pydantic schema (如 SymbolResponse, KlineResponse)
指导 AI 在 backend/app/api/endpoints/ 中创建 market.py，实现对应的 FastAPI 路由和接口
将新的路由注册到主路由中
预期成果： 第一个可用的后端 API 接口 (/api/market/symbols, /api/market/kline/{symbol})，能够从tushare或abu获取数据。

长期愿景（回顾）：
完成整个 abu 框架的现代化改造，使其成为一个功能强大、易于使用和扩展的 Vue3 + FastAPI 量化投资平台。

风险与关注点：
准确理解和转换 abu 的核心逻辑，特别是在 abupy_adapter 层。
AI 对复杂量化概念的理解可能存在偏差，需要仔细审查和测试其生成的代码。
abu 可能存在的旧版本依赖与新环境的兼容性问题。
tushare API 与 abu 数据结构的映射：需要仔细设计数据转换层，确保 tushare 返回的数据能够正确转换为 abu 所需的格式。
tushare API 限额管理：作为付费用户，需要合理使用 API 配额，设计缓存策略减少重复请求。
多数据源一致性：确保不同数据源返回的数据格式一致，以便系统其他部分无需关心具体使用的是哪个数据源。
