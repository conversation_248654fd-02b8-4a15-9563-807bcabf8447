<template>
  <div class="sell-factor-card">
    <el-card class="factor-item-card is-hover-shadow">
      <div class="factor-card-header">
        <span class="factor-title">{{ getFriendlyFactorName(sellFactor) }}</span>
        <div class="factor-actions">
          <el-button 
            size="small" 
            type="primary" 
            plain
            @click="handleEditFactor"
          >
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            plain
            data-testid="remove-sell-factor"
            @click="handleDeleteFactor"
          >
            删除
          </el-button>
        </div>
      </div>
      <div class="factor-params">
        <div v-if="hasParameters(sellFactor)" class="param-list">
          <div 
            v-for="[key, value] in Object.entries(getFactorParameters(sellFactor))" 
            :key="key"
            class="param-item"
          >
            <span class="param-name">{{ key }}:</span>
            <span class="param-value">{{ formatParameterValue(value) }}</span>
          </div>
        </div>
        <div v-else class="no-params">无参数</div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { 
  getFriendlyFactorName, 
  getFactorParameters, 
  formatParameterValue,
  hasParameters 
} from '@/utils/factorUtils'
import type { SellFactor } from '@/types/factor'

interface Props {
  sellFactor: SellFactor
}

interface Emits {
  (e: 'edit'): void
  (e: 'delete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 事件处理
const handleEditFactor = () => {
  emit('edit')
}

const handleDeleteFactor = () => {
  emit('delete')
}
</script>

<style scoped>
@import '@/styles/components/_factor-card.scss';

.sell-factor-card {
  width: 100%;
}
</style>
