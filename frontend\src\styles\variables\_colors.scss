// 设计令牌 - 颜色变量定义

// ===== 主色调系统 =====
$primary-color: #1890ff;
$primary-light: #40a9ff;
$primary-lighter: #69c0ff;
$primary-dark: #096dd9;
$primary-darker: #0050b3;

// ===== 功能色系统 =====
$success-color: #52c41a;
$success-light: #73d13d;
$success-dark: #389e0d;

$warning-color: #faad14;
$warning-light: #ffc53d;
$warning-dark: #d48806;

$danger-color: #ff4d4f;
$danger-light: #ff7875;
$danger-dark: #cf1322;

$info-color: #1890ff;
$info-light: #40a9ff;
$info-dark: #096dd9;

// ===== 文本色系统 =====
$text-color-primary: #262626;    // 主要文本
$text-color-secondary: #595959;  // 次要文本
$text-color-placeholder: #bfbfbf; // 占位符文本
$text-color-disabled: #d9d9d9;   // 禁用文本
$text-color-inverse: #ffffff;    // 反色文本
$text-disabled: $text-color-disabled; // 禁用文本色（兼容旧代码）
$text-primary: $text-color-primary;   // 主要文本色（兼容旧代码）
$text-secondary: $text-color-secondary; // 次要文本色（兼容旧代码）
$text-inverse: $text-color-inverse;   // 反色文本（兼容旧代码）

// ===== 背景色系统 =====
$bg-color-base: #f7f8fa;         // 页面主背景色（极浅灰）
$bg-color-card: #ffffff;         // 卡片背景色（纯白）
$bg-color-container: #fafafa;    // 容器背景色
$bg-color-hover: #f5f5f5;        // 悬停背景色
$bg-color-active: #e6f7ff;       // 激活背景色
$bg-color-dark: #001529;         // 深色背景
$bg-tertiary: $bg-color-hover;   // 第三级背景色（兼容旧代码）
$bg-primary: $bg-color-base;     // 主要背景色（兼容旧代码）
$bg-secondary: $bg-color-container; // 次要背景色（兼容旧代码）
$bg-dark: $bg-color-dark;        // 深色背景（兼容旧代码）

// 功能色（兼容旧代码）
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #1890ff;
$profit-color: #52c41a;
$loss-color: #ff4d4f;
$neutral-color: #8c8c8c;

// 边框色
$border-color-base: #d9d9d9;     // 基础边框色
$border-color-light: #f0f0f0;    // 浅色边框
$border-color-split: #f0f0f0;    // 分割线颜色
$border-color-dark: #434343;     // 深色边框
$border-color: $border-color-base; // 默认边框色（兼容旧代码）

// ===== 阴影系统 =====
$shadow-light: rgba(0, 0, 0, 0.04);
$shadow-medium: rgba(0, 0, 0, 0.08);
$shadow-dark: rgba(0, 0, 0, 0.16);
$shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
$shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.12);

// ===== CSS自定义属性 (设计令牌) =====
:root {
  // 主色调系统
  --color-primary: #{$primary-color};
  --color-primary-light: #{$primary-light};
  --color-primary-lighter: #{$primary-lighter};
  --color-primary-dark: #{$primary-dark};
  --color-primary-darker: #{$primary-darker};
  
  // 功能色系统
  --color-success: #{$success-color};
  --color-success-light: #{$success-light};
  --color-success-dark: #{$success-dark};
  
  --color-warning: #{$warning-color};
  --color-warning-light: #{$warning-light};
  --color-warning-dark: #{$warning-dark};
  
  --color-danger: #{$danger-color};
  --color-danger-light: #{$danger-light};
  --color-danger-dark: #{$danger-dark};
  
  --color-info: #{$info-color};
  --color-info-light: #{$info-light};
  --color-info-dark: #{$info-dark};
  
  // 文本色系统
  --text-color-primary: #{$text-color-primary};
  --text-color-secondary: #{$text-color-secondary};
  --text-color-placeholder: #{$text-color-placeholder};
  --text-color-disabled: #{$text-color-disabled};
  --text-color-inverse: #{$text-color-inverse};
  
  // 背景色系统
  --bg-color-base: #{$bg-color-base};
  --bg-color-card: #{$bg-color-card};
  --bg-color-container: #{$bg-color-container};
  --bg-color-hover: #{$bg-color-hover};
  --bg-color-active: #{$bg-color-active};
  --bg-color-dark: #{$bg-color-dark};
  
  // 边框色系统
  --border-color-base: #{$border-color-base};
  --border-color-light: #{$border-color-light};
  --border-color-split: #{$border-color-split};
  --border-color-dark: #{$border-color-dark};
  
  // 阴影系统
  --shadow-light: #{$shadow-light};
  --shadow-medium: #{$shadow-medium};
  --shadow-dark: #{$shadow-dark};
  --shadow-card: #{$shadow-card};
  --shadow-elevated: #{$shadow-elevated};
}

// ===== 量化投资特定颜色系统 =====
$profit-color: $success-color;     // 盈利绿色
$profit-light: $success-light;     // 浅盈利绿色
$profit-dark: $success-dark;       // 深盈利绿色

$loss-color: $danger-color;        // 亏损红色
$loss-light: $danger-light;        // 浅亏损红色
$loss-dark: $danger-dark;          // 深亏损红色

$neutral-color: #8c8c8c;           // 中性灰色
$neutral-light: #bfbfbf;           // 浅中性灰色
$neutral-dark: #595959;            // 深中性灰色

// ===== 图表颜色系统 =====
$chart-colors: (
  primary: $primary-color,
  secondary: $success-color,
  tertiary: $warning-color,
  quaternary: $danger-color,
  quinary: #722ed1,
  senary: #13c2c2,
  septenary: #eb2f96,
  octonary: #52c41a
);

// ===== 量化投资CSS自定义属性 =====
:root {
  // 量化投资特定颜色
  --color-profit: #{$profit-color};
  --color-profit-light: #{$profit-light};
  --color-profit-dark: #{$profit-dark};
  
  --color-loss: #{$loss-color};
  --color-loss-light: #{$loss-light};
  --color-loss-dark: #{$loss-dark};
  
  --color-neutral: #{$neutral-color};
  --color-neutral-light: #{$neutral-light};
  --color-neutral-dark: #{$neutral-dark};
  
  // 图表颜色
  --chart-color-primary: #{map-get($chart-colors, primary)};
  --chart-color-secondary: #{map-get($chart-colors, secondary)};
  --chart-color-tertiary: #{map-get($chart-colors, tertiary)};
  --chart-color-quaternary: #{map-get($chart-colors, quaternary)};
  --chart-color-quinary: #{map-get($chart-colors, quinary)};
  --chart-color-senary: #{map-get($chart-colors, senary)};
  --chart-color-septenary: #{map-get($chart-colors, septenary)};
  --chart-color-octonary: #{map-get($chart-colors, octonary)};
  
  // 背景色变体
  --bg-color-profit: #f6ffed;
  --bg-color-profit-light: #fcffe6;
  --bg-color-loss: #fff2f0;
  --bg-color-loss-light: #fff1f0;
  --bg-color-neutral: #{$bg-color-container};
}