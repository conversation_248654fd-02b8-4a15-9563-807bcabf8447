import pandas as pd
import logging
from app.schemas.market import KlineData
from abupy.IndicatorBu.ABuNDAtr import atr21, atr14 # <--- 导入abupy官方的ATR计算函数

def convert_kline_data_to_abu_df(kline_data: KlineData, symbol: str) -> pd.DataFrame | None:
    """
    [最终稳定版 V20.0] 一个统一的、健壮的数据适配器。
    采用“后处理增强”方案，将外部KlineData转换为包含所有必需衍生指标的、
    可供abupy安全消费的Pandas DataFrame。
    """
    if not kline_data or not kline_data.data:
        logging.warning(f"传入的KlineData为空或不含数据 for symbol: {symbol}")
        return None

    # 1. 从Pydantic模型转换为基础DataFrame
    df = pd.DataFrame([item.model_dump() for item in kline_data.data])
    if df.empty:
        return df

    # ==================== [核心增强逻辑] ====================

    # 2. 准备必需列
    # 兼容 'vol' -> 'volume'
    if 'vol' in df.columns and 'volume' not in df.columns:
        df.rename(columns={'vol': 'volume'}, inplace=True)
    
    # 计算 'pre_close' (ATR计算必需)
    df['pre_close'] = df['close'].shift(1)
    
    # 确保基础列存在且为数值类型
    base_numeric_cols = ['open', 'high', 'low', 'close', 'pre_close', 'volume']
    for col in base_numeric_cols:
        if col not in df.columns:
            df[col] = 0.0 # 用浮点数0填充
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # 3. [关键] 手动计算并添加衍生指标
    # 用0填充所有可能因转换失败而产生的NaN值，这对于ATR计算至关重要
    df.fillna(0, inplace=True)
    
    if len(df) > 21:
        df['atr21'] = atr21(df['high'], df['low'], df['pre_close'])
    else:
        df['atr21'] = (df['high'] - df['low']).rolling(min(len(df), 21)).mean()
        
    if len(df) > 14:
        df['atr14'] = atr14(df['high'], df['low'], df['pre_close'])
    else:
        df['atr14'] = (df['high'] - df['low']).rolling(min(len(df), 14)).mean()
    
    # 再次填充，因为ATR计算可能会在开头产生NaN
    df.fillna(method='bfill', inplace=True)
    df.fillna(0, inplace=True)

    # 4. 准备abupy最终需要的格式
    df['datetime'] = pd.to_datetime(df['date'])
    df.set_index('datetime', inplace=True)
    df['date'] = df.index.strftime('%Y%m%d').astype(int)
    
    # 添加abupy必需的时间相关列
    df['date_week'] = df.index.dayofweek  # 0=Monday, 6=Sunday
    df['key'] = range(len(df))

    logging.info(f"--- [DATA_ADAPTER] DataFrame for {symbol} 增强完成，最终shape: {df.shape}, columns: {df.columns.tolist()}")
    return df