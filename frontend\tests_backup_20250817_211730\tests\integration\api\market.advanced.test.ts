// Market API Advanced 测试 - 高级集成测试
// 测试复杂业务场景、系统集成和端到端流程

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { getSymbols, getKlineData } from '../../../src/api/market';
import { SimpleMarketDataFactory } from '../../factories/SimpleMarketDataFactory';

// 创建MSW服务器
const server = setupServer();

describe('Market API Advanced Tests', () => {
  beforeAll(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterAll(() => {
    server.close();
  });

  beforeEach(() => {
    server.resetHandlers();
  });

  describe('复杂业务场景测试', () => {
    it('应该支持完整的市场数据工作流', async () => {
      // 1. 获取股票列表
      const symbols = SimpleMarketDataFactory.createSymbols(5);
      const symbolsResponse = SimpleMarketDataFactory.createSymbolsResponse(symbols);

      server.use(
        http.get('/market/symbols', () => {
          return HttpResponse.json(symbolsResponse);
        })
      );

      const symbolsResult = await getSymbols();
      expect(symbolsResult.success).toBe(true);
      expect(symbolsResult.data).toHaveLength(5);

      // 2. 为每个股票获取K线数据
      const klinePromises = symbolsResult.data.map(symbol => {
        const klineResponse = SimpleMarketDataFactory.createKlineDataResponse(
          symbol.symbol, 
          '1d',
          SimpleMarketDataFactory.createKlineDataSeries(30)
        );

        server.use(
          http.get(`/market/kline/${symbol.symbol}`, () => {
            return HttpResponse.json(klineResponse);
          })
        );

        return getKlineData(symbol.symbol, '1d');
      });

      const klineResults = await Promise.all(klinePromises);
      
      // 3. 验证所有数据都正确获取
      expect(klineResults).toHaveLength(5);
      klineResults.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data.symbol).toBe(symbols[index].symbol);
        expect(result.data.data).toHaveLength(30);
      });
    });

    it('应该处理市场数据的实时更新场景', async () => {
      const symbol = '000001.SZ';
      let requestCount = 0;

      server.use(
        http.get(`/market/kline/${symbol}`, () => {
          requestCount++;
          const klineData = SimpleMarketDataFactory.createKlineDataSeries(1);
          // 模拟实时价格变化
          klineData[0].close = 10.0 + requestCount * 0.1;
          
          const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, '1m', klineData);
          return HttpResponse.json(response);
        })
      );

      // 模拟多次实时数据请求
      const results = [];
      for (let i = 0; i < 5; i++) {
        const result = await getKlineData(symbol, '1m');
        results.push(result);
        // 模拟时间间隔
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 验证价格确实在变化
      expect(results).toHaveLength(5);
      expect(results[0].data.data[0].close).toBe(10.1);
      expect(results[4].data.data[0].close).toBe(10.5);
    });

    it('应该处理多市场数据聚合场景', async () => {
      const complexMarketData = SimpleMarketDataFactory.createComplexMarketData();
      
      // 设置多个市场的数据响应
      Object.keys(complexMarketData.klineData).forEach(period => {
        Object.keys(complexMarketData.klineData[period]).forEach(symbol => {
          const klineData = complexMarketData.klineData[period][symbol];
          const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, period, klineData);
          
          server.use(
            http.get(`/market/kline/${symbol}`, ({ request }) => {
              const url = new URL(request.url);
              if (url.searchParams.get('period') === period) {
                return HttpResponse.json(response);
              }
              return HttpResponse.json(
                SimpleMarketDataFactory.createErrorResponse('Period not found')
              );
            })
          );
        });
      });

      // 测试获取不同周期的数据
      const symbol = complexMarketData.symbols[0].symbol;
      const periods = ['1d', '1w', '1M'];
      
      const results = await Promise.all(
        periods.map(period => getKlineData(symbol, period))
      );

      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data.period).toBe(periods[index]);
        expect(result.data.symbol).toBe(symbol);
      });
    });
  });

  describe('错误恢复和容错测试', () => {
    it('应该处理部分API失败的情况', async () => {
      const symbols = SimpleMarketDataFactory.createSymbols(3);
      
      // 设置第二个API调用失败
      symbols.forEach((symbol, index) => {
        if (index === 1) {
          server.use(
            http.get(`/market/kline/${symbol.symbol}`, () => {
              return HttpResponse.json(
                SimpleMarketDataFactory.createErrorResponse('Server error'),
                { status: 500 }
              );
            })
          );
        } else {
          const response = SimpleMarketDataFactory.createKlineDataResponse(symbol.symbol, '1d');
          server.use(
            http.get(`/market/kline/${symbol.symbol}`, () => {
              return HttpResponse.json(response);
            })
          );
        }
      });

      // 测试部分成功的情况
      const promises = symbols.map(symbol => 
        getKlineData(symbol.symbol, '1d').catch(error => ({ error, symbol: symbol.symbol }))
      );
      
      const results = await Promise.all(promises);
      
      // 验证成功和失败的结果
      expect(results[0]).not.toHaveProperty('error');
      expect(results[1]).toHaveProperty('error');
      expect(results[2]).not.toHaveProperty('error');
    });

    it('应该处理网络不稳定的情况', async () => {
      let attemptCount = 0;
      const symbol = '000001.SZ';
      
      server.use(
        http.get(`/market/kline/${symbol}`, () => {
          attemptCount++;
          
          // 前两次请求失败，第三次成功
          if (attemptCount < 3) {
            return HttpResponse.json(
              SimpleMarketDataFactory.createErrorResponse('Network error'),
              { status: 503 }
            );
          }
          
          const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, '1d');
          return HttpResponse.json(response);
        })
      );

      // 模拟重试逻辑（这里简化为多次调用）
      let result;
      let lastError;
      
      for (let i = 0; i < 3; i++) {
        try {
          result = await getKlineData(symbol, '1d');
          break;
        } catch (error) {
          lastError = error;
          await new Promise(resolve => setTimeout(resolve, 100)); // 等待重试
        }
      }

      expect(result?.success).toBe(true);
      expect(attemptCount).toBe(3);
    });
  });

  describe('数据一致性和完整性测试', () => {
    it('应该验证K线数据的时间序列一致性', async () => {
      const symbol = '000001.SZ';
      const klineData = SimpleMarketDataFactory.createKlineDataSeries(30);
      
      // 确保日期是连续的
      klineData.forEach((data, index) => {
        if (index > 0) {
          const prevDate = new Date(klineData[index - 1].date);
          const currentDate = new Date(data.date);
          expect(currentDate.getTime()).toBeGreaterThan(prevDate.getTime());
        }
      });

      const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, '1d', klineData);
      
      server.use(
        http.get(`/market/kline/${symbol}`, () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getKlineData(symbol, '1d');
      
      // 验证返回的数据时间序列一致性
      result.data.data.forEach((data, index) => {
        if (index > 0) {
          const prevDate = new Date(result.data.data[index - 1].date);
          const currentDate = new Date(data.date);
          expect(currentDate.getTime()).toBeGreaterThan(prevDate.getTime());
        }
      });
    });

    it('应该验证价格数据的逻辑一致性', async () => {
      const symbol = '000001.SZ';
      const klineData = SimpleMarketDataFactory.createKlineDataSeries(10);
      
      const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, '1d', klineData);
      
      server.use(
        http.get(`/market/kline/${symbol}`, () => {
          return HttpResponse.json(response);
        })
      );

      const result = await getKlineData(symbol, '1d');
      
      // 验证每个K线数据的价格逻辑
      result.data.data.forEach(data => {
        expect(data.high).toBeGreaterThanOrEqual(data.open);
        expect(data.high).toBeGreaterThanOrEqual(data.close);
        expect(data.high).toBeGreaterThanOrEqual(data.low);
        expect(data.low).toBeLessThanOrEqual(data.open);
        expect(data.low).toBeLessThanOrEqual(data.close);
        expect(data.volume).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('缓存和性能优化测试', () => {
    it('应该支持数据缓存机制', async () => {
      const symbol = '000001.SZ';
      let requestCount = 0;
      
      server.use(
        http.get(`/market/kline/${symbol}`, () => {
          requestCount++;
          const response = SimpleMarketDataFactory.createKlineDataResponse(symbol, '1d');
          return HttpResponse.json(response);
        })
      );

      // 连续请求相同数据
      await getKlineData(symbol, '1d');
      await getKlineData(symbol, '1d');
      await getKlineData(symbol, '1d');

      // 注意：这里测试的是API调用，实际缓存逻辑可能在更高层实现
      expect(requestCount).toBe(3); // 当前实现没有缓存，每次都会请求
    });

    it('应该支持批量数据请求优化', async () => {
      const symbols = SimpleMarketDataFactory.createSymbols(10);
      
      symbols.forEach(symbol => {
        const response = SimpleMarketDataFactory.createKlineDataResponse(symbol.symbol, '1d');
        server.use(
          http.get(`/market/kline/${symbol.symbol}`, () => {
            return HttpResponse.json(response);
          })
        );
      });

      const startTime = Date.now();
      
      // 并发请求多个股票数据
      const promises = symbols.map(symbol => getKlineData(symbol.symbol, '1d'));
      const results = await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(10);
      expect(duration).toBeLessThan(5000); // 5秒内完成所有请求
      
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });
});