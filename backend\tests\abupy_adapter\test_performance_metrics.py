"""
性能指标计算模块测试
"""
import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from app.schemas.strategy import PerformanceMetrics
from app.abupy_adapter.execution.result_processor import calculate_performance_metrics

class TestPerformanceMetrics:
    """性能指标计算模块测试类"""
    
    def create_sample_orders_pd(self):
        """创建样本订单数据"""
        # 创建一个包含买入和卖出记录的订单DataFrame
        data = {
            'symbol': ['sh000001', 'sh000001', 'sz000002', 'sz000002'],
            'buy_date': [
                pd.Timestamp('2023-01-01'), 
                pd.Timestamp('2023-01-10'), 
                pd.Timestamp('2023-01-05'), 
                pd.Timestamp('2023-01-15')
            ],
            'buy_price': [10.0, 12.0, 20.0, 22.0],
            'buy_cnt': [100, 100, 50, 50],
            'sell_date': [
                pd.Timestamp('2023-01-08'), 
                pd.Timestamp('2023-01-20'), 
                pd.Timestamp('2023-01-12'), 
                None  # 未平仓
            ],
            'sell_price': [11.0, 13.0, 19.0, None],  # 一个盈利，一个亏损，一个未平仓
        }
        return pd.DataFrame(data)
    
    def create_sample_benchmark_df(self):
        """创建样本基准指数数据"""
        # 创建一个日期范围的基准指数DataFrame
        dates = pd.date_range(start='2023-01-01', end='2023-01-31', freq='D')
        close_prices = np.linspace(100, 110, len(dates))  # 线性增长的收盘价
        data = {'close': close_prices}
        df = pd.DataFrame(data, index=dates)
        df.index.name = 'date'
        return df
    
    def create_mock_capital_obj(self, initial_capital=100000.0):
        """创建模拟的资金对象"""
        mock_capital = MagicMock()
        mock_capital.read_cash = initial_capital
        return mock_capital
    
    def create_mock_benchmark_obj(self):
        """创建模拟的基准对象"""
        mock_benchmark = MagicMock()
        mock_benchmark.kl_pd = self.create_sample_benchmark_df()
        return mock_benchmark
    
    def test_calculate_performance_metrics_with_trades(self):
        """测试有交易订单时的性能指标计算"""
        # 准备测试数据
        orders_pd = self.create_sample_orders_pd()
        capital_obj = self.create_mock_capital_obj()
        benchmark_obj = self.create_mock_benchmark_obj()
        
        # 调用被测试方法
        metrics = calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj)
        
        # 验证返回值类型
        assert isinstance(metrics, PerformanceMetrics)
        
        # 验证关键指标
        assert metrics.cumulative_return is not None
        assert metrics.annualized_return is not None
        assert metrics.max_drawdown is not None
        assert metrics.sharpe_ratio is not None
        assert metrics.win_rate is not None
        assert metrics.profit_loss_ratio is not None
        assert metrics.alpha is not None
        assert metrics.beta is not None
        assert metrics.total_trades > 0
        assert metrics.annualized_volatility is not None
        assert metrics.benchmark_return is not None
        assert metrics.benchmark_annualized_return is not None
        assert metrics.information_ratio is not None
        
        # 验证胜率计算 (3个已平仓交易中有2个盈利)
        assert round(metrics.win_rate, 4) == 0.6667
        
        # 验证总交易次数 (3个已平仓交易)
        assert metrics.total_trades == 3
    
    def test_calculate_performance_metrics_no_trades(self):
        """测试无交易订单时的性能指标计算"""
        # 准备测试数据 - 空的订单DataFrame
        orders_pd = pd.DataFrame()
        capital_obj = self.create_mock_capital_obj()
        benchmark_obj = self.create_mock_benchmark_obj()
        
        # 调用被测试方法
        metrics = calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj)
        
        # 验证返回值类型
        assert isinstance(metrics, PerformanceMetrics)
        
        # 验证默认指标值
        assert metrics.cumulative_return == 0
        assert metrics.annualized_return == 0
        assert metrics.max_drawdown == 0
        assert metrics.sharpe_ratio == 0
        assert metrics.win_rate == 0
        assert metrics.profit_loss_ratio == 0
        assert metrics.alpha == 0
        assert metrics.beta == 0
        assert metrics.total_trades == 0
        assert metrics.annualized_volatility == 0
        assert metrics.benchmark_return == 0
        assert metrics.benchmark_annualized_return == 0
        assert metrics.information_ratio == 0
    
    def test_calculate_performance_metrics_exception_handling(self):
        """测试异常处理"""
        # 准备测试数据 - 有效的订单DataFrame
        orders_pd = self.create_sample_orders_pd()
        capital_obj = self.create_mock_capital_obj()
        benchmark_obj = self.create_mock_benchmark_obj()
        
        # 模拟异常情况
        with patch('backend.app.abupy_adapter.execution.result_processor.pd.DataFrame.iterrows', 
                  side_effect=Exception("模拟计算错误")):
            # 调用被测试方法
            metrics = calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj)
            
            # 验证在异常情况下返回默认的PerformanceMetrics对象
            assert isinstance(metrics, PerformanceMetrics)
            assert metrics.cumulative_return == 0
            assert metrics.total_trades == 0
    
    def test_calculate_performance_metrics_realistic_scenario(self):
        """测试更真实的场景，包括多个交易和复杂的市场行为"""
        # 创建更复杂的订单数据
        dates = pd.date_range(start='2023-01-01', end='2023-03-31', freq='D')
        
        # 创建更复杂的订单数据
        complex_orders = []
        symbols = ['sh000001', 'sz000002', 'sh600000', 'sz300001']
        
        # 添加多个交易，包括盈利和亏损
        for i, symbol in enumerate(symbols):
            # 买入日期在前30天内
            buy_date = dates[i * 5]
            # 卖出日期在后30天内，确保有些交易是盈利的，有些是亏损的
            sell_date = dates[i * 5 + 15]
            
            # 设置价格，使一些交易盈利，一些亏损
            buy_price = 100.0 + i * 10
            if i % 2 == 0:  # 盈利
                sell_price = buy_price * 1.1
            else:  # 亏损
                sell_price = buy_price * 0.9
            
            complex_orders.append({
                'symbol': symbol,
                'buy_date': buy_date,
                'buy_price': buy_price,
                'buy_cnt': 100,
                'sell_date': sell_date,
                'sell_price': sell_price
            })
        
        # 添加一个未平仓的交易
        complex_orders.append({
            'symbol': 'sh600001',
            'buy_date': dates[-10],
            'buy_price': 150.0,
            'buy_cnt': 100,
            'sell_date': None,
            'sell_price': None
        })
        
        orders_pd = pd.DataFrame(complex_orders)
        
        # 创建更复杂的基准数据
        benchmark_dates = pd.date_range(start='2023-01-01', end='2023-03-31', freq='D')
        # 模拟一个有波动的市场
        np.random.seed(42)  # 设置随机种子以确保可重复性
        benchmark_prices = np.cumsum(np.random.normal(0.001, 0.02, len(benchmark_dates))) + 100
        benchmark_df = pd.DataFrame({'close': benchmark_prices}, index=benchmark_dates)
        benchmark_df.index.name = 'date'
        
        # 创建模拟对象
        capital_obj = self.create_mock_capital_obj(initial_capital=100000.0)
        benchmark_obj = MagicMock()
        benchmark_obj.kl_pd = benchmark_df
        
        # 调用被测试方法
        metrics = calculate_performance_metrics(orders_pd, capital_obj, benchmark_obj)
        
        # 验证关键指标
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.total_trades == 4  # 4个已平仓交易
        assert metrics.win_rate == 0.5  # 2个盈利，2个亏损
        
        # 验证其他指标的合理性
        assert -1 <= metrics.cumulative_return <= 1  # 累计收益率在合理范围内
        assert -1 <= metrics.annualized_return <= 1  # 年化收益率在合理范围内
        assert 0 <= metrics.max_drawdown <= 1  # 最大回撤在0-1之间
        assert metrics.annualized_volatility >= 0  # 波动率非负
