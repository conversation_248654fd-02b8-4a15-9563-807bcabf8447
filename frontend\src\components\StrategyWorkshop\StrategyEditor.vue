<template>
  <div class="right-panel">
    <div v-if="loading" class="editor-loading">
      <el-skeleton :rows="8" animated />
    </div>
    
    <!-- 有选中策略时显示编辑器 -->
    <div v-else-if="strategy !== null" class="editor-content">
      <!-- 策略头部信息 -->
      <StrategyHeader :strategy="strategy" />
      
      <el-tabs v-model="activeTab" class="editor-tabs">
        <el-tab-pane :label="UI_TEXT.STRATEGY_CONFIG" name="core">
          <div class="config-section">
            <!-- 基础信息配置 -->
            <BasicInfoForm
              :strategy="strategy"
              @update="handleBasicInfoUpdate"
            />
            
            <!-- 因子配置 -->
            <FactorManager
              :strategy="strategy"
              @update="handleFactorUpdate"
            />
            
            <!-- 保存策略按钮 -->
            <div class="save-strategy-section">
              <el-button
                type="primary"
                size="large"
                :disabled="!canSaveStrategy || isSaving"
                :loading="isSaving"
                data-testid="save-strategy-btn"
                @click="handleSave"
              >
                {{ isSaving ? UI_TEXT.SAVING : (isNewStrategyFlag ? UI_TEXT.CREATE_STRATEGY : UI_TEXT.SAVE_STRATEGY) }}
              </el-button>
              <span v-if="saveStatus" class="save-help" :class="saveStatusClass">
                {{ saveStatusText }}
              </span>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane :label="UI_TEXT.ADVANCED_SETTINGS" name="advanced">
          <div class="advanced-settings">
            <p>高级设置功能正在开发中...</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane :label="UI_TEXT.BACKTEST_CONFIG" name="backtest">
          <div class="backtest-solutions-tab" data-testid="backtest-solutions-tab">
            <!-- 回测配置区域 -->
            <div class="backtest-launcher" data-testid="backtest-launcher">
              <el-form 
                ref="backtestFormRef"
                :model="backtestForm" 
                :rules="backtestRules"
                label-width="100px"
                class="backtest-form"
              >
                <!-- 卡片一：数据源配置 -->
                <el-card class="config-card data-source-card">
                  <template #header>
                    <div class="card-header">
                      <span>回测标的</span>
                    </div>
                  </template>
                  
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="股票池" prop="choice_symbols" required>
                        <el-select 
                          v-model="backtestForm.choice_symbols" 
                          data-testid="choice-symbols-select"
                          placeholder="请选择股票池"
                          multiple
                          filterable
                          style="width: 100%"
                        >
                          <el-option value="000300.SH" label="沪深300" />
                          <el-option value="sh000016" label="上证50" />
                          <el-option value="sh000905" label="中证500" />
                          <el-option value="510300.SH" label="沪深300ETF" />
                          <el-option value="600519.SH" label="贵州茅台" />
                          <el-option value="000858.SZ" label="五粮液" />
                          <el-option value="custom" label="自定义" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    
                    <el-col :span="12">
                      <el-form-item label="应用选股策略" prop="stock_selection">
                        <el-tooltip content="选股器功能即将上线，敬请期待" placement="top">
                          <el-select 
                            v-model="backtestForm.stock_selection" 
                            data-testid="stock-picks-select"
                            placeholder="请选择选股策略"
                            disabled
                            style="width: 100%"
                          >
                            <el-option 
                               v-for="picker in mockStockPickers"
                               :key="picker.id"
                               :value="picker.id"
                               :label="picker.name"
                             />
                           </el-select>
                         </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>
                
                <!-- 卡片二：时间与成本配置 -->
                <el-card class="config-card time-cost-card">
                  <template #header>
                    <div class="card-header">
                      <span>回测周期与成本</span>
                    </div>
                  </template>
                  
                  <el-row :gutter="24">
                    <el-col :span="11">
                      <!-- 回测周期选择 -->
                      <div class="period-selection">
                        <el-form-item label="回测模式" style="margin-bottom: 16px;">
                          <el-radio-group v-model="backtestPeriodMode" data-testid="backtest-period-mode" @change="handleBacktestModeChange">
                            <el-radio value="n_folds">按交叉验证(n_folds)</el-radio>
                            <el-radio value="date_range">按日期范围(start/end)</el-radio>
                          </el-radio-group>
                        </el-form-item>
                        
                        <!-- n_folds 模式 -->
                        <el-form-item v-if="backtestPeriodMode === 'n_folds'" label="折数" prop="n_folds" required>
                          <el-input-number
                            v-model="backtestForm.n_folds"
                            data-testid="n-folds-input"
                            :min="1"
                            :max="10"
                            placeholder="折数"
                            style="width: 100%"
                          />
                        </el-form-item>
                        
                        <!-- 日期范围模式 -->
                        <div v-if="backtestPeriodMode === 'date_range'">
                          <el-form-item label="开始日期" prop="start_date" style="margin-bottom: 12px;">
                            <el-date-picker
                              v-model="backtestForm.start_date"
                              data-testid="start-date-picker"
                              type="date"
                              placeholder="开始日期"
                              format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD"
                              style="width: 100%"
                            />
                          </el-form-item>
                          <el-form-item label="结束日期" prop="end_date">
                            <el-date-picker
                              v-model="backtestForm.end_date"
                              data-testid="end-date-picker"
                              type="date"
                              placeholder="结束日期"
                              format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD"
                              style="width: 100%"
                            />
                          </el-form-item>
                        </div>
                      </div>
                    </el-col>
                    
                    <!-- 分割线 -->
                    <el-col :span="2" class="divider-col">
                      <el-divider direction="vertical" class="section-divider" />
                    </el-col>
                    
                    <el-col :span="11">
                      <el-form-item label="手续费模板" prop="commission_template">
                        <el-select 
                          v-model="backtestForm.commission_template" 
                          data-testid="commission-dict-select"
                          placeholder="请选择手续费模板"
                          style="width: 100%"
                          @change="handleCommissionTemplateChange"
                        >
                          <el-option value="default" label="默认手续费模板" />
                          <el-option value="low" label="低费率" />
                          <el-option value="high" label="高费率" />
                          <el-option value="custom" label="自定义" />
                        </el-select>
                      </el-form-item>
                      
                      <!-- 自定义手续费率 -->
                      <el-form-item v-if="backtestForm.commission_template === 'custom'" label="手续费率" prop="commission">
                        <el-input-number
                          v-model="backtestForm.commission"
                          data-testid="commission-input"
                          :min="0"
                          :max="0.01"
                          :step="0.0001"
                          :precision="4"
                          style="width: 100%"
                        >
                          <template #suffix>%</template>
                        </el-input-number>
                      </el-form-item>
                      
                      <el-form-item label="初始资金" prop="capital" required>
                  <el-input-number
                    v-model="backtestForm.capital"
                          data-testid="initial-capital-input"
                          :min="100000"
                          :step="100000"
                          placeholder="请输入初始资金"
                          style="width: 100%"
                        >
                          <template #suffix>元</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>
                
                <!-- 卡片三：高级风控配置 -->
                <el-card class="config-card risk-control-card">
                  <template #header>
                    <div class="card-header">
                      <span>高级风控</span>
                    </div>
                  </template>
                  
                  <el-row :gutter="16">
                    <el-col :span="24">
                      <div class="risk-control-content">
                        <el-form-item style="margin-bottom: 0;" class="umpire-form-item" label-width="100px">
                          <template #label>
                            <span>裁判系统 (UmpBu)</span>
                          </template>
                          <div class="umpire-controls">
                            <el-switch
                              v-model="backtestForm.enable_umpire"
                              data-testid="umpire-switch"
                              active-text="启用"
                              inactive-text="关闭"
                              style="margin-right: 20px;"
                            />
                            
                            <el-select 
                              v-if="backtestForm.enable_umpire"
                              v-model="backtestForm.umpire_market" 
                              data-testid="umpire-market-select"
                              placeholder="选择裁判模型市场"
                              style="width: 200px;"
                            >
                              <el-option value="cn" label="牛市模型" />
                              <el-option value="us" label="熊市模型" />
                              <el-option value="hk" label="震荡市模型" />
                              <el-option value="global" label="趋势市模型" />
                            </el-select>
                          </div>
                        </el-form-item>
                      </div>
                    </el-col>
                  </el-row>
                </el-card>
                
                <!-- 开始回测按钮区域 -->
                <div class="backtest-action-area">
                  <el-button 
                    type="primary"
                    size="large"
                    data-testid="start-backtest-btn"
                    :disabled="!canStartBacktest"
                    :loading="backtestStore.isLoading"
                    @click="handleStartBacktest"
                    class="start-backtest-btn"
                  >
                    {{ backtestStore.isLoading ? '回测中...' : '开始回测' }}
                  </el-button>
                </div>
              </el-form>
            </div>
            
            <!-- 回测历史区域 -->
            <div class="backtest-history" data-testid="backtest-history" style="margin-top: 20px;">
              <el-card class="history-card">
              <template #header>
                <div class="card-header">
                  <span>回测历史</span>
                  <div>
                    <el-button 
                      type="primary" 
                      size="small"
                      style="margin-right: 8px;"
                    >
                      新建
                    </el-button>
                    <el-button 
                      type="text" 
                      size="small"
                      data-testid="refresh-history-btn"
                      @click="refreshHistory"
                    >
                      全部清空
                    </el-button>
                  </div>
                </div>
              </template>
                  
                  <el-table 
                    :data="backtestHistory" 
                    data-testid="history-table"
                    style="width: 100%"
                    max-height="600px"
                    v-loading="backtestStore.isLoading"
                  >
                    <el-table-column prop="strategy_name" label="策略名称" width="120" />
                    <el-table-column prop="symbol" label="股票池" width="80" />
                    <el-table-column prop="created_at" label="创建时间" width="100">
                      <template #default="{ row }">
                        {{ formatDate(row.created_at) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="80">
                      <template #default="{ row }">
                        <el-tag 
                          :type="getStatusType(row.status)"
                          size="small"
                        >
                          {{ getStatusText(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="140">
                       <template #default="{ row }">
                         <el-button 
                           type="text"
                           size="small"
                           data-testid="view-report-btn"
                           :disabled="row.status !== 'completed'"
                           @click="handleViewReport(row)"
                         >
                           查看报告
                         </el-button>
                         <el-button 
                           type="text"
                           size="small"
                           data-testid="delete-backtest-btn"
                           style="color: #f56c6c; margin-left: 8px;"
                           @click="handleDeleteBacktest(row)"
                         >
                           删除
                         </el-button>
                       </template>
                     </el-table-column>
                  </el-table>
                  
                  <!-- 空状态 -->
                  <div v-if="!backtestHistory.length && !backtestStore.isLoading" class="empty-history">
                    <el-empty description="暂无回测记录" />
                  </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane :label="UI_TEXT.OPTIMIZATION_CONFIG" name="optimization">
          <div class="optimization-config">
            <p>参数优化功能正在开发中...</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-editor-state">
      <div class="empty-content">
        <el-icon size="64" color="#c0c4cc">
          <DocumentAdd />
        </el-icon>
        <h3>开始创建您的策略</h3>
        <p>选择左侧已有策略进行编辑，或创建一个新的量化交易策略</p>
        <el-button 
          type="primary" 
          size="large" 
          class="new-strategy-btn"
          @click="$emit('create-strategy')"
        >
          {{ UI_TEXT.NEW_STRATEGY }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { Strategy } from '@/api/types'
import { useStrategyEditor } from '@/composables/useStrategyEditor'
import { useStrategyStore, useBacktestStore } from '@/stores'
import { SUCCESS_MESSAGES, UI_TEXT, isNewStrategy } from '@/constants/strategy'
import { showSuccess, showError } from '@/utils/errorHandler'
import { useRouter } from 'vue-router'
import StrategyHeader from './StrategyHeader.vue'
import BasicInfoForm from './BasicInfoForm.vue'
import FactorManager from './FactorManager.vue'

interface Props {
  strategy: Strategy | null
  loading: boolean
}

interface Emits {
  (e: 'save-strategy', strategy: Strategy): void
  (e: 'update-strategy', strategy: Strategy): void
  (e: 'create-strategy'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用组合式函数管理编辑器状态
const {
  editingStrategy,
  isDirty,
  isSaving,
  canSave,
  saveStrategy,
  onSaveSuccess,
  onSaveError,
  updateStrategy,
  resetChanges
} = useStrategyEditor()

// 使用stores
const strategyStore = useStrategyStore()
const backtestStore = useBacktestStore()
const router = useRouter()

// 模拟选股策略数据
const mockStockPickers = [
  { id: 'picker_1', name: '动量策略 (开发中)' },
  { id: 'picker_2', name: '小市值策略 (开发中)' }
]

// 本地状态
const activeTab = ref('core')
const saveStatus = ref<'saved' | 'unsaved' | 'error' | null>(null)

// 回测周期模式
const backtestPeriodMode = ref('n_folds') // 'n_folds' 或 'date_range'

// 回测表单数据
const backtestForm = ref({
  choice_symbols: ['000300.SH', '510300.SH'], // 默认包含沪深300指数和可交易的ETF
  stock_selection: '',
  n_folds: 2,
  start_date: '',
  end_date: '',
  commission_template: 'default',
  commission: 0.0003,
  capital: 1000000, // 修改字段名以匹配后端API期望的参数名
  enable_umpire: false,
  umpire_market: ''
})

// 动态表单验证规则
const backtestRules = computed(() => {
  const baseRules = {
    choice_symbols: [
      { required: true, message: '请选择股票池', trigger: 'change' }
    ],
    // 暂时移除选股策略的必填验证，因为功能尚未上线
    // stock_selection: [
    //   { required: true, message: '请选择选股策略', trigger: 'change' }
    // ],
    commission_template: [
      { required: true, message: '请选择手续费模板', trigger: 'change' }
    ],
    commission: [
      { required: true, message: '请输入手续费率', trigger: 'blur' },
      { type: 'number', min: 0, max: 0.01, message: '手续费率应在0-1%之间', trigger: 'blur' }
    ],
    capital: [
      { required: true, message: '请输入初始资金', trigger: 'blur' },
      { type: 'number', min: 100000, message: '初始资金不能少于10万元', trigger: 'blur' }
    ],
    umpire_market: [
      { required: true, message: '请选择裁判市场', trigger: 'change' }
    ]
  }
  
  // 根据回测周期模式添加相应的验证规则
  if (backtestPeriodMode.value === 'n_folds') {
    baseRules.n_folds = [
      { required: true, message: '请输入折数', trigger: 'blur' },
      { type: 'number', min: 1, max: 10, message: '折数应在1-10之间', trigger: 'blur' }
    ]
  } else if (backtestPeriodMode.value === 'date_range') {
    baseRules.start_date = [
      { required: true, message: '请选择开始日期', trigger: 'change' }
    ]
    baseRules.end_date = [
      { required: true, message: '请选择结束日期', trigger: 'change' }
    ]
  }
  
  return baseRules
})

// 表单引用
const backtestFormRef = ref()

// 回测历史数据
const backtestHistory = computed(() => backtestStore.backtestHistory)

// 计算属性
const isNewStrategyFlag = computed(() => {
  return isNewStrategy(props.strategy)
})

const canSaveStrategy = computed(() => {
  return canSave.value && (isNewStrategyFlag.value || isDirty.value)
})

const saveStatusClass = computed(() => ({
  'saved': saveStatus.value === 'saved',
  'unsaved-changes': saveStatus.value === 'unsaved',
  'error': saveStatus.value === 'error'
}))

const saveStatusText = computed(() => {
  switch (saveStatus.value) {
    case 'saved':
      return '✓ 已保存'
    case 'unsaved':
      return '● 有未保存的更改'
    case 'error':
      return '✗ 保存失败'
    default:
      return ''
  }
})

// 回测相关计算属性
const canStartBacktest = computed(() => {
  const strategy = editingStrategy.value
  if (!strategy) {
    console.log('canStartBacktest: 没有策略')
    return false
  }
  
  const hasBuyFactor = strategy.buy_factors && strategy.buy_factors.length > 0
  const hasSellFactor = strategy.sell_factors && strategy.sell_factors.length > 0
  
  // 根据回测周期模式验证不同字段
  let hasValidPeriod = false
  if (backtestPeriodMode.value === 'n_folds') {
    hasValidPeriod = backtestForm.value.n_folds && backtestForm.value.n_folds > 0
  } else {
    hasValidPeriod = backtestForm.value.start_date && backtestForm.value.end_date
  }
  
  const hasValidForm = backtestForm.value.choice_symbols && 
                      // 暂时跳过选股策略验证，因为功能尚未上线
                      // backtestForm.value.stock_selection &&
                      hasValidPeriod &&
                      backtestForm.value.commission_template &&
                      backtestForm.value.capital > 0 &&
                      (!backtestForm.value.enable_umpire || backtestForm.value.umpire_market)
  
  console.log('canStartBacktest 调试信息:', {
    hasBuyFactor,
    hasSellFactor,
    hasValidPeriod,
    backtestPeriodMode: backtestPeriodMode.value,
    choice_symbols: backtestForm.value.choice_symbols,
    stock_selection: backtestForm.value.stock_selection,
    commission_template: backtestForm.value.commission_template,
    capital: backtestForm.value.capital,
    enable_umpire: backtestForm.value.enable_umpire,
    umpire_market: backtestForm.value.umpire_market,
    n_folds: backtestForm.value.n_folds,
    start_date: backtestForm.value.start_date,
    end_date: backtestForm.value.end_date
  })
  
  return hasBuyFactor && hasSellFactor && hasValidForm
})

// 监听策略变化，同步到编辑器
watch(() => props.strategy, (newStrategy, oldStrategy) => {
  if (newStrategy) {
    // 如果策略ID发生变化（比如从临时ID变为真实ID），重置原始状态
    const strategyIdChanged = oldStrategy?.id !== newStrategy.id
    
    // 如果当前没有未保存的更改，也重置原始状态（比如保存后的策略更新）
    const shouldResetOriginal = strategyIdChanged || !isDirty.value
    
    updateStrategy(newStrategy, shouldResetOriginal)
    saveStatus.value = null
  }
}, { deep: true, immediate: true })

// 监听脏状态变化，更新保存状态
watch(isDirty, (dirty) => {
  if (dirty) {
    saveStatus.value = 'unsaved'
  } else {
    // 如果变为非脏状态，且当前是unsaved状态，则清除状态
    if (saveStatus.value === 'unsaved') {
      saveStatus.value = null
    }
  }
})

// 事件处理
const handleBasicInfoUpdate = (updates: Partial<Strategy>) => {
  const updated = { ...editingStrategy.value, ...updates }
  updateStrategy(updated)
  emit('update-strategy', updated)
}

const handleFactorUpdate = (updates: Partial<Strategy>) => {
  const updated = { ...editingStrategy.value, ...updates }
  updateStrategy(updated)
  emit('update-strategy', updated)
}

const handleSave = async () => {
  try {
    // 验证并设置loading状态，但不自动重置
    await saveStrategy(false)
    
    // 发出保存事件给父组件，父组件需要处理实际保存
    emit('save-strategy', editingStrategy.value)
    
    // 注意：状态重置应该在父组件保存成功后调用
    // 这里暂时不重置，等待父组件的回调或者在onSaveComplete中处理
    
  } catch (error) {
    console.error('保存策略失败:', error)
    
    // 保存失败，重置loading状态
    onSaveError()
    saveStatus.value = 'error'
    
    showError(error)
  }
}

// 新增一个方法供父组件调用，当保存完成时
const onSaveComplete = (success: boolean, message?: string) => {
  if (success) {
    onSaveSuccess()
    saveStatus.value = 'saved'
    
    // 延时清除状态，让用户看到成功提示
    setTimeout(() => {
      saveStatus.value = null
    }, 2000)
    
    showSuccess(message || (isNewStrategyFlag.value ? SUCCESS_MESSAGES.STRATEGY_CREATED : SUCCESS_MESSAGES.STRATEGY_SAVED))
    
    // 强制触发响应式更新，确保按钮状态立即更新
    nextTick(() => {
      // 这里什么都不做，只是为了确保DOM更新
    })
  } else {
    onSaveError()
    saveStatus.value = 'error'
    showError(message || '保存失败')
  }
}

// 日期格式转换函数：将YYYY-MM-DD转换为YYYYMMDD
const formatDateForBackend = (dateStr: string) => {
  if (!dateStr) return dateStr
  return dateStr.replace(/-/g, '')
}

// 动态构建回测参数的函数
const assembleBacktestParams = () => {
  const baseParams = {
    choice_symbols: backtestForm.value.choice_symbols,
    initial_capital: backtestForm.value.capital,  // 修正字段名
    benchmark_symbol: '000300.SH',  // 沪深300指数的正确代码
    commission: backtestForm.value.commission,
    commission_template: backtestForm.value.commission_template,
    stock_selection: backtestForm.value.stock_selection || '',
    enable_umpire: backtestForm.value.enable_umpire || false,
    umpire_market: backtestForm.value.umpire_market || '',
    data_source: 'local'  // 添加必需的数据源
  }

  // 根据当前选中的模式，动态添加相应的参数
  if (backtestPeriodMode.value === 'n_folds') {
    // n_folds模式：根据用户选择的折数动态计算时间范围
    const currentYear = new Date().getFullYear()
    const nFolds = backtestForm.value.n_folds || 2
    const startYear = currentYear - nFolds  // 根据折数倒推年份
    return {
      ...baseParams,
      n_folds: nFolds,
      start_date: formatDateForBackend(`${startYear}-01-01`),
      end_date: formatDateForBackend(`${currentYear - 1}-12-31`)
    }
  } else {
    return { 
      ...baseParams, 
      start_date: formatDateForBackend(backtestForm.value.start_date), 
      end_date: formatDateForBackend(backtestForm.value.end_date) 
    }
  }
}

// 前端前置校验函数
const validateBacktestForm = () => {
  // 检查股票池是否选择
  if (!backtestForm.value.choice_symbols || backtestForm.value.choice_symbols.length === 0) {
    ElMessage.error('请至少选择一个股票池进行回测！')
    return false
  }

  // 检查初始资金
  if (!backtestForm.value.capital || backtestForm.value.capital < 100000) {
    ElMessage.error('请输入有效的初始资金（不少于10万元）！')
    return false
  }

  // 根据当前模式检查必需字段
  if (backtestPeriodMode.value === 'n_folds') {
    if (!backtestForm.value.n_folds || backtestForm.value.n_folds <= 0) {
      ElMessage.error('请输入有效的折数（1-10之间）！')
      return false
    }
  } else if (backtestPeriodMode.value === 'date_range') {
    if (!backtestForm.value.start_date) {
      ElMessage.error('请选择回测开始日期！')
      return false
    }
    if (!backtestForm.value.end_date) {
      ElMessage.error('请选择回测结束日期！')
      return false
    }
    // 检查日期逻辑
    if (new Date(backtestForm.value.start_date) >= new Date(backtestForm.value.end_date)) {
      ElMessage.error('回测开始日期必须早于结束日期！')
      return false
    }
  }

  // 检查手续费模板
  if (!backtestForm.value.commission_template) {
    ElMessage.error('请选择手续费模板！')
    return false
  }

  // 如果启用了裁判系统，检查裁判市场
  if (backtestForm.value.enable_umpire && !backtestForm.value.umpire_market) {
    ElMessage.error('启用裁判系统时，请选择裁判市场！')
    return false
  }

  return true
}

// 回测相关方法
const handleStartBacktest = async () => {
  if (!editingStrategy.value) return
  
  // 前端前置校验
  if (!validateBacktestForm()) {
    return
  }
  
  try {
    const strategyId = editingStrategy.value.id
    // 使用动态构建的参数，只包含当前模式需要的字段
    const executeParams = assembleBacktestParams()
    
    // 调试日志：查看实际发送的参数
    console.log('=== 回测参数调试 ===')
    console.log('策略ID:', strategyId)
    console.log('回测模式:', backtestPeriodMode.value)
    console.log('发送参数:', JSON.stringify(executeParams, null, 2))
    console.log('原始表单数据:', JSON.stringify(backtestForm.value, null, 2))
    console.log('===================')
    
    const result = await strategyStore.executeStrategy(strategyId, executeParams)
    
    // 关闭加载提示
    ElMessage.closeAll()
    
    // 存储回测结果到store
    backtestStore.setBacktestResult(result)
    
    // 跳转到回测报告页面
    const reportId = result.id || result.task_id || Date.now().toString()
    await router.push(`/backtest/report/${reportId}`)
    
  } catch (error) {
    console.error('执行回测失败:', error)
    
    // 关闭加载提示
    ElMessage.closeAll()
    
    // 显示错误信息
    showError('执行回测失败')
  }
}

const handleViewReport = (backtest: any) => {
  console.log('查看回测报告:', backtest)
  // TODO: 实现查看报告功能
}

// 手续费模板变更处理
const handleCommissionTemplateChange = (template: string) => {
  const commissionMap = {
    'default': 0.0003,
    'low': 0.0001,
    'high': 0.0005,
    'custom': backtestForm.value.commission
  }
  
  if (template !== 'custom') {
    backtestForm.value.commission = commissionMap[template] || 0.0003
  }
}

// 回测模式切换处理
const handleBacktestModeChange = (newMode: string) => {
  // 只清空与模式相关的字段，不触碰共享字段如choice_symbols
  if (newMode === 'n_folds') {
    // 切换到n_folds模式时，清空日期相关字段
    backtestForm.value.start_date = ''
    backtestForm.value.end_date = ''
    // 设置默认的n_folds值
    if (!backtestForm.value.n_folds || backtestForm.value.n_folds <= 0) {
      backtestForm.value.n_folds = 2
    }
  } else if (newMode === 'date_range') {
    // 切换到date_range模式时，清空n_folds字段并设置默认日期范围
    backtestForm.value.n_folds = 0
    // 设置默认日期范围为本地数据实际存在的范围
    if (!backtestForm.value.start_date) {
      backtestForm.value.start_date = '2021-01-04'
    }
    if (!backtestForm.value.end_date) {
      backtestForm.value.end_date = '2022-12-30'
    }
  }
}

// 刷新回测历史
const refreshHistory = async () => {
  if (editingStrategy.value?.id) {
    await backtestStore.fetchBacktestHistory(editingStrategy.value.id)
  }
}

// 删除回测记录
const handleDeleteBacktest = async (backtest: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除回测记录 "${backtest.strategy_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await backtestStore.deleteBacktest(backtest.id)
    ElMessage.success('删除成功')
    
    // 刷新历史记录
    await refreshHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除回测失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    'pending': '',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '等待中',
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

// 监听当前策略变化，获取回测历史
watch(() => editingStrategy.value?.id, async (strategyId) => {
  if (strategyId) {
    await backtestStore.fetchBacktestHistory(strategyId)
  }
}, { immediate: true })

// 暴露方法给父组件使用
defineExpose({
  onSaveComplete
})
</script>

<style scoped>
.right-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* margin-left: 24px; 移除固定margin，使用父容器的gap控制间隙 */
}

.editor-loading {
  margin: var(--space-lg);
}

.editor-content {
  flex: 1 1 auto;
  min-height: 0;
  overflow: auto;
  padding: var(--space-lg);
}

.editor-tabs {
  min-height: 0;
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-config-cards-gap);
}

.save-strategy-section {
  margin-top: var(--space-xl);
  padding: var(--space-lg);
  border-top: 2px solid #e4e7ed;
  text-align: center;
  background: linear-gradient(135deg, #f8faff 0%, #f0f9ff 100%);
  border-radius: 8px;
}

.save-help {
  display: block;
  margin-top: 8px;
  color: #f56c6c;
  font-size: 12px;
}

.save-help.unsaved-changes {
  color: #e6a23c;
  font-weight: 500;
}

.save-help.saved {
  color: #67c23a;
  font-weight: 500;
}

.save-help.error {
  color: #f56c6c;
  font-weight: 500;
}

.advanced-settings,
.backtest-config,
.optimization-config {
  padding: var(--space-lg);
  text-align: center;
  color: #909399;
}

.empty-editor-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 500px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
  padding: var(--space-xl) var(--space-lg);
}

.empty-content h3 {
  margin: var(--space-lg) 0 var(--space-md) 0;
  color: #606266;
  font-size: 20px;
  font-weight: 500;
}

.empty-content p {
  margin: 0 0 var(--space-lg) 0;
  color: #909399;
  font-size: 14px;
  line-height: 1.6;
}

.empty-content .new-strategy-btn {
  font-size: 16px;
  padding: var(--space-md) var(--space-lg);
  border-radius: 8px;
}

/* 回测标签页样式 */
.backtest-solutions-tab {
  padding: 0;
}

.config-card,
.history-card {
  height: 100%;
  min-height: auto;
}

.config-card .el-card__body,
.history-card .el-card__body {
  padding: var(--space-lg);
}

/* 回测配置区域样式 */
.backtest-launcher {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.backtest-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

/* 卡片样式 */
.config-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
}

.data-source-card .card-header {
  color: #409eff;
  font-weight: 600;
}

.time-cost-card .card-header {
  color: #67c23a;
  font-weight: 600;
}

.risk-control-card .card-header {
  color: #e6a23c;
  font-weight: 600;
}

/* 回测周期选择区域 */
.period-selection {
  background: #f8f9fa;
  border-radius: 6px;
  padding: var(--space-md);
  border: 1px solid #e4e7ed;
}

/* 分割线样式 */
.divider-col {
  display: flex;
  justify-content: center;
  align-items: stretch;
  padding: 0 var(--space-sm);
}

.section-divider {
  height: 100%;
  min-height: 200px;
  margin: 0;
  border-color: #e4e7ed;
}

/* 风控配置内容 */
.risk-control-content {
  padding: var(--space-sm) 0;
}

.umpire-form-item {
  /* 确保标签不换行 */
}

.umpire-form-item .el-form-item__label {
  /* 删除强制的最小宽度设置，让它使用form的label-width */
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow: visible !important;
}

.umpire-form-item .el-form-item__content {
  flex: 1;
  min-width: 0;
}

.umpire-controls {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex-wrap: nowrap;
  width: 100%;
}

/* 开始回测按钮区域 */
.backtest-action-area {
  display: flex;
  justify-content: center;
  padding: var(--space-lg) 0;
  margin-top: var(--space-sm);
}

.start-backtest-btn {
  min-width: 160px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.start-backtest-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

/* 单选按钮组样式优化 */
.config-card .el-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.config-card .el-radio {
  margin-right: 0 !important;
  white-space: nowrap;
}

.config-card .card-header,
.history-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.backtest-form {
  max-height: none;
  overflow-y: visible;
}

.backtest-form .el-form-item {
  margin-bottom: 18px;
}

.backtest-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}



.empty-history {
  padding: var(--space-xl) var(--space-lg);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-card,
  .history-card {
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .backtest-solutions-tab {
    padding: var(--space-sm);
  }
  
  .config-card,
  .history-card {
    min-height: 400px;
  }
}
</style>
