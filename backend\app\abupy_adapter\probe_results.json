{"abupy_version_probe": {"probe_date": "2025-07-25", "notes": "针对项目环境中特定 abupy 版本的探测。此探测确定了核心类的非标准位置以及可用的因子确切列表。", "core_classes": {"ABuEnv": "abupy.CoreBu (作为模块 'env' 导入)", "GridSearch": "abupy.MetricsBu"}, "available_factors": {"buy": ["AbuFactorBuyBase", "AbuFactorBuyXD", "AbuFactorBuyTD", "AbuFactorBuyBreak", "AbuFactorBuyWD", "AbuFactorBuyPutBreak", "AbuFactorBuyBreakUmpDemo", "AbuFactorBuyBreakReocrdHitDemo", "AbuFactorBuyBreakHitPredictDemo"], "sell": ["AbuFactorSellBase", "AbuFactorSellXD", "AbuFactorPreAtrNStop", "AbuFactorAtrNStop", "AbuFactorCloseAtrNStop", "AbuFactorSellBreak", "AbuFactorSellNDay"]}}}