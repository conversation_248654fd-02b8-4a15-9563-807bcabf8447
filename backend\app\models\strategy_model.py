"""
策略数据库模型定义模块

将现有的Pydantic模型转换为SQLModel模型，用于数据库持久化。
使用JSON类型存储嵌套的结构如buy_factors、sell_factors和parameters。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import JSON, Column
from sqlmodel import Field, SQLModel

from app.schemas.strategy import BuyFactor, SellFactor


class StrategyModel(SQLModel, table=True):
    """
    策略数据库模型
    
    使用SQLModel实现，支持数据库持久化
    对于复杂字段如buy_factors、sell_factors和parameters，使用JSON类型存储
    """
    __tablename__ = "strategies"
    __table_args__ = {'extend_existing': True}
    
    id: str = Field(primary_key=True)
    name: str
    description: Optional[str] = None
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)
    owner: Optional[str] = None
    is_public: bool = Field(default=False)
    
    # 使用SQLAlchemy的JSON类型存储嵌套结构
    buy_factors: List[Dict[str, Any]] = Field(
        sa_column=Column(JSON), 
        default_factory=list
    )
    sell_factors: List[Dict[str, Any]] = Field(
        sa_column=Column(JSON), 
        default_factory=list
    )
    parameters: Dict[str, Any] = Field(
        sa_column=Column(JSON), 
        default_factory=dict
    )
    tags: Optional[List[str]] = Field(
        sa_column=Column(JSON), 
        default=None
    )
    position_strategy: Optional[Dict[str, Any]] = Field(
        sa_column=Column(JSON),
        default=None
    )
    umpire_rules: Optional[list] = Field(default=None, sa_column=Column(JSON))
    
    def to_schema_strategy(self) -> 'Strategy':
        """
        转换为API Schema模型
        
        将数据库模型转换回API用的Pydantic模型，处理嵌套结构
        
        Returns:
            Strategy: API Schema模型实例
        """
        from app.schemas.strategy import Strategy
        
        # 将存储为JSON的因子列表转换回Pydantic模型
        from app.schemas.strategy import PositionStrategy
        buy_factors_schema = [BuyFactor(**factor) for factor in self.buy_factors]
        sell_factors_schema = [SellFactor(**factor) for factor in self.sell_factors]
        position_strategy_schema = PositionStrategy(**self.position_strategy) if self.position_strategy else None

        # 创建并返回Schema模型
        return Strategy(
            id=self.id,
            name=self.name,
            description=self.description,
            create_time=self.create_time,
            update_time=self.update_time,
            owner=self.owner,
            is_public=self.is_public,
            buy_factors=buy_factors_schema,
            sell_factors=sell_factors_schema,
            parameters=self.parameters,
            tags=self.tags,
            position_strategy=position_strategy_schema,
            umpire_rules=self.umpire_rules
        )
    
    @classmethod
    def from_schema_strategy(cls, strategy: 'Strategy') -> 'StrategyModel':
        """
        从API Schema模型创建数据库模型
        
        处理嵌套结构(如因子列表)的转换
        
        Args:
            strategy: API Schema模型实例
            
        Returns:
            StrategyModel: 数据库模型实例
        """
        # 将Pydantic模型转换为字典
        buy_factors_dict = [factor.model_dump(by_alias=True) for factor in strategy.buy_factors]
        sell_factors_dict = [factor.model_dump(by_alias=True) for factor in strategy.sell_factors]
        position_strategy_dict = strategy.position_strategy.model_dump(by_alias=True) if strategy.position_strategy else None

        # 创建并返回数据库模型
        return cls(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            create_time=strategy.create_time or datetime.now(),
            update_time=strategy.update_time or datetime.now(),
            owner=strategy.owner,
            is_public=strategy.is_public,
            buy_factors=buy_factors_dict,
            sell_factors=sell_factors_dict,
            parameters=strategy.parameters,
            tags=strategy.tags,
            position_strategy=position_strategy_dict,
            umpire_rules=strategy.umpire_rules
        )


# 确保models目录被识别为包
if not hasattr(SQLModel, "metadata"):
    raise ImportError("SQLModel not properly initialized!")
