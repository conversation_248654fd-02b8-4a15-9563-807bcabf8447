import pandas as pd
import h5py

# 检查本地数据的日期范围
data_file = 'data/market_data.h5'

print("=== 使用 pandas 读取 HDF5 数据 ===")
try:
    # 尝试使用 pandas 直接读取
    with pd.HDFStore(data_file, 'r') as store:
        print(f"HDF5 存储中的键: {store.keys()}")
        
        # 检查 000300.SH 数据
        if '/000300.SH' in store.keys():
            df = store['/000300.SH']
            print(f"\n000300.SH 数据信息:")
            print(f"数据形状: {df.shape}")
            print(f"列名: {df.columns.tolist()}")
            print(f"索引类型: {type(df.index)}")
            print(f"索引范围: {df.index.min()} 到 {df.index.max()}")
            print(f"\n前5行数据:")
            print(df.head())
            print(f"\n后5行数据:")
            print(df.tail())
            
            # 如果索引是日期类型，显示日期范围
            if hasattr(df.index, 'date'):
                print(f"\n日期范围: {df.index.min().date()} 到 {df.index.max().date()}")
            elif df.index.dtype == 'int64':
                 # 尝试转换为日期
                 try:
                     if len(str(df.index[0])) == 8:
                         dates = pd.to_datetime(df.index, format='%Y%m%d')
                         print(f"\n转换后的日期范围: {dates.min().date()} 到 {dates.max().date()}")
                 except Exception as date_e:
                     print(f"日期转换失败: {date_e}")
except Exception as e:
    print(f"使用 pandas 读取失败: {e}")

print("\n=== 使用 h5py 直接读取 ===")
try:
    with h5py.File(data_file, 'r') as f:
        print(f"HDF5 文件中的键: {list(f.keys())}")
        
        if '000300.SH' in f.keys():
            group = f['000300.SH']
            print(f"\n000300.SH 组结构: {list(group.keys())}")
            
            # 检查是否有 table 数据集
            if 'table' in group.keys():
                table = group['table']
                print(f"table 形状: {table.shape}")
                print(f"table 数据类型: {table.dtype}")
                
                # 读取少量数据查看结构
                sample_data = table[:10]
                print(f"\n前10行原始数据:")
                for i, row in enumerate(sample_data):
                    print(f"行 {i}: {row}")
                    
            # 检查 _i_table 中的索引
            if '_i_table' in group.keys():
                i_table = group['_i_table']
                print(f"\n_i_table 结构: {list(i_table.keys())}")
                
                if 'index' in i_table.keys():
                    index_group = i_table['index']
                    print(f"index 组结构: {list(index_group.keys())}")
                    
                    # 遍历 index 组中的所有项
                    for key in index_group.keys():
                        item = index_group[key]
                        print(f"\n{key} 类型: {type(item)}")
                        if hasattr(item, 'shape'):
                            print(f"{key} 形状: {item.shape}")
                            print(f"{key} 数据类型: {item.dtype}")
                            try:
                                data = item[:]
                                print(f"{key} 前5个值: {data[:5]}")
                                print(f"{key} 后5个值: {data[-5:]}")
                                
                                # 尝试转换为日期
                                if data.dtype == 'int64' and len(str(data[0])) == 8:
                                    try:
                                        dates = pd.to_datetime(data, format='%Y%m%d')
                                        print(f"{key} 日期范围: {dates.min().date()} 到 {dates.max().date()}")
                                    except:
                                        print(f"{key} 无法转换为日期")
                            except Exception as e:
                                print(f"读取 {key} 失败: {e}")
except Exception as e:
    print(f"使用 h5py 读取失败: {e}")