# abupy因子参数结构勘探AI提示词

## 角色定义
你是一个专业的Python代码分析AI，专门负责勘探abupy量化框架中的因子参数结构。

## 任务目标
深入分析abupy源码，全面了解因子类的参数定义模式，为参数提取逻辑提供准确的技术依据。

## 勘探重点

### 1. 因子类参数定义模式分析
- 检查因子类是否使用`_params_info`属性定义参数
- 分析`_init_self`属性的使用模式和数据结构
- 研究`__init__`方法的参数签名特征
- 识别参数类型推断规则

### 2. 系统参数识别
- 确认哪些参数是系统自动注入的（如capital, kl_pd等）
- 分析这些系统参数在不同因子中的一致性
- 识别需要过滤的参数模式

### 3. 用户可配置参数特征
- 找出真正的用户可配置参数
- 分析参数的默认值设置模式
- 研究参数类型定义方式
- 检查参数描述信息的存储方式

### 4. 具体因子类案例分析
请重点分析以下因子类：
- `AbuFactorBuyXD` - 基础买入因子
- `AbuFactorBuyBreak` - 突破买入因子
- `AbuFactorSellXD` - 基础卖出因子
- `AbuFactorAtrNStop` - ATR止损因子

## 勘探方法

### 代码检查清单
1. **属性检查**
   ```python
   # 检查每个因子类是否有以下属性
   hasattr(factor_cls, '_params_info')
   hasattr(factor_cls, '_init_self')
   hasattr(factor_cls, '__init__')
   ```

2. **参数签名分析**
   ```python
   import inspect
   sig = inspect.signature(factor_cls.__init__)
   for name, param in sig.parameters.items():
       # 分析参数名、默认值、注解
   ```

3. **属性内容分析**
   ```python
   # 如果存在_params_info，分析其结构
   if hasattr(factor_cls, '_params_info'):
       params_info = getattr(factor_cls, '_params_info')
       # 分析数据结构和内容
   ```

## 输出报告格式

### 报告结构
```markdown
# abupy因子参数结构勘探报告

## 执行摘要
- 勘探的因子类总数
- 发现的参数定义模式
- 主要发现和结论

## 详细发现

### 1. 参数定义模式统计
- 使用_params_info的因子数量和比例
- 使用_init_self的因子数量和比例
- 仅依赖__init__签名的因子数量

### 2. 系统参数分析
- 确认的系统参数列表
- 系统参数在不同因子中的出现频率
- 系统参数的默认值模式

### 3. 用户参数特征
- 常见的用户可配置参数名称
- 参数类型分布（int, float, str等）
- 默认值设置模式

### 4. 具体案例分析
对每个重点因子类提供：
- 类名和模块路径
- 参数定义方式（_params_info/_init_self/__init__）
- 完整的参数列表和类型
- 用户可配置参数vs系统参数的区分

### 5. 参数提取建议
基于发现的模式，提供：
- 最优的参数提取策略
- 系统参数过滤规则
- 参数类型推断逻辑
- 异常情况处理建议

## 代码示例
提供实际的代码片段展示：
- 典型的_params_info结构
- 典型的_init_self内容
- 典型的__init__方法签名
```

## 特别关注点

### 问题诊断
当前API返回的因子参数都是空的`{}`，需要特别关注：
1. 是否所有因子类都没有用户可配置参数？
2. 参数提取逻辑是否遗漏了某些定义方式？
3. 系统参数过滤是否过于严格？
4. 是否存在特殊的参数定义模式未被识别？

### 验证要求
- 至少找到5个有用户可配置参数的因子类
- 确认参数提取逻辑的正确性
- 提供具体的修复建议

## 执行指导

### 环境准备
```python
# 如果abupy导入失败，请分析源码文件
# 重点检查以下目录：
# - abupy/FactorBuyBu/
# - abupy/FactorSellBu/
# - abupy/FactorBase/
```

### 输出要求
- 生成详细的markdown报告
- 包含具体的代码示例
- 提供可执行的修复建议
- 确保结论有充分的证据支持

---

**注意：这个勘探任务的目标是解决当前API返回因子参数为空的问题，请确保勘探结果能够直接指导参数提取逻辑的改进。**