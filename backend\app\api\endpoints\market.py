"""市场数据API端点

该模块提供市场数据相关的API端点，包括获取K线数据、基本面数据等功能。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, HTTPException, Path
from pydantic import BaseModel

from backend.app.schemas.market import (
    KlineData, 
    KlineRequest, 
    KlineResponse,
    FundamentalData,
    FundamentalResponse,
    StockListRequest,
    StockListResponse,
    StockItem
)
from backend.app.services.market.kline_provider import KlineProvider
from backend.app.services.market.fundamental_provider import FundamentalProvider
from backend.app.services.market.symbol_provider import SymbolProvider
from backend.app.core.exceptions import (
    ResourceNotFoundError, 
    ExternalAPIError,
    ValidationError
)

# 注意：异常类不需要显式导入，因为我们使用了全局异常处理器

router = APIRouter(prefix="/market", tags=["market"])


@router.get("/klines", response_model=KlineResponse)
async def get_klines(
    symbol: str = Query(..., description="股票代码，例如：sh000001, AAPL, 0700.HK"),
    market: str = Query(..., description="市场类型：A股(CN)、美股(US)、港股(HK)"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYYMMDD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYYMMDD"),
    limit: Optional[int] = Query(None, description="获取K线数量"),
    ktype: str = Query("D", description="K线周期，D=日, W=周, M=月, m5=5分钟, m15=15分钟, m30=30分钟, m60=60分钟")
) -> KlineResponse:
    """获取股票K线数据"""
    try:
        # 构建请求对象
        request = KlineRequest(
            symbol=symbol,
            market=market,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            ktype=ktype
        )
        
        # 调用服务获取K线数据
        kline_data = KlineProvider.get_klines(request)
        
        # 构建响应
        return KlineResponse(
            symbol=symbol,
            market=market,
            ktype=ktype,
            data=kline_data
        )
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取K线数据失败: {str(e)}")


@router.get("/fundamental/{symbol}", response_model=FundamentalResponse)
async def get_fundamental(
    symbol: str = Path(..., description="股票代码，例如：sh000001, AAPL, 0700.HK"),
    market: str = Query(..., description="市场类型：A股(CN)、美股(US)、港股(HK)"),
    date: Optional[str] = Query(None, description="日期 YYYYMMDD，默认为最新")
) -> FundamentalResponse:
    """获取股票基本面数据"""
    try:
        # 调用服务获取基本面数据
        fundamental_data = FundamentalProvider.get_fundamental(symbol, market, date)
        
        # 构建响应
        return FundamentalResponse(
            symbol=symbol,
            market=market,
            date=date or "latest",
            data=fundamental_data
        )
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取基本面数据失败: {str(e)}")


@router.get("/stocks", response_model=StockListResponse)
async def get_stock_list(
    market: str = Query(..., description="市场类型：A股(CN)、美股(US)、港股(HK)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    name_filter: Optional[str] = Query(None, description="名称过滤条件"),
    limit: Optional[int] = Query(None, description="返回数量限制")
) -> StockListResponse:
    """获取股票列表"""
    try:
        # 构建请求对象
        request = StockListRequest(
            market=market,
            industry=industry,
            name_filter=name_filter,
            limit=limit
        )
        
        # 调用服务获取股票列表
        stock_list = SymbolProvider.get_stock_list(request)
        
        # 构建响应
        return StockListResponse(
            market=market,
            count=len(stock_list),
            data=stock_list
        )
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ExternalAPIError as e:
        raise HTTPException(status_code=502, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")
