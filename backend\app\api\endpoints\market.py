# -*- coding: utf-8 -*-
"""
市场数据API端点
"""
from typing import List, Optional
from fastapi import APIRouter, Query
import logging

from app.services.market_service import MarketService
from app.schemas.market import StockBasic, KlineData, StockFundamental
# 注意：异常类不需要显式导入，因为我们使用了全局异常处理器

router = APIRouter()
market_service = MarketService()


@router.get("/stock/list", response_model=List[StockBasic])
async def get_stock_list(
    market: Optional[str] = Query(None, description="市场类型：A股(CN)、美股(US)、港股(HK)"),
    industry: Optional[str] = Query(None, description="行业类型"),
    name: Optional[str] = Query(None, description="股票名称模糊查询")
):
    """
    获取股票列表
    """
    logging.debug(f"获取股票列表: market={market}, industry={industry}, name={name}")
    return market_service.get_stock_list(market, industry, name)


@router.get("/kline/{symbol}", response_model=KlineData)
async def get_kline_data(
    symbol: str,
    start_date: Optional[str] = Query(None, description="开始日期 YYYYMMDD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYYMMDD"),
    period: str = Query("daily", description="周期：daily, weekly, monthly")
):
    """
    获取K线数据
    """
    logging.debug(f"获取K线数据: symbol={symbol}, start_date={start_date}, end_date={end_date}, period={period}")
    return market_service.get_kline_data(symbol, start_date, end_date, period)


@router.get("/fundamental/{symbol}", response_model=StockFundamental)
async def get_fundamental_data(
    symbol: str,
    date: Optional[str] = Query(None, description="日期 YYYYMMDD，默认为最新")
):
    """
    获取基本面数据
    """
    logging.debug(f"获取基本面数据: symbol={symbol}, date={date}")
    return market_service.get_fundamental_data(symbol, date)
