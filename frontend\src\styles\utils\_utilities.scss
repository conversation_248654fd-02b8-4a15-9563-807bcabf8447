// 工具类样式

// 显示/隐藏
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

// Flex布局
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

// 对齐
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

// 文本对齐
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

// 文本样式
.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.font-weight-light { font-weight: $font-weight-light !important; }
.font-weight-normal { font-weight: $font-weight-normal !important; }
.font-weight-medium { font-weight: $font-weight-medium !important; }
.font-weight-semibold { font-weight: $font-weight-semibold !important; }
.font-weight-bold { font-weight: $font-weight-bold !important; }

.font-italic { font-style: italic !important; }

// 文本颜色
.text-primary { color: $text-primary !important; }
.text-secondary { color: $text-secondary !important; }
.text-disabled { color: $text-disabled !important; }
.text-inverse { color: $text-inverse !important; }
.text-success { color: $success-color !important; }
.text-warning { color: $warning-color !important; }
.text-error { color: $error-color !important; }
.text-info { color: $info-color !important; }
.text-profit { color: $profit-color !important; }
.text-loss { color: $loss-color !important; }
.text-neutral { color: $neutral-color !important; }

// 背景颜色
.bg-primary { background-color: $bg-primary !important; }
.bg-secondary { background-color: $bg-secondary !important; }
.bg-tertiary { background-color: $bg-tertiary !important; }
.bg-dark { background-color: $bg-dark !important; }
.bg-success { background-color: $success-color !important; }
.bg-warning { background-color: $warning-color !important; }
.bg-error { background-color: $error-color !important; }
.bg-info { background-color: $info-color !important; }

// 边框
.border { border: $border-width-thin solid $border-color !important; }
.border-top { border-top: $border-width-thin solid $border-color !important; }
.border-right { border-right: $border-width-thin solid $border-color !important; }
.border-bottom { border-bottom: $border-width-thin solid $border-color !important; }
.border-left { border-left: $border-width-thin solid $border-color !important; }
.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

// 圆角
.rounded { border-radius: $border-radius-base !important; }
.rounded-sm { border-radius: $border-radius-sm !important; }
.rounded-lg { border-radius: $border-radius-lg !important; }
.rounded-xl { border-radius: $border-radius-xl !important; }
.rounded-circle { border-radius: $border-radius-round !important; }
.rounded-0 { border-radius: 0 !important; }

// 位置
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// 溢出
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

// 宽度
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

// 高度
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

// 间距 - Margin
@each $size, $value in (
  0: 0,
  1: $spacing-xs,
  2: $spacing-sm,
  3: $spacing-md,
  4: $spacing-lg,
  5: $spacing-xl,
  auto: auto
) {
  .m-#{$size} { margin: $value !important; }
  .mt-#{$size} { margin-top: $value !important; }
  .mr-#{$size} { margin-right: $value !important; }
  .mb-#{$size} { margin-bottom: $value !important; }
  .ml-#{$size} { margin-left: $value !important; }
  .mx-#{$size} {
    margin-left: $value !important;
    margin-right: $value !important;
  }
  .my-#{$size} {
    margin-top: $value !important;
    margin-bottom: $value !important;
  }
}

// 间距 - Padding
@each $size, $value in (
  0: 0,
  1: $spacing-xs,
  2: $spacing-sm,
  3: $spacing-md,
  4: $spacing-lg,
  5: $spacing-xl
) {
  .p-#{$size} { padding: $value !important; }
  .pt-#{$size} { padding-top: $value !important; }
  .pr-#{$size} { padding-right: $value !important; }
  .pb-#{$size} { padding-bottom: $value !important; }
  .pl-#{$size} { padding-left: $value !important; }
  .px-#{$size} {
    padding-left: $value !important;
    padding-right: $value !important;
  }
  .py-#{$size} {
    padding-top: $value !important;
    padding-bottom: $value !important;
  }
}

// 阴影
.shadow-sm {
  box-shadow: 0 1px 2px 0 $shadow-light !important;
}

.shadow {
  box-shadow: 0 1px 3px 0 $shadow-light, 0 1px 2px 0 $shadow-medium !important;
}

.shadow-md {
  box-shadow: 0 4px 6px -1px $shadow-light, 0 2px 4px -1px $shadow-medium !important;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px $shadow-light, 0 4px 6px -2px $shadow-medium !important;
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px $shadow-light, 0 10px 10px -5px $shadow-medium !important;
}

.shadow-none {
  box-shadow: none !important;
}

// 基于CSS自定义属性的工具类
// 间距工具类 (使用CSS变量)
.space-xs { gap: var(--space-xs) !important; }
.space-sm { gap: var(--space-sm) !important; }
.space-md { gap: var(--space-md) !important; }
.space-lg { gap: var(--space-lg) !important; }
.space-xl { gap: var(--space-xl) !important; }

// 字体大小工具类 (使用CSS变量)
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-xxl { font-size: var(--font-size-xxl) !important; }
.text-xxxl { font-size: var(--font-size-xxxl) !important; }

// 字重工具类 (使用CSS变量)
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

// 颜色工具类 (使用CSS变量)
.text-profit { color: var(--color-profit) !important; }
.text-loss { color: var(--color-loss) !important; }
.text-neutral { color: var(--color-neutral) !important; }

.bg-profit { background-color: var(--bg-color-profit) !important; }
.bg-loss { background-color: var(--bg-color-loss) !important; }
.bg-neutral { background-color: var(--bg-color-neutral) !important; }

// 边框半径工具类 (使用CSS变量)
.rounded-sm-var { border-radius: var(--border-radius-sm) !important; }
.rounded-base-var { border-radius: var(--border-radius-base) !important; }
.rounded-lg-var { border-radius: var(--border-radius-lg) !important; }
.rounded-xl-var { border-radius: var(--border-radius-xl) !important; }

// 阴影工具类 (使用CSS变量)
.shadow-1 { box-shadow: var(--shadow-1) !important; }
.shadow-2 { box-shadow: var(--shadow-2) !important; }
.shadow-3 { box-shadow: var(--shadow-3) !important; }
.shadow-4 { box-shadow: var(--shadow-4) !important; }
.shadow-5 { box-shadow: var(--shadow-5) !important; }

// 过渡动画工具类 (使用CSS变量)
.transition-fast { transition: var(--transition-fast) !important; }
.transition-base { transition: var(--transition-base) !important; }
.transition-slow { transition: var(--transition-slow) !important; }

// 量化投资特定工具类
.profit-indicator {
  color: var(--color-profit) !important;
  font-weight: var(--font-weight-medium) !important;
}

.loss-indicator {
  color: var(--color-loss) !important;
  font-weight: var(--font-weight-medium) !important;
}

.neutral-indicator {
  color: var(--color-neutral) !important;
  font-weight: var(--font-weight-normal) !important;
}

// 卡片样式工具类
.card-padding {
  padding: var(--card-body-padding) !important;
}

.card-header-padding {
  padding: var(--card-header-padding) !important;
}

// 布局工具类
.sidebar-width {
  width: var(--sidebar-width) !important;
}

.sidebar-width-collapsed {
  width: var(--sidebar-width-collapsed) !important;
}

.header-height {
  height: var(--header-height) !important;
}