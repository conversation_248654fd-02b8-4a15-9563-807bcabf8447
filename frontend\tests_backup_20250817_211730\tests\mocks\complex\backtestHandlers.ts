// tests/mocks/backtestHandlers.ts

import { http, HttpResponse, delay } from 'msw';
import type {
  BacktestConfig,
  BacktestTask,
  BacktestResult,
  BacktestHistoryParams,
  BacktestTaskResponse,
  BacktestResultResponse,
  BacktestHistoryResponse,
  BacktestProgressResponse,
  BacktestStopResponse
} from '../../src/api/types/backtest';
import { BacktestStatus } from '../../src/api/types/backtest';
import { BacktestDataFactory } from '../factories/BacktestDataFactory';
import {
  ApiSchemas,
  ContractValidator,
  type StrategyExecuteRequest,
  type StrategyExecuteResponse
} from '../contracts/api-schemas';

// 定义错误响应类型
type ApiErrorResponse = {
  success: false;
  error: {
    code: string;
    message: string;
    details?: string;
  };
};

/**
 * 模拟的回测任务存储
 */
class MockBacktestStorage {
  private tasks: Map<string, BacktestTask> = new Map();
  private results: Map<string, BacktestResult> = new Map();
  private taskCounter = 1;
  public currentScenario: 'profitable' | 'loss' | 'mixed' | 'no_trades' | 'crisis' | 'precision' | 'large_dataset' = 'profitable';
  private contractViolations: Array<{ api: string, error: string, timestamp: string }> = [];
  
  // 状态化任务管理
  private taskStates: Map<string, {
    phase: 'initializing' | 'data_loading' | 'calculating' | 'finalizing';
    startTime: number;
    estimatedDuration: number;
    resourceUsage: { cpu: number; memory: number };
  }> = new Map();
  
  // 性能测试配置
  private performanceConfig = {
    networkJitter: false,
    highLatency: false,
    concurrentLimit: 5,
    largeDatasetMode: false
  };
  
  // 业务验证规则
  private businessRules = {
    maxBacktestDuration: 365 * 5, // 最大5年
    minCapital: 10000, // 最小1万
    maxCapital: 100000000, // 最大1亿
    maxConcurrentTasks: 10,
    supportedSymbols: ['000001', '000002', '600000', '600036', 'AAPL', 'TSLA'],
    tradingHours: { start: '09:30', end: '15:00' },
    maxCommissionRate: 0.003, // 最大手续费率0.3%
    maxSlippageRate: 0.01 // 最大滑点1%
  };

  /**
   * 创建新的回测任务
   */
  createTask(config: BacktestConfig): BacktestTask {
    const taskId = `task-${this.taskCounter.toString().padStart(6, '0')}`;
    this.taskCounter++;

    const task: BacktestTask = {
      id: taskId,
      strategy_id: config.strategy_id,
      strategy_name: `策略-${config.strategy_id}`,
      symbol: config.symbol,
      start_date: config.start_date,
      end_date: config.end_date,
      status: BacktestStatus.PENDING,
      progress: 0,
      created_at: new Date().toISOString(),
      config
    };

    this.tasks.set(taskId, task);
    
    // 初始化任务状态
    this.initializeTaskState(taskId, config);
    
    // 模拟异步执行
    this.simulateBacktestExecution(taskId);
    
    return task;
  }
  
  /**
   * 初始化任务状态
   */
  private initializeTaskState(taskId: string, config: BacktestConfig): void {
    const startDate = new Date(config.start_date);
    const endDate = new Date(config.end_date);
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // 根据数据量估算执行时间
    let estimatedDuration = daysDiff * 10; // 基础：每天10ms
    
    if (this.performanceConfig.largeDatasetMode) {
      estimatedDuration *= 10; // 大数据集模式增加10倍时间
    }
    
    this.taskStates.set(taskId, {
      phase: 'initializing',
      startTime: Date.now(),
      estimatedDuration,
      resourceUsage: { cpu: 0, memory: 0 }
    });
  }

  /**
   * 获取任务
   */
  getTask(taskId: string): BacktestTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取结果
   */
  getResult(taskId: string): BacktestResult | undefined {
    return this.results.get(taskId);
  }

  /**
   * 获取所有任务（用于历史查询）
   */
  getAllTasks(): BacktestTask[] {
    return Array.from(this.tasks.values()).sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  /**
   * 停止任务
   */
  stopTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (task && task.status === BacktestStatus.RUNNING) {
      task.status = BacktestStatus.STOPPED;
      task.completed_at = new Date().toISOString();
      return true;
    }
    return false;
  }

  /**
   * 模拟回测执行过程（增强版）
   */
  private async simulateBacktestExecution(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    const taskState = this.taskStates.get(taskId);
    if (!task || !taskState) return;

    try {
      // 阶段1：初始化
      await this.simulatePhase(taskId, 'initializing', 500);
      
      // 阶段2：数据加载
      await this.simulatePhase(taskId, 'data_loading', taskState.estimatedDuration * 0.3);
      
      // 阶段3：计算
      await this.simulatePhase(taskId, 'calculating', taskState.estimatedDuration * 0.6);
      
      // 阶段4：结果整理
      await this.simulatePhase(taskId, 'finalizing', taskState.estimatedDuration * 0.1);
      
      // 检查是否被停止
      const currentTask = this.tasks.get(taskId);
      if (currentTask && currentTask.status === BacktestStatus.STOPPED) {
        this.taskStates.delete(taskId);
        return;
      }
      
      // 根据场景决定结果
      const shouldFail = this.shouldSimulateFailure(task);
      if (shouldFail.fail) {
        task.status = BacktestStatus.FAILED;
        task.error_message = shouldFail.reason;
        task.completed_at = new Date().toISOString();
        this.taskStates.delete(taskId);
        return;
      }
      
      // 成功完成
      task.status = BacktestStatus.COMPLETED;
      task.progress = 100;
      task.completed_at = new Date().toISOString();
      
      // 生成结果
      const result = BacktestDataFactory.createBacktestResult(this.currentScenario, {
        task_id: taskId,
        symbol: task.symbol,
        start_date: task.start_date,
        end_date: task.end_date,
        initial_capital: task.config.capital
      });
      
      this.results.set(taskId, result);
      this.taskStates.delete(taskId);
      
    } catch (error) {
      task.status = BacktestStatus.FAILED;
      task.error_message = `执行异常: ${error}`;
      task.completed_at = new Date().toISOString();
      this.taskStates.delete(taskId);
    }
  }
  
  /**
   * 模拟执行阶段
   */
  private async simulatePhase(
    taskId: string, 
    phase: 'initializing' | 'data_loading' | 'calculating' | 'finalizing',
    duration: number
  ): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;
    
    // 更新任务状态
    task.status = BacktestStatus.RUNNING;
    if (!task.started_at) {
      task.started_at = new Date().toISOString();
    }
    
    this.updateTaskState(taskId, { 
      phase,
      resourceUsage: this.generateResourceUsage(phase)
    });
    
    // 模拟阶段进度
    const phaseProgressMap = {
      initializing: { start: 0, end: 10 },
      data_loading: { start: 10, end: 40 },
      calculating: { start: 40, end: 90 },
      finalizing: { start: 90, end: 100 }
    };
    
    const { start, end } = phaseProgressMap[phase];
    const steps = 5;
    const stepDuration = duration / steps;
    const progressStep = (end - start) / steps;
    
    for (let i = 0; i < steps; i++) {
      const currentTask = this.tasks.get(taskId);
      if (currentTask && currentTask.status === BacktestStatus.STOPPED) {
        return;
      }
      
      if (currentTask) {
        currentTask.progress = start + (i + 1) * progressStep;
      }
      
      // 添加网络抖动
      const delay = this.performanceConfig.networkJitter 
        ? stepDuration + (Math.random() - 0.5) * stepDuration * 0.5
        : stepDuration;
        
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  /**
   * 生成资源使用情况
   */
  private generateResourceUsage(phase: string): { cpu: number; memory: number } {
    const baseUsage = {
      initializing: { cpu: 20, memory: 100 },
      data_loading: { cpu: 60, memory: 300 },
      calculating: { cpu: 90, memory: 500 },
      finalizing: { cpu: 30, memory: 200 }
    };
    
    const base = baseUsage[phase as keyof typeof baseUsage] || { cpu: 50, memory: 200 };
    
    return {
      cpu: base.cpu + Math.random() * 20 - 10,
      memory: base.memory + Math.random() * 100 - 50
    };
  }
  
  /**
   * 判断是否应该模拟失败
   */
  private shouldSimulateFailure(task: BacktestTask): { fail: boolean; reason?: string } {
    // 基础失败概率
    if (Math.random() < 0.05) {
      const reasons = [
        '数据源连接超时',
        '内存不足',
        '计算资源不足',
        '数据质量异常',
        '网络连接中断'
      ];
      return {
        fail: true,
        reason: reasons[Math.floor(Math.random() * reasons.length)]
      };
    }
    
    // 特定场景的失败
    if (this.currentScenario === 'crisis' && Math.random() < 0.3) {
      return {
        fail: true,
        reason: '极端市场条件下数据处理异常'
      };
    }
    
    if (this.currentScenario === 'large_dataset' && Math.random() < 0.2) {
      return {
        fail: true,
        reason: '大数据集处理超时'
      };
    }
    
    return { fail: false };
  }

  /**
   * 清空所有数据（用于测试重置）
   */
  clear(): void {
    this.tasks.clear();
    this.results.clear();
    this.taskCounter = 1;
    this.currentScenario = 'profitable';
    this.contractViolations = [];
  }

  /**
   * 设置测试场景
   */
  setScenario(scenario: 'profitable' | 'loss' | 'mixed' | 'no_trades' | 'crisis' | 'precision' | 'large_dataset'): void {
    this.currentScenario = scenario;
  }
  
  /**
   * 增强的业务验证
   */
  validateBusinessRules(config: BacktestConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // 验证回测时间范围
    const startDate = new Date(config.start_date);
    const endDate = new Date(config.end_date);
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff > this.businessRules.maxBacktestDuration) {
      errors.push(`回测时间范围不能超过${this.businessRules.maxBacktestDuration}天`);
    }
    
    // 验证资金范围
    if (config.capital < this.businessRules.minCapital) {
      errors.push(`初始资金不能少于${this.businessRules.minCapital}元`);
    }
    
    if (config.capital > this.businessRules.maxCapital) {
      errors.push(`初始资金不能超过${this.businessRules.maxCapital}元`);
    }
    
    // 验证股票代码
    if (!this.businessRules.supportedSymbols.includes(config.symbol)) {
      errors.push(`不支持的股票代码: ${config.symbol}`);
    }
    
    // 验证手续费率
    if (config.commission && config.commission > this.businessRules.maxCommissionRate) {
      errors.push(`手续费率不能超过${this.businessRules.maxCommissionRate * 100}%`);
    }
    
    // 验证滑点
    if (config.slippage && config.slippage > this.businessRules.maxSlippageRate) {
      errors.push(`滑点不能超过${this.businessRules.maxSlippageRate * 100}%`);
    }
    
    // 验证并发任务数量
    const runningTasks = Array.from(this.tasks.values()).filter(
      task => task.status === BacktestStatus.RUNNING
    ).length;
    
    if (runningTasks >= this.businessRules.maxConcurrentTasks) {
      errors.push(`并发任务数量已达上限(${this.businessRules.maxConcurrentTasks})`);
    }
    
    // 验证交易时间（如果配置了特定时间）
    if ((config as any).trading_hours) {
      const { start, end } = (config as any).trading_hours;
      if (start < this.businessRules.tradingHours.start || end > this.businessRules.tradingHours.end) {
        errors.push(`交易时间必须在${this.businessRules.tradingHours.start}-${this.businessRules.tradingHours.end}之间`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * 配置性能测试模式
   */
  setPerformanceConfig(config: Partial<typeof this.performanceConfig>): void {
    this.performanceConfig = { ...this.performanceConfig, ...config };
  }
  
  /**
   * 获取任务状态详情
   */
  getTaskState(taskId: string): any {
    return this.taskStates.get(taskId);
  }
  
  /**
   * 更新任务状态
   */
  updateTaskState(taskId: string, updates: Partial<{
    phase: 'initializing' | 'data_loading' | 'calculating' | 'finalizing';
    resourceUsage: { cpu: number; memory: number };
  }>): void {
    const currentState = this.taskStates.get(taskId);
    if (currentState) {
      this.taskStates.set(taskId, { ...currentState, ...updates });
    }
  }

  /**
   * 记录契约违规
   */
  recordContractViolation(api: string, error: string): void {
    this.contractViolations.push({
      api,
      error,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取契约违规记录
   */
  getContractViolations(): Array<{ api: string, error: string, timestamp: string }> {
    return [...this.contractViolations];
  }

  /**
   * 清除契约违规记录
   */
  clearContractViolations(): void {
    this.contractViolations = [];
  }
}

// 全局存储实例
const mockStorage = new MockBacktestStorage();

// 初始化一些历史数据
mockStorage.clear();
const historyTasks = BacktestDataFactory.createBacktestHistory(10, ['completed', 'failed', 'running']);
historyTasks.forEach(task => {
  mockStorage['tasks'].set(task.id, task);
  if (task.status === BacktestStatus.COMPLETED) {
    const result = BacktestDataFactory.createBacktestResult('mixed', {
      task_id: task.id,
      symbol: task.symbol,
      start_date: task.start_date,
      end_date: task.end_date
    });
    mockStorage['results'].set(task.id, result);
  }
});

/**
 * 验证回测配置
 */
function validateBacktestConfig(config: any): string[] {
  const errors: string[] = [];

  if (!config.strategy_id) {
    errors.push('策略ID不能为空');
  }

  if (!config.symbol) {
    errors.push('股票代码不能为空');
  }

  if (!config.start_date) {
    errors.push('开始日期不能为空');
  }

  if (!config.end_date) {
    errors.push('结束日期不能为空');
  }

  if (config.start_date && config.end_date) {
    const startDate = new Date(config.start_date);
    const endDate = new Date(config.end_date);
    if (startDate >= endDate) {
      errors.push('开始日期必须早于结束日期');
    }
  }

  if (typeof config.capital !== 'number' || config.capital <= 0) {
    errors.push('初始资金必须大于0');
  }

  if (config.commission !== undefined && (typeof config.commission !== 'number' || config.commission < 0)) {
    errors.push('手续费率不能为负');
  }

  if (config.slippage !== undefined && (typeof config.slippage !== 'number' || config.slippage < 0)) {
    errors.push('滑点不能为负');
  }

  return errors;
}

/**
 * 增强的网络延迟模拟
 */
async function simulateNetworkDelay(config?: {
  baseDelay?: number;
  jitter?: boolean;
  highLatency?: boolean;
}): Promise<void> {
  const { baseDelay = 200, jitter = false, highLatency = false } = config || {};
  
  let delayTime = baseDelay;
  
  // 高延迟模式
  if (highLatency) {
    delayTime += 1000 + Math.random() * 2000; // 额外1-3秒
  }
  
  // 网络抖动
  if (jitter) {
    const jitterAmount = delayTime * 0.5; // 50%的抖动
    delayTime += (Math.random() - 0.5) * jitterAmount;
  } else {
    delayTime += Math.random() * 300; // 基础随机延迟
  }
  
  await delay(Math.max(50, delayTime)); // 最小50ms延迟
}

/**
 * 增强的网络错误模拟
 */
function shouldSimulateError(errorRate: number = 0.1): boolean {
  return Math.random() < errorRate;
}

/**
 * 模拟特定类型的网络错误
 */
function simulateSpecificError(): { status: number; error: any } {
  const errorTypes = [
    {
      status: 500,
      error: {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
          details: '数据库连接失败'
        }
      }
    },
    {
      status: 503,
      error: {
        success: false,
        error: {
          code: 'SERVICE_UNAVAILABLE',
          message: '服务暂时不可用',
          details: '系统维护中'
        }
      }
    },
    {
      status: 429,
      error: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: '请求频率过高',
          details: '请稍后重试'
        }
      }
    },
    {
      status: 408,
      error: {
        success: false,
        error: {
          code: 'REQUEST_TIMEOUT',
          message: '请求超时',
          details: '网络连接超时'
        }
      }
    }
  ];
  
  return errorTypes[Math.floor(Math.random() * errorTypes.length)];
}

/**
 * 获取状态描述信息
 */
function getStatusMessage(status: BacktestStatus, phase?: string): string {
  const messages = {
    [BacktestStatus.PENDING]: '任务等待中',
    [BacktestStatus.RUNNING]: phase ? `正在${getPhaseDescription(phase)}` : '任务执行中',
    [BacktestStatus.COMPLETED]: '任务已完成',
    [BacktestStatus.FAILED]: '任务执行失败',
    [BacktestStatus.STOPPED]: '任务已停止'
  };
  return messages[status] || '未知状态';
}

/**
 * 获取阶段描述
 */
function getPhaseDescription(phase: string): string {
  const descriptions = {
    initializing: '初始化',
    data_loading: '加载数据',
    calculating: '计算中',
    finalizing: '整理结果'
  };
  return descriptions[phase as keyof typeof descriptions] || phase;
}

/**
 * MSW 处理器定义
 */
export const backtestHandlers = [
  /**
   * POST /api/v1/strategy/:strategyId/execute - 启动回测（符合后端API契约）
   */
  http.post('/api/v1/strategy/:strategyId/execute', async ({ request, params }) => {
    const { strategyId } = params;
    
    try {
      const requestData = await request.json() as StrategyExecuteRequest;
      
      // 1. 契约验证
      const isValidRequest = ContractValidator.validateRequest(
        requestData,
        ApiSchemas.StrategyExecuteRequest,
        'StrategyExecuteRequest'
      );
      
      if (!isValidRequest) {
        const errorResponse: ApiErrorResponse = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: ContractValidator.getValidationErrors().join('; ')
          }
        };
        mockStorage.recordContractViolation('StrategyExecuteRequest', '请求格式不符合契约');
        return HttpResponse.json(errorResponse, { status: 400 });
      }
      
      // 转换为BacktestConfig格式进行业务规则验证
      const backtestConfig: BacktestConfig = {
        strategy_id: params.strategyId as string,
        symbol: requestData.choice_symbols[0],
        start_date: requestData.start_date,
        end_date: requestData.end_date,
        capital: requestData.capital || 1000000,
        benchmark: requestData.benchmark_symbol
      };
      const businessValidation = mockStorage.validateBusinessRules(backtestConfig);
      if (!businessValidation.isValid) {
        const errorResponse: ApiErrorResponse = {
          success: false,
          error: {
            code: 'BUSINESS_RULE_VIOLATION',
            message: '业务规则验证失败',
            details: businessValidation.errors.join('; ')
          }
        };
        return HttpResponse.json(errorResponse, { status: 422 });
      }
      
      // 3. 网络延迟模拟
      await simulateNetworkDelay({
        jitter: mockStorage['performanceConfig'].networkJitter,
        highLatency: mockStorage['performanceConfig'].highLatency
      });
      
      // 4. 网络错误模拟
      if (shouldSimulateError(0.08)) { // 8%错误率
        const errorInfo = simulateSpecificError();
        return HttpResponse.json(errorInfo.error, { status: errorInfo.status });
      }
      
      // 5. 并发限制检查
      const runningTasks = Array.from(mockStorage['tasks'].values()).filter(
        task => task.status === BacktestStatus.RUNNING
      ).length;
      
      if (runningTasks >= mockStorage['businessRules'].maxConcurrentTasks) {
        const errorResponse: ApiErrorResponse = {
          success: false,
          error: {
            code: 'RESOURCE_LIMIT_EXCEEDED',
            message: '并发任务数量已达上限',
            details: `当前运行任务: ${runningTasks}, 最大限制: ${mockStorage['businessRules'].maxConcurrentTasks}`
          }
        };
        return HttpResponse.json(errorResponse, { status: 429 });
      }
      
      // 6. 创建任务
      const task = mockStorage.createTask(backtestConfig);
      
      // 7. 生成符合契约的响应
      const validScenarios = ['profitable', 'loss', 'mixed', 'no_trades'] as const;
      const scenario = validScenarios.includes(mockStorage.currentScenario as any) 
        ? mockStorage.currentScenario as 'profitable' | 'loss' | 'mixed' | 'no_trades'
        : 'profitable';
      const response = BacktestDataFactory.createStrategyExecuteResponse(
        scenario,
        requestData
      );
      
      // 任务ID已在响应中设置
      
      // 8. 验证响应符合契约
      const isValidResponse = ContractValidator.validateResponse(
        response,
        ApiSchemas.StrategyExecuteResponse,
        'StrategyExecuteResponse'
      );
      
      if (!isValidResponse) {
        console.error('生成的响应不符合API契约');
        mockStorage.recordContractViolation('StrategyExecuteResponse', '响应格式不符合契约');
        
        // 返回内部错误
        const errorResponse: ApiErrorResponse = {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: '响应生成失败',
            details: '内部数据格式错误'
          }
        };
        return HttpResponse.json(errorResponse, { status: 500 });
      }
      
      return HttpResponse.json(response);
      
    } catch (error) {
      const errorResponse: ApiErrorResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '请求处理异常',
          details: String(error)
        }
      };
      return HttpResponse.json(errorResponse, { status: 500 });
    }
  }),

  /**
   * POST /api/backtest/run - 启动回测（兼容旧接口）
   */
  http.post('/api/backtest/run', async ({ request }) => {
    // 模拟网络延迟和抖动
    await simulateNetworkDelay({
      jitter: mockStorage['performanceConfig'].networkJitter,
      highLatency: mockStorage['performanceConfig'].highLatency
    });

    // 网络错误模拟
    if (shouldSimulateError(0.1)) {
      const errorInfo = simulateSpecificError();
      return HttpResponse.json(errorInfo.error, { status: errorInfo.status });
    }

    try {
      const config = await request.json() as BacktestConfig;
      
      // 1. 基础参数验证
      const basicErrors = validateBacktestConfig(config);
      if (basicErrors.length > 0) {
        return HttpResponse.json(
          {
            success: false,
            message: '请求参数无效',
            errors: basicErrors.reduce((acc, error, index) => {
              acc[`error_${index}`] = error;
              return acc;
            }, {} as Record<string, string>)
          },
          { status: 400 }
        );
      }
      
      // 2. 业务规则验证
      const businessValidation = mockStorage.validateBusinessRules(config);
      if (!businessValidation.isValid) {
        return HttpResponse.json(
          {
            success: false,
            message: '业务规则验证失败',
            errors: businessValidation.errors.reduce((acc, error, index) => {
              acc[`business_error_${index}`] = error;
              return acc;
            }, {} as Record<string, string>)
          },
          { status: 422 }
        );
      }
      
      // 3. 并发限制检查
      const runningTasks = Array.from(mockStorage['tasks'].values()).filter(
        task => task.status === BacktestStatus.RUNNING
      ).length;
      
      if (runningTasks >= mockStorage['businessRules'].maxConcurrentTasks) {
        return HttpResponse.json(
          {
            success: false,
            message: '系统繁忙，请稍后重试',
            errors: {
              concurrent_limit: `当前运行任务数: ${runningTasks}, 已达上限`
            }
          },
          { status: 429 }
        );
      }

      // 4. 创建任务
      const task = mockStorage.createTask(config);
      
      const response: BacktestTaskResponse = {
        success: true,
        data: task,
        message: '回测任务已创建'
      };

      return HttpResponse.json(response);
    } catch (error) {
      return HttpResponse.json(
        {
          success: false,
          message: '请求处理异常',
          error: String(error)
        },
        { status: 500 }
      );
    }
  }),

  /**
   * GET /api/backtest/results/:id - 获取回测结果
   */
  http.get('/api/backtest/results/:id', async ({ params }) => {
    // 网络延迟模拟
    await simulateNetworkDelay({
      baseDelay: 100, // 查询操作延迟较短
      jitter: mockStorage['performanceConfig'].networkJitter,
      highLatency: mockStorage['performanceConfig'].highLatency
    });

    const taskId = params.id as string;
    
    // 网络错误模拟
    if (shouldSimulateError(0.05)) { // 查询操作错误率较低
      const errorInfo = simulateSpecificError();
      return HttpResponse.json(errorInfo.error, { status: errorInfo.status });
    }

    const task = mockStorage.getTask(taskId);
    if (!task) {
      return HttpResponse.json(
        {
          success: false,
          message: '回测任务不存在',
          error: {
            code: 'TASK_NOT_FOUND',
            details: `任务ID: ${taskId}`
          }
        },
        { status: 404 }
      );
    }

    // 如果任务还在进行中，返回增强的进度信息
    if (task.status !== BacktestStatus.COMPLETED) {
      const taskState = mockStorage.getTaskState(taskId);
      
      const progressResponse: BacktestProgressResponse = {
        success: true,
        data: {
          task_id: taskId,
          status: task.status,
          progress: task.progress || 0,
          current_date: task.status === BacktestStatus.RUNNING ? '2023-06-15' : undefined,
          message: task.error_message
        },
        message: getStatusMessage(task.status, taskState?.phase)
      };
      return HttpResponse.json(progressResponse);
    }

    // 返回完整结果
    const result = mockStorage.getResult(taskId);
    if (!result) {
      return HttpResponse.json(
        {
          success: false,
          message: '回测结果不存在',
          error: {
            code: 'RESULT_NOT_FOUND',
            details: `任务${taskId}已完成但结果丢失`
          }
        },
        { status: 404 }
      );
    }

    const response: BacktestResultResponse = {
      success: true,
      data: result,
      message: '获取成功'
    };

    return HttpResponse.json(response);
  }),

  /**
   * GET /api/backtest/history - 获取回测历史
   */
  http.get('/api/backtest/history', async ({ request }) => {
    // 模拟网络延迟和抖动
     await simulateNetworkDelay({
       jitter: mockStorage['performanceConfig'].networkJitter,
       highLatency: mockStorage['performanceConfig'].highLatency
     });

    // 模拟网络错误
    if (shouldSimulateError()) {
      return HttpResponse.json(
        {
          success: false,
          message: '获取历史记录失败'
        },
        { status: 500 }
      );
    }

    const url = new URL(request.url);
    const params: BacktestHistoryParams = {
      page: parseInt(url.searchParams.get('page') || '1'),
      page_size: parseInt(url.searchParams.get('page_size') || '10'),
      strategy_id: url.searchParams.get('strategy_id') || undefined,
      symbol: url.searchParams.get('symbol') || undefined,
      status: url.searchParams.get('status') as any || undefined,
      start_date: url.searchParams.get('start_date') || undefined,
      end_date: url.searchParams.get('end_date') || undefined
    };

    let tasks = mockStorage.getAllTasks();

    // 应用过滤器
    if (params.strategy_id) {
      tasks = tasks.filter(task => task.strategy_id === params.strategy_id);
    }
    if (params.symbol) {
      tasks = tasks.filter(task => task.symbol.toLowerCase().includes(params.symbol!.toLowerCase()));
    }
    if (params.status) {
      tasks = tasks.filter(task => task.status === params.status);
    }
    if (params.start_date) {
      tasks = tasks.filter(task => task.start_date >= params.start_date!);
    }
    if (params.end_date) {
      tasks = tasks.filter(task => task.end_date <= params.end_date!);
    }

    // 分页
    const total = tasks.length;
    const page = params.page || 1;
    const pageSize = params.page_size || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedTasks = tasks.slice(startIndex, endIndex);

    const response: BacktestHistoryResponse = {
      success: true,
      data: paginatedTasks,
      total,
      page,
      page_size: pageSize,
      message: '获取成功'
    };

    return HttpResponse.json(response);
  }),

  /**
   * POST /api/backtest/stop/:id - 停止回测
   */
  http.post('/api/backtest/stop/:id', async ({ params }) => {
    // 模拟网络延迟和抖动
    await simulateNetworkDelay({
      jitter: mockStorage['performanceConfig'].networkJitter,
      highLatency: mockStorage['performanceConfig'].highLatency
    });

    const taskId = params.id as string;
    
    // 模拟网络错误
    if (shouldSimulateError()) {
      return HttpResponse.json(
        {
          success: false,
          message: '停止操作失败'
        },
        { status: 500 }
      );
    }

    const task = mockStorage.getTask(taskId);
    if (!task) {
      return HttpResponse.json(
        {
          success: false,
          message: '回测任务不存在'
        },
        { status: 404 }
      );
    }

    if (task.status !== BacktestStatus.RUNNING) {
      return HttpResponse.json(
        {
          success: false,
          message: '只能停止正在运行的任务'
        },
        { status: 400 }
      );
    }

    const stopped = mockStorage.stopTask(taskId);
    if (!stopped) {
      return HttpResponse.json(
        {
          success: false,
          message: '停止任务失败'
        },
        { status: 500 }
      );
    }

    const response: BacktestStopResponse = {
      success: true,
      data: mockStorage.getTask(taskId)!,
      message: '任务已停止'
    };

    return HttpResponse.json(response);
  }),

  /**
   * GET /api/backtest/progress/:id - 获取回测进度（轮询接口）
   */
  http.get('/api/backtest/progress/:id', async ({ params }) => {
    // 进度查询延迟较短
    await delay(50 + Math.random() * 100);

    const taskId = params.id as string;
    const task = mockStorage.getTask(taskId);
    
    if (!task) {
      return HttpResponse.json(
        {
          success: false,
          message: '任务不存在'
        },
        { status: 404 }
      );
    }

    const response: BacktestProgressResponse = {
      success: true,
      data: {
        task_id: taskId,
        status: task.status,
        progress: task.progress || 0,
        current_date: task.status === BacktestStatus.RUNNING ? '2023-06-15' : undefined,
        message: task.error_message
      }
    };

    return HttpResponse.json(response);
  })
];

/**
 * 导出存储实例，用于测试中的数据操作
 */
export { mockStorage };

/**
 * 重置模拟数据
 */
export function resetMockData(): void {
  mockStorage.clear();
  
  // 重新初始化历史数据
  const historyTasks = BacktestDataFactory.createBacktestHistory(5, ['completed', 'failed']);
  historyTasks.forEach(task => {
    mockStorage['tasks'].set(task.id, task);
    if (task.status === BacktestStatus.COMPLETED) {
      const result = BacktestDataFactory.createBacktestResult('mixed', {
        task_id: task.id,
        symbol: task.symbol,
        start_date: task.start_date,
        end_date: task.end_date
      });
      mockStorage['results'].set(task.id, result);
    }
  });
}

/**
 * 创建特定场景的测试数据
 */
export function setupTestScenario(scenario: 'empty' | 'with_data' | 'with_errors'): void {
  mockStorage.clear();
  
  switch (scenario) {
    case 'empty':
      // 保持空状态
      break;
      
    case 'with_data':
      const tasks = BacktestDataFactory.createBacktestHistory(20, ['completed', 'running', 'failed']);
      tasks.forEach(task => {
        mockStorage['tasks'].set(task.id, task);
        if (task.status === BacktestStatus.COMPLETED) {
          const result = BacktestDataFactory.createBacktestResult('profitable', {
            task_id: task.id,
            symbol: task.symbol,
            start_date: task.start_date,
            end_date: task.end_date
          });
          mockStorage['results'].set(task.id, result);
        }
      });
      break;
      
    case 'with_errors':
      // 创建一些有错误的任务
      const errorTasks = BacktestDataFactory.createBacktestHistory(5, ['failed']);
      errorTasks.forEach(task => {
        task.error_message = '模拟错误：数据源连接失败';
        mockStorage['tasks'].set(task.id, task);
      });
      break;
  }
}

/**
 * 设置高级测试场景
 */
export function setupAdvancedTestScenario(scenario: {
  backtestScenario?: 'profitable' | 'loss' | 'mixed' | 'no_trades' | 'crisis' | 'precision' | 'large_dataset';
  networkJitter?: boolean;
  highLatency?: boolean;
  concurrentLimit?: number;
  largeDatasetMode?: boolean;
  errorRate?: number;
}): void {
  if (scenario.backtestScenario !== undefined) {
    mockStorage.setScenario(scenario.backtestScenario);
  }
  
  // 配置性能测试参数
  mockStorage.setPerformanceConfig({
    networkJitter: scenario.networkJitter,
    highLatency: scenario.highLatency,
    largeDatasetMode: scenario.largeDatasetMode,
    concurrentLimit: scenario.concurrentLimit || 5
  });
}

/**
 * 配置性能测试模式
 */
export function configurePerformanceTestMode(config: {
  networkJitter?: boolean;
  highLatency?: boolean;
  largeDatasetMode?: boolean;
  concurrentLimit?: number;
}): void {
  mockStorage.setPerformanceConfig(config);
}

/**
 * 模拟并发回测场景
 */
export function simulateConcurrentBacktests(count: number): Promise<string[]> {
  const taskIds: string[] = [];
  
  for (let i = 0; i < count; i++) {
    const config: BacktestConfig = {
      strategy_id: `concurrent-strategy-${i}`,
      symbol: '000001',
      start_date: '2023-01-01',
      end_date: '2023-12-31',
      capital: 100000,
      commission: 0.001,
      slippage: 0.001
    };
    
    const task = mockStorage.createTask(config);
    taskIds.push(task.id);
  }
  
  return Promise.resolve(taskIds);
}

/**
 * 获取系统性能指标
 */
export function getSystemPerformanceMetrics(): {
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageExecutionTime: number;
  resourceUsage: { cpu: number; memory: number };
} {
  const allTasks = mockStorage.getAllTasks();
  const runningTasks = allTasks.filter(task => task.status === BacktestStatus.RUNNING);
  const completedTasks = allTasks.filter(task => task.status === BacktestStatus.COMPLETED);
  const failedTasks = allTasks.filter(task => task.status === BacktestStatus.FAILED);
  
  // 计算平均执行时间
  const completedWithTimes = completedTasks.filter(task => task.started_at && task.completed_at);
  const averageExecutionTime = completedWithTimes.length > 0 
    ? completedWithTimes.reduce((sum, task) => {
        const start = new Date(task.started_at!).getTime();
        const end = new Date(task.completed_at!).getTime();
        return sum + (end - start);
      }, 0) / completedWithTimes.length
    : 0;
  
  // 计算当前资源使用情况
  const currentResourceUsage = runningTasks.reduce(
    (total, task) => {
      const taskState = mockStorage.getTaskState(task.id);
      if (taskState) {
        total.cpu += taskState.resourceUsage.cpu;
        total.memory += taskState.resourceUsage.memory;
      }
      return total;
    },
    { cpu: 0, memory: 0 }
  );
  
  return {
    runningTasks: runningTasks.length,
    completedTasks: completedTasks.length,
    failedTasks: failedTasks.length,
    averageExecutionTime,
    resourceUsage: currentResourceUsage
  };
}

/**
 * 模拟网络抖动场景
 */
export function simulateNetworkJitter(enabled: boolean = true): void {
  mockStorage.setPerformanceConfig({ networkJitter: enabled });
}

/**
 * 模拟高延迟网络环境
 */
export function simulateHighLatencyNetwork(enabled: boolean = true): void {
  mockStorage.setPerformanceConfig({ highLatency: enabled });
}

/**
 * 重置性能配置
 */
export function resetPerformanceConfig(): void {
  mockStorage.setPerformanceConfig({
    networkJitter: false,
    highLatency: false,
    concurrentLimit: 5,
    largeDatasetMode: false
  });
}

/**
 * 获取契约验证报告
 */
export function getContractValidationReport(): {
  violations: Array<{ api: string, error: string, timestamp: string }>;
  validationErrors: string[];
} {
  return {
    violations: mockStorage.getContractViolations(),
    validationErrors: ContractValidator.getValidationErrors()
  };
}

/**
 * 清除契约验证记录
 */
export function clearContractValidationRecords(): void {
  mockStorage.clearContractViolations();
  ContractValidator.clearValidationErrors();
}