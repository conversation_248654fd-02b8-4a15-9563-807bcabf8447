# abu_modern 前端架构与动态布局规范 (Structural & Dynamic Layout Spec V1.1)

## 1. 应用级骨架 (The Application Shell)

- 核心原则：整个应用共享一个唯一的、不可变的顶层骨架。
- 实现文件：App.vue
- 结构：App.vue 的模板中，只包含一个 `<router-view />`。所有页面布局逻辑，下放到各自的路由组件中。

```vue
<!-- App.vue -->
<template>
  <router-view />
</template>
```

---

## 2. 认证布局 vs. 主应用布局

- 核心原则：应用存在两种布局状态——"用户未登录"和"用户已登录"。
- 实现文件：layouts/AuthLayout.vue, layouts/DefaultLayout.vue
- 路由配置（router/index.ts）：

```ts
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
// import AuthLayout from '@/layouts/AuthLayout.vue'
// import DefaultLayout from '@/layouts/DefaultLayout.vue'
// import LoginView from '@/views/LoginView.vue'
// import DashboardView from '@/views/Dashboard.vue'
// import StrategyWorkshopView from '@/views/StrategyWorkshop.vue'

const routes = [
  {
    path: '/login',
    component: AuthLayout, // 使用认证布局
    children: [{ path: '', component: LoginView }]
  },
  {
    path: '/',
    component: DefaultLayout, // 使用主应用布局
    meta: { requiresAuth: true },
    children: [
      // 所有需要登录后访问的页面路由
      { path: 'dashboard', component: DashboardView },
      { path: 'workshop', component: StrategyWorkshopView }
      // ...
    ]
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

---

## 3. 主应用布局 (DefaultLayout.vue) —— "圣杯布局"

- 核心原则：采用经典"圣杯布局"，顶栏和侧边栏固定，主内容区独立滚动。

**【V1.1新增/更新】技术实现具体化：**
我们最终采用的是**CSS Grid布局**来实现"圣杯布局"，经过实践验证的详细实现如下：

```vue
<!-- layouts/DefaultLayout.vue -->
<template>
  <div class="app-layout" :class="{ 'sidebar-collapsed': isCollapsed }">
    <header class="app-header">...</header>
    <aside class="app-sidebar">...</aside>
    <main class="app-main-content">
      <router-view />
    </main>
  </div>
</template>

<style>
/* 【V1.1新增】全局布局样式 - 必须放在全局样式块中 */
.app-layout {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar content";
  grid-template-rows: var(--header-height, 60px) 1fr;
  grid-template-columns: var(--sidebar-width, 200px) 1fr;
  height: 100vh;
  overflow: hidden;                      /* 关键：禁止整个页面滚动 */
}

/* 【V1.1新增】侧边栏动态折叠支持 */
.app-layout.sidebar-collapsed {
  grid-template-columns: var(--sidebar-width-collapsed, 64px) 1fr;
}

.app-header {
  grid-area: header;
}

.app-sidebar {
  grid-area: sidebar;
  overflow-y: auto;                      /* 侧边栏内容多时，自己滚动 */
}

.app-main-content {
  grid-area: content;
  overflow-y: auto;                      /* 关键：主内容区独立滚动 */
  padding: var(--space-lg, 24px);
}
</style>

<style scoped>
/* 【V1.1新增】组件内部样式 - 使用scoped作用域 */
.sidebar-toggle-btn {
  /* 组件特异性样式 */
}
</style>
```

**【V1.1新增】动态行为规范：**
- **侧边栏动态折叠**：布局通过CSS类切换响应折叠状态变化
- **grid-template-columns动态切换**：
  - 展开状态：`var(--sidebar-width, 200px) 1fr`
  - 折叠状态：`var(--sidebar-width-collapsed, 64px) 1fr`
- **状态管理**：通过添加/移除`.sidebar-collapsed`类来控制布局变化

- 约束：
  - 任何页面级组件，不允许改变这个顶层布局。
  - 不允许在页面组件内部再嵌套一个带滚动条的 ElMain。

---

## 4. 页面级布局 — 两大模式

所有位于 /views 目录下的页面级组件，其内部布局必须遵循以下两种模式之一。

### 4.1 模式一：单面板布局 (Single-Panel Layout)

- 适用场景：仪表盘、回测报告、系统设置等信息展示或简单表单页面。
- 结构规范：

```vue
<!-- views/Dashboard.vue -->
<template>
  <div class="dashboard-page">
    <el-page-header title="仪表盘" />
    <el-row :gutter="16">
      <el-col :span="12">
        <el-card>...</el-card>
      </el-col>
      <!-- ... -->
    </el-row>
  </div>
</template>

<script setup lang="ts">
// ...
</script>

<style scoped>
.dashboard-page {
  /* 页面自定义样式 */
}
</style>
```

- 行为规范：整个页面内容，随着主内容区（.app-main-content）的滚动条滚动。

### 4.2 模式二：固定分栏布局 (Fixed-Split-Panel Layout)

- 适用场景：策略工场、选股器等，需要"列表-详情"联动的复杂交互页面。
- 核心原则：左右（或上下）分栏，各自独立滚动。

**【V1.1新增】头部固定规范：**
根据实践验证，增加以下约束：
- **右侧编辑器的头部区域** (`.editor-header`) 应采用`position: sticky`实现固定定位，以确保在内容滚动时，核心操作（如'保存策略'按钮）始终可见。

**【V1.1新增】对齐优化规范：**
- **内容区对齐最佳实践**：通过精确的padding和margin调整，确保左右两栏在视觉上的完美对齐
- **边距统一管理**：所有间距值应使用CSS变量，避免硬编码

```vue
<!-- views/StrategyWorkshop.vue -->
<template>
  <div class="workshop-layout">
    <div class="workshop-left-panel">
      <!-- 策略列表 -->
    </div>
    <div class="workshop-right-panel">
      <!-- 策略编辑器 -->
      <div class="editor-header">
        <!-- 【V1.1新增】策略简介 / 顶部工具栏 - 使用sticky定位 -->
      </div>
      <div class="editor-content">
        <!-- 右侧主体（如 ElTabs） -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// ...
</script>

<style scoped>
.workshop-layout {
  display: flex;
  height: 100%;       /* 关键：撑满父容器(.app-main-content)的高度 */
  gap: var(--space-workshop-gap, 24px);  /* 【V1.1更新】使用CSS变量管理栏间距 */
}

.workshop-left-panel {
  width: 320px;       /* 固定宽度 */
  flex-shrink: 0;
  overflow-y: auto;   /* 关键：左侧独立滚动 */
}

.workshop-right-panel {
  flex: 1;            /* 占据剩余宽度 */
  display: flex;
  flex-direction: column;
  overflow: hidden;   /* 关键：右侧本身不滚动 */
}

/* 【V1.1新增】在右侧内部，再定义滚动区域 */
.editor-header {
  flex-shrink: 0;
  position: sticky;   /* 【V1.1新增】固定定位 */
  top: 0;
  background: var(--bg-color-card, #ffffff);
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 【V1.1新增】增强视觉层次 */
}

.editor-content {
  flex: 1;
  overflow-y: auto;   /* 关键：右侧的内容区独立滚动 */
  padding: var(--space-md, 16px); /* 【V1.1新增】统一内边距管理 */
}
</style>
```

- 约束：
  - 当需要"列表-详情"联动时，必须采用此布局模式。
  - 严禁将整个两栏式布局放在一个会随页面滚动的容器内。

---

## 5. 元素与容器的嵌套关系

- 顶层容器：
  - 任何页面的顶层 div，应有一个唯一的 class，如 .dashboard-page、.workshop-layout。
- 页面标题：
  - ElPageHeader 或自定义标题，应是顶层 div 的直接子元素。
- 布局容器：
  - 行（ElRow）：用于水平方向的栅格布局。
  - 卡片（ElCard）：用于包裹语义上独立的模块。不要用一个巨大的 ElCard 包裹整个页面。ElCard 应放置在 ElCol 或 flex/grid 的单元格内。
- 禁止的嵌套：
  - 严禁在 ElCard 内部再使用 ElRow 进行复杂的栅格布局。卡片内部应保持简单的垂直流布局。如果卡片内布局复杂，说明需要将其拆分为更小的组件。
  - 严禁出现"滚动条套滚动条"的情况。一个视图区域，只应有一个主滚动条。

---

## **【V1.1新增】6. 样式作用域管理规范**

基于实践中关于scoped样式的深刻洞察，建立以下规范：

### 6.1 全局布局样式规范
- **全局布局样式**（如DefaultLayout.vue中的核心布局规则）**必须**放在全局样式块 (`<style>`) 中
- **目的**：确保CSS变量和全局规则能正确应用
- **适用场景**：
  - CSS Grid布局定义
  - CSS自定义属性（变量）定义
  - 全局布局容器样式
  - 跨组件共享的布局规则

### 6.2 组件内部样式规范
- **组件内部的、特异性的样式**，**必须**放在**scoped样式块** (`<style scoped>`) 中
- **目的**：避免样式污染，确保组件样式封装
- **适用场景**：
  - 组件特有的装饰性样式
  - 组件内部元素的特殊样式
  - 不需要全局共享的样式规则

### 6.3 样式作用域最佳实践

```vue
<template>
  <div class="layout-container">
    <div class="component-specific-element">...</div>
  </div>
</template>

<style>
/* 【全局样式块】- 布局相关的关键样式 */
.layout-container {
  display: grid;
  grid-template-columns: var(--sidebar-width, 200px) 1fr;
  /* 其他全局布局规则 */
}
</style>

<style scoped>
/* 【scoped样式块】- 组件特异性样式 */
.component-specific-element {
  border-radius: 4px;
  background-color: var(--component-bg);
  /* 其他组件内部样式 */
}
</style>
```

### 6.4 CSS变量访问规范
- **全局CSS变量**：在全局样式块中定义，可在任何地方访问
- **组件CSS变量**：在scoped样式块中定义，仅在当前组件内访问
- **变量优先级**：避免在JavaScript中直接setProperty覆盖CSS变量，优先使用CSS类切换

### 6.5 作用域冲突解决
- **问题识别**：当CSS变量无法生效时，首先检查样式作用域
- **解决方案**：将影响全局布局的样式移至全局样式块
- **验证方法**：确保关键布局样式能够正确应用CSS变量