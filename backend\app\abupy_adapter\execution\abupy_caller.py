import logging
import traceback
from typing import Dict, Any, List, Tuple

import pandas as pd
from abupy.AlphaBu.ABuPickTimeExecute import do_symbols_with_same_factors
from abupy.CoreBu import ABuEnv
from abupy.MarketBu import ABuSymbolPd
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.UmpBu import ABuUmpManager

from backend.app.abupy_adapter.exceptions import AdapterError, DataNotFoundError
from backend.app.utils.symbol_util import to_tushare_symbol_format, from_abupy_symbol_format


def force_fix_date_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    统一修正DataFrame的日期列，确保索引为DatetimeIndex，并添加date和date_int列
    
    Args:
        df: 需要修正的DataFrame
        
    Returns:
        修正后的DataFrame
    """
    df = df.copy()
    # 索引必须是 DatetimeIndex
    if not isinstance(df.index, pd.DatetimeIndex):
        if 'date' in df.columns:
            try:
                df.index = pd.to_datetime(df['date'].astype(str), format='%Y%m%d', errors='raise')
            except Exception:
                df.index = pd.to_datetime(df['date'], errors='raise')
        elif 'trade_date' in df.columns:
            try:
                df.index = pd.to_datetime(df['trade_date'].astype(str), format='%Y%m%d', errors='raise')
            except Exception:
                df.index = pd.to_datetime(df['trade_date'], errors='raise')
    df.index = df.index.tz_localize(None)
    df.sort_index(inplace=True)

    # 用索引覆盖 date 列，确保 dtype 为 datetime64[ns]
    df['date'] = pd.to_datetime(df.index)
    # 同时提供整型日期（如果上游习惯用 int）
    df['date_int'] = df.index.strftime('%Y%m%d').astype(int)
    return df


def call_abupy_backtest(
    abupy_choice_symbols: List[str],
    benchmark_obj: AbuBenchmark,
    capital_obj: AbuCapital,
    abu_buy_factors: List[Dict[str, Any]],
    abu_sell_factors: List[Dict[str, Any]],
    all_raw_kline_data: Dict[str, pd.DataFrame]
) -> Tuple[pd.DataFrame, pd.DataFrame, Any]:
    """
    专门负责调用abupy核心回测函数，并管理关键的补丁逻辑。
    """
    original_make_kl_df = ABuSymbolPd.make_kl_df
    original_combine_pre_kl_pd = ABuSymbolPd.combine_pre_kl_pd

    def make_kl_df_from_cache(symbol_or_obj, *args, **kwargs):
        symbol_str = getattr(symbol_or_obj, 'value', symbol_or_obj)
        possible_keys = [
            to_tushare_symbol_format(symbol_str),
            from_abupy_symbol_format(symbol_str, list(all_raw_kline_data.keys())),
            symbol_str.split('.')[-1] if '.' in symbol_str else symbol_str[2:]
        ]
        
        df = None
        used_key = None
        
        for key in possible_keys:
            if key in all_raw_kline_data:
                df = all_raw_kline_data[key]
                used_key = key
                logging.info(f"MAKE_KL_DF_PATCH: 使用键 '{key}' 找到了 '{symbol_str}' 的数据。")
                break
        
        if df is None:
            logging.warning(f"MAKE_KL_DF_PATCH: Symbol '{symbol_str}' 在缓存中未找到。已尝试的键: {possible_keys}。返回空DataFrame。")
            df = pd.DataFrame()

        df.name = symbol_str
        return df

    def patched_combine_pre_kl_pd(kl_pd, n_folds=1): return kl_pd

    try:
        # 强制设置单进程模式
        original_cpu_cnt = getattr(ABuEnv, 'g_cpu_cnt', 1)
        ABuEnv.g_cpu_cnt = 1
        logging.info(f"--- [ABUPY_ENV] 强制设置单进程模式: 原值={original_cpu_cnt}, 新值={ABuEnv.g_cpu_cnt}")
        
        ABuSymbolPd.make_kl_df = make_kl_df_from_cache
        ABuSymbolPd.combine_pre_kl_pd = patched_combine_pre_kl_pd

        # 关键修复：在调用abupy之前，对所有DataFrame应用日期列修复
        logging.info(f"--- [DATE_FIX] 开始对所有DataFrame应用日期列修复 ---")
        fixed_kline_data = {}
        for key, df in all_raw_kline_data.items():
            try:
                fixed_df = force_fix_date_columns(df)
                fixed_kline_data[key] = fixed_df
                logging.info(f"--- [DATE_FIX] {key}: 修复完成，数据形状: {fixed_df.shape}, 日期范围: {fixed_df.index.min()} 到 {fixed_df.index.max()}")
            except Exception as e:
                logging.error(f"--- [DATE_FIX] {key}: 修复失败 - {str(e)}")
                fixed_kline_data[key] = df  # 如果修复失败，使用原始数据
        
        # 更新数据引用
        all_raw_kline_data = fixed_kline_data
        logging.info(f"--- [DATE_FIX] 日期列修复完成 ---")
        
        # 调试日志：调用前的参数检查
        logging.info(f"--- [ABUPY_CALL] 即将调用do_symbols_with_same_factors ---")
        logging.info(f"--- [ABUPY_CALL] target_symbols: {abupy_choice_symbols}")
        logging.info(f"--- [ABUPY_CALL] benchmark类型: {type(benchmark_obj)}")
        logging.info(f"--- [ABUPY_CALL] capital类型: {type(capital_obj)}, 初始资金: {getattr(capital_obj, 'read_cash', 'N/A')}")
        logging.info(f"--- [ABUPY_CALL] buy_factors数量: {len(abu_buy_factors)}")
        logging.info(f"--- [ABUPY_CALL] buy_factors详情: {abu_buy_factors}")
        logging.info(f"--- [ABUPY_CALL] sell_factors数量: {len(abu_sell_factors)}")
        logging.info(f"--- [ABUPY_CALL] sell_factors详情: {abu_sell_factors}")
        logging.info(f"--- [ABUPY_CALL] 可用数据键: {list(all_raw_kline_data.keys())}")
        
        # 检查每个目标股票的数据可用性
        for symbol in abupy_choice_symbols:
            possible_keys = [
                to_tushare_symbol_format(symbol),
                from_abupy_symbol_format(symbol, list(all_raw_kline_data.keys())),
                symbol.split('.')[-1] if '.' in symbol else symbol[2:]
            ]
            found_key = None
            for key in possible_keys:
                if key in all_raw_kline_data:
                    found_key = key
                    break
            if found_key:
                df = all_raw_kline_data[found_key]
                logging.info(f"--- [ABUPY_CALL] {symbol} -> {found_key}: {df.shape[0]}条数据, 日期范围: {df.index.min()} 到 {df.index.max()}")
            else:
                logging.warning(f"--- [ABUPY_CALL] {symbol}: 未找到对应数据，尝试的键: {possible_keys}")
        
        # 调用abupy核心回测函数
        orders_pd, action_pd, _ = do_symbols_with_same_factors(
            target_symbols=abupy_choice_symbols,
            benchmark=benchmark_obj,
            buy_factors=abu_buy_factors,
            sell_factors=abu_sell_factors,
            capital=capital_obj
        )
        
        # 调试日志：调用后的结果检查
        logging.info(f"--- [ABUPY_RESULT] do_symbols_with_same_factors调用完成 ---")
        logging.info(f"--- [ABUPY_RESULT] orders_pd类型: {type(orders_pd)}, 形状: {getattr(orders_pd, 'shape', 'N/A')}")
        logging.info(f"--- [ABUPY_RESULT] action_pd类型: {type(action_pd)}, 形状: {getattr(action_pd, 'shape', 'N/A')}")
        
        if hasattr(orders_pd, 'empty') and not orders_pd.empty:
            logging.info(f"--- [ABUPY_RESULT] orders_pd前5行:\n{orders_pd.head()}")
        else:
            logging.warning(f"--- [ABUPY_RESULT] orders_pd为空或无效")
            
        if hasattr(action_pd, 'empty') and not action_pd.empty:
            logging.info(f"--- [ABUPY_RESULT] action_pd前5行:\n{action_pd.head()}")
        else:
            logging.warning(f"--- [ABUPY_RESULT] action_pd为空或无效")
        
        return orders_pd, action_pd, _

    except Exception as e:
        logging.error(f"abupy回测引擎(do_symbols_with_same_factors)执行失败: {e}", exc_info=True)
        raise AdapterError(
            message=f"abupy回测引擎失败: {str(e)}",
            error_code="ABUPY_CORE_EXECUTION_FAILED",
            data={"traceback": traceback.format_exc()}
        )
    finally:
        ABuSymbolPd.make_kl_df = original_make_kl_df
        ABuSymbolPd.combine_pre_kl_pd = original_combine_pre_kl_pd
        # 恢复原始CPU计数设置
        ABuEnv.g_cpu_cnt = original_cpu_cnt
