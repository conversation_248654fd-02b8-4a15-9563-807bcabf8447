import logging
import traceback
from typing import Dict, Any, List, Tuple

import pandas as pd
from abupy.AlphaBu.ABuPickTimeExecute import do_symbols_with_same_factors
from abupy.CoreBu import ABuEnv
from abupy.MarketBu import ABuSymbolPd
from abupy.TradeBu.ABuBenchmark import AbuBenchmark
from abupy.TradeBu.ABuCapital import AbuCapital
from abupy.UmpBu import ABuUmpManager

from app.abupy_adapter.exceptions import AdapterError, DataNotFoundError
from app.utils.symbol_util import to_tushare_symbol_format, from_abupy_symbol_format


def call_abupy_backtest(
    abupy_choice_symbols: List[str],
    benchmark_obj: AbuBenchmark,
    capital_obj: AbuCapital,
    abu_buy_factors: List[Dict[str, Any]],
    abu_sell_factors: List[Dict[str, Any]],
    all_raw_kline_data: Dict[str, pd.DataFrame],
    umpire_instances: List[Any]
) -> Tuple[pd.<PERSON><PERSON>rame, pd.DataFrame, Any]:
    """
    专门负责调用abupy核心回测函数，并管理关键的补丁逻辑。
    """
    original_make_kl_df = ABuSymbolPd.make_kl_df
    original_combine_pre_kl_pd = ABuSymbolPd.combine_pre_kl_pd

    def make_kl_df_from_cache(symbol_or_obj, *args, **kwargs):
        symbol_str = getattr(symbol_or_obj, 'value', symbol_or_obj)
        possible_keys = [
            to_tushare_symbol_format(symbol_str),
            from_abupy_symbol_format(symbol_str, list(all_raw_kline_data.keys())),
            symbol_str.split('.')[-1] if '.' in symbol_str else symbol_str[2:]
        ]
        
        df = None
        used_key = None
        
        for key in possible_keys:
            if key in all_raw_kline_data:
                df = all_raw_kline_data[key]
                used_key = key
                logging.info(f"MAKE_KL_DF_PATCH: 使用键 '{key}' 找到了 '{symbol_str}' 的数据。")
                break
        
        if df is None:
            logging.warning(f"MAKE_KL_DF_PATCH: Symbol '{symbol_str}' 在缓存中未找到。已尝试的键: {possible_keys}。返回空DataFrame。")
            df = pd.DataFrame()

        df.name = symbol_str
        return df

    def patched_combine_pre_kl_pd(kl_pd, n_folds=1): return kl_pd

    try:
        ABuSymbolPd.make_kl_df = make_kl_df_from_cache
        ABuSymbolPd.combine_pre_kl_pd = patched_combine_pre_kl_pd

        if umpire_instances:
            ABuUmpManager.g_enable_user_ump = True
            ABuEnv.g_enable_ml_feature = True
            ABuUmpManager.clear_user_ump()
            for ump in umpire_instances:
                ABuUmpManager.append_user_ump(ump)
            logging.info(f"成功配置 {len(umpire_instances)} 个裁判: {[str(u) for u in umpire_instances]}")

        orders_pd, action_pd, _ = do_symbols_with_same_factors(
            target_symbols=abupy_choice_symbols,
            benchmark=benchmark_obj,
            buy_factors=abu_buy_factors,
            sell_factors=abu_sell_factors,
            capital=capital_obj
        )
        return orders_pd, action_pd, _

    except Exception as e:
        logging.error(f"abupy回测引擎(do_symbols_with_same_factors)执行失败: {e}", exc_info=True)
        raise AdapterError(
            message=f"abupy回测引擎失败: {str(e)}",
            error_code="ABUPY_CORE_EXECUTION_FAILED",
            details=traceback.format_exc()
        )
    finally:
        ABuSymbolPd.make_kl_df = original_make_kl_df
        ABuSymbolPd.combine_pre_kl_pd = original_combine_pre_kl_pd
        if umpire_instances:
            ABuUmpManager.g_enable_user_ump = False
            ABuEnv.g_enable_ml_feature = False
            ABuUmpManager.clear_user_ump()
            logging.info("回测执行完毕，已清理并关闭裁判系统。")