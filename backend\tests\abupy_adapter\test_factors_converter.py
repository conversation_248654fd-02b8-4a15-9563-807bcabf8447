"""
FactorsConverter 模块测试
"""
import pytest
from unittest.mock import patch, MagicMock, call
import importlib
import inspect
import traceback
import pandas as pd

from backend.app.schemas.strategy import BuyFactor, SellFactor
from backend.app.abupy_adapter.factors_converter import FactorsConverter
from backend.app.core.exceptions import FactorError, AdapterError

class TestFactorsConverter:
    """FactorsConverter 测试类"""
    
    def test_convert_buy_factors(self):
        """测试买入因子转换"""
        buy_factors = [
            BuyFactor(
                name="AbuFactorBuyBreak",
                class_name="FactorBuyBreak",
                parameters={"xd": 60},
                factor_type="buy"
            )
        ]
        
        # 模拟导入的 Abu 模块和类
        with patch('backend.app.abupy_adapter.factors_converter.importlib.import_module') as mock_import_module:
            mock_factor_obj = type('AbuFactorBuyBreak', (), {
                '__module__': 'abupy.FactorBuyBu.ABuFactorBuyBreak'
            })
            assert isinstance(mock_factor_obj, type)

            # 让 importlib.import_module 返回一个模拟的模块
            mock_module = MagicMock()
            mock_module.AbuFactorBuyBreak = mock_factor_obj
            mock_import_module.return_value = mock_module
            
            # 调用转换方法
            mock_capital = 1000000.0
            mock_kl_pd = pd.DataFrame()
            mock_benchmark = pd.DataFrame()
            mock_combine_kl_pd = None
            result = FactorsConverter.convert_to_abu_factors(
                buy_factors,
                capital=mock_capital,
                kl_pd=mock_kl_pd,
                benchmark=mock_benchmark,
                combine_kl_pd=mock_combine_kl_pd
            )
            
            # 验证结果
            assert len(result) == 1
            # 验证导入调用，注意可能的导入路径差异
            assert mock_import_module.call_count == 1
            # 检查是否创建了因子实例
            assert len(result) == 1
            # 确保因子对象的存在性
            assert result[0] is not None
    
    def test_convert_sell_factors(self):
        """测试卖出因子转换"""
        sell_factors = [
            SellFactor(
                name="AbuFactorSellBreak",
                class_name="FactorSellBreak",
                parameters={"xd": 20},
                factor_type="sell"
            )
        ]
        
        # 模拟导入的 Abu 模块和类
        with patch('backend.app.abupy_adapter.factors_converter.importlib.import_module') as mock_import_module:
            mock_factor_obj = type('AbuFactorSellBreak', (), {
                '__module__': 'abupy.FactorSellBu.ABuFactorSellBreak'
            })
            assert isinstance(mock_factor_obj, type)

            mock_module = MagicMock()
            mock_module.AbuFactorSellBreak = mock_factor_obj
            mock_import_module.return_value = mock_module
            
            # 调用转换方法
            mock_capital = 1000000.0
            mock_kl_pd = pd.DataFrame()
            mock_benchmark = pd.DataFrame()
            mock_combine_kl_pd = None
            result = FactorsConverter.convert_to_abu_factors(
                sell_factors,
                capital=mock_capital,
                kl_pd=mock_kl_pd,
                benchmark=mock_benchmark,
                combine_kl_pd=mock_combine_kl_pd
            )
            
            # 验证结果
            assert len(result) == 1
            # 验证导入调用，注意可能的导入路径差异
            assert mock_import_module.call_count == 1
            # 检查是否创建了因子实例
            assert len(result) == 1
            # 确保因子对象的存在性
            assert result[0] is not None
    
    def test_convert_both_factors(self):
        """测试同时转换买入和卖出因子"""
        buy_factors = [
            BuyFactor(
                name="AbuFactorBuyBreak",
                class_name="FactorBuyBreak",
                parameters={"xd": 60},
                factor_type="buy"
            )
        ]
        
        sell_factors = [
            SellFactor(
                name="AbuFactorSellBreak",
                class_name="FactorSellBreak",
                parameters={"xd": 20},
                factor_type="sell"
            )
        ]
        
        # 模拟导入的 Abu 模块和类
        with patch('backend.app.abupy_adapter.factors_converter.importlib.import_module') as mock_import_module:
            def mock_import(module_name):
                mock_module = MagicMock()
                if 'FactorBuy' in module_name:
                    # 创建真实类而不是MagicMock
                    mock_module.AbuFactorBuyBreak = type('AbuFactorBuyBreak', (), {
                        '__init__': lambda self, **kwargs: None,
                        '__module__': 'abupy.FactorBuyBu.ABuFactorBuyBreak'
                    })
                    # 确保类型检查成功
                    assert isinstance(mock_module.AbuFactorBuyBreak, type)
                elif 'FactorSell' in module_name:
                    # 创建真实类而不是MagicMock
                    mock_module.AbuFactorSellBreak = type('AbuFactorSellBreak', (), {
                        '__init__': lambda self, **kwargs: None,
                        '__module__': 'abupy.FactorSellBu.ABuFactorSellBreak'
                    })
                    # 确保类型检查成功
                    assert isinstance(mock_module.AbuFactorSellBreak, type)
                return mock_module
            
            mock_import_module.side_effect = mock_import
            
            # 调用转换方法
            mock_capital = 1000000.0
            mock_kl_pd = pd.DataFrame()
            mock_benchmark = pd.DataFrame()
            mock_combine_kl_pd = None
            buy_result = FactorsConverter.convert_to_abu_factors(
                buy_factors,
                capital=mock_capital,
                kl_pd=mock_kl_pd,
                benchmark=mock_benchmark,
                combine_kl_pd=mock_combine_kl_pd
            )
            sell_result = FactorsConverter.convert_to_abu_factors(
                sell_factors,
                capital=mock_capital,
                kl_pd=mock_kl_pd,
                benchmark=mock_benchmark,
                combine_kl_pd=mock_combine_kl_pd
            )
            result = buy_result + sell_result
            
            # 验证结果
            assert len(result) == 2
            assert mock_import_module.call_count == 2
    
    def test_convert_factors_unknown_class(self):
        """测试：当因子类在abupy模块中不存在时，应抛出AdapterError"""
        buy_factors = [
            BuyFactor(
                name="AbuFactorBuyNonExistent",
                class_name="ThisClassDoesNotExist",
                factor_type="buy"
            )
        ]

        # 黑盒测试：我们只关心最终的结果是否符合预期
        with pytest.raises(AdapterError, match=r"因子 'AbuFactorBuyNonExistent' \('ThisClassDoesNotExist'\) 配置或实例化时发生致命错误: module 'abupy.FactorBuyBu' has no attribute 'ThisClassDoesNotExist'"):
            FactorsConverter.convert_to_abu_factors(buy_factors)
    
    @patch('backend.app.abupy_adapter.factors_converter.importlib.import_module')
    def test_convert_factors_class_is_not_a_class(self, mock_import):
        """测试：如果动态导入的结果不是一个类，应抛出AdapterError"""
        mock_module = MagicMock()
        # 模拟一个字符串而不是类
        mock_module.AbuFactorBuyBreak = "not a class"
        mock_import.return_value = mock_module

        buy_factors = [
            BuyFactor(
                name="AbuFactorBuyBreak",
                class_name="FactorBuyBreak",
                factor_type="buy"
            )
        ]

        with pytest.raises(AdapterError, match=r"因子 'AbuFactorBuyBreak' \('FactorBuyBreak'\) 配置或实例化时发生致命错误: NotAClass"):
            FactorsConverter.convert_to_abu_factors(buy_factors)
    
    def test_validate_factors_success(self):
        """测试validate_factors方法成功验证有效因子"""
        # 测试有效的买入因子
        valid_buy_factors = [
            BuyFactor(
                name="突破买入因子",
                class_name="FactorBuyBreak",
                factor_type="buy",
                parameters={"xd": 20}
            ),
            BuyFactor(
                name="BTC日买入因子",
                class_name="AbuBTCDayBuy",
                factor_type="buy",
                parameters={}
            )
        ]
        
        # 测试有效的卖出因子
        valid_sell_factors = [
            SellFactor(
                name="N日卖出因子",
                class_name="FactorSellNDay",
                factor_type="sell",
                parameters={"sell_n": 5}
            ),
            SellFactor(
                name="ATR止损因子",
                class_name="AbuFactorAtrNStop",
                factor_type="sell",
                parameters={"stop_loss_n": 2.0}
            )
        ]
        
        # 这些调用应该不抛出任何异常
        try:
            FactorsConverter.validate_factors(valid_buy_factors)
            FactorsConverter.validate_factors(valid_sell_factors)
            FactorsConverter.validate_factors(valid_buy_factors + valid_sell_factors)
            FactorsConverter.validate_factors([])  # 空列表也应该通过验证
        except ValueError as e:
            pytest.fail(f"validate_factors应该通过验证，但抛出了异常: {e}")
    
    def test_validate_factors_invalid_factor(self):
        """测试validate_factors方法对无效因子的验证"""
        # 测试无效的因子class_name
        invalid_factors = [
            BuyFactor(
                name="无效因子",
                class_name="NonExistentFactor",
                factor_type="buy",
                parameters={}
            )
        ]
        
        # 应该抛出ValueError异常
        with pytest.raises(ValueError, match=r"因子 'NonExistentFactor' 是一个无效或不受支持的因子。"):
            FactorsConverter.validate_factors(invalid_factors)
