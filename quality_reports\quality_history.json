[{"timestamp": "2025-07-11T20:24:26.439681", "metrics": {"code_quality_score": 67.53, "test_coverage_percentage": 11.58, "documentation_coverage": 78.6, "refactoring_opportunities": 835, "high_priority_issues": 28, "technical_debt_hours": 1814, "maintainability_index": 39.45, "overall_score": 45.38}}, {"timestamp": "2025-07-11T23:07:55.320274", "metrics": {"code_quality_score": 67.26, "test_coverage_percentage": 11.57, "documentation_coverage": 78.44, "refactoring_opportunities": 836, "high_priority_issues": 28, "technical_debt_hours": 1815, "maintainability_index": 39.34, "overall_score": 45.24}}, {"timestamp": "2025-07-12T16:39:56.222890", "metrics": {"code_quality_score": 67.26, "test_coverage_percentage": 11.57, "documentation_coverage": 78.44, "refactoring_opportunities": 836, "high_priority_issues": 28, "technical_debt_hours": 1815, "maintainability_index": 39.34, "overall_score": 45.24}}, {"timestamp": "2025-07-20T10:47:47.317805", "metrics": {"code_quality_score": 70.55, "test_coverage_percentage": 11.17, "documentation_coverage": 83.53, "refactoring_opportunities": 854, "high_priority_issues": 28, "technical_debt_hours": 1857, "maintainability_index": 41.22, "overall_score": 47.39}}, {"timestamp": "2025-07-20T18:21:39.704626", "metrics": {"code_quality_score": 70.68, "test_coverage_percentage": 11.22, "documentation_coverage": 83.66, "refactoring_opportunities": 855, "high_priority_issues": 27, "technical_debt_hours": 1855, "maintainability_index": 41.3, "overall_score": 47.48}}, {"timestamp": "2025-07-24T09:55:45.435801", "metrics": {"code_quality_score": 71.89, "test_coverage_percentage": 15.81, "documentation_coverage": 83.5, "refactoring_opportunities": 776, "high_priority_issues": 24, "technical_debt_hours": 1545, "maintainability_index": 43.01, "overall_score": 49.5}}, {"timestamp": "2025-07-24T10:03:58.890072", "metrics": {"code_quality_score": 71.89, "test_coverage_percentage": 0, "documentation_coverage": 83.5, "refactoring_opportunities": 758, "high_priority_issues": 24, "technical_debt_hours": 1497, "maintainability_index": 38.27, "overall_score": 43.89}}, {"timestamp": "2025-07-24T10:13:42.629610", "metrics": {"code_quality_score": 71.89, "test_coverage_percentage": 64.52, "documentation_coverage": 83.5, "refactoring_opportunities": 756, "high_priority_issues": 24, "technical_debt_hours": 1495, "maintainability_index": 57.62, "overall_score": 66.8}}]