/**
 * 日期工具函数
 */

/**
 * 格式化日期为友好的显示格式
 */
export function formatDate(dateString: string | undefined | null): string {
  if (!dateString) {
    return '未知时间'
  }
  
  try {
    const date = new Date(dateString)
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '无效日期'
    }
    
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    // 如果是今天内
    if (diffDays === 0) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 如果是本周内
    if (diffDays < 7) {
      return `${diffDays}天前`
    }
    
    // 如果是本年内
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString('zh-CN', {
        month: 'numeric',
        day: 'numeric'
      })
    }
    
    // 完整日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric'
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return '格式错误'
  }
}

/**
 * 格式化日期为完整格式
 */
export function formatFullDate(dateString: string | undefined | null): string {
  if (!dateString) {
    return '未知时间'
  }
  
  try {
    const date = new Date(dateString)
    
    if (isNaN(date.getTime())) {
      return '无效日期'
    }
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('完整日期格式化错误:', error)
    return '格式错误'
  }
}

/**
 * 获取相对时间描述
 */
export function getRelativeTime(dateString: string | undefined | null): string {
  if (!dateString) {
    return '未知时间'
  }
  
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffTime / (1000 * 60))
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffMinutes < 1) {
      return '刚刚'
    }
    
    if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    }
    
    if (diffHours < 24) {
      return `${diffHours}小时前`
    }
    
    if (diffDays < 30) {
      return `${diffDays}天前`
    }
    
    if (diffDays < 365) {
      const diffMonths = Math.floor(diffDays / 30)
      return `${diffMonths}个月前`
    }
    
    const diffYears = Math.floor(diffDays / 365)
    return `${diffYears}年前`
  } catch (error) {
    console.error('相对时间计算错误:', error)
    return '时间错误'
  }
}

/**
 * 检查日期是否为今天
 */
export function isToday(dateString: string): boolean {
  try {
    const date = new Date(dateString)
    const today = new Date()
    
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear()
  } catch (error) {
    return false
  }
}

/**
 * 检查日期是否为本周
 */
export function isThisWeek(dateString: string): boolean {
  try {
    const date = new Date(dateString)
    const today = new Date()
    const diffTime = today.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays >= 0 && diffDays < 7
  } catch (error) {
    return false
  }
}
