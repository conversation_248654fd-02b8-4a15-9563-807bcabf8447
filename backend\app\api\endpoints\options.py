"""选项API端点

该模块提供策略选项相关的API端点，包括获取仓位管理策略和裁判规则等功能。
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query

from backend.app.schemas.options import (
    PositionOption,
    PositionOptionResponse,
    JudgeOption,
    JudgeOptionResponse,
    FeatureFilterOption,
    FeatureFilterResponse
)
from backend.app.services.options_service import OptionsService
from backend.app.core.exceptions import ResourceNotFoundError, ValidationError

router = APIRouter(prefix="/options", tags=["options"])
options_service = OptionsService()


@router.get("/positions", response_model=PositionOptionResponse)
async def get_position_options() -> PositionOptionResponse:
    """获取所有支持的仓位管理策略及其参数信息"""
    try:
        position_options = options_service.get_position_options()
        return PositionOptionResponse(options=position_options, total=len(position_options))
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仓位管理策略失败: {str(e)}")


@router.get("/judges", response_model=JudgeOptionResponse)
async def get_judge_options() -> JudgeOptionResponse:
    """获取所有支持的裁判规则及其参数信息"""
    try:
        judge_options = options_service.get_judge_options()
        return JudgeOptionResponse(options=judge_options, total=len(judge_options))
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取裁判规则失败: {str(e)}")


@router.get("/feature-filters", response_model=FeatureFilterResponse)
async def get_feature_filters(
    model_name: Optional[str] = Query(None, description="已训练好的模型名称")
) -> FeatureFilterResponse:
    """基于K线角度特征，过滤大概率亏损的交易模式"""
    try:
        feature_filters = options_service.get_feature_filters(model_name)
        return FeatureFilterResponse(filters=feature_filters, total=len(feature_filters))
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取特征过滤器失败: {str(e)}")
