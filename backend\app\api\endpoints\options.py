from fastapi import APIRouter
from typing import List, Dict, Any
from app.abupy_adapter.position_adapter import SUPPORTED_POSITIONS

router = APIRouter()

@router.get("/positions", response_model=List[Dict[str, Any]])
def get_position_options():
    """
    获取所有支持的仓位管理策略及其参数信息。
    """
    return SUPPORTED_POSITIONS

SUPPORTED_UMPIRES = [
    {
        "name": "角度主裁",
        "class_name": "AbuUmpMainDeg",
        "description": "基于K线角度特征，过滤大概率亏损的交易模式。",
        "type": "main",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_main_ump"
            }
        ]
    },
    {
        "name": "角度边裁",
        "class_name": "AbuUmpEdgeDeg",
        "description": "基于K线角度特征，防范极端亏损的交易。",
        "type": "edge",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_edge_ump"
            }
        ]
    },
    {
        "name": "价格主裁",
        "class_name": "AbuUmpMainPrice",
        "description": "基于价格位置特征，过滤大概率亏损的交易模式。",
        "type": "main",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_main_ump"
            }
        ]
    },
    {
        "name": "价格边裁",
        "class_name": "AbuUmpEdgePrice",
        "description": "基于价格位置特征，防范极端亏损的交易。",
        "type": "edge",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_edge_ump"
            }
        ]
    },
    {
        "name": "波动主裁",
        "class_name": "AbuUmpMainWave",
        "description": "基于价格波动率特征，过滤大概率亏损的交易模式。",
        "type": "main",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_main_ump"
            }
        ]
    },
    {
        "name": "波动边裁",
        "class_name": "AbuUmpEdgeWave",
        "description": "基于价格波动率特征，防范极端亏损的交易。",
        "type": "edge",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_edge_ump"
            }
        ]
    },
    {
        "name": "跳空主裁",
        "class_name": "AbuUmpMainJump",
        "description": "基于是否存在跳空缺口，过滤大概率亏损的交易模式。",
        "type": "main",
        "parameters": [
            {
                "name": "market_name",
                "type": "string",
                "required": True,
                "description": "已训练好的模型名称。",
                "default": "train_main_ump"
            }
        ]
    }
]

@router.get("/umpires", response_model=List[Dict[str, Any]])
def get_umpire_options():
    """
    获取所有支持的裁判(Umpire)规则及其参数信息。
    """
    return SUPPORTED_UMPIRES
