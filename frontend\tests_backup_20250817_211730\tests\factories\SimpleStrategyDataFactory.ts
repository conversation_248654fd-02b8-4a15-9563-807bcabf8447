// 简化数据工厂 - Strategy
// 用于TDD快速测试数据生成

import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../src/api/types';

/**
 * Strategy数据工厂类
 * 提供类型安全的mock数据生成方法
 */
export class SimpleStrategyDataFactory {
  /**
   * 创建基础策略数据
   */
  static createStrategy(overrides: Partial<Strategy> = {}): Strategy {
    return {
      id: 'strategy-001',
      name: '双均线策略',
      description: '基于短期和长期移动平均线的交叉信号进行买卖决策',
      author: 'test-user',
      content: JSON.stringify({
        type: 'moving_average_crossover',
        short_period: 5,
        long_period: 20
      }),
      create_time: '2024-01-01T00:00:00Z',
      update_time: '2024-01-01T00:00:00Z',
      owner: 'test-user',
      is_public: true,
      buy_factors: [
        {
          factor_id: 'ma_cross_up',
          weight: 1.0,
          threshold: 0.02
        }
      ],
      sell_factors: [
        {
          factor_id: 'ma_cross_down',
          weight: 1.0,
          threshold: -0.02
        }
      ],
      position_strategy: {
        type: 'fixed_ratio',
        ratio: 0.8
      },
      parameters: {
        short_ma: 5,
        long_ma: 20,
        stop_loss: 0.05,
        take_profit: 0.15
      },
      tags: ['技术分析', '均线策略'],
      umpire_rules: [
        {
          rule_id: 'max_position',
          value: 0.8
        }
      ],
      ...overrides
    };
  }

  /**
   * 创建策略创建请求数据
   */
  static createStrategyRequest(overrides: Partial<CreateStrategyRequest> = {}): CreateStrategyRequest {
    return {
      name: '新策略',
      description: '测试策略描述',
      content: JSON.stringify({
        type: 'test_strategy',
        parameters: {}
      }),
      is_public: false,
      buy_factors: [],
      sell_factors: [],
      parameters: {},
      ...overrides
    };
  }

  /**
   * 创建策略更新请求数据
   */
  static createUpdateStrategyRequest(overrides: Partial<UpdateStrategyRequest> = {}): UpdateStrategyRequest {
    return {
      name: '更新的策略名称',
      description: '更新的策略描述',
      ...overrides
    };
  }

  /**
   * 创建多个策略数据
   */
  static createStrategies(count: number = 3): Strategy[] {
    return Array.from({ length: count }, (_, index) => 
      this.createStrategy({
        id: `strategy-${String(index + 1).padStart(3, '0')}`,
        name: `策略${index + 1}`,
        description: `测试策略${index + 1}的描述`
      })
    );
  }

  /**
   * 创建边界情况的策略数据
   */
  static createBoundaryStrategy(): Strategy {
    return this.createStrategy({
      id: 'boundary-strategy',
      name: 'A'.repeat(100), // 长名称
      description: 'B'.repeat(500), // 长描述
      buy_factors: [],
      sell_factors: [],
      parameters: {}
    });
  }

  /**
   * 创建无效的策略数据（用于错误测试）
   */
  static createInvalidStrategy(): Partial<Strategy> {
    return {
      id: '',
      name: '',
      description: '',
      author: '',
      content: 'invalid-json',
      is_public: true,
      buy_factors: [],
      sell_factors: [],
      parameters: {}
    };
  }

  /**
   * 创建复杂策略数据
   */
  static createComplexStrategy(): Strategy {
    return this.createStrategy({
      id: 'complex-strategy',
      name: '多因子量化策略',
      description: '结合技术指标、基本面和市场情绪的综合策略',
      buy_factors: [
        {
          factor_id: 'rsi_oversold',
          weight: 0.3,
          threshold: 30
        },
        {
          factor_id: 'pe_ratio_low',
          weight: 0.4,
          threshold: 15
        },
        {
          factor_id: 'volume_surge',
          weight: 0.3,
          threshold: 2.0
        }
      ],
      sell_factors: [
        {
          factor_id: 'rsi_overbought',
          weight: 0.4,
          threshold: 70
        },
        {
          factor_id: 'pe_ratio_high',
          weight: 0.3,
          threshold: 50
        },
        {
          factor_id: 'volume_drop',
          weight: 0.3,
          threshold: 0.5
        }
      ],
      parameters: {
        rsi_period: 14,
        volume_ma_period: 20,
        pe_lookback: 252,
        stop_loss: 0.08,
        take_profit: 0.20,
        max_position_size: 0.1
      },
      tags: ['多因子', '量化', '技术分析', '基本面'],
      umpire_rules: [
        {
          rule_id: 'max_single_position',
          value: 0.1
        },
        {
          rule_id: 'sector_limit',
          value: 0.3
        }
      ]
    });
  }

  /**
   * 创建有效的策略数据（用于测试）
   * 包含默认的买入和卖出因子
   */
  static createValidStrategy(overrides: Partial<Strategy> = {}): Strategy {
    return this.createStrategy({
      id: 'strategy-valid-001',
      name: '有效测试策略',
      description: '用于测试的有效策略配置',
      buy_factors: [
        {
          factor_id: 'ma_cross_up',
          weight: 1.0,
          threshold: 0.02
        }
      ],
      sell_factors: [
        {
          factor_id: 'ma_cross_down',
          weight: 1.0,
          threshold: -0.02
        }
      ],
      parameters: {
        short_ma: 5,
        long_ma: 20,
        stop_loss: 0.05,
        take_profit: 0.15
      },
      tags: ['测试', '有效策略'],
      ...overrides
    });
  }

  /**
   * 验证策略数据的API契约
   */
  static validateStrategyContract(strategy: Strategy): boolean {
    const requiredFields = ['id', 'name', 'description', 'author', 'content', 'is_public', 'buy_factors', 'sell_factors', 'parameters'];
    
    for (const field of requiredFields) {
      if (!(field in strategy)) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // 验证content是否为有效JSON
    try {
      JSON.parse(strategy.content);
    } catch (error) {
      console.error('Invalid JSON in content field:', error);
      return false;
    }

    // 验证因子数组
    if (!Array.isArray(strategy.buy_factors) || !Array.isArray(strategy.sell_factors)) {
      console.error('buy_factors and sell_factors must be arrays');
      return false;
    }

    return true;
  }

  /**
   * 创建API响应格式的数据
   */
  static createApiResponse<T>(data: T, success: boolean = true, message: string = 'Success'): {
    data: T;
    success: boolean;
    message: string;
  } {
    return {
      data,
      success,
      message
    };
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(message: string = 'Internal Server Error'): {
    data: null;
    success: boolean;
    message: string;
  } {
    return {
      data: null,
      success: false,
      message
    };
  }
}

// 导出便捷方法
export const {
  createStrategy,
  createStrategyRequest,
  createUpdateStrategyRequest,
  createStrategies,
  createBoundaryStrategy,
  createInvalidStrategy,
  createComplexStrategy,
  createValidStrategy,
  validateStrategyContract,
  createApiResponse,
  createErrorResponse
} = SimpleStrategyDataFactory;