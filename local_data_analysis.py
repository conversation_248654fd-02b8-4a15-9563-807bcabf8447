import pandas as pd

# 分析本地数据的日期范围
data_file = 'data/market_data.h5'

print("=== 本地数据日期范围分析 ===")
try:
    with pd.HDFStore(data_file, 'r') as store:
        print(f"HDF5 存储中的股票代码: {store.keys()}")
        
        # 检查 000300.SH 数据
        if '/000300.SH' in store.keys():
            df = store['/000300.SH']
            print(f"\n000300.SH 数据详情:")
            print(f"数据形状: {df.shape}")
            print(f"列名: {df.columns.tolist()}")
            
            # 检查 trade_date 列
            if 'trade_date' in df.columns:
                trade_dates = df['trade_date']
                print(f"\n交易日期信息:")
                print(f"最早日期: {trade_dates.min()}")
                print(f"最晚日期: {trade_dates.max()}")
                print(f"总记录数: {len(trade_dates)}")
                
                # 转换为标准日期格式
                try:
                    date_series = pd.to_datetime(trade_dates, format='%Y%m%d')
                    print(f"\n标准日期格式:")
                    print(f"最早日期: {date_series.min().strftime('%Y-%m-%d')}")
                    print(f"最晚日期: {date_series.max().strftime('%Y-%m-%d')}")
                    
                    # 检查是否包含2024年数据
                    dates_2024 = date_series[date_series.dt.year == 2024]
                    print(f"\n2024年数据: {len(dates_2024)} 条")
                    if len(dates_2024) > 0:
                        print(f"2024年日期范围: {dates_2024.min().strftime('%Y-%m-%d')} 到 {dates_2024.max().strftime('%Y-%m-%d')}")
                    else:
                        print("❌ 本地数据中没有2024年的数据")
                        
                    # 检查是否包含2024年1月数据
                    dates_202401 = date_series[(date_series.dt.year == 2024) & (date_series.dt.month == 1)]
                    print(f"\n2024年1月数据: {len(dates_202401)} 条")
                    if len(dates_202401) == 0:
                        print("❌ 本地数据中没有2024年1月的数据")
                        print("\n这就是为什么在指定日期范围 20240101-20240131 内找不到数据的原因！")
                        
                        # 建议使用本地数据的实际日期范围
                        print(f"\n💡 建议使用本地数据的实际日期范围:")
                        print(f"   起始日期: {date_series.min().strftime('%Y%m%d')}")
                        print(f"   结束日期: {date_series.max().strftime('%Y%m%d')}")
                        print(f"   或者选择一个子范围，例如:")
                        print(f"   2021年: 20210104-20211231")
                        print(f"   2022年: 20220104-20221230")
                except Exception as date_e:
                    print(f"日期转换失败: {date_e}")
                        
except Exception as e:
    print(f"分析失败: {e}")
    
print("\n=== 结论 ===")
print("用户请求的日期范围是 20240101-20240131 (2024年1月)")
print("但本地数据只包含 2021年1月4日 到 2022年12月30日 的数据")
print("因此无法找到指定日期范围内的数据")
print("\n解决方案:")
print("1. 修改测试用例，使用本地数据实际存在的日期范围")
print("2. 或者更新本地数据，添加2024年的数据")