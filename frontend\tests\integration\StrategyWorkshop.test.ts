import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StrategyWorkshop from '../../src/views/StrategyWorkshop.vue'
import type { Strategy } from '../../src/api/types'

// Mock stores
const mockUseStrategyStore = {
  strategies: [],
  currentStrategy: null,
  currentSelectedStrategy: null,
  isLoading: false,
  error: '',
  fetchStrategies: vi.fn(),
  fetchStrategyById: vi.fn(),
  createStrategy: vi.fn(),
  updateStrategy: vi.fn(),
  deleteStrategy: vi.fn(),
  setCurrentStrategy: vi.fn(),
  clearCurrentStrategy: vi.fn(),
  setCurrentSelectedStrategy: vi.fn(),
  clearCurrentSelectedStrategy: vi.fn(),
  startNewStrategyCreation: vi.fn()
}

const mockUseFactorsStore = {
  factors: [],
  isLoading: false,
  fetchFactors: vi.fn()
}

vi.mock('../../src/stores', () => ({
  useStrategyStore: () => mockUseStrategyStore,
  useFactorsStore: () => mockUseFactorsStore
}))

// Mock components
vi.mock('../../src/components/StrategyWorkshop/StrategyList.vue', () => ({
  default: {
    name: 'StrategyList',
    template: `
      <div data-testid="strategy-list">
        <div v-for="strategy in strategies" :key="strategy.id" 
             @click="$emit('select-strategy', strategy)"
             :data-testid="'strategy-item-' + strategy.id">
          {{ strategy.name }}
        </div>
        <button @click="$emit('create-strategy')" data-testid="create-strategy-btn">
          新建策略
        </button>
      </div>
    `,
    props: ['strategies', 'currentStrategy', 'loading', 'error'],
    emits: ['select-strategy', 'create-strategy']
  }
}))

vi.mock('../../src/components/StrategyWorkshop/StrategyEditor.vue', () => ({
  default: {
    name: 'StrategyEditor',
    template: `
      <div data-testid="strategy-editor">
        <div v-if="currentStrategy">
          编辑策略: {{ currentStrategy.name }}
        </div>
        <div v-else>
          请选择或创建策略
        </div>
        <button @click="$emit('save-strategy', mockStrategy)" data-testid="save-strategy-btn">
          保存策略
        </button>
      </div>
    `,
    props: ['currentStrategy'],
    emits: ['save-strategy', 'update-strategy', 'create-strategy', 'error'],
    setup() {
      const mockStrategy = {
        name: '新策略',
        description: '测试策略描述'
      }
      return { mockStrategy }
    }
  }
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn()
  },
  ElPageHeader: {
    name: 'ElPageHeader',
    template: '<div class="el-page-header"><slot /></div>'
  }
}))

describe('StrategyWorkshop Integration', () => {
  const mockStrategies: Strategy[] = [
    {
      id: 'strategy-1',
      name: '策略一',
      description: '第一个策略',
      author: '用户A',
      is_public: true,
      create_time: '2023-01-01T00:00:00Z',
      update_time: '2023-01-01T00:00:00Z',
      factors: []
    },
    {
      id: 'strategy-2',
      name: '策略二',
      description: '第二个策略',
      author: '用户B',
      is_public: false,
      create_time: '2023-01-02T00:00:00Z',
      update_time: '2023-01-02T00:00:00Z',
      factors: []
    }
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    
    // 重置 mock store 状态
    mockUseStrategyStore.strategies = []
    mockUseStrategyStore.currentStrategy = null
    mockUseStrategyStore.currentSelectedStrategy = null
    mockUseStrategyStore.isLoading = false
    mockUseStrategyStore.error = ''
    
    // 重新设置 mock 函数
    mockUseStrategyStore.fetchStrategies = vi.fn().mockResolvedValue([])
    mockUseStrategyStore.fetchStrategyById = vi.fn()
    mockUseStrategyStore.createNewStrategy = vi.fn()
    mockUseStrategyStore.updateExistingStrategy = vi.fn()
    mockUseStrategyStore.deleteExistingStrategy = vi.fn()
    mockUseStrategyStore.createStrategy = vi.fn()
    mockUseStrategyStore.updateStrategy = vi.fn()
    mockUseStrategyStore.setCurrentStrategy = vi.fn()
    mockUseStrategyStore.clearCurrentStrategy = vi.fn()
    mockUseStrategyStore.setCurrentSelectedStrategy = vi.fn()
    mockUseStrategyStore.clearCurrentSelectedStrategy = vi.fn()
    
    mockUseFactorsStore.fetchFactors = vi.fn().mockResolvedValue([])
  })

  const createWrapper = () => {
    return mount(StrategyWorkshop, {
      global: {
        stubs: {
          StrategyList: {
            name: 'StrategyList',
            template: '<div class="strategy-list"></div>',
            props: ['strategies', 'currentStrategy', 'loading', 'error']
          },
          StrategyEditor: {
            name: 'StrategyEditor',
            template: '<div class="strategy-editor"></div>',
            props: ['strategy', 'loading'],
            methods: {
              onSaveComplete: vi.fn()
            }
          }
        }
      }
    })
  }

  describe('初始化', () => {
    it('应该在组件挂载时获取策略列表', () => {
      createWrapper()
      
      expect(mockUseStrategyStore.fetchStrategies).toHaveBeenCalled()
    })

    it('应该在组件挂载时获取因子列表', () => {
      createWrapper()
      
      expect(mockUseFactorsStore.fetchFactors).toHaveBeenCalled()
    })

    it('应该正确渲染策略列表和编辑器组件', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="strategy-list"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="strategy-editor"]').exists()).toBe(true)
    })
  })

  describe('策略选择流程', () => {
    it('应该能够选择策略并更新编辑器', async () => {
      mockUseStrategyStore.strategies = mockStrategies
      const wrapper = createWrapper()
      
      // 模拟选择策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('select-strategy', mockStrategies[0])
      
      expect(mockUseStrategyStore.setCurrentSelectedStrategy).toHaveBeenCalledWith(mockStrategies[0])
    })

    it('应该在选择策略时设置当前选中策略', async () => {
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('select-strategy', mockStrategies[0])
      
      expect(mockUseStrategyStore.setCurrentSelectedStrategy).toHaveBeenCalledWith(mockStrategies[0])
    })

    it('应该能够正常选择策略', async () => {
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('select-strategy', mockStrategies[0])
      
      // 验证策略选择
      expect(mockUseStrategyStore.setCurrentSelectedStrategy).toHaveBeenCalledWith(mockStrategies[0])
    })
  })

  describe('策略创建流程', () => {
    it('应该能够创建新策略', async () => {
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      
      expect(mockUseStrategyStore.startNewStrategyCreation).toHaveBeenCalled()
    })

    it('应该在创建新策略时清空编辑器', async () => {
      mockUseStrategyStore.currentStrategy = mockStrategies[0]
      
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('create-strategy')
      
      expect(mockUseStrategyStore.startNewStrategyCreation).toHaveBeenCalled()
    })
  })

  describe('策略保存流程', () => {
    it('应该能够保存新策略', async () => {
      mockUseStrategyStore.currentStrategy = null
      mockUseStrategyStore.createNewStrategy.mockResolvedValue({
        id: 'new-strategy',
        name: '新策略'
      })
      
      const wrapper = createWrapper()
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', {
        name: '新策略',
        description: '新策略描述'
      })
      
      expect(mockUseStrategyStore.createStrategy).toHaveBeenCalledWith({
        name: '新策略',
        description: '新策略描述'
      })
    })

    it('应该能够更新现有策略', async () => {
      mockUseStrategyStore.currentSelectedStrategy = mockStrategies[0]
      mockUseStrategyStore.updateStrategy.mockResolvedValue(mockStrategies[0])
      
      const wrapper = createWrapper()
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', {
        id: 'strategy-1',
        name: '更新的策略',
        description: '更新的描述'
      })
      
      expect(mockUseStrategyStore.updateStrategy).toHaveBeenCalledWith(
        'strategy-1',
        {
          id: 'strategy-1',
          name: '更新的策略',
          description: '更新的描述'
        }
      )
    })

    it('应该在保存成功后调用onSaveComplete', async () => {
      mockUseStrategyStore.createStrategy.mockResolvedValue({
        id: 'new-strategy',
        name: '新策略'
      })
      
      const wrapper = createWrapper()
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', {
        name: '新策略',
        description: '新策略描述'
      })
      
      // 等待异步操作完成
      await wrapper.vm.$nextTick()
      
      expect(mockUseStrategyStore.createStrategy).toHaveBeenCalled()
    })

    it('应该在保存成功后刷新策略列表', async () => {
      mockUseStrategyStore.createNewStrategy.mockResolvedValue({
        id: 'new-strategy',
        name: '新策略'
      })
      
      const wrapper = createWrapper()
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', {
        name: '新策略',
        description: '新策略描述'
      })
      
      await wrapper.vm.$nextTick()
      
      expect(mockUseStrategyStore.fetchStrategies).toHaveBeenCalledTimes(2) // 初始化 + 保存后刷新
    })

    it('应该处理保存时的错误', async () => {
      mockUseStrategyStore.createStrategy.mockRejectedValue(new Error('保存失败'))
      
      const wrapper = createWrapper()
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', {
        name: '新策略',
        description: '新策略描述'
      })
      
      await wrapper.vm.$nextTick()
      
      expect(mockUseStrategyStore.createStrategy).toHaveBeenCalled()
    })
  })

  describe('数据传递', () => {
    it('应该正确传递策略列表数据到StrategyList组件', () => {
      mockUseStrategyStore.strategies = mockStrategies
      mockUseStrategyStore.isLoading = true
      mockUseStrategyStore.error = '加载错误'
      
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      expect(strategyList.props('strategies')).toEqual(mockStrategies)
      expect(strategyList.props('loading')).toBe(true)
      expect(strategyList.props('error')).toBe('加载错误')
    })

    it('应该正确传递当前策略数据到StrategyEditor组件', () => {
      mockUseStrategyStore.currentSelectedStrategy = mockStrategies[0]
      
      const wrapper = createWrapper()
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      expect(strategyEditor.props('strategy')).toEqual(mockStrategies[0])
    })
  })

  describe('状态管理', () => {
    it('应该响应store状态变化', async () => {
      // 在创建组件前设置store状态
      mockUseStrategyStore.strategies = mockStrategies
      mockUseStrategyStore.currentSelectedStrategy = mockStrategies[0]
      
      const wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      expect(strategyList.props('strategies')).toEqual(mockStrategies)
      expect(strategyEditor.props('strategy')).toEqual(mockStrategies[0])
    })

    it('应该处理加载状态', () => {
      mockUseStrategyStore.isLoading = true
      
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      expect(strategyList.props('loading')).toBe(true)
    })

    it('应该处理错误状态', () => {
      mockUseStrategyStore.error = '网络连接失败'
      
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      expect(strategyList.props('error')).toBe('网络连接失败')
    })
  })

  describe('组件间通信', () => {
    it('应该正确处理StrategyList和StrategyEditor之间的数据流', async () => {
      mockUseStrategyStore.strategies = mockStrategies
      
      const wrapper = createWrapper()
      
      // 1. 从列表选择策略
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      await strategyList.vm.$emit('select-strategy', mockStrategies[0])
      
      expect(mockUseStrategyStore.setCurrentSelectedStrategy).toHaveBeenCalledWith(mockStrategies[0])
      
      // 2. 在编辑器中保存策略
      mockUseStrategyStore.currentSelectedStrategy = mockStrategies[0]
      mockUseStrategyStore.updateStrategy.mockResolvedValue(mockStrategies[0])
      
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      await strategyEditor.vm.$emit('save-strategy', {
        ...mockStrategies[0],
        name: '更新的策略',
        description: '更新的描述'
      })
      
      expect(mockUseStrategyStore.updateStrategy).toHaveBeenCalled()
    })
  })

  describe('错误边界', () => {
    it.skip('应该处理组件初始化时的错误', async () => {
      mockUseStrategyStore.fetchStrategies.mockRejectedValue(new Error('初始化失败'))
      
      const wrapper = createWrapper()
      
      // 等待组件完成初始化
      await wrapper.vm.$nextTick()
      
      expect(wrapper.exists()).toBe(true)
    })

    it('应该处理空数据状态', () => {
      mockUseStrategyStore.strategies = []
      mockUseStrategyStore.currentStrategy = null
      
      const wrapper = createWrapper()
      
      const strategyList = wrapper.findComponent({ name: 'StrategyList' })
      const strategyEditor = wrapper.findComponent({ name: 'StrategyEditor' })
      
      expect(strategyList.props('strategies')).toEqual([])
      expect(strategyEditor.props('strategy')).toBe(null)
    })
  })
})