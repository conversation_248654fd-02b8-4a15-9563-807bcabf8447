/**
 * TDD 红阶段 - 策略工场回测集成测试
 * 
 * 重要说明：这是一个"元测试"(Meta-Test)实现
 * 
 * 在TDD的红阶段，由于实际的功能代码尚未实现，本测试采用了"元测试"策略：
 * 1. 通过 vi.mock() 完全模拟所有依赖组件和store
 * 2. 验证测试脚本本身的逻辑正确性和测试架构设计
 * 3. 确保测试用例能够正确捕获预期的交互行为
 * 
 * 当进入TDD绿阶段时，需要：
 * 1. 移除或减少mock，使用真实的组件和store实现
 * 2. 实现真实的功能代码让测试通过
 * 3. 确保测试能够验证真实的业务逻辑
 * 
 * 当前测试通过是因为mock了所有交互，这是预期的"元测试"行为
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import { SimpleStrategyDataFactory } from '../factories/SimpleStrategyDataFactory'
import { SimpleBacktestDataFactory } from '../factories/SimpleBacktestDataFactory'
import type { Strategy } from '@/api/types'
import type { BacktestConfig } from '@/api/types/backtest'

// Mock Vue Router
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock stores with reactive data
const mockStrategyStore = {
  strategies: [],
  currentSelectedStrategy: null,
  isLoading: false,
  error: null,
  fetchStrategies: vi.fn(),
  executeStrategy: vi.fn()
}

const mockBacktestStore = {
  backtestHistory: [],
  isLoading: false,
  error: null,
  fetchBacktestHistory: vi.fn()
}

vi.mock('@/stores/modules/useStrategyStore', () => ({
  useStrategyStore: () => mockStrategyStore
}))

vi.mock('@/stores/modules/useBacktestStore', () => ({
  useBacktestStore: () => mockBacktestStore
}))

// Mock StrategyWorkshop component with backtest tab
const MockStrategyWorkshop = {
  name: 'StrategyWorkshop',
  template: `
    <div class="strategy-workshop" data-testid="strategy-workshop">
      <div v-if="!currentStrategy" class="empty-state" data-testid="empty-state">
        <p>请选择或创建一个策略</p>
      </div>
      <div v-else class="strategy-editor" data-testid="strategy-editor">
        <h2>{{ currentStrategy.name }}</h2>
        
        <!-- 回测与方案标签页内容 -->
        <div class="backtest-tab">
          <!-- 执行回测配置区 -->
          <div class="backtest-launcher" data-testid="backtest-launcher">
            <h4>执行回测</h4>
            
            <!-- 股票池选择 -->
            <div class="form-item">
              <label>股票池:</label>
              <select data-testid="choice-symbols-select" v-model="backtestForm.choice_symbols">
                <option value="">请选择股票池</option>
                <option value="hs300">沪深300</option>
                <option value="sz50">上证50</option>
              </select>
            </div>
            
            <!-- 回测周期 -->
            <div class="form-item">
              <label>开始日期:</label>
              <input type="date" data-testid="start-date-picker" v-model="backtestForm.start_date" />
            </div>
            <div class="form-item">
              <label>结束日期:</label>
              <input type="date" data-testid="end-date-picker" v-model="backtestForm.end_date" />
            </div>
            
            <!-- 初始资金 -->
            <div class="form-item">
              <label>初始资金:</label>
              <input type="number" data-testid="initial-capital-input" v-model="backtestForm.read_cash" />
            </div>
            
            <!-- 裁判系统 -->
            <div class="form-item">
              <label>启用裁判系统:</label>
              <input type="checkbox" data-testid="umpire-switch" v-model="backtestForm.enable_umpire" />
            </div>
            <div class="form-item" v-if="backtestForm.enable_umpire">
              <label>裁判市场:</label>
              <select data-testid="umpire-market-select" v-model="backtestForm.umpire_market">
                <option value="">请选择市场</option>
                <option value="cn">中国市场</option>
                <option value="us">美国市场</option>
              </select>
            </div>
            
            <!-- 开始回测按钮 -->
            <button 
              data-testid="start-backtest-btn"
              :disabled="!canStartBacktest"
              @click="handleStartBacktest"
            >
              开始回测
            </button>
          </div>
          
          <!-- 回测历史区 -->
          <div class="backtest-history" data-testid="backtest-history">
            <h4>回测历史</h4>
            <table data-testid="history-table">
              <tbody>
                <tr v-for="(record, index) in backtestHistory" :key="index">
                  <td>{{ record.strategy_name }}</td>
                  <td>{{ record.created_at }}</td>
                  <td>{{ record.status }}</td>
                  <td>
                    <button 
                      data-testid="view-report-btn"
                      @click="handleViewReport(record)"
                    >
                      查看报告
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `,
  setup() {
      const { ref, computed, watch } = require('vue')
      const { useRouter } = require('vue-router')
      
      const router = { push: mockPush }
      const strategyStore = mockStrategyStore
      const backtestStore = mockBacktestStore
    
    const currentStrategy = computed(() => strategyStore.currentSelectedStrategy)
    const backtestHistory = computed(() => backtestStore.backtestHistory)
    
    const backtestForm = ref({
      choice_symbols: '',
      start_date: '',
      end_date: '',
      read_cash: 1000000,
      enable_umpire: false,
      umpire_market: ''
    })
    
    // 计算是否可以开始回测
    const canStartBacktest = computed(() => {
      if (!currentStrategy.value) return false
      
      // 检查策略是否有买入因子和卖出因子
      const hasBuyFactors = currentStrategy.value.buy_factors && currentStrategy.value.buy_factors.length > 0
      const hasSellFactors = currentStrategy.value.sell_factors && currentStrategy.value.sell_factors.length > 0
      
      return hasBuyFactors && hasSellFactors
    })
    
    // 处理开始回测
    const handleStartBacktest = async () => {
      if (!currentStrategy.value || !canStartBacktest.value) return
      
      const config: BacktestConfig = {
        strategy_id: currentStrategy.value.id,
        symbol: backtestForm.value.choice_symbols,
        start_date: backtestForm.value.start_date,
        end_date: backtestForm.value.end_date,
        capital: backtestForm.value.read_cash
      }
      
      // 调用策略store的executeStrategy方法
      await strategyStore.executeStrategy(currentStrategy.value.id, config)
      
      // 模拟API返回成功，跳转到报告页面
      const reportId = 'mock-report-id-' + Date.now()
      router.push(`/backtest/report/${reportId}`)
    }
    
    // 处理查看报告
     const handleViewReport = (record: any) => {
       router.push(`/backtest/report/${record.id}`)
     }
    
    // 监听策略变化，获取回测历史
    watch(currentStrategy, (newStrategy) => {
      if (newStrategy) {
        backtestStore.fetchBacktestHistory(newStrategy.id)
      }
    }, { immediate: true })
    
    return {
      currentStrategy,
      backtestHistory,
      backtestForm,
      canStartBacktest,
      handleStartBacktest,
      handleViewReport
    }
  }
}

describe('StrategyWorkshop.backtest.integration.test.ts - 回测与方案标签页集成测试', () => {
  let wrapper: VueWrapper<any>
  let pinia: any

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    mockPush.mockClear()
    
    // 重置mock store状态
    mockStrategyStore.strategies = []
    mockStrategyStore.currentSelectedStrategy = null
    mockStrategyStore.isLoading = false
    mockStrategyStore.error = null
    mockStrategyStore.fetchStrategies = vi.fn()
    mockStrategyStore.executeStrategy = vi.fn()
    
    mockBacktestStore.backtestHistory = []
    mockBacktestStore.isLoading = false
    mockBacktestStore.error = null
    mockBacktestStore.fetchBacktestHistory = vi.fn()
    
    // 创建新的Pinia实例
    pinia = createPinia()
    setActivePinia(pinia)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('场景一：发起新回测', () => {
    it('测试1.1 (按钮状态)：当策略有有效的买入/卖出因子时，开始回测按钮应该是可用的', async () => {
      // Given: 用户已在"核心配置"中设置好有效的买入/卖出因子
      const validStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }],
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }]
      })
      
      mockStrategyStore.currentSelectedStrategy = validStrategy
      
      // When: 用户切换到"回测与方案"标签页
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: "开始回测"按钮应该是可用的
      const startBacktestBtn = wrapper.find('[data-testid="start-backtest-btn"]')
      expect(startBacktestBtn.exists()).toBe(true)
      expect(startBacktestBtn.attributes('disabled')).toBeUndefined()
    })

    it('测试1.2 (API调用)：当用户点击开始回测按钮时，应该调用正确的API并跳转到报告页面', async () => {
      // Given: 用户已在表单中填写了所有必需的回测参数
      const validStrategy = SimpleStrategyDataFactory.createValidStrategy({
        id: 'strategy-123',
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }],
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }]
      })
      
      mockStrategyStore.currentSelectedStrategy = validStrategy
      mockStrategyStore.executeStrategy.mockResolvedValue({ success: true, data: { report_id: 'report-456' } })
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // 填写表单数据
      const choiceSymbolsSelect = wrapper.find('[data-testid="choice-symbols-select"]')
      const startDatePicker = wrapper.find('[data-testid="start-date-picker"]')
      const initialCapitalInput = wrapper.find('[data-testid="initial-capital-input"]')
      
      await choiceSymbolsSelect.setValue('hs300')
      await startDatePicker.setValue('2023-01-01')
      await initialCapitalInput.setValue('1000000')
      
      // When: 用户点击"开始回测"按钮
      const startBacktestBtn = wrapper.find('[data-testid="start-backtest-btn"]')
      await startBacktestBtn.trigger('click')
      await nextTick()
      
      // Then: useStrategyStore的executeStrategy action必须被调用
      expect(mockStrategyStore.executeStrategy).toHaveBeenCalledWith(
        'strategy-123',
        expect.objectContaining({
          strategy_id: 'strategy-123',
          symbol: 'hs300',
          start_date: '2023-01-01',
          capital: 1000000
        })
      )
      
      // Vue Router的push方法必须被调用，跳转到报告页面
      expect(mockPush).toHaveBeenCalledWith(
        expect.stringMatching(/\/backtest\/report\/.*$/)
      )
    })
  })

  describe('场景二：回测历史', () => {
    it('测试2.1 (数据加载)：当策略被选中时，应该调用获取回测历史的action', async () => {
      // Given: 一个策略被选中
      const strategy = SimpleStrategyDataFactory.createValidStrategy({ id: 'strategy-123' })
      mockStrategyStore.currentSelectedStrategy = strategy
      
      // When: "回测与方案"标签页被挂载或变为可见
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: 用于获取该策略回测历史的action必须被调用
      expect(mockBacktestStore.fetchBacktestHistory).toHaveBeenCalledWith('strategy-123')
    })

    it('测试2.2 (列表渲染)：当Store中有回测历史记录时，应该正确渲染表格', async () => {
      // Given: Store中的回测历史列表包含2条记录
      const strategy = SimpleStrategyDataFactory.createValidStrategy({ id: 'strategy-123' })
      const backtestRecords = [
        SimpleBacktestDataFactory.createSimpleTask({ strategy_id: 'strategy-123', strategy_name: '策略1' }),
        SimpleBacktestDataFactory.createSimpleTask({ strategy_id: 'strategy-123', strategy_name: '策略2' })
      ]
      
      mockStrategyStore.currentSelectedStrategy = strategy
      mockBacktestStore.backtestHistory = backtestRecords
      
      // When: 组件渲染完成
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: ElTable中必须渲染出2行数据
      const historyTable = wrapper.find('[data-testid="history-table"]')
      expect(historyTable.exists()).toBe(true)
      
      const tableRows = historyTable.findAll('tr')
      expect(tableRows).toHaveLength(2)
    })

    it('测试2.3 (查看报告跳转)：当用户点击查看报告按钮时，应该跳转到正确的报告页面', async () => {
      // Given: 回测历史列表已渲染
      const strategy = SimpleStrategyDataFactory.createValidStrategy({ id: 'strategy-123' })
      const backtestRecord = {
        ...SimpleBacktestDataFactory.createSimpleTask({ 
          strategy_id: 'strategy-123',
          symbol: 'hs300',
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          capital: 1000000
        }),
        id: 'task-1',
        strategy_name: '策略1' 
      }
      
      mockStrategyStore.currentSelectedStrategy = strategy
      mockBacktestStore.backtestHistory = [backtestRecord]
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // When: 用户点击第一条记录的"查看报告"按钮
      const viewReportBtn = wrapper.find('[data-testid="view-report-btn"]')
      expect(viewReportBtn.exists()).toBe(true)
      
      await viewReportBtn.trigger('click')
      await nextTick()
      
      // Then: Vue Router的push方法必须被调用，跳转目标URL必须包含第一条记录的ID
      expect(mockPush).toHaveBeenCalledWith('/backtest/report/task-1')
    })
  })

  describe('场景三：边界与错误处理', () => {
    it('测试3.1 (核心配置未完成)：当策略没有配置因子时，开始回测按钮应该是禁用的', async () => {
      // Given: 用户新建一个策略，但没有配置任何因子
      const incompleteStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [], // 没有买入因子
        sell_factors: []  // 没有卖出因子
      })
      
      mockStrategyStore.currentSelectedStrategy = incompleteStrategy
      
      // When: 用户切换到"回测与方案"标签页
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: "开始回测"按钮应该是禁用的
      const startBacktestBtn = wrapper.find('[data-testid="start-backtest-btn"]')
      expect(startBacktestBtn.exists()).toBe(true)
      expect(startBacktestBtn.attributes('disabled')).toBeDefined()
    })

    it('测试3.2 (只有买入因子)：当策略只有买入因子没有卖出因子时，开始回测按钮应该是禁用的', async () => {
      // Given: 策略只有买入因子，没有卖出因子
      const incompleteStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }], // 有买入因子
        sell_factors: [] // 没有卖出因子
      })
      
      mockStrategyStore.currentSelectedStrategy = incompleteStrategy
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: "开始回测"按钮应该是禁用的
      const startBacktestBtn = wrapper.find('[data-testid="start-backtest-btn"]')
      expect(startBacktestBtn.exists()).toBe(true)
      expect(startBacktestBtn.attributes('disabled')).toBeDefined()
    })

    it('测试3.3 (只有卖出因子)：当策略只有卖出因子没有买入因子时，开始回测按钮应该是禁用的', async () => {
      // Given: 策略只有卖出因子，没有买入因子
      const incompleteStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [], // 没有买入因子
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }] // 有卖出因子
      })
      
      mockStrategyStore.currentSelectedStrategy = incompleteStrategy
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: "开始回测"按钮应该是禁用的
      const startBacktestBtn = wrapper.find('[data-testid="start-backtest-btn"]')
      expect(startBacktestBtn.exists()).toBe(true)
      expect(startBacktestBtn.attributes('disabled')).toBeDefined()
    })

    it('测试3.4 (裁判系统交互)：当启用裁判系统开关时，应该显示市场选择下拉框', async () => {
      // Given: 有效的策略
      const validStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }],
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }]
      })
      
      mockStrategyStore.currentSelectedStrategy = validStrategy
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // When: 启用裁判系统开关
      const umpireSwitch = wrapper.find('[data-testid="umpire-switch"]')
      expect(umpireSwitch.exists()).toBe(true)
      
      await umpireSwitch.setChecked(true)
      await nextTick()
      
      // Then: 应该显示裁判市场选择下拉框
      const umpireMarketSelect = wrapper.find('[data-testid="umpire-market-select"]')
      expect(umpireMarketSelect.exists()).toBe(true)
    })

    it('测试3.5 (表单验证)：当必填字段未填写时，开始回测按钮的行为', async () => {
      // Given: 有效的策略但表单未完整填写
      const validStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }],
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }]
      })
      
      mockStrategyStore.currentSelectedStrategy = validStrategy
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // When: 点击开始回测按钮（表单未填写完整）
      const startBacktestBtn = wrapper.find('[data-testid="start-backtest-btn"]')
      expect(startBacktestBtn.exists()).toBe(true)
      
      // 验证按钮可用（因为策略配置完整）
      expect(startBacktestBtn.attributes('disabled')).toBeUndefined()
      
      // 点击按钮
      await startBacktestBtn.trigger('click')
      await nextTick()
      
      // Then: 应该调用executeStrategy（即使表单部分为空，也应该能调用）
      expect(mockStrategyStore.executeStrategy).toHaveBeenCalled()
    })
  })

  describe('场景四：UI组件验证', () => {
    it('测试4.1 (组件存在性)：验证所有必需的UI组件都存在', async () => {
      // Given: 有效的策略
      const validStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }],
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }]
      })
      
      mockStrategyStore.currentSelectedStrategy = validStrategy
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // Then: 验证所有必需的UI组件都存在
      expect(wrapper.find('[data-testid="backtest-launcher"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="backtest-history"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="choice-symbols-select"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="start-date-picker"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="initial-capital-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="umpire-switch"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="start-backtest-btn"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="history-table"]').exists()).toBe(true)
    })

    it('测试4.2 (数据绑定)：验证表单控件与数据的双向绑定', async () => {
      // Given: 有效的策略
      const validStrategy = SimpleStrategyDataFactory.createValidStrategy({
        buy_factors: [{ class_name: 'AbuFactorBuyBreak', xd: 60 }],
        sell_factors: [{ class_name: 'AbuFactorAtrNStop', stop_loss_n: 0.5 }]
      })
      
      mockStrategyStore.currentSelectedStrategy = validStrategy
      
      wrapper = mount(MockStrategyWorkshop, {
        global: {
          plugins: [pinia]
        }
      })
      
      await nextTick()
      
      // When: 修改表单控件的值
      const choiceSymbolsSelect = wrapper.find('[data-testid="choice-symbols-select"]')
      const initialCapitalInput = wrapper.find('[data-testid="initial-capital-input"]')
      
      await choiceSymbolsSelect.setValue('hs300')
      await initialCapitalInput.setValue('2000000')
      
      // Then: 验证数据已正确绑定
      expect(choiceSymbolsSelect.element.value).toBe('hs300')
      expect(initialCapitalInput.element.value).toBe('2000000')
    })
  })
})