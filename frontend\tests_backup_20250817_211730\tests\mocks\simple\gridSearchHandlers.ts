// 网格搜索API Mock处理器 - 严格按照API契约
// 路径、方法、数据结构完全按照backend/app/api/endpoints/grid_search.py定义

import { http, HttpResponse } from 'msw';

export const gridSearchHandlers = [
  // POST /grid_search/run - 启动网格搜索任务
  http.post('/api/grid-search/run', async ({ request }) => {
    const data = await request.json() as any;
    
    if (!data.strategy_template) {
      return HttpResponse.json({
        success: false,
        message: "策略模板不能为空",
        error_code: "MISSING_STRATEGY_TEMPLATE"
      }, { status: 400 });
    }
    
    if (!data.param_grid || Object.keys(data.param_grid).length === 0) {
      return HttpResponse.json({
        success: false,
        message: "参数网格不能为空",
        error_code: "MISSING_PARAM_GRID"
      }, { status: 400 });
    }
    
    if (!data.symbols || !Array.isArray(data.symbols) || data.symbols.length === 0) {
      return HttpResponse.json({
        success: false,
        message: "股票代码列表不能为空",
        error_code: "MISSING_SYMBOLS"
      }, { status: 400 });
    }
    
    if (!data.start_date || !data.end_date) {
      return HttpResponse.json({
        success: false,
        message: "开始日期和结束日期不能为空",
        error_code: "MISSING_DATE_RANGE"
      }, { status: 400 });
    }
    
    if (!data.initial_capital || data.initial_capital <= 0) {
      return HttpResponse.json({
        success: false,
        message: "初始资金必须大于0",
        error_code: "INVALID_CAPITAL"
      }, { status: 400 });
    }
    
    // 生成任务ID
    const taskId = 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    return HttpResponse.json({
      success: true,
      message: "网格搜索任务启动成功",
      data: {
        task_id: taskId,
        status: "running",
        created_at: new Date().toISOString(),
        estimated_duration: 300, // 预估5分钟
        total_combinations: Object.values(data.param_grid).reduce((acc: number, values: any) => acc * values.length, 1)
      }
    }, { status: 202 });
  }),

  // GET /grid_search/status/{task_id} - 查询任务状态
  http.get('/api/grid-search/status/:task_id', ({ params }) => {
    const { task_id } = params;
    
    if (task_id === 'not-found') {
      return HttpResponse.json({
        success: false,
        message: "任务不存在",
        error_code: "TASK_NOT_FOUND"
      }, { status: 404 });
    }
    
    // 模拟不同的任务状态
    if (task_id.includes('running')) {
      return HttpResponse.json({
        success: true,
        message: "获取任务状态成功",
        data: {
          task_id: task_id,
          status: "running",
          progress: 0.35,
          completed_combinations: 35,
          total_combinations: 100,
          current_combination: {
            params: {
              "buy_factor.xd": 5,
              "sell_factor.xd": 20
            },
            symbol: "000001"
          },
          started_at: "2024-01-01T10:00:00Z",
          estimated_completion: "2024-01-01T10:05:00Z"
        }
      });
    }
    
    if (task_id.includes('completed')) {
      return HttpResponse.json({
        success: true,
        message: "获取任务状态成功",
        data: {
          task_id: task_id,
          status: "completed",
          progress: 1.0,
          completed_combinations: 100,
          total_combinations: 100,
          started_at: "2024-01-01T10:00:00Z",
          completed_at: "2024-01-01T10:04:30Z",
          results: {
            best_combination: {
              params: {
                "buy_factor.xd": 8,
                "sell_factor.xd": 25
              },
              metrics: {
                total_return: 0.25,
                sharpe_ratio: 1.8,
                max_drawdown: -0.06,
                win_rate: 0.72
              },
              symbol: "000001"
            },
            top_combinations: [
              {
                params: {
                  "buy_factor.xd": 8,
                  "sell_factor.xd": 25
                },
                metrics: {
                  total_return: 0.25,
                  sharpe_ratio: 1.8,
                  max_drawdown: -0.06,
                  win_rate: 0.72
                },
                symbol: "000001",
                rank: 1
              },
              {
                params: {
                  "buy_factor.xd": 10,
                  "sell_factor.xd": 30
                },
                metrics: {
                  total_return: 0.22,
                  sharpe_ratio: 1.65,
                  max_drawdown: -0.08,
                  win_rate: 0.68
                },
                symbol: "000002",
                rank: 2
              }
            ],
            summary: {
              total_tested: 100,
              profitable_combinations: 65,
              avg_return: 0.12,
              avg_sharpe: 1.15,
              best_return: 0.25,
              worst_return: -0.15
            }
          }
        }
      });
    }
    
    if (task_id.includes('failed')) {
      return HttpResponse.json({
        success: true,
        message: "获取任务状态成功",
        data: {
          task_id: task_id,
          status: "failed",
          progress: 0.15,
          completed_combinations: 15,
          total_combinations: 100,
          started_at: "2024-01-01T10:00:00Z",
          failed_at: "2024-01-01T10:01:30Z",
          error: {
            message: "数据获取失败",
            code: "DATA_FETCH_ERROR",
            details: "无法获取股票000003的历史数据"
          }
        }
      });
    }
    
    // 默认返回pending状态
    return HttpResponse.json({
      success: true,
      message: "获取任务状态成功",
      data: {
        task_id: task_id,
        status: "pending",
        progress: 0,
        completed_combinations: 0,
        total_combinations: 100,
        created_at: "2024-01-01T10:00:00Z",
        estimated_start: "2024-01-01T10:00:30Z"
      }
    });
  })
];