"""代码质量管理器

整合所有代码质量工具，提供统一的质量检查和改进建议。
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# 导入质量工具
from .code_quality import check_code_quality, CodeQualityChecker
from .test_coverage import analyze_test_coverage, TestCoverageAnalyzer
from .doc_generator import generate_documentation, DocumentationGenerator
from .refactoring_tools import analyze_refactoring_opportunities, RefactoringAnalyzer
from .performance_monitor import PerformanceMonitor, performance_context
from .config_manager import ConfigManager

@dataclass
class QualityMetrics:
    """质量指标"""
    code_quality_score: float
    test_coverage_percentage: float
    documentation_coverage: float
    refactoring_opportunities: int
    high_priority_issues: int
    technical_debt_hours: float
    maintainability_index: float
    overall_score: float

@dataclass
class QualityReport:
    """质量报告"""
    timestamp: str
    project_path: str
    metrics: QualityMetrics
    recommendations: List[str]
    action_items: List[Dict[str, Any]]
    trends: Optional[Dict[str, List[float]]] = None

class QualityManager:
    """代码质量管理器"""
    
    def __init__(self, project_root: str, config_path: Optional[str] = None):
        self.project_root = Path(project_root)
        self.source_dir = self.project_root / "backend" / "app"
        self.test_dir = self.project_root / "backend" / "tests"
        self.reports_dir = self.project_root / "quality_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # 加载配置
        self.config = ConfigManager(config_path) if config_path else self._get_default_config()
        
        # 历史数据
        self.history_file = self.reports_dir / "quality_history.json"
        self.history = self._load_history()
        
        # 性能监控
        self.performance_monitor = PerformanceMonitor()
    
    def run_comprehensive_analysis(self) -> QualityReport:
        """运行综合质量分析"""
        print("🔍 Starting comprehensive code quality analysis...")
        
        with performance_context("comprehensive_analysis"):
            # 1. 代码质量检查
            print("📊 Analyzing code quality...")
            code_quality_result = self._analyze_code_quality()
            
            # 2. 测试覆盖率分析
            print("🧪 Analyzing test coverage...")
            coverage_result = self._analyze_test_coverage()
            
            # 3. 文档覆盖率检查
            print("📚 Checking documentation coverage...")
            doc_result = self._analyze_documentation()
            
            # 4. 重构机会分析
            print("🔧 Identifying refactoring opportunities...")
            refactoring_result = self._analyze_refactoring()
            
            # 5. 计算综合指标
            print("📈 Calculating quality metrics...")
            metrics = self._calculate_quality_metrics(
                code_quality_result,
                coverage_result,
                doc_result,
                refactoring_result
            )
            
            # 6. 生成建议和行动项
            recommendations = self._generate_recommendations(metrics, {
                'code_quality': code_quality_result,
                'coverage': coverage_result,
                'documentation': doc_result,
                'refactoring': refactoring_result
            })
            
            action_items = self._generate_action_items({
                'code_quality': code_quality_result,
                'coverage': coverage_result,
                'refactoring': refactoring_result
            })
            
            # 7. 创建报告
            report = QualityReport(
                timestamp=datetime.now().isoformat(),
                project_path=str(self.project_root),
                metrics=metrics,
                recommendations=recommendations,
                action_items=action_items,
                trends=self._calculate_trends(metrics)
            )
            
            # 8. 保存历史数据
            self._save_to_history(report)
            
            print("✅ Analysis complete!")
            return report
    
    def _analyze_code_quality(self) -> Dict[str, Any]:
        """分析代码质量"""
        try:
            return check_code_quality(
                str(self.source_dir),
                exclude_patterns=['__pycache__', '.git', 'venv', '.venv']
            )
        except Exception as e:
            print(f"⚠️ Code quality analysis failed: {e}")
            return {'summary': {'total_issues': 0}, 'issues': []}
    
    def _analyze_test_coverage(self) -> Dict[str, Any]:
        """分析测试覆盖率"""
        try:
            if self.test_dir.exists():
                return analyze_test_coverage(str(self.source_dir), str(self.test_dir))
            else:
                print("⚠️ Test directory not found")
                return {'summary': {'overall_coverage': 0}}
        except Exception as e:
            print(f"⚠️ Test coverage analysis failed: {e}")
            return {'summary': {'overall_coverage': 0}}
    
    def _analyze_documentation(self) -> Dict[str, Any]:
        """分析文档覆盖率"""
        try:
            doc_generator = DocumentationGenerator(str(self.source_dir))
            doc_generator._analyze_source_code()
            
            total_items = 0
            documented_items = 0
            
            for module_doc in doc_generator.modules.values():
                # 统计函数文档
                for func in module_doc.functions:
                    total_items += 1
                    if func.docstring:
                        documented_items += 1
                
                # 统计类文档
                for cls in module_doc.classes:
                    total_items += 1
                    if cls.docstring:
                        documented_items += 1
                    
                    # 统计方法文档
                    for method in cls.methods:
                        total_items += 1
                        if method.docstring:
                            documented_items += 1
            
            coverage_percentage = (documented_items / max(total_items, 1)) * 100
            
            return {
                'total_items': total_items,
                'documented_items': documented_items,
                'coverage_percentage': coverage_percentage,
                'modules': len(doc_generator.modules)
            }
        except Exception as e:
            print(f"⚠️ Documentation analysis failed: {e}")
            return {'coverage_percentage': 0, 'total_items': 0, 'documented_items': 0}
    
    def _analyze_refactoring(self) -> Dict[str, Any]:
        """分析重构机会"""
        try:
            return analyze_refactoring_opportunities(str(self.source_dir))
        except Exception as e:
            print(f"⚠️ Refactoring analysis failed: {e}")
            return {'summary': {'total_opportunities': 0, 'high_priority': 0}}
    
    def _calculate_quality_metrics(self, code_quality: Dict, coverage: Dict, 
                                 documentation: Dict, refactoring: Dict) -> QualityMetrics:
        """计算质量指标"""
        # 代码质量分数 (基于问题数量)
        total_issues = code_quality.get('summary', {}).get('total_issues', 0)
        files_checked = code_quality.get('summary', {}).get('files_checked', 1)
        code_quality_score = max(0, 100 - (total_issues / max(files_checked, 1)) * 10)
        
        # 测试覆盖率
        test_coverage = coverage.get('summary', {}).get('overall_coverage', 0)
        
        # 文档覆盖率
        doc_coverage = documentation.get('coverage_percentage', 0)
        
        # 重构机会
        refactoring_opps = refactoring.get('summary', {}).get('total_opportunities', 0)
        high_priority_issues = refactoring.get('summary', {}).get('high_priority', 0)
        
        # 技术债务（小时）
        tech_debt_hours = refactoring.get('summary', {}).get('estimated_effort_hours', 0)
        
        # 可维护性指数 (综合指标)
        maintainability_index = (
            code_quality_score * 0.3 +
            test_coverage * 0.3 +
            doc_coverage * 0.2 +
            max(0, 100 - refactoring_opps * 2) * 0.2
        )
        
        # 总体分数
        overall_score = (
            code_quality_score * 0.25 +
            test_coverage * 0.25 +
            doc_coverage * 0.15 +
            maintainability_index * 0.35
        )
        
        return QualityMetrics(
            code_quality_score=round(code_quality_score, 2),
            test_coverage_percentage=round(test_coverage, 2),
            documentation_coverage=round(doc_coverage, 2),
            refactoring_opportunities=refactoring_opps,
            high_priority_issues=high_priority_issues,
            technical_debt_hours=round(tech_debt_hours, 2),
            maintainability_index=round(maintainability_index, 2),
            overall_score=round(overall_score, 2)
        )
    
    def _generate_recommendations(self, metrics: QualityMetrics, 
                                analysis_results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于整体分数的建议
        if metrics.overall_score < 60:
            recommendations.append("🚨 Overall code quality is below acceptable threshold. Immediate action required.")
        elif metrics.overall_score < 80:
            recommendations.append("⚠️ Code quality needs improvement. Focus on high-priority issues.")
        else:
            recommendations.append("✅ Good code quality. Continue maintaining standards.")
        
        # 测试覆盖率建议
        if metrics.test_coverage_percentage < 60:
            recommendations.append("📈 Increase test coverage to at least 80%. Add unit tests for uncovered functions.")
        elif metrics.test_coverage_percentage < 80:
            recommendations.append("🧪 Good test coverage. Add integration tests and edge case testing.")
        
        # 文档建议
        if metrics.documentation_coverage < 50:
            recommendations.append("📚 Add docstrings to functions and classes. Documentation is critical for maintainability.")
        elif metrics.documentation_coverage < 80:
            recommendations.append("📝 Improve documentation coverage. Add examples and usage instructions.")
        
        # 重构建议
        if metrics.high_priority_issues > 5:
            recommendations.append("🔧 Address high-priority refactoring opportunities to reduce technical debt.")
        
        if metrics.technical_debt_hours > 40:
            recommendations.append("⏰ Significant technical debt detected. Plan refactoring sprints.")
        
        # 具体的代码质量建议
        code_issues = analysis_results.get('code_quality', {}).get('summary', {}).get('issues_by_severity', {})
        if code_issues.get('error', 0) > 0:
            recommendations.append("🐛 Fix critical code quality errors immediately.")
        
        return recommendations
    
    def _generate_action_items(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成行动项"""
        action_items = []
        
        # 高优先级代码质量问题
        code_quality = analysis_results.get('code_quality', {})
        high_priority_code_issues = [
            issue for issue in code_quality.get('issues', [])
            if issue.severity == 'error'
        ]
        
        for issue in high_priority_code_issues[:5]:  # 限制前5个
            action_items.append({
                'type': 'code_quality',
                'priority': 'high',
                'title': f"Fix {issue.issue_type} in {Path(issue.file_path).name}",
                'description': issue.message,
                'file': issue.file_path,
                'line': issue.line_number,
                'estimated_effort': '30 minutes'
            })
        
        # 高优先级重构机会
        refactoring = analysis_results.get('refactoring', {})
        high_priority_refactoring = [
            opp for opp in refactoring.get('opportunities', [])
            if opp.get('severity') == 'high'
        ]
        
        for opp in high_priority_refactoring[:3]:  # 限制前3个
            action_items.append({
                'type': 'refactoring',
                'priority': 'high',
                'title': f"Refactor: {opp.get('description', '')}",
                'description': opp.get('suggested_code', ''),
                'file': opp.get('file_path'),
                'line': opp.get('line_number'),
                'estimated_effort': self._map_effort_level(opp.get('effort_level', 'medium'))
            })
        
        # 测试覆盖率改进
        coverage = analysis_results.get('coverage', {})
        if coverage.get('summary', {}).get('overall_coverage', 0) < 80:
            action_items.append({
                'type': 'testing',
                'priority': 'medium',
                'title': 'Improve test coverage',
                'description': 'Add unit tests for uncovered functions and classes',
                'estimated_effort': '4-8 hours'
            })
        
        return action_items
    
    def _map_effort_level(self, effort_level: str) -> str:
        """映射工作量级别到时间估算"""
        mapping = {
            'low': '1-2 hours',
            'medium': '4-6 hours',
            'high': '1-2 days'
        }
        return mapping.get(effort_level, '2-4 hours')
    
    def _calculate_trends(self, current_metrics: QualityMetrics) -> Dict[str, List[float]]:
        """计算趋势数据"""
        if not self.history:
            return {}
        
        trends = {
            'overall_score': [],
            'test_coverage': [],
            'code_quality_score': [],
            'documentation_coverage': []
        }
        
        # 获取最近10次的数据
        recent_history = self.history[-10:]
        
        for record in recent_history:
            metrics = record.get('metrics', {})
            trends['overall_score'].append(metrics.get('overall_score', 0))
            trends['test_coverage'].append(metrics.get('test_coverage_percentage', 0))
            trends['code_quality_score'].append(metrics.get('code_quality_score', 0))
            trends['documentation_coverage'].append(metrics.get('documentation_coverage', 0))
        
        # 添加当前数据
        trends['overall_score'].append(current_metrics.overall_score)
        trends['test_coverage'].append(current_metrics.test_coverage_percentage)
        trends['code_quality_score'].append(current_metrics.code_quality_score)
        trends['documentation_coverage'].append(current_metrics.documentation_coverage)
        
        return trends
    
    def _load_history(self) -> List[Dict[str, Any]]:
        """加载历史数据"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return []
        return []
    
    def _save_to_history(self, report: QualityReport):
        """保存到历史数据"""
        try:
            self.history.append({
                'timestamp': report.timestamp,
                'metrics': asdict(report.metrics)
            })
            
            # 只保留最近50次记录
            self.history = self.history[-50:]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Failed to save history: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'quality_thresholds': {
                'overall_score': 80,
                'test_coverage': 80,
                'documentation_coverage': 70,
                'code_quality_score': 85
            },
            'exclude_patterns': ['__pycache__', '.git', 'venv', '.venv', 'node_modules'],
            'max_issues_per_file': 10,
            'max_complexity': 10
        }
    
    def generate_html_report(self, report: QualityReport, output_file: Optional[str] = None) -> str:
        """生成HTML格式的质量报告"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = str(self.reports_dir / f"quality_report_{timestamp}.html")
        
        html_content = self._create_html_report(report)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_file
    
    def _create_html_report(self, report: QualityReport) -> str:
        """创建HTML报告内容"""
        metrics = report.metrics
        
        # 确定整体质量等级
        if metrics.overall_score >= 90:
            quality_class = 'excellent'
            quality_label = 'Excellent'
        elif metrics.overall_score >= 80:
            quality_class = 'good'
            quality_label = 'Good'
        elif metrics.overall_score >= 60:
            quality_class = 'fair'
            quality_label = 'Fair'
        else:
            quality_class = 'poor'
            quality_label = 'Poor'
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Code Quality Report</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }}
                .header h1 {{ margin: 0; font-size: 2.5em; }}
                .header .subtitle {{ opacity: 0.9; margin-top: 10px; }}
                .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; padding: 30px; }}
                .metric-card {{ background: #f8f9fa; border-radius: 8px; padding: 20px; border-left: 4px solid #007bff; }}
                .metric-value {{ font-size: 2em; font-weight: bold; color: #333; }}
                .metric-label {{ color: #666; margin-top: 5px; }}
                .score-excellent {{ color: #28a745; }}
                .score-good {{ color: #17a2b8; }}
                .score-fair {{ color: #ffc107; }}
                .score-poor {{ color: #dc3545; }}
                .recommendations {{ padding: 30px; }}
                .recommendations h2 {{ color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }}
                .recommendation {{ background: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; margin: 10px 0; border-radius: 4px; }}
                .action-items {{ padding: 30px; background: #f8f9fa; }}
                .action-item {{ background: white; border-radius: 6px; padding: 15px; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .priority-high {{ border-left: 4px solid #dc3545; }}
                .priority-medium {{ border-left: 4px solid #ffc107; }}
                .priority-low {{ border-left: 4px solid #28a745; }}
                .footer {{ text-align: center; padding: 20px; color: #666; border-top: 1px solid #eee; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 Code Quality Report</h1>
                    <div class="subtitle">Generated on {datetime.fromisoformat(report.timestamp).strftime('%Y-%m-%d %H:%M:%S')}</div>
                    <div class="subtitle">Overall Quality: <strong class="score-{quality_class}">{quality_label} ({metrics.overall_score:.1f}/100)</strong></div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value score-{self._get_score_class(metrics.overall_score)}">{metrics.overall_score:.1f}</div>
                        <div class="metric-label">Overall Score</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value score-{self._get_score_class(metrics.test_coverage_percentage)}">{metrics.test_coverage_percentage:.1f}%</div>
                        <div class="metric-label">Test Coverage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value score-{self._get_score_class(metrics.code_quality_score)}">{metrics.code_quality_score:.1f}</div>
                        <div class="metric-label">Code Quality Score</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value score-{self._get_score_class(metrics.documentation_coverage)}">{metrics.documentation_coverage:.1f}%</div>
                        <div class="metric-label">Documentation Coverage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{metrics.refactoring_opportunities}</div>
                        <div class="metric-label">Refactoring Opportunities</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{metrics.technical_debt_hours:.1f}h</div>
                        <div class="metric-label">Technical Debt</div>
                    </div>
                </div>
                
                <div class="recommendations">
                    <h2>🎯 Recommendations</h2>
                    {''.join([f'<div class="recommendation">{rec}</div>' for rec in report.recommendations])}
                </div>
                
                <div class="action-items">
                    <h2>📋 Action Items</h2>
                    {''.join([self._format_action_item(item) for item in report.action_items])}
                </div>
                
                <div class="footer">
                    <p>Report generated by Code Quality Manager</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def _get_score_class(self, score: float) -> str:
        """获取分数对应的CSS类"""
        if score >= 90:
            return 'excellent'
        elif score >= 80:
            return 'good'
        elif score >= 60:
            return 'fair'
        else:
            return 'poor'
    
    def _format_action_item(self, item: Dict[str, Any]) -> str:
        """格式化行动项为HTML"""
        priority_class = f"priority-{item.get('priority', 'medium')}"
        return f"""
        <div class="action-item {priority_class}">
            <h4>{item.get('title', 'Action Item')}</h4>
            <p>{item.get('description', '')}</p>
            <small><strong>Effort:</strong> {item.get('estimated_effort', 'Unknown')} | 
                   <strong>Priority:</strong> {item.get('priority', 'Medium').title()}</small>
        </div>
        """
    
    def print_summary(self, report: QualityReport):
        """打印质量报告摘要"""
        metrics = report.metrics
        
        print("\n" + "="*60)
        print("📊 CODE QUALITY SUMMARY")
        print("="*60)
        print(f"📈 Overall Score: {metrics.overall_score:.1f}/100")
        print(f"🧪 Test Coverage: {metrics.test_coverage_percentage:.1f}%")
        print(f"📚 Documentation: {metrics.documentation_coverage:.1f}%")
        print(f"🔧 Refactoring Opportunities: {metrics.refactoring_opportunities}")
        print(f"⚠️ High Priority Issues: {metrics.high_priority_issues}")
        print(f"⏰ Technical Debt: {metrics.technical_debt_hours:.1f} hours")
        
        print("\n🎯 TOP RECOMMENDATIONS:")
        for i, rec in enumerate(report.recommendations[:3], 1):
            print(f"{i}. {rec}")
        
        print("\n📋 IMMEDIATE ACTION ITEMS:")
        high_priority_items = [item for item in report.action_items if item.get('priority') == 'high']
        for i, item in enumerate(high_priority_items[:3], 1):
            print(f"{i}. {item.get('title')} ({item.get('estimated_effort')})")
        
        print("\n" + "="*60)

# 便捷函数
def run_quality_analysis(project_root: str, generate_html: bool = True) -> QualityReport:
    """运行质量分析的便捷函数"""
    manager = QualityManager(project_root)
    report = manager.run_comprehensive_analysis()
    
    # 打印摘要
    manager.print_summary(report)
    
    # 生成HTML报告
    if generate_html:
        html_file = manager.generate_html_report(report)
        print(f"\n📄 HTML report generated: {html_file}")
    
    return report