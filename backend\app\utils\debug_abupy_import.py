#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试abupy导入问题
"""

import sys
sys.path.append('.')

print('=== 调试abupy导入问题 ===')

# 尝试逐步导入abupy
try:
    print('1. 尝试导入abupy...')
    import abupy
    print('   ✅ abupy导入成功')
    
    print('2. 尝试导入买入因子基类...')
    from abupy.FactorBuyBu.ABuFactorBuyBase import AbuFactorBuyBase
    print('   ✅ AbuFactorBuyBase导入成功')
    
    print('3. 尝试导入卖出因子基类...')
    from abupy.FactorSellBu.ABuFactorSellBase import AbuFactorSellBase
    print('   ✅ AbuFactorSellBase导入成功')
    
    print('4. 尝试导入买入因子模块...')
    import abupy.FactorBuyBu as buy_module
    print('   ✅ 买入因子模块导入成功')
    
    print('5. 尝试导入卖出因子模块...')
    import abupy.FactorSellBu as sell_module
    print('   ✅ 卖出因子模块导入成功')
    
    print('6. 尝试通配符导入买入因子...')
    from abupy.FactorBuyBu import *
    print('   ✅ 买入因子通配符导入成功')
    
    print('7. 尝试通配符导入卖出因子...')
    from abupy.FactorSellBu import *
    print('   ✅ 卖出因子通配符导入成功')
    
    print('\n=== abupy导入完全成功 ===')
    
    # 检查因子数量
    buy_factors = []
    for attr_name in dir(buy_module):
        attr = getattr(buy_module, attr_name)
        if (isinstance(attr, type) and 
            issubclass(attr, AbuFactorBuyBase) and 
            attr is not AbuFactorBuyBase and 
            'Base' not in attr_name):
            buy_factors.append(attr)
    
    sell_factors = []
    for attr_name in dir(sell_module):
        attr = getattr(sell_module, attr_name)
        if (isinstance(attr, type) and 
            issubclass(attr, AbuFactorSellBase) and 
            attr is not AbuFactorSellBase and 
            'Base' not in attr_name):
            sell_factors.append(attr)
    
    print(f'\n找到买入因子: {len(buy_factors)} 个')
    print(f'找到卖出因子: {len(sell_factors)} 个')
    print(f'总因子数量: {len(buy_factors) + len(sell_factors)} 个')
    
except ImportError as e:
    print(f'   ❌ 导入失败: {e}')
    print(f'   错误类型: {type(e).__name__}')
    
    # 检查abupy是否安装
    try:
        import pkg_resources
        abupy_dist = pkg_resources.get_distribution('abupy')
        print(f'   abupy版本: {abupy_dist.version}')
        print(f'   安装位置: {abupy_dist.location}')
    except:
        print('   abupy可能未安装')
        
except Exception as e:
    print(f'   ❌ 其他错误: {e}')
    print(f'   错误类型: {type(e).__name__}')
    import traceback
    traceback.print_exc()

print('\n=== 检查Python路径 ===')
for i, path in enumerate(sys.path):
    print(f'{i+1:2d}. {path}')