# UmpBu 风险控制功能集成工作日志

**日期**: 2025-06-28
**负责人**: 实现者AI

## 1. 任务背景

根据“勘探者AI”关于UmpBu（裁判系统）的技术实现指南，本次任务旨在将UmpBu风险控制功能集成到`abu_modern`项目的后端系统中。目标是让用户能够在创建和执行量化策略时，配置并应用一系列风险控制规则（即“裁判”），以提高策略的稳定性和风控能力。

## 2. 实现方案

本次集成工作遵循“勘探者AI”的建议，涉及数据模型、服务层、适配器层和API层的全面更新。核心实现思路如下：

1.  **扩展数据模型**：在Pydantic模型和SQLModel中增加`umpire_rules`字段，用于存储用户配置的裁判规则。
2.  **更新服务层**：修改策略的CRUD服务，使其能够正确处理和持久化`umpire_rules`字段。
3.  **创建裁判适配器**：新建`umpire_adapter.py`，负责将前端传入的JSON格式裁判规则，转换为`abupy`库可识别的裁判对象实例。
4.  **集成到策略执行器**：在`strategy_executor.py`中，于回测执行前根据策略配置动态加载和启用裁判系统，并在回测结束后进行清理。
5.  **提供配置选项API**：创建新的API端点`/api/options/umpires`，向前端提供所有支持的裁判规则及其参数信息，便于动态生成配置界面。

## 3. 主要变更内容

### 3.1. 数据模型更新 (Pydantic & SQLModel)

-   **文件**: `backend/app/schemas/strategy.py`
    -   **变更**: 在`StrategyCreate`和`StrategyUpdate`模型中，将`umpire_rules`字段的类型从`Optional[List[str]]`修正为`Optional[List[Dict[str, Any]]]`，以支持复杂的参数配置。
-   **文件**: `backend/app/models/strategy_model.py`
    -   **变更**: 在`StrategyModel`中添加了`umpire_rules: Optional[list] = Field(default=None, sa_column=Column(JSON))`字段。
    -   **变更**: 更新了`to_schema_strategy`和`from_schema_strategy`方法，以确保模型与Schema之间转换时能正确处理`umpire_rules`字段。

### 3.2. 服务层更新 (Service Layer)

-   **文件**: `backend/app/services/strategy_service.py`
    -   **变更**: 在`create_strategy`方法中，增加了对`umpire_rules`字段的传递。
    -   **变更**: 在`update_strategy`方法中，为`umpire_rules`以及其他复杂字段（如`buy_factors`）添加了专门的更新逻辑，确保部分更新时数据不会丢失。

### 3.3. 裁判适配器实现 (Umpire Adapter)

-   **文件**: `backend/app/abupy_adapter/umpire_adapter.py` (新建)
    -   **实现**: 定义了`UMPIRE_CLASS_MAP`，用于映射前端传入的`class_name`到`abupy`中实际的裁判类。
    -   **实现**: 实现了`create_umpire_managers`核心函数，该函数负责解析`umpire_rules`，实例化主裁判和边裁判，并进行参数校验和错误处理。

### 3.4. 策略执行器集成 (Strategy Executor)

-   **文件**: `backend/app/abupy_adapter/strategy_executor.py`
    -   **变更**: 在`execute_strategy`方法中，增加了完整的裁判系统生命周期管理：
        1.  **导入**: 导入`create_umpire_managers`和`abupy`相关模块。
        2.  **配置**: 在回测开始前，检查策略是否包含`umpire_rules`。如果包含，则调用`create_umpire_managers`创建裁判实例，并设置到`ABuUmpManager`中。
        3.  **启用**: 开启`abupy`的全局裁判开关`g_enable_ump_main_edge_choice`。
        4.  **清理**: 在`finally`块中，确保回测结束后关闭全局开关并清理`ABuUmpManager`，避免对后续操作产生影响。

### 3.5. 新增API端点 (API Endpoint)

-   **文件**: `backend/app/api/endpoints/options.py`
    -   **变更**: 新增了`GET /api/options/umpires`端点。
    -   **实现**: 该端点返回一个硬编码的JSON数组`SUPPORTED_UMPIRES`，详细定义了所有支持的裁判规则，包括其名称、描述、`class_name`、类型（主裁/边裁）以及所需参数，为前端动态UI生成提供了数据支持。

## 4. 遇到的问题与修复

在本次UmpBu集成过程中，我们遇到并解决了一系列问题，这些问题主要涉及数据模型的不一致、数据库的同步以及与`abupy`核心库的兼容性。下面将详细记录这些问题及其解决方案。

### 4.1. 数据库列缺失 (`sqlalchemy.exc.OperationalError`)

- **问题描述**: 在`StrategyModel`中添加`umpire_rules`字段后，创建策略时API返回500错误，日志显示`sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) table strategies has no column named umpire_rules`。
- **根本原因**: 项目当前使用的数据库初始化方式`SQLModel.metadata.create_all(engine)`只会在数据库文件不存在时创建新的表，但不会在模型变更后自动更新现有表的结构（即不会执行`ALTER TABLE`操作）。这导致了数据库的Schema与代码中的`StrategyModel`定义不一致。
- **修复措施**: 最直接的解决方案是在开发环境中删除旧的数据库文件（`backend/data/abu_modern.db`），然后重启应用。FastAPI的`lifespan`事件会重新调用`create_db_and_tables()`，从而生成一个包含新`umpire_rules`列的全新数据库。此方法简单有效，适用于开发阶段。对于生产环境，未来应考虑引入数据库迁移工具（如Alembic）来管理Schema的变更。

### 4.2. Pydantic模型属性错误 (`AttributeError`)

- **问题描述**: 在解决了数据库列缺失问题后，创建策略时再次遇到500错误，日志显示`AttributeError: 'Strategy' object has no attribute 'umpire_rules'`。
- **根本原因**: 错误发生在`strategy_model.py`的`from_schema_strategy`方法中。该方法尝试从一个`Strategy` Pydantic模型实例中读取`umpire_rules`属性，但`backend/app/schemas/strategy.py`中的`Strategy`模型定义遗漏了该字段，导致模型间数据转换失败。
- **修复措施**: 在`backend/app/schemas/strategy.py`的`Strategy`模型中，添加`umpire_rules: Optional[List[Dict[str, Any]]] = None`字段，使其与`StrategyModel`和`StrategyCreate`等模型保持一致，解决了属性缺失的问题。

### 4.3. Pydantic别名转换错误 (`pydantic.ValidationError`)

- **问题描述**: 在修复了前两个问题后，创建策略时仍然失败，日志显示`pydantic.ValidationError: 1 validation error for BuyFactor class_name Field required`。
- **根本原因**: `BaseFactor`及其子类（如`BuyFactor`）中，用于存储`abupy`类名的字段被定义为`factor_class: str = Field(..., alias='class_name')`。当`from_schema_strategy`方法调用`factor.model_dump()`将Pydantic模型转换为字典以存入数据库时，默认生成的字典键是字段名`factor_class`，而不是其别名`class_name`。然而，当`to_schema_strategy`方法从数据库（字典）转换回Pydantic模型时，它期望的是`class_name`键，从而导致验证失败。
- **修复措施**: 在`strategy_model.py`的`from_schema_strategy`方法中，将`model_dump()`调用修改为`model_dump(by_alias=True)`。这强制Pydantic在生成字典时使用字段的别名，确保了存入数据库的数据结构与API及前端的预期保持一致，彻底解决了数据在往返转换过程中的不一致问题。

```bash
curl -X POST "http://127.0.0.1:8000/api/strategies/execute/{strategy_id}" -H "Content-Type: application/json" -d '{
    "capital": 1000000,
    "benchmark_symbol": "sh000300",
    "choice_symbols": ["600519", "000858", "601318", "002594", "300750"],
    "start_date": "2018-01-01",
    "end_date": "2021-01-01"
}'
```