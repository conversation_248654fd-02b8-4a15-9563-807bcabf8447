# -*- coding: utf-8 -*-
"""
Dashboard Pydantic Schemas
"""
from pydantic import BaseModel
from typing import List

class MarketPerformance(BaseModel):
    """
    市场表现数据
    """
    date: List[str]
    value: List[float]

class DashboardSummary(BaseModel):
    """
    仪表盘核心摘要数据
    """
    today_gain: float
    active_strategies: int
    total_turnover_wan: float
    signals_count: int
    market_performance: MarketPerformance