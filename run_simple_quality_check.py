#!/usr/bin/env python3
"""简化的代码质量检查脚本

运行基本的代码质量分析，避免复杂的工具导致的问题。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from backend.app.utils.code_quality import check_code_quality
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def main():
    """主函数"""
    try:
        print("🚀 Starting Simple Code Quality Analysis...")
        print(f"📁 Project Root: {project_root}")
        
        source_dir = project_root / "backend" / "app"
        print(f"📊 Analyzing code quality in: {source_dir}")
        
        # 运行代码质量检查
        result = check_code_quality(
            str(source_dir),
            exclude_patterns=['__pycache__', '.git', 'venv', '.venv']
        )
        
        # 打印结果
        summary = result.get('summary', {})
        print("\n" + "="*60)
        print("📊 CODE QUALITY SUMMARY")
        print("="*60)
        print(f"📁 Files Checked: {summary.get('files_checked', 0)}")
        print(f"⚠️ Total Issues: {summary.get('total_issues', 0)}")
        
        # 按严重程度分类
        issues_by_severity = summary.get('issues_by_severity', {})
        print(f"🔴 Errors: {issues_by_severity.get('error', 0)}")
        print(f"🟡 Warnings: {issues_by_severity.get('warning', 0)}")
        print(f"🔵 Info: {issues_by_severity.get('info', 0)}")
        
        # 按类型分类
        issues_by_type = summary.get('issues_by_type', {})
        print("\n📋 Issues by Type:")
        for issue_type, count in issues_by_type.items():
            if count > 0:
                print(f"  - {issue_type}: {count}")
        
        # 显示前10个问题
        issues = result.get('issues', [])
        if issues:
            print("\n🔍 Top Issues:")
            for i, issue in enumerate(issues[:10], 1):
                # 处理QualityIssue对象
                if hasattr(issue, 'file_path'):
                    file_name = Path(issue.file_path).name
                    severity = issue.severity
                    issue_type = issue.issue_type
                    line_number = issue.line_number
                    message = issue.message
                else:
                    # 处理字典格式
                    file_name = Path(issue.get('file_path', '')).name
                    severity = issue.get('severity', 'info')
                    issue_type = issue.get('issue_type', 'unknown')
                    line_number = issue.get('line_number', 0)
                    message = issue.get('message', '')
                
                print(f"{i:2d}. [{severity.upper()}] {issue_type} in {file_name}:{line_number}")
                print(f"    {message}")
                if i < len(issues[:10]):
                    print()
        
        # 计算质量分数
        total_issues = summary.get('total_issues', 0)
        files_checked = summary.get('files_checked', 1)
        quality_score = max(0, 100 - (total_issues / max(files_checked, 1)) * 10)
        
        print(f"\n🎯 Quality Score: {quality_score:.1f}/100")
        
        if quality_score >= 90:
            print("✅ Excellent code quality!")
        elif quality_score >= 80:
            print("👍 Good code quality.")
        elif quality_score >= 60:
            print("⚠️ Code quality needs improvement.")
        else:
            print("🚨 Poor code quality. Immediate action required.")
        
        print("\n🎉 Simple quality analysis completed successfully!")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())