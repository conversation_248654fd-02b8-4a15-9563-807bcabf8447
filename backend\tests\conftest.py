# backend/tests/conftest.py

# ！！！确保测试环境也应用兼容性补丁！！！
from backend.app.core.compatibility import apply_patches
apply_patches()

import pytest
from typing import Generator
from fastapi.testclient import TestClient
from sqlmodel import Session, SQLModel, create_engine

# --- 关键改动：先导入 app，再导入 get_session ---
# 这样做可以确保在尝试进行任何数据库操作之前，
# FastAPI 应用及其所有依赖（包括所有模型）都已被加载。
from backend.main import app
from backend.app.core.database import get_session

# --- 测试数据库配置 ---
TEST_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})

@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """
    [会话级 Fixture, 自动使用]
    在整个测试会话（所有测试开始前）执行一次：
    1. 创建所有在元数据中注册的表。
    
    在整个测试会话（所有测试结束后）执行一次：
    1. 删除所有表。
    """
    # 由于 app 已经被导入，所有继承自 SQLModel 的模型都应该已经被加载到内存中，
    # 并注册到了 SQLModel.metadata 中。
    # 现在调用 create_all 是安全的。
    SQLModel.metadata.create_all(engine)
    
    yield
    
    SQLModel.metadata.drop_all(engine)


@pytest.fixture(scope="function", name="session")
def session_fixture() -> Generator[Session, None, None]:
    """
    [函数级 Fixture]
    为每个测试函数提供一个独立的、干净的数据库会话。
    使用事务来隔离测试，并在测试结束后回滚所有操作。
    """
    connection = engine.connect()
    transaction = connection.begin()
    # 使用 SQLModel 的 Session，而不是 SQLAlchemy 的 Session
    session = Session(connection)

    yield session

    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function", name="client")
def client_fixture(session: Session) -> Generator[TestClient, None, None]:
    """
    [函数级 Fixture]
    为每个测试函数创建一个 FastAPI TestClient 实例，并覆盖 get_session 依赖。
    """
    def get_session_override() -> Session:
        return session

    app.dependency_overrides[get_session] = get_session_override

    print("\n--- Registered Routes: ---")
    for route in app.routes:
        print(f"Path: {route.path}, Name: {route.name}, Methods: {getattr(route, 'methods', 'N/A')}")
    print("--------------------------\n")

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()