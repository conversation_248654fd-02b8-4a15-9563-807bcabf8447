# -*- coding: utf-8 -*-
"""
模拟ipywidgets模块，替代abu框架中的ipywidgets依赖
"""
import logging

# 模拟FloatProgress小部件
class FloatProgress:
    def __init__(self, min=0, max=100, value=0, description='', **kwargs):
        self.min = min
        self.max = max
        self.value = value
        self.description = description
        logging.debug(f"Mock FloatProgress created: {description}")
    
    def __repr__(self):
        return f"FloatProgress(value={self.value}, min={self.min}, max={self.max})"

# 模拟Text小部件
class Text:
    def __init__(self, value='', description='', **kwargs):
        self.value = value
        self.description = description
        logging.debug(f"Mock Text created: {description}")
    
    def __repr__(self):
        return f"Text(value='{self.value}')"

# 模拟Box布局容器
class Box:
    def __init__(self, children=None, **kwargs):
        self.children = children or []
        logging.debug(f"Mock Box created with {len(self.children)} children")
    
    def __repr__(self):
        return f"Box(children={len(self.children)} items)"

# 模拟HBox水平布局
class HBox(Box):
    def __repr__(self):
        return f"HBox(children={len(self.children)} items)"

# 模拟VBox垂直布局
class VBox(Box):
    def __repr__(self):
        return f"VBox(children={len(self.children)} items)"

# 模拟Button小部件
class Button:
    def __init__(self, description='', button_style='', **kwargs):
        self.description = description
        self.button_style = button_style
        self.on_click_callbacks = []
        logging.debug(f"Mock Button created: {description}")
    
    def on_click(self, callback):
        self.on_click_callbacks.append(callback)
        logging.debug(f"Mock Button callback registered")

# 模拟Label小部件
class Label:
    def __init__(self, value='', **kwargs):
        self.value = value
        logging.debug(f"Mock Label created: {value}")
    
    def __repr__(self):
        return f"Label(value='{self.value}')"

# 导出模拟的布局函数
def Layout(**kwargs):
    logging.debug(f"Mock Layout created with {kwargs}")
    return type('Layout', (), kwargs)
