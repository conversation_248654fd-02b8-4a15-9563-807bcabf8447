<template>
  <div :class="['app-layout', { 'header-visible': isHeaderVisible }]" @mousemove="handleMouseMove">
    <!-- 顶部触发区域 -->
    <div class="header-trigger-zone" @mouseenter="showHeader"></div>
    
    <header :class="['app-header', { 'header-hidden': !isHeaderVisible }]" @mouseleave="hideHeader">
      <div class="header-left">
        <div :class="['logo-container', { 'logo-container-collapsed': isCollapsed }]">
          <img alt="logo" class="logo-img" src="/vite.svg" />
          <div :class="['logo-text', { 'logo-text-collapsed': isCollapsed }]">
            <h1 class="logo-title-en">EasyTrading</h1>
            <p class="logo-title-zh">量化投研平台</p>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-info">
            <el-avatar :size="32" class="user-avatar">
              <i-ep-user />
            </el-avatar>
            <span class="welcome-text">ccxx，欢迎回来</span>
            <el-icon class="dropdown-icon">
              <i-ep-arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人中心</el-dropdown-item>
              <el-dropdown-item>账户设置</el-dropdown-item>
              <el-dropdown-item divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <aside class="app-sidebar">
      <div class="sidebar-toggle">
        <el-button 
          type="text" 
          @click="toggleSidebar"
          class="collapse-btn"
        >
          <el-icon>
            <i-ep-expand v-if="isCollapsed" />
            <i-ep-fold v-else />
          </el-icon>
        </el-button>
      </div>
      <nav class="sidebar-nav">
        <div v-for="item in menuItems" :key="item.index" class="nav-item-wrapper">
          <button 
            @click="navigateTo(item.index)"
            @mouseenter="showTooltip(item, $event)"
            @mouseleave="hideTooltip"
            :class="[
              'nav-item',
              $route.path === item.index 
                ? 'nav-item-active' 
                : 'nav-item-normal'
            ]"
          >
            <el-icon :class="[
              'nav-icon',
              isCollapsed ? 'nav-icon-collapsed' : 'nav-icon-expanded'
            ]">
              <i-ep-data-analysis v-if="item.index === '/'" />
              <i-ep-cpu v-else-if="item.index === '/workshop'" />
              <i-ep-pie-chart v-else-if="item.index === '/market'" />
              <i-ep-search v-else-if="item.index === '/screener'" />
              <i-ep-view v-else-if="item.index === '/cockpit'" />
              <i-ep-setting v-else-if="item.index === '/settings'" />
            </el-icon>
            <span v-if="!isCollapsed" class="nav-text">{{ item.title }}</span>
            <span 
              v-if="isCollapsed && $route.path === item.index" 
              class="active-dot"
            ></span>
          </button>
        </div>
      </nav>
    </aside>
    
    <main class="app-main-content">
      <router-view />
    </main>
    
    <!-- 自定义tooltip -->
    <div 
      v-if="tooltipVisible" 
      class="custom-tooltip"
      :style="{
        top: tooltipPosition.top + 'px',
        left: tooltipPosition.left + 'px'
      }"
    >
      {{ tooltipContent }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 侧边栏折叠状态
const isCollapsed = ref(false)

// Header显示隐藏状态
const isHeaderVisible = ref(false)
let hideHeaderTimer: NodeJS.Timeout | null = null

// 自定义tooltip状态
const tooltipVisible = ref(false)
const tooltipContent = ref('')
const tooltipPosition = ref({ top: 0, left: 0 })

// 菜单项配置
const menuItems = [
  { index: '/', title: '仪表盘', icon: 'i-ep-data-analysis' },
  { index: '/workshop', title: '策略工场', icon: 'i-ep-cpu' },
  { index: '/market', title: '市场中心', icon: 'i-ep-pie-chart' },
  { index: '/screener', title: '选股器', icon: 'i-ep-search' },
  { index: '/cockpit', title: '交易驾驶舱', icon: 'i-ep-view' },
  
  { index: '/settings', title: '系统设置', icon: 'i-ep-setting' }
]

// 导航到指定路由
const navigateTo = (path: string) => {
  router.push(path)
}

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
  // 更新CSS变量 - 从CSS变量中读取值而不是硬编码
  const appLayout = document.querySelector('.app-layout') as HTMLElement
  if (appLayout) {
    const rootStyles = getComputedStyle(document.documentElement)
    const normalWidth = rootStyles.getPropertyValue('--sidebar-width').trim()
    const collapsedWidth = rootStyles.getPropertyValue('--sidebar-width-collapsed').trim()
    appLayout.style.setProperty('--sidebar-width', isCollapsed.value ? collapsedWidth : normalWidth)
  }
}

// 显示自定义tooltip
const showTooltip = (item: any, event: MouseEvent) => {
  if (!isCollapsed.value) return
  
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  tooltipContent.value = item.title
  tooltipPosition.value = {
    top: rect.top + rect.height / 2,
    left: rect.right + 10
  }
  tooltipVisible.value = true
}

// 隐藏自定义tooltip
const hideTooltip = () => {
  tooltipVisible.value = false
}

// 显示header
const showHeader = () => {
  if (hideHeaderTimer) {
    clearTimeout(hideHeaderTimer)
    hideHeaderTimer = null
  }
  isHeaderVisible.value = true
}

// 隐藏header（延迟执行）
const hideHeader = () => {
  hideHeaderTimer = setTimeout(() => {
    isHeaderVisible.value = false
  }, 300) // 300ms延迟，避免鼠标快速移动时闪烁
}

// 处理鼠标移动事件
const handleMouseMove = (event: MouseEvent) => {
  // 如果鼠标在顶部20px区域内，显示header
  if (event.clientY <= 20) {
    showHeader()
  }
}
</script>

<style>
/* 全局样式 - 布局相关 */
.app-layout {
  display: grid;
  grid-template-areas:
    "sidebar content";
  grid-template-rows: 1fr;
  grid-template-columns: var(--sidebar-width) 1fr;
  height: 100vh;
  overflow: hidden; /* 关键：禁止整个页面滚动 */
  /* 使用全局CSS变量，在_spacing.scss中定义 */
  padding-top: 0; /* header隐藏时不需要顶部间距 */
  transition: padding-top 0.3s ease-in-out;
}

.app-main-content {
  grid-area: content;
  overflow-y: auto; /* 关键：主内容区独立滚动 */
  padding: var(--space-lg);
  background-color: #f0f2f5;
}
</style>

<style scoped>
/* 组件内部样式 */

.app-layout:has(.app-sidebar.collapsed) {
  --sidebar-width: var(--sidebar-width-collapsed);
}

.app-layout.header-visible {
  padding-top: 60px;
}

.app-header {
  grid-area: header;
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-lg);
  background-color: var(--el-bg-color);
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  transform: translateY(0);
  transition: transform 0.3s ease-in-out;
}

.app-header.header-hidden {
  transform: translateY(-100%);
}

.header-trigger-zone {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  z-index: 1001;
  background: transparent;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  padding-right: var(--space-md);
}

.app-sidebar {
  grid-area: sidebar;
  overflow-y: auto; /* 侧边栏内容多时，自己滚动 */
  background-color: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s ease;
}



.logo-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 12px;
  transition: all 0.3s ease;
  flex-direction: row;
  height: 100%;
  box-sizing: border-box;
  overflow: visible;
}

/* 收缩状态下的logo容器样式 */
.logo-container-collapsed {
  padding: 8px 6px;
}

.logo-img {
  height: 24px;
  margin-right: 8px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* 收缩状态下的logo图片样式 */
.logo-container-collapsed .logo-img {
  margin-right: 4px;
}

.logo-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.3s ease;
  text-align: left;
  margin-top: 0;
  flex: 1;
  overflow: visible;
}

/* 收缩状态下的logo文字样式 */
.logo-text-collapsed {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-text-collapsed .logo-title-en {
  font-size: 14px;
}

.logo-text-collapsed .logo-title-zh {
  font-size: 10px;
}

.logo-title-en {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  text-align: left;
  transition: all 0.3s ease;
}

.logo-title-zh {
  font-size: 12px;
  font-weight: 400;
  color: var(--el-text-color-regular);
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  text-align: left;
  transition: all 0.3s ease;
}

/* 侧边栏导航样式 */
.sidebar-nav {
  padding: 8px;
}

.nav-item-wrapper {
  margin-bottom: 4px;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
}

.nav-item-normal {
  color: var(--el-text-color-regular);
}

.nav-item-normal:hover {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
}

.nav-item-active {
  background-color: #e6f7ff;
  color: var(--el-color-primary);
  border-right: 3px solid var(--el-color-primary);
}

.nav-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.nav-icon-expanded {
  margin-right: 12px;
}

.nav-icon-collapsed {
  margin: 0 auto;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-toggle {
  display: flex;
  justify-content: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--el-menu-border-color);
}

.collapse-btn {
  font-size: 16px;
  padding: 6px;
  color: var(--el-text-color-regular);
}

.collapse-btn:hover {
  color: var(--el-color-primary);
}



.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: var(--el-fill-color-light);
}

.user-avatar {
  margin-right: 8px;
}

.welcome-text {
  margin-right: 8px;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.dropdown-icon {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 收缩状态下的红色圆点 */
.active-dot {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: var(--el-color-danger);
  border-radius: 50%;
}

/* 自定义tooltip样式 */
.custom-tooltip {
  position: fixed;
  z-index: 9999;
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid var(--el-border-color-light);
  white-space: nowrap;
  pointer-events: none;
  transform: translateY(-50%);
}




</style>