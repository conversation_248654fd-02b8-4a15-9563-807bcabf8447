<template>
  <el-container class="layout-container main-layout">
    <el-aside width="200px">
      <div class="logo-container">
        <img alt="logo" class="logo-img" src="/vite.svg" />
        <h1 class="logo-title">Abu Modern</h1>
      </div>
      <el-menu
        :default-active="$route.path"
        class="el-menu-vertical-demo"
        router
      >
        <el-menu-item index="/">
          <el-icon><i-ep-data-analysis /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>
        <el-menu-item index="/strategies">
          <el-icon><i-ep-setting /></el-icon>
          <span>策略管理</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-container>
      <el-header class="layout-header">Header Content</el-header>
      <el-main class="layout-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
// For this example, we are using unplugin-icons for Element Plus icons.
// Make sure you have `unplugin-icons` and `@iconify-json/ep` installed and configured in vite.config.ts
</script>

<style scoped>
.main-layout {
  height: 100%;
}

.layout-container {
  height: 100vh;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  border-bottom: 1px solid var(--el-menu-border-color);
}

.logo-img {
    height: 32px;
    margin-right: 10px;
}

.logo-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
}

.el-menu {
  border-right: none;
}

.layout-header, .el-header {
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
}

.layout-main, .el-main {
  flex-grow: 1;
  background-color: #f0f2f5;
  padding: 20px;
}
</style>