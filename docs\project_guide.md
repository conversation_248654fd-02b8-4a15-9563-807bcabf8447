项目架构、打包与环境配置最终指南
版本： 1.0
状态： 现行标准 (Active Standard)
注意： 本文档取代了 log_20250722 和 log_20250723 两份备忘录，是 abu_modern 项目在项目结构、打包、环境配置及兼容性处理方面的唯一权威参考。

1. 核心理念：统一的项目架构
经过深入的排查与讨论，项目最终确立了以项目根目录 (abu_modern/) 为基础，将 backend 作为一个顶层包 (Top-level Package) 的架构。

项目结构：

复制
abu_modern/
├── .venv/
├── backend/
│   ├── __init__.py
│   ├── app/
│   │   ├── __init__.py
│   │   └── ...
│   └── tests/
├── pyproject.toml
└── ...
导入规范： 所有项目内部的绝对导入，必须以 backend 作为根。
正确示例： from backend.app.services import ...
错误示例： from app.services import ...
优势： 这种结构明确、无歧义，能被 IDE (VSCode, PyCharm)、静态分析工具 (mypy) 和构建工具 (setuptools) 完美理解，是 Python 社区的现代最佳实践。
2. 指令蓝图：pyproject.toml 的权威配置
pyproject.toml 是项目的“身份证”和“说明书”，完全取代了旧的 setup.py。

2.1. 项目元数据 [project]
定义项目的基本信息。

toml

[project]
name = "abu-modern-backend"
version = "0.1.0"
# ... 其他元数据
2.2. 源码发现机制 [tool.setuptools.packages.find]
这是整个架构的基石，也是对旧指南的关键修正。

toml

复制
[tool.setuptools.packages.find]
# 此处必须为空，或不包含 `where` 键。
# 这会告知 setuptools 从项目根目录开始寻找所有包含 `__init__.py` 的包。
# 因此，`backend` 会被正确地识别为一个顶层包。
重要： 我们不再使用 where = ["backend"] 配置。该配置会导致一种不同的、更复杂的项目结构，与我们确立的统一导入规范相冲突，并已被废弃。

3. 标准工作流程：venv 与可编辑安装
这是一个关于Python项目“隔离”与“可见性”的核心概念。

虚拟环境 (.venv) 的角色：一个“独立的洁净实验室”

目的： 隔离项目依赖，避免全局环境污染。
操作： 始终在激活虚拟环境后进行所有操作。
可编辑安装 (pip install -e .) 的角色：一张“项目的实时开发蓝图”

目的： 让当前项目本身能被环境内的其他工具（如 pytest）所“看见”和“导入”。
机制： 在虚拟环境的 site-packages 目录下创建一个指向你项目源代码目录的快捷方式。你对源代码的任何修改都能被立刻识别，无需重新安装。
操作： 在项目初始化或依赖变更后，于项目根目录执行 uv pip install -e . 或 pip install -e .。
两者协同： 激活 .venv 是为了进入隔离空间；执行 pip install -e . 是为了告知这个空间如何找到并使用你正在开发的项目。

4. 兼容性与运行时补丁 (Monkey Patching)
为了解决第三方库（如 abupy）与我们后端环境的兼容性问题，我们设立了统一的补丁机制。

问题： abupy 依赖 ipywidgets 等前端库，但在后端运行时会引发 ModuleNotFoundError。
解决方案： 我们在应用程序启动的最开始阶段，通过“猴子补丁”动态模拟这些不存在的模块。
实现：
所有补丁逻辑被集中在 backend/app/core/compatibility.py 的 apply_patches() 函数中。
为了确保环境一致性，apply_patches() 必须在两个地方被调用：
主程序入口 backend/main.py 的最顶部，以保证 uvicorn 运行环境正确。
测试配置文件 backend/tests/conftest.py 的最顶部，以保证 pytest 运行环境正确。
5. 附录：开发与调试最佳实践
5.1. pytest vs. python -m pytest
pytest： 方便，适用于本地开发。
python -m pytest： 更可靠、无歧义。它保证了执行测试的 pytest 版本与你项目使用的 python 解释器严格绑定。
推荐： 在自动化脚本（CI/CD）或任何怀疑环境有问题的场景下，始终使用 python -m pytest。
5.2. ModuleNotFoundError 排查黄金法则
在进行复杂的环境配置排查前，请先回归基础：

检查物理路径的连续性 (__init__.py)：

对于 from backend.app.core...，确保 backend/、backend/app/、backend/app/core/ 每一个目录都含有 __init__.py 文件。
核对最终模块的物理存在性（文件名拼写）：

确保导入的模块名与文件名（不含.py）完全一致。仔细检查拼写，如 logging vs. logging_config。

## 6. 如何运行项目

在进行任何操作前，请确保您已在项目根目录，并激活了虚拟环境。

### 6.1. 安装/更新依赖
本项目所有依赖由 `pyproject.toml` 统一管理。使用以下命令进行安装或更新：

```bash
pip install -e .