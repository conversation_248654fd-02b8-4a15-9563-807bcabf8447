# abu_modern 前端UI布局技术规范 (UI Layout Specification V1.0)

## 1. 核心设计原则

- 一致性 (Consistency): 相似的元素应有相似的外观和行为。
- 对齐 (Alignment): 所有元素都应在不可见的网格上对齐，创造秩序感。
- 留白 (Whitespace): 慷慨地使用留白来组织内容，降低认知负荷。
- 层次感 (Hierarchy): 通过大小、粗细、颜色，清晰地展示信息的主次关系。

## 2. 基础栅格与间距系统 (Grid & Spacing)

- 基础单位 (Base Unit): 8px。所有的间距、边距、填充都应为 8px 的整数倍。
- 栅格系统 (ElRow / ElCol): 遵循 Element Plus 的 24 栅格系统。
- 列间距 (gutter): 标准列间距应设置为 16px (`:gutter="16"`) 或 24px (`:gutter="24"`).

- 标准间距令牌 (Spacing Tokens):
  - `--space-xs: 4px` (0.5x) - 用于元素内部极小的间距。
  - `--space-sm: 8px` (1x) - 用于小组件之间，或组件内部。
  - `--space-md: 16px` (2x) - 最常用，用于卡片内边距、表单项之间。
  - `--space-lg: 24px` (3x) - 用于大模块之间，或页面的主要分区。
  - `--space-xl: 32px` (4x) - 用于页面的顶部和底部外边距。

## 3. 整体页面布局

### 主布局 (DefaultLayout.vue)

- 顶栏 (ElHeader) 高度: 60px。
- 侧边栏 (ElAside) 宽度: 220px (展开时)，64px (折叠时)。
- 主内容区 (ElMain):
  - 内边距 (padding): 24px (`--space-lg`)。

### 页面标题 (ElPageHeader 或自定义标题)

- 应作为每个视图 (`.vue` 文件位于 `/views`) 的第一个元素。
- 主标题 (font-size): 20px，font-weight: 500。
- 副标题/描述 (font-size): 14px，颜色: `var(--el-text-color-secondary)`。
- 与下方内容的间距 (margin-bottom): 24px (`--space-lg`)。

## 4. 卡片 (ElCard) 布局规范

- 标准用法: 用于包裹一个独立的功能或信息模块。
- 外边距 (margin): 卡片之间不应互相设置外边距，应由其父级的栅格 gutter 或 flex/grid 的 gap 来控制间距。
- 内边距 (padding):
  - 卡片头部 (`.el-card__header`): `padding: 16px 20px;`
  - 卡片主体 (`.el-card__body`): `padding: 20px;`
- 标题 (header slot):
  - 标题文字 (font-size): 16px，font-weight: 500。
  - 标题应与卡片左侧对齐。
  - 如果头部有操作按钮（如“+ 添加”），按钮应右对齐。

示例：

```html
<div class="card-header">
  <span>模块标题</span>
  <el-button type="primary" size="small">+ 添加</el-button>
</div>
```

```css
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
```

## 5. 表单 (ElForm) 布局规范

- 标签位置 (label-position): 统一使用 top，提供清晰的垂直阅读流，适配不同宽度的屏幕。
- 表单项间距 (margin-bottom): 每个 ElFormItem 的下外边距应为 18px（Element Plus 默认）或统一为 16px（`--space-md`）。
- 控件宽度: 表单内的输入控件（ElInput、ElSelect 等）宽度默认为 100%，占据其所在的栅格列。
- 操作按钮组:
  - 表单底部的“保存”“取消”等按钮，应右对齐。
  - 主操作按钮（如“保存”）应为 `type="primary"`。
  - 按钮之间应有 8px（`--space-sm`）的间距。

## 6. 两栏式布局规范（如“策略工场”）

- 左侧列表栏:
  - 宽度: 300px 到 360px 之间，具体取决于内容。
  - 与右侧编辑器之间应有清晰的分隔线（`border-right`）或 24px 的间距。
- 右侧内容/编辑器栏:
  - 占据剩余的全部宽度（`flex: 1`）。
- 滚动行为:
  - 必须采用“左右分栏各自独立滚动”的设计。
  - 外层容器应设置固定高度（如 `height: calc(100vh - ...)`）并设置 `overflow: hidden`。
  - 左右两栏应设置 `overflow-y: auto`，各自管理自己的滚动条。

## 7. 对话框 (ElDialog) 布局规范

- 标准宽度:
  - 小 (small): 400px（用于简单的确认、提示）。
  - 中 (medium): 600px（最常用，用于简单的表单）。
  - 大 (large): 800px（用于复杂的表单或内容展示）。
- 页脚 (footer slot):
  - 按钮组必须右对齐。
  - 标准顺序: [ 取消 ] [ 确认 ]（主操作在右）。