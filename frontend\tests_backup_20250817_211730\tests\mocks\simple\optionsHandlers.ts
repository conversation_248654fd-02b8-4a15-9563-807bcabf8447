// 选项API Mock处理器 - 严格按照API契约

import { http, HttpResponse } from 'msw';

export const optionsHandlers = [
  // 获取仓位管理策略选项
  http.get('/api/options/positions', () => {
    const mockPositionOptions = [
      {
        id: 'AbuPositionBase',
        name: '基础仓位管理',
        description: '基础的仓位管理策略',
        parameters: [
          {
            name: 'position_base',
            type: 'float',
            default: 0.1,
            description: '基础仓位比例',
            min: 0.01,
            max: 1.0
          }
        ]
      },
      {
        id: 'AbuPositionKelly',
        name: 'Kelly仓位管理',
        description: '基于Kelly公式的仓位管理',
        parameters: [
          {
            name: 'win_rate',
            type: 'float',
            default: 0.6,
            description: '胜率',
            min: 0.1,
            max: 0.9
          },
          {
            name: 'profit_loss_ratio',
            type: 'float',
            default: 2.0,
            description: '盈亏比',
            min: 1.0,
            max: 10.0
          }
        ]
      }
    ];

    return HttpResponse.json({
      success: true,
      message: "获取仓位管理策略成功",
      data: {
        options: mockPositionOptions,
        total: mockPositionOptions.length
      }
    });
  }),

  // 获取裁判规则选项
  http.get('/api/options/judges', () => {
    const mockJudgeOptions = [
      {
        id: 'AbuJudgeStopLoss',
        name: '止损裁判',
        description: '基于止损比例的裁判规则',
        parameters: [
          {
            name: 'stop_loss_rate',
            type: 'float',
            default: 0.1,
            description: '止损比例',
            min: 0.01,
            max: 0.5
          }
        ]
      },
      {
        id: 'AbuJudgeTakeProfit',
        name: '止盈裁判',
        description: '基于止盈比例的裁判规则',
        parameters: [
          {
            name: 'take_profit_rate',
            type: 'float',
            default: 0.2,
            description: '止盈比例',
            min: 0.05,
            max: 1.0
          }
        ]
      }
    ];

    return HttpResponse.json({
      success: true,
      message: "获取裁判规则成功",
      data: {
        options: mockJudgeOptions,
        total: mockJudgeOptions.length
      }
    });
  }),

  // 获取特征过滤器
  http.get('/api/options/feature-filters', ({ request }) => {
    const url = new URL(request.url);
    const modelName = url.searchParams.get('model_name');

    const mockFeatureFilters = [
      {
        id: 'kline_angle_filter',
        name: 'K线角度过滤器',
        description: '基于K线角度特征过滤交易信号',
        model_name: modelName || 'default_model',
        parameters: [
          {
            name: 'angle_threshold',
            type: 'float',
            default: 30.0,
            description: '角度阈值（度）',
            min: 0.0,
            max: 90.0
          },
          {
            name: 'window_size',
            type: 'int',
            default: 5,
            description: '窗口大小',
            min: 3,
            max: 20
          }
        ]
      }
    ];

    return HttpResponse.json({
      success: true,
      message: "获取特征过滤器成功",
      data: {
        filters: mockFeatureFilters,
        total: mockFeatureFilters.length
      }
    });
  })
];