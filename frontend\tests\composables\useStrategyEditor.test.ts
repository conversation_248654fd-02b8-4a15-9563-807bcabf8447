import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useStrategyEditor, strategyUtils } from '@/composables/useStrategyEditor'
import type { Strategy } from '@/api/types'

// Mock strategy constants
const mockStrategyConstants = vi.hoisted(() => {
  return {
    STRATEGY_STATES: {
      DRAFT: 'draft',
      ACTIVE: 'active',
      PAUSED: 'paused'
    },
    ERROR_MESSAGES: {
      STRATEGY_NAME_REQUIRED: '策略名称不能为空',
      DESCRIPTION_TOO_LONG: '描述过长',
      BUY_FACTORS_REQUIRED: '至少需要一个买入因子',
      VALIDATION_FAILED: '验证失败'
    },
    isNewStrategy: vi.fn(),
    validateStrategyName: vi.fn(),
    validateStrategyDescription: vi.fn()
  }
})

vi.mock('@/constants/strategy', () => mockStrategyConstants)

describe('useStrategyEditor', () => {
  let strategyEditor: ReturnType<typeof useStrategyEditor>
  
  beforeEach(() => {
    vi.clearAllMocks()
    strategyEditor = useStrategyEditor()
    
    // 设置默认的mock返回值
    mockStrategyConstants.isNewStrategy.mockImplementation((strategy) => {
      return strategy ? true : false // null/undefined时返回false
    })
    mockStrategyConstants.validateStrategyName.mockReturnValue({ isValid: true })
    mockStrategyConstants.validateStrategyDescription.mockReturnValue({ isValid: true })
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(strategyEditor.editingStrategy.value).toBeNull()
      expect(strategyEditor.isDirty.value).toBe(false)
      expect(strategyEditor.isSaving.value).toBe(false)
      expect(strategyEditor.errors.value).toEqual([])
    })

    it('应该有正确的计算属性初始值', () => {
      expect(strategyEditor.canSave.value).toBe(false)
      expect(strategyEditor.isNewStrategy.value).toBe(false) // 因为editingStrategy为null
    })
  })

  describe('updateStrategy', () => {
    const mockStrategy: Strategy = {
      id: '1',
      name: 'TestStrategy',
      description: '测试策略',
      buy_factors: [{ name: 'BuyFactor1' }],
      sell_factors: []
    } as Strategy

    it('应该更新编辑中的策略', () => {
      strategyEditor.updateStrategy(mockStrategy)
      
      expect(strategyEditor.editingStrategy.value).toEqual(mockStrategy)
    })

    it('应该设置原始策略状态', () => {
      strategyEditor.updateStrategy(mockStrategy)
      
      // 原始策略应该被设置（因为之前为null）
      expect(strategyEditor.isDirty.value).toBe(false)
    })

    it('应该在resetOriginal为true时重置原始状态', () => {
      const initialStrategy: Strategy = {
        id: '1',
        name: 'Initial',
        buy_factors: []
      } as Strategy
      
      const updatedStrategy: Strategy = {
        id: '1',
        name: 'Updated',
        buy_factors: [{ name: 'NewFactor' }]
      } as Strategy
      
      strategyEditor.updateStrategy(initialStrategy)
      strategyEditor.updateStrategy(updatedStrategy, true)
      
      expect(strategyEditor.isDirty.value).toBe(false)
    })

    it('应该检测策略变更', () => {
      strategyEditor.updateStrategy(mockStrategy)
      
      const modifiedStrategy = {
        ...mockStrategy,
        name: 'ModifiedStrategy'
      }
      
      strategyEditor.updateStrategy(modifiedStrategy)
      
      expect(strategyEditor.isDirty.value).toBe(true)
    })

    it('应该验证策略并设置错误', () => {
      mockStrategyConstants.validateStrategyName.mockReturnValue({
        isValid: false,
        message: '名称无效'
      })
      
      strategyEditor.updateStrategy(mockStrategy)
      
      expect(strategyEditor.errors.value).toContainEqual({
        field: 'name',
        message: '名称无效'
      })
    })
  })

  describe('canSave计算属性', () => {
    it('应该在没有策略时返回false', () => {
      expect(strategyEditor.canSave.value).toBe(false)
    })

    it('应该在策略名称为空时返回false', () => {
      const strategy: Strategy = {
        id: '1',
        name: '',
        buy_factors: [{ name: 'BuyFactor' }]
      } as Strategy
      
      strategyEditor.updateStrategy(strategy)
      
      expect(strategyEditor.canSave.value).toBe(false)
    })

    it('应该在没有买入因子时返回false', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: []
      } as Strategy
      
      strategyEditor.updateStrategy(strategy)
      
      expect(strategyEditor.canSave.value).toBe(false)
    })

    it('应该在有验证错误时返回false', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [{ name: 'BuyFactor' }]
      } as Strategy
      
      mockStrategyConstants.validateStrategyName.mockReturnValue({
        isValid: false,
        message: '名称无效'
      })
      
      strategyEditor.updateStrategy(strategy)
      
      expect(strategyEditor.canSave.value).toBe(false)
    })

    it('应该在满足所有条件时返回true', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [{ name: 'BuyFactor' }]
      } as Strategy
      
      strategyEditor.updateStrategy(strategy)
      
      expect(strategyEditor.canSave.value).toBe(true)
    })
  })

  describe('isNewStrategy计算属性', () => {
    it('应该调用isNewStrategy函数', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy'
      } as Strategy
      
      mockStrategyConstants.isNewStrategy.mockReturnValue(false)
      
      strategyEditor.updateStrategy(strategy)
      
      expect(strategyEditor.isNewStrategy.value).toBe(false)
      expect(mockStrategyConstants.isNewStrategy).toHaveBeenCalledWith(strategy)
    })
  })

  describe('validateStrategy', () => {
    const mockStrategy: Strategy = {
      id: '1',
      name: 'TestStrategy',
      description: '测试策略',
      buy_factors: [{ name: 'BuyFactor' }]
    } as Strategy

    it('应该验证策略名称', () => {
      strategyEditor.validateStrategy(mockStrategy)
      
      expect(mockStrategyConstants.validateStrategyName).toHaveBeenCalledWith('TestStrategy')
    })

    it('应该验证策略描述', () => {
      strategyEditor.validateStrategy(mockStrategy)
      
      expect(mockStrategyConstants.validateStrategyDescription).toHaveBeenCalledWith('测试策略')
    })

    it('应该返回名称验证错误', () => {
      mockStrategyConstants.validateStrategyName.mockReturnValue({
        isValid: false,
        message: '名称太短'
      })
      
      const errors = strategyEditor.validateStrategy(mockStrategy)
      
      expect(errors).toContainEqual({
        field: 'name',
        message: '名称太短'
      })
    })

    it('应该返回描述验证错误', () => {
      mockStrategyConstants.validateStrategyDescription.mockReturnValue({
        isValid: false,
        message: '描述太长'
      })
      
      const errors = strategyEditor.validateStrategy(mockStrategy)
      
      expect(errors).toContainEqual({
        field: 'description',
        message: '描述太长'
      })
    })

    it('应该验证买入因子是否存在', () => {
      const strategyWithoutBuyFactors: Strategy = {
        ...mockStrategy,
        buy_factors: []
      }
      
      const errors = strategyEditor.validateStrategy(strategyWithoutBuyFactors)
      
      expect(errors).toContainEqual({
        field: 'buy_factors',
        message: mockStrategyConstants.ERROR_MESSAGES.BUY_FACTORS_REQUIRED
      })
    })

    it('应该处理undefined的买入因子', () => {
      const strategyWithUndefinedBuyFactors: Strategy = {
        ...mockStrategy,
        buy_factors: undefined as any
      }
      
      const errors = strategyEditor.validateStrategy(strategyWithUndefinedBuyFactors)
      
      expect(errors).toContainEqual({
        field: 'buy_factors',
        message: mockStrategyConstants.ERROR_MESSAGES.BUY_FACTORS_REQUIRED
      })
    })

    it('应该处理空字符串字段', () => {
      const strategyWithEmptyFields: Strategy = {
        ...mockStrategy,
        name: '',
        description: ''
      }
      
      mockStrategyConstants.validateStrategyName.mockReturnValue({
        isValid: false,
        message: '名称不能为空'
      })
      
      const errors = strategyEditor.validateStrategy(strategyWithEmptyFields)
      
      expect(errors.length).toBeGreaterThan(0)
    })
  })

  describe('saveStrategy', () => {
    const validStrategy: Strategy = {
      id: '1',
      name: 'TestStrategy',
      buy_factors: [{ name: 'BuyFactor' }]
    } as Strategy

    beforeEach(() => {
      strategyEditor.updateStrategy(validStrategy)
    })

    it('应该成功保存有效策略', async () => {
      await strategyEditor.saveStrategy()
      
      expect(strategyEditor.isSaving.value).toBe(false)
      expect(strategyEditor.isDirty.value).toBe(false)
      expect(strategyEditor.errors.value).toEqual([])
    })

    it('应该在保存过程中设置isSaving状态', async () => {
      const savePromise = strategyEditor.saveStrategy()
      
      // 在保存过程中应该为true（虽然这里会立即完成）
      await savePromise
      
      expect(strategyEditor.isSaving.value).toBe(false)
    })

    it('应该在没有策略时抛出错误', async () => {
      strategyEditor.clearEditor()
      
      await expect(strategyEditor.saveStrategy()).rejects.toThrow(
        mockStrategyConstants.ERROR_MESSAGES.VALIDATION_FAILED
      )
    })

    it('应该在canSave为false时抛出错误', async () => {
      const invalidStrategy: Strategy = {
        id: '1',
        name: '',
        buy_factors: []
      } as Strategy
      
      strategyEditor.updateStrategy(invalidStrategy)
      
      await expect(strategyEditor.saveStrategy()).rejects.toThrow(
        mockStrategyConstants.ERROR_MESSAGES.VALIDATION_FAILED
      )
    })

    it('应该在验证失败时抛出错误', async () => {
      mockStrategyConstants.validateStrategyName.mockReturnValue({
        isValid: false,
        message: '验证失败'
      })
      
      strategyEditor.updateStrategy(validStrategy)
      
      await expect(strategyEditor.saveStrategy()).rejects.toThrow('无法保存策略：验证失败')
      expect(strategyEditor.isSaving.value).toBe(false)
    })

    it('应该支持不自动重置状态', async () => {
      await strategyEditor.saveStrategy(false)
      
      // 当autoResetStates为false时，状态不应该被重置
      expect(strategyEditor.isSaving.value).toBe(false)
    })
  })

  describe('onSaveSuccess', () => {
    it('应该重置保存成功后的状态', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [{ name: 'BuyFactor' }]
      } as Strategy
      
      strategyEditor.updateStrategy(strategy)
      
      // 模拟修改
      const modifiedStrategy = { ...strategy, name: 'Modified' }
      strategyEditor.updateStrategy(modifiedStrategy)
      
      expect(strategyEditor.isDirty.value).toBe(true)
      
      strategyEditor.onSaveSuccess()
      
      expect(strategyEditor.isDirty.value).toBe(false)
      expect(strategyEditor.errors.value).toEqual([])
      expect(strategyEditor.isSaving.value).toBe(false)
    })

    it('应该处理没有编辑策略的情况', () => {
      strategyEditor.onSaveSuccess()
      
      expect(strategyEditor.isSaving.value).toBe(false)
    })
  })

  describe('onSaveError', () => {
    it('应该重置保存状态', () => {
      strategyEditor.onSaveError()
      
      expect(strategyEditor.isSaving.value).toBe(false)
    })
  })

  describe('resetChanges', () => {
    it('应该重置到原始策略状态', () => {
      const originalStrategy: Strategy = {
        id: '1',
        name: 'Original',
        buy_factors: [{ name: 'OriginalFactor' }]
      } as Strategy
      
      strategyEditor.updateStrategy(originalStrategy)
      
      const modifiedStrategy = {
        ...originalStrategy,
        name: 'Modified'
      }
      
      strategyEditor.updateStrategy(modifiedStrategy)
      
      expect(strategyEditor.isDirty.value).toBe(true)
      
      strategyEditor.resetChanges()
      
      expect(strategyEditor.editingStrategy.value).toEqual(originalStrategy)
      expect(strategyEditor.isDirty.value).toBe(false)
      expect(strategyEditor.errors.value).toEqual([])
    })

    it('应该处理没有原始策略的情况', () => {
      strategyEditor.resetChanges()
      
      // 应该不会抛出错误
      expect(strategyEditor.editingStrategy.value).toBeNull()
    })
  })

  describe('clearEditor', () => {
    it('应该清空所有编辑器状态', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [{ name: 'BuyFactor' }]
      } as Strategy
      
      strategyEditor.updateStrategy(strategy)
      
      strategyEditor.clearEditor()
      
      expect(strategyEditor.editingStrategy.value).toBeNull()
      expect(strategyEditor.isDirty.value).toBe(false)
      expect(strategyEditor.errors.value).toEqual([])
      expect(strategyEditor.isSaving.value).toBe(false)
    })
  })

  describe('边界情况', () => {
    it('应该处理null策略的验证', () => {
      const errors = strategyEditor.validateStrategy(null as any)
      
      expect(errors.length).toBeGreaterThan(0)
    })

    it('应该处理undefined策略的验证', () => {
      const errors = strategyEditor.validateStrategy(undefined as any)
      
      expect(errors.length).toBeGreaterThan(0)
    })

    it('应该处理空对象策略', () => {
      const errors = strategyEditor.validateStrategy({} as Strategy)
      
      expect(errors.length).toBeGreaterThan(0)
    })
  })
})

describe('strategyUtils', () => {
  describe('isNewStrategy', () => {
    it('应该调用导入的isNewStrategy函数', () => {
      const strategy: Strategy = { id: '1', name: 'Test' } as Strategy
      
      strategyUtils.isNewStrategy(strategy)
      
      expect(mockStrategyConstants.isNewStrategy).toHaveBeenCalledWith(strategy)
    })
  })

  describe('generateDescription', () => {
    it('应该生成包含买入因子的描述', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [
          { description: '移动平均线突破', class_name: 'MA' },
          { description: 'RSI超卖', class_name: 'RSI' }
        ],
        sell_factors: []
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toContain('买入规则：移动平均线突破；RSI超卖')
    })

    it('应该生成包含卖出因子的描述', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [{ description: '买入信号', class_name: 'Buy' }],
        sell_factors: [
          { description: '止损', class_name: 'StopLoss' },
          { description: '止盈', class_name: 'TakeProfit' }
        ]
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toContain('买入规则：买入信号')
      expect(description).toContain('卖出规则：止损；止盈')
    })

    it('应该使用class_name作为备用描述', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [
          { class_name: 'MovingAverage' }, // 没有description
          { description: '', class_name: 'RSI' } // 空description
        ],
        sell_factors: []
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toContain('买入规则：MovingAverage；RSI')
    })

    it('应该过滤空描述', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [
          { description: '有效描述', class_name: 'Valid' },
          { description: '', class_name: '' }, // 都为空
          { description: '   ', class_name: 'Whitespace' } // 只有空格
        ],
        sell_factors: []
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toContain('买入规则：有效描述；Whitespace')
      expect(description).not.toContain(';;') // 不应该有连续的分号
    })

    it('应该处理没有因子的策略', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [],
        sell_factors: []
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toBe('请添加买入和卖出因子来完善此策略。')
    })

    it('应该处理undefined的因子列表', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy'
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toBe('请添加买入和卖出因子来完善此策略。')
    })

    it('应该只有买入因子时生成正确描述', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [{ description: '买入信号', class_name: 'Buy' }],
        sell_factors: []
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toBe('买入规则：买入信号。')
    })

    it('应该只有卖出因子时生成正确描述', () => {
      const strategy: Strategy = {
        id: '1',
        name: 'TestStrategy',
        buy_factors: [],
        sell_factors: [{ description: '卖出信号', class_name: 'Sell' }]
      } as Strategy
      
      const description = strategyUtils.generateDescription(strategy)
      
      expect(description).toBe('卖出规则：卖出信号。')
    })
  })

  describe('handleError', () => {
    let consoleSpy: any
    
    beforeEach(() => {
      consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    })
    
    afterEach(() => {
      consoleSpy.mockRestore()
    })

    it('应该记录错误并返回错误信息', () => {
      const error = new Error('测试错误')
      const context = '测试上下文'
      
      const result = strategyUtils.handleError(error, context)
      
      expect(consoleSpy).toHaveBeenCalledWith('[测试上下文]', error)
      expect(result).toEqual({
        message: '测试错误',
        context: '测试上下文'
      })
    })

    it('应该处理没有消息的错误', () => {
      const error = new Error()
      const context = '测试上下文'
      
      const result = strategyUtils.handleError(error, context)
      
      expect(result.message).toBe('操作失败')
      expect(result.context).toBe(context)
    })

    it('应该处理空消息的错误', () => {
      const error = new Error('')
      const context = '测试上下文'
      
      const result = strategyUtils.handleError(error, context)
      
      expect(result.message).toBe('操作失败')
    })
  })
})