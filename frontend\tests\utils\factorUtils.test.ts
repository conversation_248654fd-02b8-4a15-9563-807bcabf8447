import { describe, it, expect } from 'vitest'
import {
  getFriendlyFactorName,
  getFactorParameters,
  formatParameterValue,
  normalizeFactorParameters,
  validateRequiredParameters,
  initializeFormData,
  hasParameters
} from '@/utils/factorUtils'
import type { FactorParameter } from '@/types'

describe('factorUtils', () => {
  describe('getFriendlyFactorName', () => {
    it('应该返回友好的因子名称', () => {
      const factor = {
        name: '移动平均线',
        class_name: 'MovingAverage'
      }
      
      const result = getFriendlyFactorName(factor)
      expect(result).toBe('MovingAverage')
    })

    it('应该在没有name时使用class_name', () => {
      const factor = {
        class_name: 'MovingAverage'
      }
      
      const result = getFriendlyFactorName(factor)
      expect(result).toBe('MovingAverage')
    })

    it('应该处理空对象', () => {
      const result = getFriendlyFactorName({})
      expect(result).toBe('未命名因子')
    })

    it('应该处理null和undefined', () => {
      expect(getFriendlyFactorName(null)).toBe('未命名因子')
      expect(getFriendlyFactorName(undefined)).toBe('未命名因子')
    })

    it('应该处理空字符串', () => {
      const factor = {
        name: '',
        class_name: 'MovingAverage'
      }
      
      const result = getFriendlyFactorName(factor)
      expect(result).toBe('MovingAverage')
    })
  })

  describe('getFactorParameters', () => {
    it('应该返回因子的参数对象', () => {
      const factor = {
        parameters: {
          period: 20,
          threshold: 0.5
        }
      }
      
      const result = getFactorParameters(factor)
      expect(result).toEqual({
        period: 20,
        threshold: 0.5
      })
    })

    it('应该处理没有parameters的因子', () => {
      const factor = {
        name: 'TestFactor'
      }
      
      const result = getFactorParameters(factor)
      expect(result).toEqual({})
    })

    it('应该处理null和undefined', () => {
      expect(getFactorParameters(null)).toEqual({})
      expect(getFactorParameters(undefined)).toEqual({})
    })

    it('应该处理parameters为null的情况', () => {
      const factor = {
        parameters: null
      }
      
      const result = getFactorParameters(factor)
      expect(result).toEqual({})
    })
  })

  describe('formatParameterValue', () => {
    it('应该格式化数字值', () => {
      expect(formatParameterValue(123)).toBe('123')
      expect(formatParameterValue(123.456)).toBe('123.456')
      expect(formatParameterValue(0)).toBe('0')
    })

    it('应该格式化字符串值', () => {
      expect(formatParameterValue('test')).toBe('test')
      expect(formatParameterValue('')).toBe('')
    })

    it('应该格式化布尔值', () => {
      expect(formatParameterValue(true)).toBe('是')
      expect(formatParameterValue(false)).toBe('否')
    })

    it('应该处理null和undefined', () => {
      expect(formatParameterValue(null)).toBe('-')
      expect(formatParameterValue(undefined)).toBe('-')
    })

    it('应该格式化数组', () => {
      expect(formatParameterValue([1, 2, 3])).toBe('1, 2, 3')
      expect(formatParameterValue([])).toBe('')
    })

    it('应该格式化对象', () => {
      const obj = { a: 1, b: 2 }
      const result = formatParameterValue(obj)
      expect(result).toBe('{"a":1,"b":2}')
    })
  })

  describe('normalizeFactorParameters', () => {
    it('应该将对象参数转换为标准化列表', () => {
      const parameters = {
        period: 20,
        threshold: 0.5,
        enabled: true
      }
      
      const result = normalizeFactorParameters(parameters)
      
      expect(result).toHaveLength(3)
      expect(result.find(p => p.name === 'period')).toMatchObject({
        name: 'period',
        label: 'period',
        description: 'period'
      })
      expect(result.find(p => p.name === 'threshold')).toMatchObject({
        name: 'threshold',
        label: 'threshold', 
        description: 'threshold'
      })
      expect(result.find(p => p.name === 'enabled')).toMatchObject({
        name: 'enabled',
        label: 'enabled',
        description: 'enabled'
      })
    })

    it('应该处理已经是数组的参数', () => {
      const parameters: FactorParameter[] = [
        {
          name: 'period',
          value: 20,
          type: 'number',
          required: true,
          description: '周期参数'
        }
      ]
      
      const result = normalizeFactorParameters(parameters)
      expect(result).toEqual(parameters)
    })

    it('应该正确推断参数类型', () => {
      const parameters = {
        stringParam: 'test',
        numberParam: 42,
        booleanParam: true,
        arrayParam: [1, 2, 3],
        objectParam: { key: 'value' }
      }
      
      const result = normalizeFactorParameters(parameters)
      
      expect(result.find(p => p.name === 'stringParam')?.type).toBeUndefined()
      expect(result.find(p => p.name === 'numberParam')?.type).toBeUndefined()
      expect(result.find(p => p.name === 'booleanParam')?.type).toBeUndefined()
      expect(result.find(p => p.name === 'arrayParam')?.type).toBeUndefined()
      expect(result.find(p => p.name === 'objectParam')?.type).toBeUndefined()
    })

    it('应该处理空对象', () => {
      const result = normalizeFactorParameters({})
      expect(result).toEqual([])
    })

    it('应该处理null和undefined', () => {
      expect(normalizeFactorParameters(null)).toEqual([])
      expect(normalizeFactorParameters(undefined)).toEqual([])
    })
  })

  describe('validateRequiredParameters', () => {
    const parameters: FactorParameter[] = [
      {
        name: 'period',
        value: 20,
        type: 'number',
        required: true,
        description: '周期参数'
      },
      {
        name: 'threshold',
        value: 0.5,
        type: 'number',
        required: false,
        description: '阈值参数'
      },
      {
        name: 'name',
        value: '',
        type: 'string',
        required: true,
        description: '名称参数'
      }
    ]

    it('应该验证所有必填参数都已填写', () => {
      const formData = {
        period: 30,
        name: 'TestStrategy'
      }
      
      const result = validateRequiredParameters(parameters, formData)
      expect(result).toBe(true)
    })

    it('应该检测缺失的必填参数', () => {
      const formData = {
        period: 30
        // 缺少必填的name参数
      }
      
      const result = validateRequiredParameters(parameters, formData)
      expect(result).toBe(false)
    })

    it('应该检测空字符串的必填参数', () => {
      const formData = {
        period: 30,
        name: '' // 空字符串
      }
      
      const result = validateRequiredParameters(parameters, formData)
      expect(result).toBe(false)
    })

    it('应该检测null值的必填参数', () => {
      const formData = {
        period: 30,
        name: null
      }
      
      const result = validateRequiredParameters(parameters, formData)
      expect(result).toBe(false)
    })

    it('应该允许数字0作为有效值', () => {
      const numericParameters: FactorParameter[] = [
        {
          name: 'value',
          value: 0,
          type: 'number',
          required: true,
          description: '数值参数'
        }
      ]
      
      const formData = {
        value: 0
      }
      
      const result = validateRequiredParameters(numericParameters, formData)
      expect(result).toBe(true)
    })

    it('应该允许false作为有效的布尔值', () => {
      const booleanParameters: FactorParameter[] = [
        {
          name: 'enabled',
          value: false,
          type: 'boolean',
          required: true,
          description: '启用参数'
        }
      ]
      
      const formData = {
        enabled: false
      }
      
      const result = validateRequiredParameters(booleanParameters, formData)
      expect(result).toBe(true)
    })

    it('应该处理空参数列表', () => {
      const result = validateRequiredParameters([], {})
      expect(result).toBe(true)
    })

    it('应该处理空表单数据', () => {
      const result = validateRequiredParameters(parameters, {})
      expect(result).toBe(false)
    })
  })

  describe('initializeFormData', () => {
    const parameters: FactorParameter[] = [
      {
        name: 'period',
        value: 20,
        type: 'number',
        required: true,
        description: '周期参数'
      },
      {
        name: 'threshold',
        value: 0.5,
        type: 'number',
        required: false,
        description: '阈值参数'
      },
      {
        name: 'enabled',
        value: true,
        type: 'boolean',
        required: false,
        description: '启用参数'
      }
    ]

    it('应该使用默认值初始化表单数据', () => {
      const result = initializeFormData(parameters)
      
      expect(result).toEqual({
        period: undefined,
        threshold: undefined,
        enabled: undefined
      })
    })

    it('应该合并现有数据和默认值', () => {
      const existingData = {
        period: 30,
        newParam: 'custom'
      }
      
      const result = initializeFormData(parameters, existingData)
      
      expect(result).toEqual({
        period: 30, // 使用现有值
        threshold: undefined, // 没有默认值
        enabled: undefined // 没有默认值
      })
    })

    it('应该处理空参数列表', () => {
      const result = initializeFormData([])
      expect(result).toEqual({})
    })

    it('应该处理没有现有数据的情况', () => {
      const result = initializeFormData(parameters, undefined)
      
      expect(result).toEqual({
        period: undefined,
        threshold: undefined,
        enabled: undefined
      })
    })

    it('应该处理现有数据为null的情况', () => {
      const result = initializeFormData(parameters, null as any)
      
      expect(result).toEqual({
        period: undefined,
        threshold: undefined,
        enabled: undefined
      })
    })

    it('应该保留现有数据中的null值', () => {
      const existingData = {
        period: null,
        threshold: 0.8
      }
      
      const result = initializeFormData(parameters, existingData)
      
      expect(result).toEqual({
        period: null, // 保留现有的null值
        threshold: 0.8, // 使用现有值
        enabled: undefined // 没有默认值时为undefined
      })
    })
  })

  describe('hasParameters', () => {
    it('应该检测有参数的因子', () => {
      const factor = {
        parameters: {
          period: 20,
          threshold: 0.5
        }
      }
      
      const result = hasParameters(factor)
      expect(result).toBe(true)
    })

    it('应该检测没有参数的因子', () => {
      const factor = {
        parameters: {}
      }
      
      const result = hasParameters(factor)
      expect(result).toBe(false)
    })

    it('应该处理没有parameters属性的因子', () => {
      const factor = {
        name: 'TestFactor'
      }
      
      const result = hasParameters(factor)
      expect(result).toBe(false)
    })

    it('应该处理null和undefined', () => {
      expect(hasParameters(null)).toBe(false)
      expect(hasParameters(undefined)).toBe(false)
    })

    it('应该处理parameters为null的情况', () => {
      const factor = {
        parameters: null
      }
      
      const result = hasParameters(factor)
      expect(result).toBe(false)
    })
  })

  describe('边界情况和错误处理', () => {
    it('应该处理循环引用的对象', () => {
      const circularObj: any = { name: 'circular' }
      circularObj.self = circularObj
      
      expect(() => {
        formatParameterValue(circularObj)
      }).toThrow('Converting circular structure to JSON')
    })

    it('应该处理非常大的数字', () => {
      const largeNumber = Number.MAX_SAFE_INTEGER
      const result = formatParameterValue(largeNumber)
      expect(result).toBe(largeNumber.toString())
    })

    it('应该处理特殊数字值', () => {
      expect(formatParameterValue(Infinity)).toBe('Infinity')
      expect(formatParameterValue(-Infinity)).toBe('-Infinity')
      expect(formatParameterValue(NaN)).toBe('NaN')
    })

    it('应该处理深层嵌套的对象', () => {
      const deepObject = {
        level1: {
          level2: {
            level3: {
              value: 'deep'
            }
          }
        }
      }
      
      const result = normalizeFactorParameters(deepObject)
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('level1')
      expect(result[0].type).toBeUndefined()
    })

    it('应该处理包含特殊字符的参数名', () => {
      const parameters = {
        'param-with-dash': 1,
        'param_with_underscore': 2,
        'param.with.dot': 3,
        'param with space': 4
      }
      
      const result = normalizeFactorParameters(parameters)
      expect(result).toHaveLength(4)
      expect(result.map(p => p.name)).toContain('param-with-dash')
      expect(result.map(p => p.name)).toContain('param_with_underscore')
      expect(result.map(p => p.name)).toContain('param.with.dot')
      expect(result.map(p => p.name)).toContain('param with space')
    })

    it('应该处理空数组参数', () => {
      const parameters = {
        emptyArray: [],
        nonEmptyArray: [1, 2, 3]
      }
      
      const result = normalizeFactorParameters(parameters)
      expect(result.find(p => p.name === 'emptyArray')?.type).toBeUndefined()
      expect(result.find(p => p.name === 'emptyObject')?.type).toBeUndefined()
    })
  })
})