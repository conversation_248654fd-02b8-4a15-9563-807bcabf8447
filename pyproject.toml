[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "abu-modern-backend" 
version = "0.1.0"
description = "A modern backend for the Abu quantitative trading system."
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "abu>=0.10",
    "abupy>=0.4.0",
    "bokeh>=3.7.3",
    "celery",
    "chardet>=5.2.0",
    "email-validator>=2.0.0",
    "fastapi>=0.95.0",
    "httpx>=0.24.0",
    "matplotlib>=3.7.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "pandas-ta>=0.3.14b0",
    "passlib>=1.7.4",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "pymongo>=4.3.0",
    "pymysql>=1.0.0",
    "pytest>=8.3.5",
    "python-dotenv>=1.0.0",
    "python-jose>=3.3.0",
    "python-multipart>=0.0.5",
    "requests>=2.28.0",
    "scikit-learn>=1.2.0",
    "scipy>=1.10.0",
    "sqlalchemy>=2.0.0",
    "sqlmodel",
    "statsmodels>=0.13.5",
    "tables>=3.10.2",
    "toolz",
    "tushare>=1.2.0",
    "uvicorn>=0.22.0",
]

[tool.pytest.ini_options]
# 告诉pytest去哪里找测试文件
testpaths = [
    "backend/tests",
]

markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "e2e_network: marks tests that require a live network connection for end-to-end testing",
    "e2e_execution: marks tests as end-to-end execution tests (may be slow)"
]
python_files = "test_*.py"
filterwarnings = [
    "ignore::DeprecationWarning:abupy.*",
    "ignore:.*imghdr.*:DeprecationWarning",
    "ignore:.*collections.Iterable.*:DeprecationWarning",
    "ignore:.*example.*deprecated.*:DeprecationWarning"
]

[tool.setuptools]
packages = ["backend", "backend.app", "backend.app.api", "backend.app.core", "backend.app.models", "backend.app.schemas", "backend.app.services", "backend.tests"]

[project.optional-dependencies]
test = [
    # --- 这里放所有测试专用的包 ---
    "pytest",
    "pytest-mock"
]