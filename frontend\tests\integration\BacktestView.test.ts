import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ref, computed } from 'vue'
import BacktestView from '../../src/views/BacktestView.vue'
import type { BacktestConfig, BacktestResult } from '../../src/api/types/backtest'

// Mock stores with reactive state
const mockBacktestState = {
  isBacktesting: ref(false),
  backtestProgress: ref(0),
  backtestResult: ref(null),
  backtestError: ref(''),
  backtestHistory: ref([])
}

const mockUseBacktestStore = {
  get isBacktesting() { return mockBacktestState.isBacktesting.value },
  set isBacktesting(value) { mockBacktestState.isBacktesting.value = value },
  get backtestProgress() { return mockBacktestState.backtestProgress.value },
  set backtestProgress(value) { mockBacktestState.backtestProgress.value = value },
  get backtestResult() { return mockBacktestState.backtestResult.value },
  set backtestResult(value) { mockBacktestState.backtestResult.value = value },
  get backtestError() { return mockBacktestState.backtestError.value },
  set backtestError(value) { mockBacktestState.backtestError.value = value },
  get backtestHistory() { return mockBacktestState.backtestHistory.value },
  set backtestHistory(value) { mockBacktestState.backtestHistory.value = value },
  startBacktest: vi.fn(),
  loadBacktestResults: vi.fn(),
  stopCurrentBacktest: vi.fn(),
  fetchBacktestHistory: vi.fn(),
  clearError: vi.fn(),
  resetBacktestState: vi.fn()
}

const mockStrategyState = {
  strategies: ref([])
}

const mockUseStrategyStore = {
  get strategies() { return mockStrategyState.strategies.value },
  set strategies(value) { mockStrategyState.strategies.value = value },
  fetchStrategies: vi.fn()
}

vi.mock('../../src/stores', () => ({
  useBacktestStore: () => mockUseBacktestStore,
  useStrategyStore: () => mockUseStrategyStore
}))

// Mock components
vi.mock('../../src/components/backtest/BacktestForm.vue', () => ({
  default: {
    name: 'BacktestForm',
    template: `
      <div data-testid="backtest-form">
        <form @submit.prevent="$emit('submit', mockConfig)">
          <input data-testid="strategy-id-input" v-model="strategyId" />
          <input data-testid="symbol-input" v-model="symbol" />
          <button type="submit" data-testid="submit-btn" :disabled="loading">
            {{ loading ? '运行中...' : '开始回测' }}
          </button>
          <button type="button" @click="$emit('reset')" data-testid="reset-btn">
            重置
          </button>
        </form>
      </div>
    `,
    props: ['loading'],
    emits: ['submit', 'reset'],
    setup() {
      const strategyId = 'test-strategy'
      const symbol = '000001.SZ'
      const mockConfig: BacktestConfig = {
        strategy_id: 'test-strategy',
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        initial_capital: 100000
      }
      return { strategyId, symbol, mockConfig }
    }
  }
}))

vi.mock('../../src/components/backtest/BacktestResults.vue', () => ({
  default: {
    name: 'BacktestResults',
    template: `
      <div data-testid="backtest-results">
        <div v-if="loading" data-testid="results-loading">
          正在计算回测结果...
        </div>
        <div v-else-if="result" data-testid="results-content">
          <div data-testid="total-return">总收益率: {{ result.total_return }}%</div>
          <div data-testid="sharpe-ratio">夏普比率: {{ result.sharpe_ratio }}</div>
          <button @click="$emit('export')" data-testid="export-btn">导出结果</button>
          <button @click="$emit('view-analysis')" data-testid="analysis-btn">查看分析</button>
        </div>
        <div v-else-if="error" data-testid="results-error">
          {{ error }}
        </div>
        <div v-else data-testid="results-empty">
          暂无回测结果
        </div>
      </div>
    `,
    props: ['result', 'loading', 'error'],
    emits: ['export', 'view-analysis']
  }
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  },
  ElProgress: {
    name: 'ElProgress',
    template: '<div class="el-progress progress-bar" :data-percentage="percentage">{{ percentage }}%</div>',
    props: ['percentage'],
    setup(props) {
      return { percentage: props.percentage }
    }
  }
}))

describe('BacktestView Integration', () => {
  const mockBacktestConfig: BacktestConfig = {
    strategy_id: 'test-strategy-123',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000
  }

  const mockBacktestResult: BacktestResult = {
    task_id: 'test-task-456',
    strategy_name: '测试策略',
    symbol: '000001.SZ',
    start_date: '2023-01-01',
    end_date: '2023-12-31',
    initial_capital: 100000,
    final_capital: 120000,
    total_return: 0.2,
    sharpe_ratio: 1.5,
    max_drawdown: -0.1,
    trades: [],
    created_at: '2023-01-01T00:00:00Z'
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    
    // 重置 mock store 状态
    mockBacktestState.isBacktesting.value = false
    mockBacktestState.backtestProgress.value = 0
    mockBacktestState.backtestResult.value = null
    mockBacktestState.backtestError.value = ''
    mockBacktestState.backtestHistory.value = []
    
    mockStrategyState.strategies.value = []
    
    // 重置所有mock函数
    vi.clearAllMocks()
  })

  const createWrapper = () => {
    return mount(BacktestView, {
      global: {
        plugins: [createPinia()]
      }
    })
  }

  describe('初始化', () => {
    it('应该在组件挂载时获取策略列表', () => {
      createWrapper()
      
      expect(mockUseStrategyStore.fetchStrategies).toHaveBeenCalled()
    })

    it('应该在组件挂载时获取回测历史', () => {
      createWrapper()
      
      expect(mockUseBacktestStore.fetchBacktestHistory).toHaveBeenCalled()
    })

    it('应该正确渲染回测表单和结果组件', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="backtest-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="backtest-results"]').exists()).toBe(true)
    })
  })

  describe('回测提交流程', () => {
    it('应该能够提交回测配置', async () => {
      mockUseBacktestStore.startBacktest.mockResolvedValue({ task_id: 'test-task' })
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('submit', mockBacktestConfig)
      
      expect(mockUseBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
    })

    it('应该在提交成功后显示成功消息', async () => {
      mockUseBacktestStore.startBacktest.mockResolvedValue({ task_id: 'test-task' })
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('submit', mockBacktestConfig)
      
      await wrapper.vm.$nextTick()
      
      const { ElMessage } = await import('element-plus')
      expect(ElMessage.success).toHaveBeenCalledWith('回测启动成功')
    })

    it('应该处理回测提交时的错误', async () => {
      mockUseBacktestStore.startBacktest.mockRejectedValue(new Error('回测启动失败'))
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('submit', mockBacktestConfig)
      
      await wrapper.vm.$nextTick()
      
      const { ElMessage } = await import('element-plus')
      expect(ElMessage.error).toHaveBeenCalledWith('回测启动失败')
    })

    it('应该在回测运行时禁用表单', () => {
      mockUseBacktestStore.isBacktesting = true
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      expect(backtestForm.props('loading')).toBe(true)
    })
  })

  describe('回测进度显示', () => {
    it('应该显示回测进度', async () => {
      mockBacktestState.isBacktesting.value = true
      mockBacktestState.backtestProgress.value = 45
      
      const wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      const progressBar = wrapper.find('.progress-bar')
      expect(progressBar.exists()).toBe(true)
      expect(progressBar.attributes('data-percentage')).toBe('45')
    })

    it('应该在回测完成时隐藏加载状态', async () => {
      mockBacktestState.isBacktesting.value = false
      mockBacktestState.backtestProgress.value = 100
      
      const wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      const progressBar = wrapper.find('.el-progress')
      expect(progressBar.exists()).toBe(false)
    })
  })

  describe('回测结果显示', () => {
    it('应该显示回测结果', () => {
      mockUseBacktestStore.backtestResult = mockBacktestResult
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      expect(backtestResults.props('result')).toEqual(mockBacktestResult)
      expect(backtestResults.props('loading')).toBe(false)
    })

    it('应该在回测运行时显示加载状态', () => {
      mockUseBacktestStore.isBacktesting = true
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      expect(backtestResults.props('loading')).toBe(true)
    })

    it('应该显示回测错误', () => {
      mockUseBacktestStore.backtestError = '回测计算失败'
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      expect(backtestResults.props('error')).toBe('回测计算失败')
    })
  })

  describe('回测停止功能', () => {
    it('应该能够停止正在运行的回测', async () => {
        mockUseBacktestStore.isBacktesting = true
      
      const wrapper = createWrapper()
      
      const stopButton = wrapper.find('[data-testid="stop-backtest-btn"]')
      if (stopButton.exists()) {
        await stopButton.trigger('click')
        expect(mockUseBacktestStore.stopCurrentBacktest).toHaveBeenCalled()
      }
    })

    it('应该在停止回测后显示确认消息', async () => {
        mockUseBacktestStore.isBacktesting = true
      mockUseBacktestStore.stopCurrentBacktest.mockResolvedValue()
      
      const wrapper = createWrapper()
      
      const stopButton = wrapper.find('[data-testid="stop-backtest-btn"]')
      if (stopButton.exists()) {
        await stopButton.trigger('click')
        await wrapper.vm.$nextTick()
        
        const { ElMessage } = require('element-plus')
        expect(ElMessage.warning).toHaveBeenCalledWith('回测已停止')
      }
    })
  })

  describe('表单重置功能', () => {
    it('应该能够重置回测表单', async () => {
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('reset')
      
      expect(mockUseBacktestStore.clearError).toHaveBeenCalled()
    })

    it('应该在重置时清除回测结果', async () => {
        mockUseBacktestStore.backtestResult = mockBacktestResult
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('reset')
      
      // 验证结果被清除的逻辑
      expect(mockUseBacktestStore.clearError).toHaveBeenCalled()
    })
  })

  describe('结果导出功能', () => {
    it('应该能够导出回测结果', async () => {
      mockBacktestState.backtestResult.value = mockBacktestResult
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      await backtestResults.vm.$emit('export')
      
      expect(wrapper.emitted('export-result')).toBeTruthy()
    })

    it('应该能够查看详细分析', async () => {
      mockBacktestState.backtestResult.value = mockBacktestResult
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      await backtestResults.vm.$emit('view-analysis')
      
      expect(wrapper.emitted('view-analysis')).toBeTruthy()
    })
  })

  describe('数据传递', () => {
    it('应该正确传递回测状态到表单组件', () => {
      mockUseBacktestStore.isBacktesting = true
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      expect(backtestForm.props('loading')).toBe(true)
    })

    it('应该正确传递回测结果到结果组件', () => {
      mockUseBacktestStore.backtestResult = mockBacktestResult
      mockUseBacktestStore.backtestError = '测试错误'
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      expect(backtestResults.props('result')).toEqual(mockBacktestResult)
      expect(backtestResults.props('error')).toBe('测试错误')
    })
  })

  describe('状态管理', () => {
    it('应该响应store状态变化', async () => {
      const wrapper = createWrapper()
      
      // 模拟回测开始
      mockUseBacktestStore.isBacktesting = true
      mockUseBacktestStore.backtestProgress = 30
      
      await wrapper.vm.$nextTick()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      
      expect(backtestForm.props('loading')).toBe(true)
      expect(backtestResults.props('loading')).toBe(true)
    })

    it('应该处理回测完成状态', async () => {
      const wrapper = createWrapper()
      
      // 模拟回测完成
      mockUseBacktestStore.isBacktesting = false
      mockUseBacktestStore.backtestResult = mockBacktestResult
      
      await wrapper.vm.$nextTick()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      
      expect(backtestForm.props('loading')).toBe(false)
      expect(backtestResults.props('result')).toEqual(mockBacktestResult)
    })
  })

  describe('组件间通信', () => {
    it('应该正确处理表单提交到结果显示的完整流程', async () => {
      // 1. 提交回测配置
      mockUseBacktestStore.startBacktest.mockResolvedValue({ task_id: 'test-task' })
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('submit', mockBacktestConfig)
      
      expect(mockUseBacktestStore.startBacktest).toHaveBeenCalledWith(mockBacktestConfig)
      
      // 2. 模拟回测运行状态
      mockUseBacktestStore.isBacktesting = true
      mockUseBacktestStore.backtestProgress = 50
      
      await wrapper.vm.$nextTick()
      
      expect(backtestForm.props('loading')).toBe(true)
      
      // 3. 模拟回测完成
      mockUseBacktestStore.isBacktesting = false
      mockUseBacktestStore.backtestResult = mockBacktestResult
      
      await wrapper.vm.$nextTick()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      expect(backtestResults.props('result')).toEqual(mockBacktestResult)
    })
  })

  describe('错误处理', () => {
    it('应该处理组件初始化时的错误', () => {
      mockUseStrategyStore.fetchStrategies.mockRejectedValue(new Error('初始化失败'))
      
      const wrapper = createWrapper()
      
      expect(wrapper.exists()).toBe(true)
    })

    it('应该处理回测历史获取失败', () => {
      mockUseBacktestStore.fetchBacktestHistory.mockRejectedValue(new Error('获取历史失败'))
      
      const wrapper = createWrapper()
      
      expect(wrapper.exists()).toBe(true)
    })

    it('应该显示回测错误状态', () => {
      mockUseBacktestStore.backtestError = '网络连接失败'
      
      const wrapper = createWrapper()
      
      const backtestResults = wrapper.findComponent({ name: 'BacktestResults' })
      expect(backtestResults.props('error')).toBe('网络连接失败')
    })
  })

  describe('边界情况', () => {
    it('应该处理空配置提交', async () => {
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('submit', null)
      
      // 应该不会调用startBacktest
      expect(mockUseBacktestStore.startBacktest).not.toHaveBeenCalled()
    })

    it('应该处理无效的回测配置', async () => {
      const invalidConfig = {
        strategy_id: '',
        symbol: '',
        start_date: '',
        end_date: '',
        initial_capital: 0
      }
      
      const wrapper = createWrapper()
      
      const backtestForm = wrapper.findComponent({ name: 'BacktestForm' })
      await backtestForm.vm.$emit('submit', invalidConfig)
      
      // 验证错误处理逻辑
      expect(wrapper.exists()).toBe(true)
    })
  })
})