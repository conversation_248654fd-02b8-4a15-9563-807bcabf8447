# -*- coding: utf-8 -*-
"""
Standard API Response Schemas
"""
from typing import Generic, TypeVar, Optional, Any
from pydantic import BaseModel, Field, ConfigDict, model_serializer
from datetime import datetime

T = TypeVar('T')


class ResponseSchema(BaseModel, Generic[T]):
    """
    标准API响应模型
    """
    code: int = Field(0, description="响应状态码，0表示成功")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    
    model_config = ConfigDict()
    
    @model_serializer
    def serialize_model(self):
        data = self.__dict__.copy()
        if isinstance(data.get('timestamp'), datetime):
            data['timestamp'] = data['timestamp'].isoformat()
        return data


class PaginatedData(BaseModel, Generic[T]):
    """分页数据模型"""
    items: list[T] = Field(description="数据项列表")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    pages: int = Field(description="总页数")
    
    @classmethod
    def create(cls, items: list[T], total: int, page: int, size: int):
        """创建分页数据"""
        pages = (total + size - 1) // size if size > 0 else 0
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )


def success(data: Any = None, msg: str = "success", error_code: str = None) -> dict:
    """
    成功响应
    """
    return {
        "code": 0,
        "message": msg,
        "data": data,
        "timestamp": datetime.now().isoformat(),
        "error_code": error_code
    }


def fail(code: int = -1, msg: str = "fail", data: Any = None, error_code: str = None) -> dict:
    """
    失败响应
    """
    return {
        "code": code,
        "message": msg,
        "data": data,
        "timestamp": datetime.now().isoformat(),
        "error_code": error_code
    }


def paginated_success(items: list, total: int, page: int, size: int, msg: str = "查询成功") -> dict:
    """
    分页成功响应
    """
    paginated_data = PaginatedData.create(items, total, page, size)
    return success(data=paginated_data.dict(), msg=msg)