# -*- coding: utf-8 -*-
"""
Standard API Response Schemas
"""
from typing import Generic, TypeVar, Optional, Any
from pydantic import BaseModel, Field

T = TypeVar('T')


class ResponseSchema(BaseModel, Generic[T]):
    """
    标准API响应模型
    """
    code: int = Field(0, description="响应状态码，0表示成功")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")


def success(data: Any = None, msg: str = "success") -> dict:
    """
    成功响应
    """
    return {
        "code": 0,
        "message": msg,
        "data": data
    }


def fail(code: int = -1, msg: str = "fail", data: Any = None) -> dict:
    """
    失败响应
    """
    return {
        "code": code,
        "message": msg,
        "data": data
    }