# -*- coding: utf-8 -*-
"""
K线数据提供者模块，负责获取和处理K线数据
"""
import pandas as pd
import numpy as np
import math
import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
import tushare as ts
import re
from pathlib import Path

# 导入abu原有模块
from abupy import ABuSymbolPd
from abupy.MarketBu import ABuMarket
from abupy.UtilBu import ABuDateUtil

# 导入我们的适配器
from backend.app.abupy_adapter.symbol_adapter import SymbolAdapter
from backend.app.abupy_adapter.data_cache_adapter import AbuDataCache, load_kline_df, dump_kline_df, save_kline_df, load_kline_df_net

# 导入自定义异常
from backend.app.core.exceptions import DataNotFoundError, ExternalAPIError, SymbolError, ValidationError

# 导入配置和模型
from backend.app.core.config import settings
from backend.app.schemas.market import KlineData, KlineItem

# 导入辅助函数
from .utils import safe_float


class KlineProvider:
    """K线数据提供者，负责获取和处理K线数据"""
    
    @staticmethod
    def get_kline_data_smart(symbol: str, start_date: Optional[str] = None, 
                            end_date: Optional[str] = None, 
                            period: str = "daily", pro_api=None) -> KlineData:
        """
        智能K线数据获取方法：优先从本地加载，不足时自动从tushare补全并保存
        
        策略：
        1. 首先尝试从本地加载完整数据
        2. 如果本地数据不存在或不完整，从tushare下载完整数据
        3. 将下载的数据保存到本地
        4. 返回完整数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            period: 周期类型，daily/weekly/monthly
            pro_api: tushare的pro_api对象，如果为None则创建新的
            
        Returns:
            KlineData: K线数据
        """
        logging.info(f"KlineProvider: 智能加载 {symbol} 的K线数据，日期: {start_date}-{end_date}")
        
        # 设置默认日期
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        
        try:
            # 首先尝试从本地加载完整数据
            local_data = KlineProvider.get_kline_data(symbol, start_date, end_date, period, 'local')
            logging.info(f"KlineProvider: 成功从本地加载 {symbol} 的完整数据，共 {len(local_data.data)} 条记录")
            return local_data
        except DataNotFoundError as e:
            logging.info(f"KlineProvider: 本地数据不足或不存在 ({e})，开始从tushare下载 {symbol} 的数据")
            
            try:
                # 从tushare下载完整数据
                tushare_data = KlineProvider.get_kline_data(symbol, start_date, end_date, period, 'tushare', pro_api)
                logging.info(f"KlineProvider: 成功从tushare下载 {symbol} 的数据，共 {len(tushare_data.data)} 条记录")
                
                # 保存到本地
                try:
                    KlineProvider.save_kline_to_local_h5(symbol, tushare_data)
                    logging.info(f"KlineProvider: 成功保存 {symbol} 的数据到本地")
                except Exception as save_error:
                    logging.warning(f"KlineProvider: 保存 {symbol} 数据到本地失败: {save_error}")
                
                return tushare_data
                
            except Exception as tushare_error:
                logging.error(f"KlineProvider: 从tushare下载 {symbol} 数据失败: {tushare_error}")
                raise DataNotFoundError(f"无法获取 {symbol} 的K线数据：本地数据不足，tushare下载也失败 - {tushare_error}")

    @staticmethod
    def get_kline_data(symbol: str, start_date: Optional[str] = None, 
                       end_date: Optional[str] = None, 
                       period: str = "daily", data_source: str = 'tushare',
                       pro_api=None) -> KlineData:
        """
        获取K线数据的统一公共入口。
        根据数据源，分发到不同的内部处理方法。
        
        Args:
            symbol: 股票代码
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            period: 周期类型，daily/weekly/monthly
            data_source: 数据源，tushare/local
            pro_api: tushare的pro_api对象，如果为None则创建新的
            
        Returns:
            KlineData: K线数据
            
        Raises:
            SymbolError: 当股票代码格式无效时
            ValidationError: 当参数无效时
            DataNotFoundError: 当没有找到K线数据时
            ExternalAPIError: 当外部API调用失败时
        """
        logging.info(f"KlineProvider: 正在为 {symbol} 获取K线数据，源: {data_source}，日期: {start_date}-{end_date}")
        try:
            # 验证股票代码
            if not symbol:
                raise SymbolError(
                    message="股票代码不能为空",
                    data={}
                )
                
            # 验证period参数
            valid_periods = ["daily", "weekly", "monthly"]
            if period not in valid_periods:
                raise ValidationError(
                    message=f"无效的周期类型: {period}",
                    data={"valid_periods": valid_periods}
                )
            
            # 验证日期格式
            if start_date and (len(start_date) != 8 or not start_date.isdigit()):
                raise ValidationError(
                    message=f"开始日期格式错误: {start_date}, 应为YYYYMMDD格式",
                    data={"start_date": start_date}
                )
                
            if end_date and (len(end_date) != 8 or not end_date.isdigit()):
                raise ValidationError(
                    message=f"结束日期格式错误: {end_date}, 应为YYYYMMDD格式",
                    data={"end_date": end_date}
                )
            
            # 使用Symbol适配器验证股票代码格式
            SymbolAdapter.validate_symbol(symbol)
            
            # 确定市场类型
            market = KlineProvider.get_market_from_symbol(symbol)
            
            # 转换日期格式
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            if not start_date:
                # 默认获取1年数据
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                
            # 根据数据源分发到不同的处理方法
            if data_source == 'local':
                return KlineProvider.get_kline_from_local_h5(symbol, start_date, end_date, period)
            elif data_source == 'tushare':
                # 转换为tushare格式的代码并调用统一的tushare获取方法
                ts_symbol = KlineProvider.convert_to_tushare_symbol(symbol)
                return KlineProvider.get_kline_from_tushare(ts_symbol, start_date, end_date, period, pro_api)
            else:
                # 如果数据源未知或为None，默认行为应该是tushare
                if data_source != 'tushare':
                    logging.warning(f"未知的数据源 '{data_source}'，将默认使用tushare。")
                
                # 复用 data_source == 'tushare' 的逻辑
                ts_symbol = KlineProvider.convert_to_tushare_symbol(symbol)
                return KlineProvider.get_kline_from_tushare(ts_symbol, start_date, end_date, period, pro_api)
            
        except Exception as e:
            if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError, SymbolError)):
                raise
            logging.error(f"获取K线数据失败: {str(e)}")
            raise ExternalAPIError(
                message=f"获取K线数据失败: {str(e)}",
                data={"symbol": symbol, "period": period}
            )
    
    @staticmethod
    def get_kline_from_local_h5(symbol: str, start_date: str, end_date: str, period: str) -> KlineData:
        """从本地HDF5文件获取K线数据，并增加了对多种key格式的健壮性支持。"""
        logging.info(f"从本地HDF5文件 '{settings.LOCAL_DATA_PATH}' 获取 '{symbol}' 的K线数据。")
        
        # 处理abupy格式的符号转换（如sh000300 -> 000300.SH）
        normalized_symbol = symbol
        if symbol.startswith('sh'):
            # sh000300 -> 000300.SH
            code = symbol[2:]
            normalized_symbol = f"{code}.SH"
        elif symbol.startswith('sz'):
            # sz000001 -> 000001.SZ
            code = symbol[2:]
            normalized_symbol = f"{code}.SZ"
        
        # 尝试多种可能的key格式，优先尝试纯数字格式（数据格式更标准）
        possible_keys = [
            f'/{normalized_symbol.split(".")[0]}', # 优先尝试纯数字的key, e.g., /000300（数据格式更标准）
            f'/{normalized_symbol}',                # 再尝试完整的key, e.g., /000300.SH
            f'/{symbol.split(".")[0]}',             # 原始符号的纯数字格式
            f'/{symbol}',                           # 原始符号的完整格式
        ]
        
        df = None
        found_key = None
        
        try:
            with pd.HDFStore(settings.LOCAL_DATA_PATH, mode='r') as store:
                available_keys = store.keys()
                for key in possible_keys:
                    if key in available_keys:
                        df = store[key]
                        found_key = key
                        logging.info(f"在本地HDF5文件中使用key '{found_key}' 找到了 '{symbol}' 的数据。")
                        break
            
            if df is None:
                raise DataNotFoundError(
                    message=f"在本地HDF5文件中未找到股票 '{symbol}' 的数据。已尝试的keys: {possible_keys}",
                    data={"symbol": symbol, "file": settings.LOCAL_DATA_PATH, "tried_keys": possible_keys}
                )
            
            # 用一次性构造替代 reset_index，避免触发 Index.insert 中的 isinstance(..., str)
            idx_name = df.index.name or "index"
            # 修复pandas新版本兼容性问题：使用to_numpy()替代to_pydatetime()
            if hasattr(df.index, 'to_pydatetime'):
                data = {idx_name: df.index.to_pydatetime()}
            else:
                # 对于DatetimeIndex，直接转换为numpy数组
                data = {idx_name: df.index.to_numpy()}
            for col in df.columns:
                data[col] = df[col].to_numpy(copy=False)
            df = pd.DataFrame(data, columns=[idx_name] + list(df.columns))

            # 如需衍生日期列，可在此处理
            # df["date"] = pd.to_datetime(df[idx_name])
            # df["date_int"] = pd.to_datetime(df[idx_name]).dt.strftime("%Y%m%d").astype(int)
        except DataNotFoundError:
            logging.warning(f"本地未找到 {symbol} 的数据，尝试从Tushare下载...")
            try:
                # 转换为tushare格式的代码
                ts_symbol = KlineProvider.convert_to_tushare_symbol(symbol)
                # 判断是否为指数
                is_index = KlineProvider.is_index(symbol)
                
                # 根据类型选择合适的接口
                if is_index:
                    logging.info(f"检测到 {symbol} 为指数，使用指数接口下载数据...")
                    kline_data_from_tushare = KlineProvider.get_index_kline_from_tushare(ts_symbol, start_date, end_date, period)
                else:
                    logging.info(f"检测到 {symbol} 为股票，使用股票接口下载数据...")
                    kline_data_from_tushare = KlineProvider.get_stock_kline_from_tushare(ts_symbol, start_date, end_date, period)
                
                # 将数据保存到HDF5文件
                KlineProvider.save_kline_to_local_h5(symbol, kline_data_from_tushare)
                
                logging.info(f"已成功从Tushare下载并保存 {symbol} 的数据到本地。")
                return kline_data_from_tushare
            except Exception as e:
                logging.error(f"从Tushare下载或保存 {symbol} 数据失败: {e}", exc_info=True)
                # 重新抛出原始的DataNotFoundError，并附加Tushare的错误信息
                raise DataNotFoundError(
                    message=f"在本地未能找到 '{symbol}' 的数据，并且从Tushare下载也失败了。Tushare错误: {e}",
                    data={"symbol": symbol, "file": settings.LOCAL_DATA_PATH, "tushare_error": str(e)}
                )


        # 检查必要的列是否存在
        required_cols = {'trade_date', 'open', 'high', 'low', 'close', 'vol'}
        if not required_cols.issubset(df.columns):
            raise DataNotFoundError(
                message=f"股票 '{symbol}' 的本地数据缺少必要列。需要: {required_cols}, 实际: {df.columns.tolist()}",
                data={"symbol": symbol}
            )

        # 确保 'trade_date' 列是 datetime 类型
        # 处理20210104这种格式的日期
        if df['trade_date'].dtype == 'object' or df['trade_date'].dtype == 'int64':
            df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
        else:
            df['trade_date'] = pd.to_datetime(df['trade_date'])

        # 按日期过滤
        start = pd.to_datetime(start_date, format='%Y%m%d')
        end = pd.to_datetime(end_date, format='%Y%m%d')
        df_filtered = df[(df['trade_date'] >= start) & (df['trade_date'] <= end)]

        if df_filtered.empty:
            raise DataNotFoundError(
                message=f"在指定日期范围 {start_date}-{end_date} 内未找到 '{symbol}' 的本地数据。",
                data={"symbol": symbol, "start": start_date, "end": end_date}
            )
        
        # 如果df_filtered不为空，则调用转换函数并返回结果
        logging.info(f"成功筛选出 {len(df_filtered)} 条 '{symbol}' 的本地数据，准备转换为KlineData。")
        return KlineProvider.convert_df_to_kline_data(df_filtered, symbol)

    @staticmethod
    def convert_df_to_kline_data(df: pd.DataFrame, symbol: str) -> KlineData:
        """将DataFrame转换为KlineData Pydantic模型"""
        df = df.copy()  # 创建副本以避免修改原始数据
        if 'vol' in df.columns:
            df.rename(columns={'vol': 'volume'}, inplace=True)

        # 确保 'trade_date' 列存在且为datetime类型，以便进行日期操作
        # 注意：我们期望上游函数（如get_kline_from_local_h5）已经处理了日期的转换
        # 但作为健壮性检查，我们再次确认
        date_col = 'trade_date' # 假设日期列名为 'trade_date'
        if date_col not in df.columns:
            # 如果没有 'trade_date'，尝试寻找其他可能的日期列，例如 'datetime' 或索引
            if isinstance(df.index, pd.DatetimeIndex):
                df[date_col] = df.index.to_series()
                logging.warning(f"'{symbol}' 的DataFrame中缺少 'trade_date' 列，已从DatetimeIndex回填。")
            else:
                raise ValidationError(f"DataFrame for '{symbol}' is missing a date column ('trade_date').")

        if not pd.api.types.is_datetime64_any_dtype(df[date_col]):
            # 处理20210104这种格式的日期
            if df[date_col].dtype == 'object' or df[date_col].dtype == 'int64':
                df[date_col] = pd.to_datetime(df[date_col], format='%Y%m%d')
            else:
                df[date_col] = pd.to_datetime(df[date_col])
            logging.warning(f"'{symbol}' 的 '{date_col}' 列不是datetime类型，已强制转换。")

        kline_items = []
        for _, row in df.iterrows(): # index不再需要，用_代替
            item = KlineItem(
                date=row[date_col].strftime('%Y-%m-%d'), # 从日期列获取日期
                open=safe_float(row['open']),
                high=safe_float(row['high']),
                low=safe_float(row['low']),
                close=safe_float(row['close']),
                volume=safe_float(row.get('volume', 0)), # 使用.get以增加健壮性
            )
            kline_items.append(item)

        market = KlineProvider.get_market_from_symbol(symbol)
        period = 'daily'
        name = symbol
        latest_date = df[date_col].max().strftime('%Y-%m-%d') if not df.empty else ''

        return KlineData(
            symbol=symbol,
            name=name,
            market=market,
            period=period,
            data=kline_items,
            latest_date=latest_date
        )

            
    def _generate_hdf5_key(self, symbol: str) -> str:
        """从股票代码生成HDF5的key"""
        # 我们从任何格式的symbol中提取出6位数字代码，作为最终的钥匙。
        match = re.search(r'(\d{6})', symbol)
        if not match:
            # 建议：可以为此创建一个自定义异常，例如 H5KeyGenerationError
            raise KeyError(f"无法从符号 '{symbol}' 中提取出6位数字代码。")
        
        # HDF5中的key格式是 '/数字'
        return f"/{match.group(1)}"

    def get_kline_from_local(self, symbol: str, start_date: str, end_date: str) -> KlineData:
        """从本地HDF5文件加载K线数据。"""
        import re # 确保导入了 re 模块

        LOCAL_DATA_PATH = Path(settings.LOCAL_DATA_PATH) if hasattr(settings, 'LOCAL_DATA_PATH') else Path('data/market_data.h5')
        
        if not LOCAL_DATA_PATH.exists():
            logging.error(f"本地数据文件不存在: {LOCAL_DATA_PATH}")
            raise FileNotFoundError(f"本地测试数据文件未找到: {LOCAL_DATA_PATH}")
        # 声明一个变量，以便在日志中追踪
        symbol_key = ""
        try:
            symbol_key = self._generate_hdf5_key(symbol)
            
            logging.info(f"根据侦察情报，为符号 '{symbol}' 生成的最终HDF5钥匙是: '{symbol_key}'")
            df = pd.read_hdf(LOCAL_DATA_PATH, key=symbol_key)

            # --- 核心修改：确保返回的数据包含abupy需要的列 ---
            # abupy需要 'date' 作为 int 类型 (YYYYMMDD) 和 'datetime' 作为索引
            if 'trade_date' in df.columns: # Tushare返回的是trade_date
                df.rename(columns={'trade_date': 'date_str'}, inplace=True)
                df['date'] = df['date_str'].astype(int)
                df['datetime'] = pd.to_datetime(df['date_str'])
                df.set_index('datetime', inplace=True)
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            mask = (df.index >= start_dt) & (df.index <= end_dt)
            filtered_df = df.loc[mask]

            if filtered_df.empty:
                raise DataNotFoundError(
                    message=f"在指定日期范围 {start_date}-{end_date} 内未找到 '{symbol}' 的本地数据。",
                    data={"symbol": symbol, "start": start_date, "end": end_date}
                )

            logging.info(f"成功从本地加载并筛选了 {symbol} 的 {len(filtered_df)} 条数据。")
            
            # 重置索引以便传递给通用转换函数
            filtered_df.reset_index(inplace=True)

            # 调用标准转换函数，确保逻辑统一
            return KlineProvider.convert_df_to_kline_data(filtered_df, symbol)
        except KeyError:
            logging.error(f"在本地数据文件 {LOCAL_DATA_PATH} 中找不到键: {symbol_key}")
            raise DataNotFoundError(
                message=f"在本地数据文件中找不到 {symbol} 的数据",
                data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
            )
        except Exception as e:
            logging.error(f"从本地文件读取数据时发生未知错误: {e}")
            raise ExternalAPIError(
                message=f"从本地文件读取数据时发生错误: {str(e)}",
                data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
            )
            
    @staticmethod
    def save_kline_to_local_h5(symbol: str, kline_data: KlineData) -> None:
        """将K线数据保存到本地HDF5文件
        
        Args:
            symbol: 股票代码
            kline_data: K线数据对象
        """
        # 统一使用代码部分作为key，例如 '600519.SH' -> '600519'
        symbol_key = symbol.split('.')[0]
        h5_key = f'/{symbol_key}'
        
        # 将KlineData转换为DataFrame
        df = pd.DataFrame([
            {
                'trade_date': item.date,
                'open': item.open,
                'high': item.high,
                'low': item.low,
                'close': item.close,
                'vol': item.volume
            }
            for item in kline_data.data
        ])
        
        # 确保trade_date列是字符串格式的YYYYMMDD
        df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y%m%d')
        
        # 按日期排序
        df = df.sort_values('trade_date')
        
        # 保存到HDF5文件
        try:
            df.to_hdf(settings.LOCAL_DATA_PATH, key=h5_key, mode='a', format='table')
            logging.info(f"已将 {symbol} 的数据保存到本地HDF5文件，key: {h5_key}")
        except Exception as e:
            logging.error(f"保存 {symbol} 数据到本地HDF5文件失败: {e}", exc_info=True)
            raise ExternalAPIError(
                message=f"保存数据到本地文件失败: {str(e)}",
                data={"symbol": symbol, "file": settings.LOCAL_DATA_PATH}
            )
    
    @staticmethod
    def get_kline_from_tushare(symbol: str, start_date: str, end_date: str, period: str = "daily", pro_api=None) -> KlineData:
        """
        从Tushare获取K线数据的通用方法
        
        Args:
            symbol: 股票代码（Tushare格式，如000001.SZ）
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            period: 周期类型，daily/weekly/monthly
            pro_api: tushare的pro_api对象，如果为None则创建新的
            
        Returns:
            KlineData: K线数据
        """
        # 判断symbol类型
        is_index = KlineProvider.is_index(symbol)
        is_etf = KlineProvider.is_etf(symbol)
        
        if is_index:
            return KlineProvider.get_index_kline_from_tushare(symbol, start_date, end_date, period, pro_api)
        elif is_etf:
            return KlineProvider.get_etf_kline_from_tushare(symbol, start_date, end_date, period, pro_api)
        else:
            return KlineProvider.get_stock_kline_from_tushare(symbol, start_date, end_date, period, pro_api)
    
    @staticmethod
    def get_stock_kline_from_tushare(symbol: str, start_date: str, end_date: str, period: str, pro_api=None) -> KlineData:
        """
        通过Tushare API(A股/港股)或abu(其他市场)从网络获取K线数据。
        """
        market = KlineProvider.get_market_from_symbol(symbol)

        # A股和港股走Tushare通道
        if market in ['CN', 'HK']:
            try:
                # 如果没有传入pro_api，则创建一个
                if pro_api is None:
                    if not settings.TUSHARE_TOKEN:
                        raise ValidationError(
                            message="缺失Tushare API Token",
                            error_code="MISSING_API_TOKEN"
                        )
                    ts.set_token(settings.TUSHARE_TOKEN)
                    pro_api = ts.pro_api()
                
                logging.info(f"调用Tushare股票接口获取 {symbol}...")
                if market == 'HK':
                    logging.debug(f"Attempting Tushare API call for HK. Symbol: {symbol}, Start: {start_date}, End: {end_date}")
                    df = pro_api.hk_daily(ts_code=symbol, start_date=start_date, end_date=end_date)
                else: # A股
                    df = pro_api.daily(ts_code=symbol, start_date=start_date, end_date=end_date)
                    logging.debug(f"使用Tushare daily获取股票 {symbol} 数据")
                    
                if df is None or df.empty:
                    raise DataNotFoundError(
                        message=f"未找到{symbol}的K线数据",
                        data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
                    )

                # 关键修复：确保TuShare数据按日期升序排列
                df = df.sort_values(by='trade_date', ascending=True)
                logging.debug(f"TuShare数据排序后，首行日期: {df['trade_date'].iloc[0]}, 末行日期: {df['trade_date'].iloc[-1]}")

                # 数据转换和返回
                kline_items = [
                    KlineItem(
                        date=row.trade_date,
                        open=safe_float(row.open),
                        high=safe_float(row.high),
                        low=safe_float(row.low),
                        close=safe_float(row.close),
                        volume=safe_float(row.vol)
                    )
                    for row in df.itertuples()
                ]
                latest_date = df['trade_date'].max() if not df.empty else ''
                return KlineData(symbol=symbol, name="", market=market, period=period, data=kline_items, latest_date=latest_date)

            except Exception as e:
                if isinstance(e, DataNotFoundError):
                    raise
                logging.error(f"通过Tushare获取 {symbol} 数据时出错: {e}", exc_info=True)
                raise ExternalAPIError(
                    message=f"通过Tushare获取K线数据失败: {str(e)}",
                    data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
                )
        # 其他市场 (如美股) 走abu通道
        else:
            try:
                start_date_abu = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:]}"
                end_date_abu = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:]}"
                
                try:
                    kl_pd = ABuSymbolPd.make_kl_df(symbol, start=start_date_abu, end=end_date_abu)
                except Exception as e:
                    logging.error(f"ABU API调用失败: {str(e)}", exc_info=True)
                    raise ExternalAPIError(
                        message=f"ABU API获取K线数据失败: {str(e)}",
                        data={"symbol": symbol, "market": market, "period": period}
                    )
                
                if kl_pd is None or kl_pd.empty:
                    raise DataNotFoundError(
                        message=f"未找到{symbol}的K线数据",
                        data={"symbol": symbol, "market": market, "start_date": start_date, "end_date": end_date, "period": period}
                    )
                
                kline_items = []
                for _, row in kl_pd.iterrows():
                    try:
                        date_str = row.name.strftime('%Y%m%d') if isinstance(row.name, (pd.Timestamp, datetime)) else str(row.get('date', ''))
                        
                        item = KlineItem(
                            date=date_str,
                            open=safe_float(row['open']),
                            high=safe_float(row['high']),
                            low=safe_float(row['low']),
                            close=safe_float(row['close']),
                            volume=safe_float(row['volume']),
                            amount=None,
                            turnover_rate=None,
                            change_rate=None
                        )
                        kline_items.append(item)
                    except Exception as e:
                        logging.warning(f"处理K线数据行出错: {str(e)}")
                        continue
                
                if not kline_items:
                    raise DataNotFoundError(
                        message=f"未能成功转换{symbol}的K线数据",
                        data={"symbol": symbol, "market": market, "period": period}
                    )
                
                name = SymbolAdapter.get_symbol_name(symbol)
                indicators = KlineProvider.calculate_indicators_from_abu(kl_pd)
                latest_date = kl_pd.index[-1].strftime('%Y%m%d') if isinstance(kl_pd.index[-1], pd.Timestamp) else str(kl_pd.index[-1])
                
                return KlineData(
                    symbol=symbol,
                    name=name,
                    market=market,
                    period=period,
                    data=kline_items,
                    latest_date=latest_date,
                    indicators=indicators
                )
                
            except Exception as e:
                if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError, SymbolError)):
                    raise
                logging.error(f"获取其他市场 K线数据失败: {str(e)}", exc_info=True)
                raise ExternalAPIError(
                    message=f"获取{market}市场K线数据失败: {str(e)}",
                    data={"symbol": symbol, "market": market, "period": period}
                )

    @staticmethod
    def get_index_kline_from_tushare(symbol: str, start_date: str, end_date: str, period: str, pro_api=None) -> KlineData:
        """从Tushare获取指数K线数据"""
        logging.info(f"从Tushare获取指数 '{symbol}' 的K线数据。")
        if pro_api is None:
            pro_api = ts.pro_api(settings.TUSHARE_TOKEN)

        # 使用Tushare Pro的index_daily接口获取指数数据
        df = pro_api.index_daily(ts_code=symbol, start_date=start_date, end_date=end_date)

        if df is None or df.empty:
            logging.info(f"Exception kline_pd symbol:{symbol} e:arg code :{symbol} format dt support")
            raise DataNotFoundError(
                message=f"未找到指数{symbol}的K线数据",
                data={"symbol": symbol, "start": start_date, "end": end_date}
            )

        # 关键修复：确保指数数据按日期升序排列
        df = df.sort_values(by='trade_date', ascending=True)
        logging.debug(f"指数数据排序后，首行日期: {df['trade_date'].iloc[0]}, 末行日期: {df['trade_date'].iloc[-1]}")

        # 数据转换和返回
        kline_items = [
            KlineItem(
                date=row.trade_date,
                open=safe_float(row.open),
                high=safe_float(row.high),
                low=safe_float(row.low),
                close=safe_float(row.close),
                volume=safe_float(row.vol)
            )
            for row in df.itertuples()
        ]

        market = 'SH' if symbol.endswith('.SH') else 'SZ' if symbol.endswith('.SZ') else 'Unknown'
        latest_date = df['trade_date'].max()
        name = SymbolAdapter.get_symbol_name(symbol)
        indicators = KlineProvider.calculate_indicators(df)

        return KlineData(
            symbol=symbol,
            name=name,
            market=market,
            period=period,
            data=kline_items,
            latest_date=latest_date,
            indicators=indicators
        )

    @staticmethod
    def get_etf_kline_from_tushare(symbol: str, start_date: str, end_date: str, period: str, pro_api=None) -> KlineData:
        """从Tushare获取ETF基金K线数据"""
        logging.info(f"从Tushare获取ETF '{symbol}' 的K线数据。")
        
        try:
            # 如果没有传入pro_api，则创建一个
            if pro_api is None:
                if not settings.TUSHARE_TOKEN:
                    raise ValidationError(
                        message="缺失Tushare API Token",
                        error_code="MISSING_API_TOKEN"
                    )
                ts.set_token(settings.TUSHARE_TOKEN)
                pro_api = ts.pro_api()
            
            logging.info(f"调用Tushare fund_daily接口获取ETF {symbol}...")
            # 使用fund_daily接口获取ETF数据
            df = pro_api.fund_daily(ts_code=symbol, start_date=start_date, end_date=end_date)
            logging.debug(f"使用Tushare fund_daily获取ETF {symbol} 数据")
                
            if df is None or df.empty:
                raise DataNotFoundError(
                    message=f"未找到ETF {symbol}的K线数据",
                    data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
                )

            # 关键修复：确保ETF数据按日期升序排列
            df = df.sort_values(by='trade_date', ascending=True)
            logging.debug(f"ETF数据排序后，首行日期: {df['trade_date'].iloc[0]}, 末行日期: {df['trade_date'].iloc[-1]}")

            # 数据转换和返回
            kline_items = [
                KlineItem(
                    date=row.trade_date,
                    open=safe_float(row.open),
                    high=safe_float(row.high),
                    low=safe_float(row.low),
                    close=safe_float(row.close),
                    volume=safe_float(row.vol),
                    amount=safe_float(getattr(row, 'amount', None)),
                    turnover_rate=safe_float(getattr(row, 'turnover_rate', None)),
                    change_rate=safe_float(getattr(row, 'pct_chg', None))
                )
                for row in df.itertuples()
            ]

            market = 'SH' if symbol.endswith('.SH') else 'SZ' if symbol.endswith('.SZ') else 'Unknown'
            latest_date = df['trade_date'].max()
            name = SymbolAdapter.get_symbol_name(symbol)
            indicators = KlineProvider.calculate_indicators(df)

            return KlineData(
                symbol=symbol,
                name=name,
                market=market,
                period=period,
                data=kline_items,
                latest_date=latest_date,
                indicators=indicators
            )
            
        except Exception as e:
            if isinstance(e, (DataNotFoundError, ExternalAPIError, ValidationError, SymbolError)):
                raise
            logging.error(f"获取ETF {symbol} K线数据失败: {str(e)}", exc_info=True)
            raise ExternalAPIError(
                message=f"获取ETF K线数据失败: {str(e)}",
                data={"symbol": symbol, "start_date": start_date, "end_date": end_date}
            )



    
    @staticmethod
    def get_market_from_symbol(symbol: str) -> str:
        """
        根据股票代码判断市场类型
        
        Args:
            symbol: 股票代码
            
        Returns:
            市场类型: CN, US, HK 等
        """
        # 优先处理Tushare格式
        if '.' in symbol:
            suffix = symbol.split('.')[-1].upper()
            if suffix == 'SH' or suffix == 'SZ':
                return 'CN'
            elif suffix == 'HK':
                return 'HK'
            else:
                # 假设其他带点的都是美股
                return 'US'

        # 处理abupy格式
        if symbol.startswith(('sh', 'sz', 'SH', 'SZ')):
            return 'CN'
        if symbol.startswith(('hk', 'HK')):
            return 'HK'

        # 处理纯数字代码
        if re.match(r'^[036]\d{5}$', symbol):
            return 'CN'
        if re.match(r'^\d{5}$', symbol):
            return 'HK'

        # 默认美股
        return 'US'

    @staticmethod
    def is_etf(symbol: str) -> bool:
        """判断一个symbol是否是ETF基金。"""
        # 明确的ETF代码列表
        known_etfs = {
            '510300.SH',  # 沪深300ETF
            '510500.SH',  # 中证500ETF
            '510050.SH',  # 上证50ETF
            '159919.SZ',  # 沪深300ETF
            '159915.SZ',  # 创业板ETF
        }

        if symbol in known_etfs:
            return True
            
        # 检查ETF代码格式
        if re.fullmatch(r'\d{6}\.(SH|SZ)', symbol):
            code = symbol.split('.')[0]
            suffix = symbol.split('.')[1]
            
            # 沪市ETF：5开头
            if suffix == 'SH' and code.startswith('5'):
                return True
                
            # 深市ETF：15开头
            if suffix == 'SZ' and code.startswith('15'):
                return True
                
        return False

    @staticmethod
    def is_index(symbol: str) -> bool:
        """判断一个symbol是否是指数，使用更可靠的规则。"""
        # 明确的指数代码列表
        known_indices = {
            '000001.SH',  # 上证指数
            '399001.SZ',  # 深证成指
            '000300.SH',  # 沪深300
            '000905.SH',  # 中证500
            '399006.SZ',  # 创业板指
            '000016.SH',  # 上证50
            'HSI.HK',     # 恒生指数
            'HSCEI.HK',   # 恒生中国企业指数
            'HSCCI.HK',   # 恒生中国内地指数
        }

        if symbol in known_indices:
            return True
            
        # 检查常见指数代码前缀
        index_prefixes = {
            '000': '.SH',  # 上证系列指数
            '399': '.SZ',  # 深证系列指数
            '930': '.CSI'  # 中证系列指数
        }
        
        for prefix, suffix in index_prefixes.items():
            if symbol.startswith(prefix) and symbol.endswith(suffix):
                return True

        # 正则表达式匹配股票代码格式：6位数字 + .SH 或 .SZ 后缀
        # 对于A股，600、601、603开头为上交所主板，000开头为深交所主板，002开头为中小板，300开头为创业板，688开头为科创板
        if re.fullmatch(r'\d{6}\.(SH|SZ)', symbol):
            code = symbol.split('.')[0]
            # 检查是否为常见股票代码前缀
            if (code.startswith(('600', '601', '603', '605', '688')) or 
                code.startswith(('000', '001', '002', '003', '004', '300'))):
                return False
                
        # 对于港股和美股的明确判断
        if symbol.endswith('.HK') and re.fullmatch(r'\d{4,5}\.HK', symbol):
            return False  # 港股股票通常是4-5位数字
            
        if symbol.endswith('.US') and not symbol[:-3].isdigit():
            return False  # 美股股票通常不是纯数字代码
            
        # 如果代码以SH或SZ开头，且后面是6位数字，则为股票
        if (symbol.upper().startswith('SH') or symbol.upper().startswith('SZ')) and len(symbol) == 8 and symbol[2:].isdigit():
            return False
            
        # 如果代码以HK开头，且后面是4-5位数字，则为港股
        if symbol.upper().startswith('HK') and len(symbol) in [6, 7] and symbol[2:].isdigit():
            return False
            
        # 如果不符合股票格式，且不在明确的指数列表中，记录警告并返回默认判断
        logging.warning(f"无法明确判断 '{symbol}' 是股票还是指数，将默认其为股票。")
        return False  # 默认为股票而非指数，避免错误地使用指数API
    
    @staticmethod
    def convert_to_tushare_symbol(symbol: str) -> str:
        """
        将通用股票代码转换为tushare格式的代码
        
        Args:
            symbol: 通用股票代码
            
        Returns:
            tushare格式的股票代码
        """
        # 已经是tushare格式
        if '.' in symbol:
            return symbol
            
        # 处理A股
        if symbol.startswith(('sh', 'sz', 'SH', 'SZ')):
            code = symbol[2:]
            prefix = symbol[:2].lower()
            if prefix == 'sh':
                return f"{code}.SH"
            elif prefix == 'sz':
                return f"{code}.SZ"
        
        # 处理港股
        if symbol.startswith(('hk', 'HK')):
            code = symbol[2:]
            return f"{code}.HK"
            
        # 处理纯数字代码
        if symbol.isdigit():
            if len(symbol) == 6:
                # A股代码判断
                if symbol.startswith(('0', '3', '6')):
                    if symbol.startswith('6'):
                        return f"{symbol}.SH"
                    else:
                        return f"{symbol}.SZ"
            elif len(symbol) == 5:
                # 港股代码
                return f"{symbol}.HK"
                
        # 默认返回原代码
        return symbol
    
    @staticmethod
    def calculate_indicators(df: pd.DataFrame) -> Dict[str, List[float]]:
        """
        计算技术指标
        
        Args:
            df: K线数据DataFrame
            
        Returns:
            技术指标字典
        """
        indicators = {}
        
        try:
            # 确保close列存在
            if 'close' not in df.columns:
                logging.warning("DataFrame中没有'close'列，无法计算指标")
                return {}
                
            # 计算移动平均线
            for period in [5, 10, 20, 30, 60]:
                if len(df) >= period:
                    ma_key = f"ma{period}"
                    ma_values = df['close'].rolling(window=period).mean().fillna(0).tolist()
                    indicators[ma_key] = [round(float(x), 2) for x in ma_values]
            
            # 计算MACD
            if len(df) >= 26:
                exp1 = df['close'].ewm(span=12, adjust=False).mean()
                exp2 = df['close'].ewm(span=26, adjust=False).mean()
                macd = exp1 - exp2
                signal = macd.ewm(span=9, adjust=False).mean()
                hist = macd - signal
                
                indicators['macd'] = [round(float(x), 2) for x in macd.tolist()]
                indicators['macd_signal'] = [round(float(x), 2) for x in signal.tolist()]
                indicators['macd_hist'] = [round(float(x), 2) for x in hist.tolist()]
            
            # 计算RSI
            if len(df) >= 14:
                delta = df['close'].diff()
                gain = delta.where(delta > 0, 0)
                loss = -delta.where(delta < 0, 0)
                
                avg_gain = gain.rolling(window=14).mean()
                avg_loss = loss.rolling(window=14).mean()
                
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                
                indicators['rsi'] = [round(float(x), 2) if not math.isnan(x) else 50 for x in rsi.tolist()]
                
        except Exception as e:
            logging.error(f"计算技术指标时出错: {e}", exc_info=True)
            
        return indicators
    
    @staticmethod
    def calculate_indicators_from_abu(kl_pd: pd.DataFrame) -> Dict[str, List[float]]:
        """
        从abu格式的K线数据计算技术指标
        
        Args:
            kl_pd: abu格式的K线数据DataFrame
            
        Returns:
            技术指标字典
        """
        indicators = {}
        
        try:
            # 确保close列存在
            if 'close' not in kl_pd.columns:
                logging.warning("Abu DataFrame中没有'close'列，无法计算指标")
                return {}
                
            # 计算移动平均线
            for period in [5, 10, 20, 30, 60]:
                if len(kl_pd) >= period:
                    ma_key = f"ma{period}"
                    ma_values = kl_pd['close'].rolling(window=period).mean().fillna(0).tolist()
                    indicators[ma_key] = [round(float(x), 2) for x in ma_values]
            
            # 使用abu内置指标计算（如果可用）
            try:
                from abupy.IndicatorBu import AbuMacd, AbuRsi
                
                # 计算MACD
                macd_obj = AbuMacd(kl_pd)
                indicators['macd'] = [round(float(x), 2) for x in macd_obj.diff.tolist()]
                indicators['macd_signal'] = [round(float(x), 2) for x in macd_obj.dea.tolist()]
                indicators['macd_hist'] = [round(float(x), 2) for x in (macd_obj.diff - macd_obj.dea).tolist()]
                
                # 计算RSI
                rsi_obj = AbuRsi(kl_pd, 14)
                indicators['rsi'] = [round(float(x), 2) if not math.isnan(x) else 50 for x in rsi_obj.rsi.tolist()]
                
            except ImportError:
                # 如果abu指标模块不可用，使用自定义计算
                logging.warning("Abu指标模块不可用，使用自定义计算")
                return KlineProvider.calculate_indicators(kl_pd)
                
        except Exception as e:
            logging.error(f"从Abu数据计算技术指标时出错: {e}", exc_info=True)
            
        return indicators
