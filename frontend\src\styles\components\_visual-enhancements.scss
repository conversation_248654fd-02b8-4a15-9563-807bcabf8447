// ===== 视觉增强效果 =====

// ===== 渐变背景系统 =====
.gradient-bg {
  &.primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  }
  
  &.success {
    background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-light) 100%);
  }
  
  &.warning {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-light) 100%);
  }
  
  &.danger {
    background: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-light) 100%);
  }
  
  &.subtle {
    background: linear-gradient(135deg, var(--bg-color-card) 0%, var(--bg-color-container) 100%);
  }
}

// ===== 微妙的背景纹理 =====
.textured-bg {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(24, 144, 255, 0.03) 1px, transparent 0);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }
}

// ===== 高级阴影效果 =====
.shadow-enhanced {
  &.level-1 {
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24);
  }
  
  &.level-2 {
    box-shadow: 
      0 3px 6px rgba(0, 0, 0, 0.16),
      0 3px 6px rgba(0, 0, 0, 0.23);
  }
  
  &.level-3 {
    box-shadow: 
      0 10px 20px rgba(0, 0, 0, 0.19),
      0 6px 6px rgba(0, 0, 0, 0.23);
  }
  
  &.level-4 {
    box-shadow: 
      0 14px 28px rgba(0, 0, 0, 0.25),
      0 10px 10px rgba(0, 0, 0, 0.22);
  }
  
  &.level-5 {
    box-shadow: 
      0 19px 38px rgba(0, 0, 0, 0.30),
      0 15px 12px rgba(0, 0, 0, 0.22);
  }
}

// ===== 彩色阴影效果 =====
.colored-shadow {
  &.primary {
    box-shadow: 0 8px 25px rgba(24, 144, 255, 0.15);
  }
  
  &.success {
    box-shadow: 0 8px 25px rgba(82, 196, 26, 0.15);
  }
  
  &.warning {
    box-shadow: 0 8px 25px rgba(250, 173, 20, 0.15);
  }
  
  &.danger {
    box-shadow: 0 8px 25px rgba(255, 77, 79, 0.15);
  }
}

// ===== 动画效果 =====
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// ===== 动画类 =====
.animate {
  &.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
  
  &.fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
  }
  
  &.fade-in-right {
    animation: fadeInRight 0.6s ease-out;
  }
  
  &.scale-in {
    animation: scaleIn 0.4s ease-out;
  }
  
  &.pulse {
    animation: pulse 2s infinite;
  }
}

// ===== 悬停效果 =====
.hover-lift {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-elevated);
  }
}

.hover-glow {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 
      0 0 20px rgba(24, 144, 255, 0.3),
      var(--shadow-card);
  }
}

.hover-scale {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

// ===== 加载骨架屏效果 =====
.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-color-container) 25%,
    var(--bg-color-hover) 50%,
    var(--bg-color-container) 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

// ===== 玻璃态效果 =====
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

// ===== 数据可视化增强 =====
.data-card {
  background: var(--bg-color-card);
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elevated);
  }
  
  .data-title {
    color: var(--text-color-secondary);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .data-value {
    color: var(--text-color-primary);
    font-size: 28px;
    font-weight: 600;
    line-height: 1.2;
    
    &.profit {
      color: var(--color-profit);
    }
    
    &.loss {
      color: var(--color-loss);
    }
    
    &.neutral {
      color: var(--color-neutral);
    }
  }
  
  .data-trend {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    
    &.up {
      color: var(--color-profit);
    }
    
    &.down {
      color: var(--color-loss);
    }
    
    &.flat {
      color: var(--color-neutral);
    }
  }
}

// ===== 状态指示器 =====
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.running {
      background-color: var(--color-success);
      animation: pulse 2s infinite;
    }
    
    &.stopped {
      background-color: var(--color-danger);
    }
    
    &.pending {
      background-color: var(--color-warning);
    }
    
    &.unknown {
      background-color: var(--color-neutral);
    }
  }
}

// ===== 进度条增强 =====
.progress-enhanced {
  .el-progress-bar__outer {
    background-color: var(--bg-color-container);
    border-radius: 10px;
    overflow: hidden;
  }
  
  .el-progress-bar__inner {
    border-radius: 10px;
    transition: all 0.3s ease;
    
    &.profit {
      background: linear-gradient(90deg, var(--color-success) 0%, var(--color-success-light) 100%);
    }
    
    &.loss {
      background: linear-gradient(90deg, var(--color-danger) 0%, var(--color-danger-light) 100%);
    }
  }
}