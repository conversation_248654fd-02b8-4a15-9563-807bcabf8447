import pytest
import re  # <-- 导入 re 模块
from backend.app.abupy_adapter.symbol.symbol_validator import SymbolValidator
from backend.app.abupy_adapter.symbol.symbol_converter import SymbolConverter
from backend.app.abupy_adapter.symbol.symbol_name_resolver import SymbolNameResolver
from backend.app.abupy_adapter.symbol.adapter_facade import SymbolAdapterFacade
from backend.app.core.exceptions import SymbolError, DataNotFoundError


class TestSymbolValidator:
    """测试 SymbolValidator 类"""

    # region Valid Symbols
    @pytest.mark.parametrize("symbol", [
        # A-shares
        "sh600036",
        "sz000001",
        "600036",
        "000001",
        "300001",
        "60003",  # <-- 将它添加到这里
        "600036.SH",
        "000001.SZ",
        "sh.600036",
        "sz.000001",
        # HK stocks
        "hk00700",
        "hk1024",
        "00700",
        "01024",
        "00700.HK",
        "hk.00700",
        # US stocks
        "usTSLA",
        "usAAPL",
        "us.TSLA",
        "us.AAPL",
        "us.BRK-A",
        # HK Indexes
        "hkhsi",
        "hkhscei",
        "hkhscci",
    ])
    def test_validate_symbol_valid(self, symbol):
        """测试有效的股票代码"""
        assert SymbolValidator.validate_symbol(symbol) is True

    # endregion

    # region Invalid Symbols
    @pytest.mark.parametrize("symbol, error_message", [
        # General
        (None, "股票代码不能为空"),
        ("", "股票代码不能为空"),
        (" ", "股票代码不能为空"),
        ("sh600036.", "股票代码格式错误，包含无效的点或空部分: sh600036."),
        (".sh600036", "股票代码格式错误，包含无效的点或空部分: .sh600036"),
        ("sh.600036.sz", "股票代码格式错误，包含无效的点或空部分: sh.600036.sz"),
        ("unknown.symbol", "无法识别的带点格式: unknown.symbol"),
        ("xx600036", "股票代码格式错误: xx600036"),
        # A-shares
        ("sh60003", "A股代码必须为sh/sz前缀+6位数字: sh60003"),
        ("sz00000", "A股代码必须为sh/sz前缀+6位数字: sz00000"),
        ("600036.SS", "无法识别的带点格式: 600036.SS"),
        ("sh.60003", "A股代码部分必须为6位数字: sh.60003"),
        # HK stocks
        ("hk123", "港股代码必须为hk前缀+4/5位数字: hk123"),
        ("123", "纯数字代码长度必须为4, 5或6位: 123"),
        ("00700.HH", "无法识别的带点格式: 00700.HH"),
        ("hk.123", "港股代码部分必须为4或5位数字: hk.123"),
        # US stocks
        ("us!@#", "美股代码格式错误: us!@#"),
        ("us.TSLA!", "美股代码部分格式错误: us.TSLA!"),
    ])
    def test_validate_symbol_invalid(self, symbol, error_message):
        """测试无效的股票代码"""
        # 使用 re.escape() 来确保 error_message 被当作纯文本进行匹配
        with pytest.raises(SymbolError, match=re.escape(error_message)):
            SymbolValidator.validate_symbol(symbol)
    # endregion


class TestSymbolConverter:
    """测试 SymbolConverter 类"""

    @pytest.mark.parametrize("symbol, expected_normalized, expected_market", [
        # A-shares
        ("sh600036", "sh600036", "CN"),
        ("sz000001", "sz000001", "CN"),
        ("600036", "sh600036", "CN"),
        ("000001", "sz000001", "CN"),
        ("300001", "sz300001", "CN"),
        ("600036.SH", "sh600036", "CN"),
        ("000001.SZ", "sz000001", "CN"),
        # HK stocks
        ("hk00700", "hk00700", "HK"),
        ("00700", "hk00700", "HK"),
        ("01024", "hk01024", "HK"),
        ("00700.HK", "hk00700", "HK"),
        # US stocks
        ("usTSLA", "usTSLA", "US"),
        # Indexes
        ("sh000001", "sh000001", "CN"),
        ("us.dji", "us.dji", "US"),
        ("hkhsi", "hkhsi", "HK"),
    ])
    def test_normalize_symbol_valid(self, symbol, expected_normalized, expected_market):
        """测试有效的股票代码标准化"""
        normalized_symbol, market = SymbolConverter.normalize_symbol(symbol)
        assert normalized_symbol == expected_normalized
        assert market == expected_market

    @pytest.mark.parametrize("symbol, error_message", [
        (None, "股票代码不能为空"),
        ("", "股票代码不能为空"),
        (" ", "股票代码不能为空"),
        ("123", "无法标准化股票代码 123"),
        ("unknown", "无法标准化股票代码 unknown"),
        ("600036.XX", "无法标准化股票代码 600036.XX"),
    ])
    def test_normalize_symbol_invalid(self, symbol, error_message):
        """测试无效的股票代码标准化"""
        with pytest.raises(SymbolError, match=re.escape(error_message)):
            SymbolConverter.normalize_symbol(symbol)


class TestSymbolNameResolver:
    """测试 SymbolNameResolver 类"""

    @pytest.fixture(autouse=True)
    def clear_cache(self):
        # 在每个测试之前清除缓存
        SymbolNameResolver._symbol_name_cache.clear()

    @pytest.mark.parametrize("symbol, expected_name", [
        ("sh000001", "上证指数"),
        ("us.dji", "道琼斯工业平均指数"),
        ("hkhsi", "恒生指数"),
    ])
    def test_get_symbol_name_from_index_list(self, symbol, expected_name):
        """测试从预定义指数列表中获取名称"""
        assert SymbolNameResolver.get_symbol_name(symbol) == expected_name

    def test_get_symbol_name_caching(self, mocker):
        """测试名称解析的缓存机制"""
        mock_tushare = mocker.patch.object(SymbolNameResolver, '_get_name_from_tushare', return_value="腾讯控股")
        
        # 第一次调用，应该会调用 mock_tushare
        assert SymbolNameResolver.get_symbol_name("hk00700") == "腾讯控股"
        mock_tushare.assert_called_once_with("hk00700")
        
        # 第二次调用，应该从缓存中获取，不会再调用 mock_tushare
        assert SymbolNameResolver.get_symbol_name("hk00700") == "腾讯控股"
        mock_tushare.assert_called_once() # 确认仍然只被调用了一次

    @pytest.mark.parametrize("symbol, expected_name", [
        ("sh600036", "600036(沪A)"),
        ("sz000001", "000001(深A)"),
        ("usTSLA", "TSLA(美股)"),
        ("hk00700", "00700(港股)"),
        ("999999", "999999"), # 未知代码
    ])
    def test_get_name_from_local_rules(self, symbol, expected_name):
        """测试本地规则解析"""
        assert SymbolNameResolver._get_name_from_local_rules(symbol) == expected_name

    def test_get_symbol_name_fallback_to_local(self, mocker):
        """测试在tushare失败后回退到本地规则"""
        mocker.patch.object(SymbolNameResolver, '_get_name_from_tushare', side_effect=Exception("Tushare Error"))
        assert SymbolNameResolver.get_symbol_name("sh600036") == "600036(沪A)"

    def test_get_symbol_name_all_fail(self, mocker):
        """测试所有解析方法都失败的情况"""
        mocker.patch.object(SymbolNameResolver, '_get_name_from_tushare', return_value=None)
        mocker.patch.object(SymbolNameResolver, '_get_name_from_local_rules', side_effect=Exception("Local Rule Error"))
        assert SymbolNameResolver.get_symbol_name("unknown_symbol") == "unknown_symbol"


class TestSymbolAdapterFacade:
    """测试 SymbolAdapterFacade 类"""

    def test_validate_symbol(self, mocker):
        mock_validator = mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.SymbolValidator.validate_symbol')
        SymbolAdapterFacade.validate_symbol("sh600036")
        mock_validator.assert_called_once_with("sh600036")

    def test_normalize_symbol(self, mocker):
        mock_converter = mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.SymbolConverter.normalize_symbol')
        SymbolAdapterFacade.normalize_symbol("600036")
        mock_converter.assert_called_once_with("600036")

    def test_get_symbol_name(self, mocker):
        mock_resolver = mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.SymbolNameResolver.get_symbol_name')
        SymbolAdapterFacade.get_symbol_name("sh600036")
        mock_resolver.assert_called_once_with("sh600036")

    @pytest.mark.parametrize("symbol, expected", [
        ("sh000001", True),
        ("sz399001", True),
        ("000001.SH", True),
        ("us.dji", True),
        ("hkhsi", True),
        ("sh600036", False),
        ("us.tsla", False),
        ("hk00700", False),
    ])
    def test_is_index(self, symbol, expected):
        assert SymbolAdapterFacade.is_index(symbol) == expected

    def test_get_kline_data_success(self, mocker):
        # 模拟ABuSymbolPd.make_kl_df成功返回数据
        mock_make_kl_df = mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.ABuSymbolPd.make_kl_df')
        mock_df = mocker.Mock()
        mock_df.empty = False
        mock_make_kl_df.return_value = mock_df

        # 模拟validate_symbol通过
        mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.SymbolValidator.validate_symbol', return_value=True)

        df = SymbolAdapterFacade.get_kline_data("sh600036")
        assert df is not None
        mock_make_kl_df.assert_called_once_with("sh600036", start=None, end=None)

    def test_get_kline_data_not_found(self, mocker):
        # 模拟ABuSymbolPd.make_kl_df返回None
        mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.ABuSymbolPd.make_kl_df', return_value=None)
        mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.SymbolValidator.validate_symbol', return_value=True)

        with pytest.raises(DataNotFoundError, match="未找到sh600036的K线数据"):
            SymbolAdapterFacade.get_kline_data("sh600036")

    def test_get_kline_data_invalid_symbol(self, mocker):
        # 模拟validate_symbol抛出异常
        mocker.patch('backend.app.abupy_adapter.symbol.adapter_facade.SymbolValidator.validate_symbol', side_effect=SymbolError("Invalid"))
        with pytest.raises(SymbolError):
            SymbolAdapterFacade.get_kline_data("invalid_symbol")