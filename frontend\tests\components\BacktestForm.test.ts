import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import BacktestForm from '../../src/components/backtest/BacktestForm.vue'
import type { BacktestConfig } from '../../src/api/types/backtest'

// Mock stores
const mockUseBacktestStore = {
  isBacktesting: false,
  backtestError: '',
  startBacktest: vi.fn()
}

vi.mock('../../src/stores', () => ({
  useBacktestStore: () => mockUseBacktestStore
}))

describe('BacktestForm', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(BacktestForm, {
      props: {
        loading: false,
        ...props
      },
      global: {
        stubs: {
          // 如果使用了Element Plus组件，可以在这里stub
        }
      }
    })
  }

  describe('渲染', () => {
    it('应该正确渲染表单元素', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="strategy-id-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="strategy-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="symbol-input"]').exists()).toBe(true)
      expect(wrapper.find('h2').text()).toBe('回测与方案')
    })

    it('应该显示必填字段标识', () => {
      const wrapper = createWrapper()
      
      const strategyIdLabel = wrapper.find('label[for="strategy-id"]')
      const symbolLabel = wrapper.find('label[for="symbol"]')
      const startDateLabel = wrapper.find('label[for="start-date"]')
      
      expect(strategyIdLabel.text()).toContain('*')
      expect(symbolLabel.text()).toContain('*')
      expect(startDateLabel.text()).toContain('*')
    })
  })

  describe('表单验证', () => {
    it('应该要求必填字段', async () => {
      const wrapper = createWrapper()
      
      const strategyIdInput = wrapper.find('[data-testid="strategy-id-input"]')
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      expect(strategyIdInput.attributes('required')).toBeDefined()
      expect(symbolInput.attributes('required')).toBeDefined()
    })

    it('应该有正确的placeholder文本', () => {
      const wrapper = createWrapper()
      
      const strategyIdInput = wrapper.find('[data-testid="strategy-id-input"]')
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      expect(strategyIdInput.attributes('placeholder')).toBe('请输入策略ID')
      expect(symbolInput.attributes('placeholder')).toBe('如：000001.SZ')
    })
  })

  describe('表单交互', () => {
    it('应该能够输入表单数据', async () => {
      const wrapper = createWrapper()
      
      const strategyIdInput = wrapper.find('[data-testid="strategy-id-input"]')
      const symbolInput = wrapper.find('[data-testid="symbol-input"]')
      
      await strategyIdInput.setValue('test-strategy-123')
      await symbolInput.setValue('000001.SZ')
      
      expect((strategyIdInput.element as HTMLInputElement).value).toBe('test-strategy-123')
      expect((symbolInput.element as HTMLInputElement).value).toBe('000001.SZ')
    })

    it('应该在loading状态下禁用提交', () => {
      const wrapper = createWrapper({ loading: true })
      
      // 查找提交按钮（可能需要根据实际实现调整选择器）
      const submitButton = wrapper.find('button[type="submit"], .submit-btn, [data-testid="submit-btn"]')
      
      if (submitButton.exists()) {
        expect(submitButton.attributes('disabled')).toBeDefined()
      }
    })
  })

  describe('表单提交', () => {
    it('应该在提交时发出submit事件', async () => {
      const wrapper = createWrapper()
      
      // 填写必填字段
      await wrapper.find('[data-testid="strategy-id-input"]').setValue('test-strategy')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      
      // 查找并填写日期字段
      const startDateInput = wrapper.find('#start-date')
      const endDateInput = wrapper.find('#end-date')
      
      if (startDateInput.exists()) {
        await startDateInput.setValue('2023-01-01')
      }
      if (endDateInput.exists()) {
        await endDateInput.setValue('2023-12-31')
      }
      
      // 提交表单
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
      
      // 验证是否发出了submit事件
      expect(wrapper.emitted('submit')).toBeTruthy()
    })

    it('应该发出正确的配置数据', async () => {
      const wrapper = createWrapper()
      
      // 填写表单数据
      await wrapper.find('[data-testid="strategy-id-input"]').setValue('test-strategy-123')
      await wrapper.find('[data-testid="strategy-input"]').setValue('测试策略')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      
      const startDateInput = wrapper.find('#start-date')
      const endDateInput = wrapper.find('#end-date')
      
      if (startDateInput.exists()) {
        await startDateInput.setValue('2023-01-01')
      }
      if (endDateInput.exists()) {
        await endDateInput.setValue('2023-12-31')
      }
      
      // 提交表单
      await wrapper.find('form').trigger('submit.prevent')
      
      const emittedEvents = wrapper.emitted('submit')
      if (emittedEvents && emittedEvents[0]) {
        const submittedConfig = emittedEvents[0][0] as BacktestConfig
        
        expect(submittedConfig.strategy_id).toBe('test-strategy-123')
        expect(submittedConfig.symbol).toBe('000001.SZ')
      }
    })
  })

  describe('重置功能', () => {
    it('应该在重置时发出reset事件', async () => {
      const wrapper = createWrapper()
      
      // 查找重置按钮
      const resetButton = wrapper.find('button[type="reset"], .reset-btn, [data-testid="reset-btn"]')
      
      if (resetButton.exists()) {
        await resetButton.trigger('click')
        expect(wrapper.emitted('reset')).toBeTruthy()
      }
    })

    it('应该清空表单字段', async () => {
      const wrapper = createWrapper()
      
      // 先填写一些数据
      await wrapper.find('[data-testid="strategy-id-input"]').setValue('test-strategy')
      await wrapper.find('[data-testid="symbol-input"]').setValue('000001.SZ')
      
      // 查找并触发重置
      const resetButton = wrapper.find('button[type="reset"], .reset-btn, [data-testid="reset-btn"]')
      
      if (resetButton.exists()) {
        await resetButton.trigger('click')
        
        // 验证字段是否被清空
        const strategyIdInput = wrapper.find('[data-testid="strategy-id-input"]')
        const symbolInput = wrapper.find('[data-testid="symbol-input"]')
        
        expect((strategyIdInput.element as HTMLInputElement).value).toBe('')
        expect((symbolInput.element as HTMLInputElement).value).toBe('')
      }
    })
  })

  describe('错误处理', () => {
    it('应该显示错误信息', async () => {
      mockUseBacktestStore.backtestError = '回测启动失败'
      
      const wrapper = createWrapper()
      
      // 查找错误信息显示元素
      const errorElement = wrapper.find('.error-message, .alert-error, [data-testid="error-message"]')
      
      if (errorElement.exists()) {
        expect(errorElement.text()).toContain('回测启动失败')
      }
    })
  })

  describe('Props响应', () => {
    it('应该响应loading prop的变化', async () => {
      const wrapper = createWrapper({ loading: false })
      
      await wrapper.setProps({ loading: true })
      
      // 验证loading状态的UI变化
      const submitButton = wrapper.find('button[type="submit"], .submit-btn, [data-testid="submit-btn"]')
      
      if (submitButton.exists()) {
        expect(submitButton.attributes('disabled')).toBeDefined()
      }
    })
  })
})