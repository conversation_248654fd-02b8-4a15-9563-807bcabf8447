# -*- coding: utf-8 -*-
"""
API路由配置
"""
from fastapi import APIRouter
from backend.app.api.endpoints import strategy, options, metrics, market, dashboard

api_router = APIRouter()
api_router.include_router(strategy.router, prefix="/strategies", tags=["strategies"])
api_router.include_router(options.router, prefix="/options", tags=["options"])
api_router.include_router(metrics.router, prefix="/metrics", tags=["metrics"])
api_router.include_router(market.router, prefix="/market", tags=["market"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
