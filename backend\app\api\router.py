# -*- coding: utf-8 -*-
"""
API路由配置
"""
from fastapi import APIRouter
from backend.app.api.endpoints import strategy, market, options, dashboard, metrics, umpire, grid_search

api_router = APIRouter()
api_router.include_router(strategy.router, prefix="/strategy", tags=["strategy"])
api_router.include_router(options.router, prefix="/options", tags=["options"])
api_router.include_router(metrics.router, prefix="/metrics", tags=["metrics"])
api_router.include_router(market.router, prefix="/market", tags=["market"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(umpire.router, prefix="/umpire", tags=["Umpire"])
api_router.include_router(grid_search.router, prefix="/grid-search", tags=["Grid Search"])
