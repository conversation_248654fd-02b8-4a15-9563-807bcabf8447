import { apiClient } from '../client';
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../types';

// 获取所有策略
export const getStrategies = async () => {
  const response = await apiClient.get('/api/v1/strategy?limit=100');
  return response.data;
};

// 根据ID获取特定策略
export const getStrategy = async (id: string): Promise<Strategy> => {
  const response = await apiClient.get(`/api/v1/strategy/${id}`);
  return response.data;
};

// 创建新策略
export const createStrategy = async (strategyData: CreateStrategyRequest): Promise<Strategy> => {
  const response = await apiClient.post('/api/v1/strategy', strategyData);
  return response.data;
};

// 更新策略
export const updateStrategy = async (id: string, strategyData: UpdateStrategyRequest): Promise<Strategy> => {
  const response = await apiClient.put(`/api/v1/strategy/${id}`, strategyData);
  return response.data;
};

// 删除策略
export const deleteStrategy = async (id: string): Promise<void> => {
  await apiClient.delete(`/api/v1/strategy/${id}`);
};

// 根据ID执行策略
export const executeStrategy = (id: string, executeParams: any) => {
    return apiClient.post(`/api/v1/strategy/${id}/execute`, executeParams);
  };