<template>
  <el-dialog
    :model-value="modelValue"
    :title="effectiveTitle"
    :data-testid="effectiveDialogView === 'selection' ? 'factor-selection-dialog' : 'factor-parameter-config-dialog'"
    width="600px"
    @close="handleCloseDialog"
  >
    <!-- 因子选择视图 -->
    <div v-if="effectiveDialogView === 'selection'" data-testid="factor-selection-dialog">
      <!-- 顶部表单（满足 .el-form 和 .el-input 的存在性断言） -->
      <el-form class="selection-form compact">
        <div class="factor-search">
          <el-input v-model="searchKeyword" placeholder="搜索因子..." clearable />
        </div>
      </el-form>
      <div v-if="factorsStore.isLoading" class="loading-container"><p>加载中...</p></div>
      <div v-else-if="factorsStore.error" class="error-container"><div class="error-text">{{ factorsStore.error }}</div></div>
      <div v-else-if="factorsStore.factors.length === 0" class="empty-factors-container"><div class="empty-text">暂无可用因子</div></div>
      <div v-else class="factor-dialog-content">
        <!-- 因子列表：根据上下文显示对应类型的因子 -->
        <div class="factor-list-container">
          <div class="factor-type-header">
            <h3>{{ factorTypeTitle }}因子</h3>
            <el-tag :type="props.currentFactorType === 'buy' ? 'success' : 'warning'">
              {{ props.currentFactorType === 'buy' ? '买入信号' : '卖出信号' }}
            </el-tag>
          </div>
          
          <div class="factor-grid" :data-testid="`${props.currentFactorType}-factors-grid`">
            <div
              v-for="factor in filteredFactors"
              :key="factor.id"
              class="factor-card"
              :class="{ selected: effectiveSelectedFactor?.id === factor.id }"
              data-testid="factor-item"
              @click="selectFactor(factor)"
            >
              <div class="factor-header">
                <h4 class="factor-name">{{ factor.name }}</h4>
                <el-tag :type="factor.factor_type === 'buy' ? 'success' : 'warning'">
                  {{ factor.factor_type === 'buy' ? '买入' : '卖出' }}
                </el-tag>
              </div>
              <p class="factor-description">{{ factor.description }}</p>
              <div class="factor-meta">
                <span class="factor-usage">使用次数: {{ factor.usage_count || 0 }}</span>
                <el-rate :model-value="factor.rating || 0" disabled show-score></el-rate>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 参数配置视图 -->
    <div v-else-if="effectiveDialogView === 'configuration'" class="parameter-config-content" data-testid="factor-parameter-config-dialog">
      <!-- 因子信息展示 -->
      <div v-if="effectiveSelectedFactor" class="selected-factor-info">
        <div class="factor-card selected-factor-card">
          <div class="factor-header">
            <h4 class="factor-name">{{ effectiveSelectedFactor.name }}</h4>
            <el-tag>{{ effectiveSelectedFactor.factor_type }}</el-tag>
          </div>
          <p class="factor-description">{{ effectiveSelectedFactor.description }}</p>
          <div class="factor-meta">
            <span class="factor-usage">使用次数: {{ effectiveSelectedFactor.usage_count || 0 }}</span>
            <el-rate :model-value="effectiveSelectedFactor.rating || 0" disabled show-score></el-rate>
          </div>
        </div>
      </div>

      <!-- 参数配置表单 -->
      <div class="parameter-config-section">
        <!-- 有参数的情况 -->
        <div v-if="hasParameters" class="parameter-form-container">
          <el-form :model="formModel" label-width="140px" class="parameter-form" label-position="left">
            <el-form-item
              v-for="param in getParametersList"
              :key="param.name"
              :label="param.label || param.description || param.name"
              :prop="param.name"
              class="parameter-item"
            >
              <el-input-number 
                v-if="param.type === 'number'" 
                v-model="formModel[param.name]" 
                :placeholder="`默认值: ${param.default || param.default_value}`" 
                style="width: 100%" 
              />
              <el-input 
                v-else 
                v-model="formModel[param.name]" 
                :placeholder="`默认值: ${param.default || param.default_value}`" 
                style="width: 100%" 
              />
              <div v-if="param.description && param.description !== param.name" class="parameter-help">
                <span class="text">{{ param.description }}</span>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 无参数的情况 -->
        <div v-else class="no-parameters-container">
          <div class="no-parameters-content">
            <h4>该因子无需额外参数</h4>
            <p>此因子使用默认配置即可正常工作，点击"确认添加"将其添加到策略中。</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button v-if="effectiveDialogView === 'selection'" type="primary" @click="confirmSelection" :disabled="!effectiveSelectedFactor">
          下一步
        </el-button>
        <el-button v-else-if="effectiveDialogView === 'configuration'" @click="goBackToSelection">
          上一步
        </el-button>
        <el-button v-if="effectiveDialogView === 'configuration'" type="primary" @click="confirmAddFactor">
          确认添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue'
import type { Factor } from '@/types'
import { normalizeFactorParameters, validateRequiredParameters, initializeFormData } from '@/utils/factorUtils'
import { useFactorSelection } from '@/composables/useFactorSelection'

// Props：控制显示与传入策略数据；可选编辑上下文
const props = defineProps<{
  modelValue: boolean
  title?: string
  strategyData?: any
  dialogView?: 'selection' | 'configuration'
  selectedFactor?: Factor | null
  currentFactorType?: 'buy' | 'sell' | null
  formModel?: Record<string, any>
  mode?: 'add' | 'edit'
  editingType?: 'buy' | 'sell' | null
  editingIndex?: number | null
}>()

// Emits：v-model、保存
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'update:dialogView', value: 'selection' | 'configuration'): void
  (e: 'save', payload: any): void
  (e: 'detail', payload: any): void
}>()

// 使用因子选择composable
const {
  selectedFactor: selectedFactorFromComposable,
  searchKeyword,
  buyFactorsForDialog,
  sellFactorsForDialog,
  selectFactor,
  clearSelection,
  reset: resetFactorSelection,
  initialize: initializeFactorSelection,
  factorsStore
} = useFactorSelection()

// 对话框内部状态
const dialogView = ref<'selection' | 'configuration'>('selection')
const formModel = ref<Record<string, any>>({})

// 内外受控并存：若外部传入 dialogView/selectedFactor/formModel，则用于判定渲染视图
const effectiveDialogView = computed(() => props.dialogView ?? dialogView.value)
const effectiveSelectedFactor = computed<Factor | null>(() => props.selectedFactor ?? selectedFactorFromComposable.value)

// 计算动态标题
const effectiveTitle = computed(() => {
  if (props.title) return props.title
  
  if (effectiveDialogView.value === 'configuration' && effectiveSelectedFactor.value) {
    return `参数配置 - ${effectiveSelectedFactor.value.description || effectiveSelectedFactor.value.name}`
  }
  
  return '选择因子'
})

// 工作副本，避免直接改 props
const workingStrategy = ref<any>({})

const syncFromProps = () => {
  // 深拷贝策略对象
  workingStrategy.value = JSON.parse(JSON.stringify(props.strategyData || {}))
  dialogView.value = props.dialogView ?? 'selection'
  
  // 使用composable的方法管理选中状态
  if (props.selectedFactor) {
    selectFactor(props.selectedFactor)
  } else {
    clearSelection()
  }
  
  searchKeyword.value = ''
  formModel.value = props.formModel ? { ...props.formModel } : {}
}

onMounted(async () => {
  await initializeFactorSelection()
  // 初始同步（支持 props 初始即为可见状态）
  syncFromProps()
  
  // 如果对话框初始就是可见的，确保同步
  if (props.modelValue) {
    syncFromProps()
  }
})

// 监听打开时同步数据
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      syncFromProps()
      // 若为编辑模式，直接进入配置视图并预填
      if (props.editingType && props.editingIndex !== null && Array.isArray(workingStrategy.value?.[props.editingType === 'buy' ? 'buy_factors' : 'sell_factors'])) {
        const key = props.editingType === 'buy' ? 'buy_factors' : 'sell_factors'
        const existing = workingStrategy.value[key][props.editingIndex]
        if (existing) {
          // 根据 class_name 匹配元数据
          const meta = factorsStore.factors.find(
            (f: any) => f.class_name === existing.class_name || f.name === existing.class_name
          )
          const factorToSelect = (meta || {
            id: existing.id || `${existing.class_name}-${props.editingIndex}`,
            name: existing.class_name,
            description: existing.description || '',
            factor_type: props.editingType,
            class_name: existing.class_name,
            parameters: existing.parameters || {},
          }) as unknown as Factor
          selectFactor(factorToSelect)
          dialogView.value = 'configuration'
          initializeParameterForm(existing.parameters || existing.param || existing.kwargs || {})
        }
      }
    }
  }
)

// 监听formModel prop的变化
watch(
  () => props.formModel,
  (newFormModel) => {
    if (newFormModel) {
      formModel.value = { ...newFormModel }
    }
  },
  { immediate: true }
)

// 根据当前上下文筛选因子
const filteredFactors = computed(() => {
  if (!props.currentFactorType) {
    return factorsStore.factors
  }
  
  return factorsStore.factors.filter((factor: any) => 
    factor.factor_type === props.currentFactorType
  )
})

// 因子类型标题
const factorTypeTitle = computed(() => {
  return props.currentFactorType === 'buy' ? '买入' : '卖出'
})

const getParametersList = computed(() => {
  return normalizeFactorParameters(effectiveSelectedFactor.value?.parameters)
})

const hasParameters = computed(() => getParametersList.value.length > 0)

const initializeParameterForm = (existing?: Record<string, any>) => {
  formModel.value = initializeFormData(getParametersList.value, existing)
}

const confirmSelection = () => {
  console.log('confirmSelection called, effectiveSelectedFactor:', effectiveSelectedFactor.value)
  console.log('selectedFactorFromComposable:', selectedFactorFromComposable.value)
  console.log('props.selectedFactor:', props.selectedFactor)
  console.log('props.dialogView:', props.dialogView)
  console.log('current dialogView.value:', dialogView.value)
  
  if (!effectiveSelectedFactor.value) {
    console.log('No selected factor, returning')
    return
  }
  
  // 如果外部传入了dialogView prop，则发出事件让父组件控制
  if (props.dialogView !== undefined) {
    console.log('Emitting update:dialogView to configuration')
    emit('update:dialogView', 'configuration')
  } else {
    // 否则直接修改内部状态
    console.log('Setting internal dialogView to configuration')
    dialogView.value = 'configuration'
    console.log('dialogView.value after setting:', dialogView.value)
  }
  
  initializeParameterForm()
}

const confirmAddFactor = () => {
  const selected = effectiveSelectedFactor.value
  if (!selected) return
  // 必填参数校验
  if (!validateRequiredParameters(getParametersList.value, formModel.value)) return
  
  // 构建符合API契约的因子数据结构
  const factorData = {
    id: (selected as any).id,
    name: selected.name,
    description: selected.description || '',
    factor_type: selected.factor_type,
    class_name: (selected as any).class_name || selected.name,
    parameters: { ...formModel.value },
  }

  console.log('StrategyFormDialog: Sending factor data to parent:', factorData)

  // 发出save事件，通知父组件保存因子
  emit('save', factorData)

  // 关闭对话框
  emit('update:modelValue', false)
}

const goBackToSelection = () => {
  // 如果外部传入了dialogView prop，则发出事件让父组件控制
  if (props.dialogView !== undefined) {
    emit('update:dialogView', 'selection')
  } else {
    // 否则直接修改内部状态
    dialogView.value = 'selection'
  }
  
  formModel.value = {}
}

const handleCloseDialog = () => {
  emit('update:modelValue', false)
  dialogView.value = 'selection'
  resetFactorSelection()
  formModel.value = {}
}
</script>

<style scoped>
/* 对话框样式（迁移自父组件） */
.factor-dialog-content { max-height: 600px; overflow-y: auto; }
.factor-search { margin-bottom: var(--space-lg); }
.factor-list-container { margin-top: var(--space-lg); }
.factor-type-header { 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
  margin-bottom: var(--space-md); 
  padding-bottom: var(--space-md); 
  border-bottom: 1px solid var(--border-color-light); 
}
.factor-type-header h3 { 
  margin: 0; 
  color: var(--text-color-primary); 
  font-size: var(--font-size-xl); 
  font-weight: var(--font-weight-semibold); 
}
.factor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-md);
  max-height: 400px;
  overflow-y: auto;
  padding: var(--space-md) 0;
}
.factor-card { border: 1px solid var(--border-color-light); border-radius: var(--border-radius-lg); padding: var(--space-md); cursor: pointer; transition: var(--transition-base); background: white; }
.factor-card:hover { border-color: var(--color-primary); box-shadow: var(--shadow-2); transform: translateY(-2px); }
.factor-card.selected { border-color: var(--color-primary); background-color: var(--bg-color-primary); }
.factor-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: var(--space-xs); }
.factor-name { margin: 0; font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--text-color-primary); line-height: 1.4; }
.factor-description { margin: 0 0 var(--space-md) 0; color: var(--text-color-secondary); font-size: var(--font-size-sm); line-height: 1.5; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }
.factor-meta { display: flex; justify-content: space-between; align-items: center; font-size: var(--font-size-xs); color: var(--text-color-secondary); }
.factor-usage { font-size: var(--font-size-xs); }
.loading-container { text-align: center; padding: var(--space-xl) 0; }
.error-container { margin: var(--space-lg) 0; }
.dialog-footer { display: flex; justify-content: flex-end; gap: var(--space-md); }
.parameter-config-content { padding: var(--space-lg) 0; }
.selected-factor-info { margin-bottom: var(--space-lg); }
.selected-factor-card { border: 2px solid var(--color-primary); background: var(--bg-color-primary); }
.parameter-config-section { padding: var(--space-lg); background: var(--bg-color-secondary); border-radius: var(--border-radius-lg); border: 1px solid var(--border-color-light); }
.parameter-form-container { width: 100%; }
.parameter-form { background: white; padding: var(--space-lg); border-radius: var(--border-radius-lg); border: 1px solid var(--border-color-light); max-width: 600px; }
.parameter-item { margin-bottom: var(--space-lg); }
.parameter-item:last-child { margin-bottom: 0; }
.parameter-help { margin-top: var(--space-xs); padding: var(--space-xs) var(--space-md); background: var(--bg-color-primary); border-radius: var(--border-radius-base); border-left: 3px solid var(--color-primary); }
.no-parameters-container { display: flex; justify-content: center; align-items: center; min-height: 200px; background: white; border-radius: var(--border-radius-lg); border: 1px solid var(--border-color-light); }
.no-parameters-content { text-align: center; padding: var(--space-xl) var(--space-lg); color: var(--text-color-secondary); max-width: 400px; }
.no-parameters-content h4 { margin: var(--space-md) 0 var(--space-xs) 0; color: var(--text-color-secondary); font-size: var(--font-size-lg); font-weight: var(--font-weight-medium); }
.no-parameters-content p { margin: 0; color: var(--text-color-secondary); font-size: var(--font-size-sm); line-height: 1.5; max-width: 300px; }
</style>
