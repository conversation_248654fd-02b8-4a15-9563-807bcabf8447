// 性能指标计算API Mock处理器 - 严格按照API契约
// 路径、方法、数据结构完全按照backend/app/api/endpoints/metrics.py定义

import { http, HttpResponse } from 'msw';

export const metricsHandlers = [
  // POST /metrics/calculate -// 计算性能指标
  http.post('/api/metrics/calculate', async ({ request }) => {
    const data = await request.json() as any;
    
    if (!data.orders || !Array.isArray(data.orders)) {
      return HttpResponse.json({
        success: false,
        message: "订单数据不能为空",
        error_code: "MISSING_ORDERS"
      }, { status: 400 });
    }
    
    if (!data.start_cash || data.start_cash <= 0) {
      return HttpResponse.json({
        success: false,
        message: "初始资金必须大于0",
        error_code: "INVALID_START_CASH"
      }, { status: 400 });
    }
    
    // 模拟性能指标计算结果
    return HttpResponse.json({
      success: true,
      message: "性能指标计算成功",
      data: {
        // 基础收益指标
        total_return: 0.15,
        annual_return: 0.12,
        cumulative_return: 0.15,
        
        // 风险指标
        volatility: 0.18,
        max_drawdown: -0.08,
        max_drawdown_duration: 15,
        
        // 风险调整收益指标
        sharpe_ratio: 1.25,
        sortino_ratio: 1.45,
        calmar_ratio: 1.5,
        
        // 交易统计
        total_trades: 25,
        winning_trades: 16,
        losing_trades: 9,
        win_rate: 0.64,
        
        // 盈亏统计
        avg_win: 0.08,
        avg_loss: -0.04,
        profit_factor: 2.0,
        
        // 资金曲线
        equity_curve: [
          { date: '2024-01-01', value: data.start_cash },
          { date: '2024-01-15', value: data.start_cash * 1.05 },
          { date: '2024-02-01', value: data.start_cash * 1.08 },
          { date: '2024-02-15', value: data.start_cash * 1.12 },
          { date: '2024-03-01', value: data.start_cash * 1.15 }
        ],
        
        // 回撤曲线
        drawdown_curve: [
          { date: '2024-01-01', value: 0 },
          { date: '2024-01-15', value: -0.02 },
          { date: '2024-02-01', value: -0.05 },
          { date: '2024-02-15', value: -0.08 },
          { date: '2024-03-01', value: -0.03 }
        ],
        
        // 月度收益
        monthly_returns: [
          { month: '2024-01', return: 0.05 },
          { month: '2024-02', return: 0.07 },
          { month: '2024-03', return: 0.03 }
        ]
      }
    });
  })
];