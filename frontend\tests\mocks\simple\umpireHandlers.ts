// 裁判API Mock处理器 - 严格按照API契约

import { http, HttpResponse } from 'msw';

export const umpireHandlers = [
  // 训练裁判模型
  http.post('/api/umpire/train', async ({ request }) => {
    try {
      const trainData = await request.json() as any;
      
      // 验证请求数据结构
      if (!trainData.market_info || !trainData.umpire_rules) {
        return HttpResponse.json({
          success: false,
          message: "训练数据格式错误，缺少market_info或umpire_rules",
          error_code: "INVALID_TRAIN_DATA"
        }, { status: 400 });
      }

      const { market_info, umpire_rules } = trainData;

      // 验证market_info必需字段
      if (!market_info.start || !market_info.end || !market_info.benchmark || !market_info.choice_symbols) {
        return HttpResponse.json({
          success: false,
          message: "market_info缺少必需字段",
          error_code: "INVALID_MARKET_INFO"
        }, { status: 400 });
      }

      // 验证umpire_rules是数组
      if (!Array.isArray(umpire_rules)) {
        return HttpResponse.json({
          success: false,
          message: "umpire_rules必须是数组",
          error_code: "INVALID_UMPIRE_RULES"
        }, { status: 400 });
      }

      // 模拟训练过程
      const mockTrainingResult = {
        task_id: `umpire_train_${Date.now()}`,
        status: 'started',
        message: '裁判模型训练已启动',
        training_config: {
          market_period: {
            start: market_info.start,
            end: market_info.end,
            benchmark: market_info.benchmark
          },
          symbols_count: market_info.choice_symbols.length,
          rules_count: umpire_rules.length,
          estimated_duration: '5-10分钟'
        },
        models: umpire_rules.map((rule: any, index: number) => ({
          rule_id: rule.id || `rule_${index}`,
          rule_name: rule.name || `规则${index + 1}`,
          model_type: rule.type || 'classification',
          status: 'pending',
          progress: 0
        })),
        created_at: new Date().toISOString()
      };

      return HttpResponse.json({
        success: true,
        message: "裁判模型训练启动成功",
        data: mockTrainingResult
      });

    } catch (error) {
      return HttpResponse.json({
        success: false,
        message: "训练请求处理失败",
        error_code: "TRAINING_REQUEST_ERROR"
      }, { status: 500 });
    }
  })
];