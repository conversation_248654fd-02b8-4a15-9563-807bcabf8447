"""
StrategyExecutor 模块测试
"""
import pytest
from unittest.mock import patch, MagicMock, call
import datetime
import inspect  # For test diagnostics
import sys      # For test diagnostics

from backend.app.schemas.strategy import Strategy, BuyFactor, SellFactor
from backend.app.core.exceptions import AdapterError, ParameterError
from backend.app.abupy_adapter.factors_converter import FactorsConverter
from backend.app.abupy_adapter.strategy_executor import StrategyExecutor
from backend.app.abupy_adapter.exceptions import FactorError, ParameterError, ExecutionError
import pandas as pd # Added for sample KL data

# 创建模拟的K线数据DataFrame的辅助函数
def create_sample_kl_df(rows=5):
    """创建模拟的K线数据DataFrame"""
    dates = pd.to_datetime([f'2023-01-{i:02d}' for i in range(1, rows + 1)])
    data = {
        'close': [10 + i for i in range(rows)],
        'open': [9.5 + i for i in range(rows)],
        'high': [10.5 + i for i in range(rows)],
        'low': [9 + i for i in range(rows)],
        'volume': [1000 * (i + 1) for i in range(rows)],
    }
    df = pd.DataFrame(data, index=dates)
    df.index.name = 'date' # Ensure index has a name, common in abupy
    return df

# 创建测试策略的辅助函数
def create_test_strategy():
    """创建用于测试的策略对象"""
    return Strategy(
        id="test_strategy_id",
        name="测试策略",
        description="用于测试的策略",
        buy_factors=[
            BuyFactor(
                name="AbuFactorBuyBreak",
                factor_class="FactorBuyBreak",
                parameters={"xd": 60},
                factor_type="buy"
            )
        ],
        sell_factors=[
            SellFactor(
                name="AbuFactorSellBreak",
                factor_class="FactorSellBreak",
                parameters={"xd": 20},
                factor_type="sell"
            )
        ],
        parameters={"initial_capital": 1000000}
    )

# 创建测试市场数据的辅助函数
def create_test_market_data():
    """创建用于测试的市场数据"""
    return {
        "choice_symbols": ["SH600000"],
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "capital": 1000000,
        "benchmark_symbol": "sh000300"
    }

class TestStrategyExecutor:
    """StrategyExecutor 测试类"""
    
    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_basic(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df):
        """测试基本策略执行流程"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Mock K-line data for benchmark
        # 设置因子转换的模拟返回值 - 分别为买入和卖出因子返回模拟因子
        mock_factor = MagicMock()
        # 重要：确保调用两次时都返回非空因子列表
        mock_convert_factors.side_effect = [[mock_factor], [mock_factor]]
        
        # 设置模拟返回值 - 重要：注意模拟对象的完整属性
        # 创建更完整的模拟对象
        mock_result = MagicMock()
        mock_orders_pd = MagicMock()
        mock_action_pd = MagicMock()
        
        # 关键：设置订单相关属性
        mock_orders_pd.empty = False
        mock_orders_pd.shape = (2, 5)
        mock_orders_pd.__len__.return_value = 2
        
        # 构建完整的结果对象
        mock_result.orders_pd = mock_orders_pd
        mock_result.action_pd = mock_action_pd
        mock_result.capital = 500000
        mock_result.symbol = "SH600000"
        
        # 设置模拟返回值
        mock_do_symbols.return_value = mock_result
        
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        
        # 执行策略
        result = StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)
        
        # 验证结果
        assert result["status"] == "success"
        assert "results" in result
        assert "parameters_used" in result
        assert "execution_summary" in result
        
        # results 是一个列表，每个元素都是一个字典，代表每个股票的结果
        assert isinstance(result["results"], list)
        assert len(result["results"]) > 0
        assert "orders_count" in result["results"][0]
        assert result["results"][0]["orders_count"] == 2
        
        assert result["parameters_used"]["capital"] == 1000000
        assert result["parameters_used"]["symbols"] == ["SH600000"]
        
        # 验证模拟函数被正确调用
        mock_do_symbols.assert_called_once()
    
    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_no_trades(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df):
        """测试无交易的策略执行"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Mock K-line data for benchmark
        # 设置因子转换的模拟返回值
        mock_factor = MagicMock()
        mock_convert_factors.return_value = [mock_factor]
        
        # 设置模拟返回值 - 无订单
        mock_orders_pd = MagicMock()
        mock_action_pd = MagicMock()
        mock_orders_pd.empty = True  # 模拟没有订单
        mock_do_symbols.return_value = {
            "orders_pd": mock_orders_pd,
            "action_pd": mock_action_pd,
            "capital": 100000,
            "benchmark": MagicMock()
        }
        
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        
        # 执行策略
        result = StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)
        
        # 验证结果
        assert result["status"] == "success"
        
        # results 是一个列表，每个元素都是一个字典，代表每个股票的结果
        assert isinstance(result["results"], list)
        assert len(result["results"]) > 0
        assert result["results"][0]["orders_count"] == 0
        assert "message" in result["results"][0]
        assert "该股票没有产生交易" in result["results"][0]["message"]
    
    def test_execute_strategy_missing_symbols(self):
        """测试缺少股票列表的情况"""
        strategy = create_test_strategy()
        # 测试缺少股票列表时应报错
        with pytest.raises(ParameterError, match="缺少必要的参数"):
            StrategyExecutor.execute_strategy(strategy, {"start_date": "2023-01-01", "end_date": "2023-12-31", "capital": 1000000}, FactorsConverter)
    
    def test_execute_strategy_missing_dates(self):
        """测试缺少日期的情况"""
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        market_data.pop("start_date")
        market_data.pop("end_date")
        
        with pytest.raises(ParameterError, match="缺少必要的参数"):
            StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)
    
    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_capital_fallback(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df):
        """测试资金从策略参数中获取的情况"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Mock K-line data for benchmark
        # 设置因子转换的模拟返回值
        mock_factor = MagicMock()
        mock_convert_factors.return_value = [mock_factor]  # 确保返回非空因子列表
        
        # 设置模拟返回值
        mock_orders_pd = MagicMock()
        mock_action_pd = MagicMock()
        mock_orders_pd.empty = True  # 模拟没有订单
        mock_do_symbols.return_value = {
            "orders_pd": mock_orders_pd,
            "action_pd": mock_action_pd,
            "capital": 100000,
            "benchmark": MagicMock()
        }
        
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        # 从市场数据中移除资金参数，测试从策略中获取
        market_data.pop("capital")
        
        # 执行策略
        result = StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)
        
        # 验证结果
        assert result["status"] == "success"
        # 此时资金应该从策略中获取，值为策略的 initial_capital
        assert result["parameters_used"]["capital"] == 1000000
        mock_do_symbols.assert_called_once()
    
    def test_execute_strategy_missing_capital(self):
        """测试完全缺少资金参数的情况"""
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        market_data.pop("capital") # Capital removed from market_data
        
        # 移除策略中的资金参数
        if strategy.parameters: 
            strategy.parameters.pop("initial_capital", None) 
        
        with pytest.raises(ParameterError, match="市场数据和策略参数中都缺少资金参数 'capital' 或 'initial_capital'"):
            StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)

    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_abupy_exception(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df):
        """测试 abupy 执行异常的情况"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Mock K-line data for benchmark
        # 设置因子转换的模拟返回值
        mock_factor = MagicMock()
        mock_convert_factors.return_value = [mock_factor]
        
        # 设置模拟函数抛出异常
        mock_do_symbols.side_effect = Exception("abupy 执行出错")
        
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        
        with pytest.raises(AdapterError, match="abu框架执行错误: abupy 执行出错"):
            StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)

    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors') 
    def test_execute_strategy_factor_error(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df):
        """测试因子转换错误的情况"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Mock K-line data for benchmark
        # 设置模拟函数抛出因子错误
        mock_convert_factors.side_effect = FactorError("因子转换出错")
        
        mock_execution_result = MagicMock() # Define a generic return for do_symbols if it were to be called
        mock_execution_result.orders_pd = MagicMock(empty=True)
        mock_execution_result.action_pd = MagicMock()
        mock_execution_result.capital = 0
        mock_execution_result.benchmark = MagicMock()
        mock_execution_result.symbol = "mock_symbol"
        mock_do_symbols.return_value = mock_execution_result

        strategy = create_test_strategy()
        market_data = create_test_market_data()
        
        with pytest.raises(AdapterError, match="执行策略时遇到因子错误: 因子转换出错"):
            StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)

    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df')
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_execute_strategy_with_mocked_execution(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df):
        """测试模拟执行策略"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Mock K-line data for benchmark
        # 设置因子转换的模拟返回值
        mock_factor = MagicMock()
        mock_convert_factors.return_value = [mock_factor]
        
        # 设置模拟返回值
        mock_orders_pd = MagicMock()
        mock_action_pd = MagicMock()
        mock_orders_pd.empty = False
        mock_orders_pd.shape = (2, 5)
        mock_orders_pd.__len__.return_value = 2 # Ensure __len__ is mocked for orders_count calculation

        mock_execution_result = MagicMock()
        mock_execution_result.orders_pd = mock_orders_pd
        mock_execution_result.action_pd = mock_action_pd
        mock_execution_result.capital = 120000
        mock_execution_result.benchmark = MagicMock()
        mock_execution_result.symbol = "SH600000" 
        mock_do_symbols.return_value = mock_execution_result
        
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        
        # 执行策略
        result = StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)
        
        # 验证结果
        assert result["status"] == "success"
        assert isinstance(result["results"], list)
        assert len(result["results"]) > 0 
        assert "orders_count" in result["results"][0]
        assert result["results"][0]["orders_count"] == 2 
        
        # 验证模拟函数被正确调用
        mock_do_symbols.assert_called_once()
    
    @patch('app.abupy_adapter.strategy_executor.ABuSymbolPd.make_kl_df') # Added mock for benchmark K-line
    @patch('app.abupy_adapter.factors_converter.FactorsConverter.convert_to_abu_factors')
    @patch('app.abupy_adapter.strategy_executor.do_symbols_with_same_factors')
    def test_parameter_extraction(self, mock_do_symbols, mock_convert_factors, mock_make_kl_df): # Added mock_make_kl_df parameter
        """测试参数提取方法"""
        mock_make_kl_df.return_value = create_sample_kl_df() # Ensure benchmark K-line is mocked
        # 设置因子转换的模拟返回值
        mock_factor = MagicMock()
        mock_convert_factors.return_value = [mock_factor]
        
        # 设置模拟返回值
        mock_orders_pd = MagicMock()
        mock_action_pd = MagicMock()
        mock_orders_pd.empty = True  # 模拟没有订单
        mock_do_symbols.return_value = {
            "orders_pd": mock_orders_pd,
            "action_pd": mock_action_pd,
            "capital": 100000,
            "benchmark": MagicMock()
        }
        
        strategy = create_test_strategy()
        market_data = create_test_market_data()
        
        # 直接验证市场数据中的参数是否正确
        assert market_data["choice_symbols"] == ["SH600000"]
        assert market_data["start_date"] == "2023-01-01"
        assert market_data["end_date"] == "2023-12-31"
        assert market_data["capital"] == 1000000
        assert market_data["benchmark_symbol"] == "sh000300"
        
        # 验证这些参数可以被正确使用
        result = StrategyExecutor.execute_strategy(strategy, market_data, FactorsConverter)
        assert result["status"] == "success"
        
        # 验证模拟函数被正确调用
        # convert_to_abu_factors 会被调用两次，一次用于买入因子，一次用于卖出因子
        assert mock_convert_factors.call_count == 2
        mock_do_symbols.assert_called_once()
