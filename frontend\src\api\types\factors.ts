// src/api/types/factors.ts

// 因子类别枚举
export const FactorCategory = {
  TECHNICAL: 'technical',        // 技术指标
  FUNDAMENTAL: 'fundamental',    // 基本面
  MACRO: 'macro',               // 宏观经济
  SENTIMENT: 'sentiment',       // 市场情绪
  CUSTOM: 'custom'              // 自定义
} as const;

export type FactorCategory = typeof FactorCategory[keyof typeof FactorCategory];

// 因子数据类型
export const FactorDataType = {
  NUMERIC: 'numeric',           // 数值型
  CATEGORICAL: 'categorical',   // 分类型
  BOOLEAN: 'boolean'            // 布尔型
} as const;

export type FactorDataType = typeof FactorDataType[keyof typeof FactorDataType];

// 因子参数接口
export interface FactorParameter {
  name: string;                  // 参数名称
  type: 'number' | 'string' | 'boolean' | 'array';
  default_value: any;            // 默认值
  description?: string;          // 参数描述
  min_value?: number;            // 最小值（数值型参数）
  max_value?: number;            // 最大值（数值型参数）
  options?: string[];            // 可选值（字符串型参数）
  required?: boolean;            // 是否必需
}

// 因子定义接口
export interface Factor {
  id: string;                    // 因子ID
  name: string;                  // 因子名称
  description: string;           // 因子描述
  factor_type: string;           // 因子类型 'buy' 或 'sell'
  class_name: string;            // 类名
  parameters: Record<string, any>; // 参数对象
  formula?: string;              // 计算公式
  dependencies?: string[];       // 依赖的数据字段
  frequency?: string;            // 计算频率
  lookback_period?: number;      // 回看周期
  created_at?: string;           // 创建时间
  updated_at?: string;           // 更新时间
  is_builtin?: boolean;          // 是否内置因子
  is_active?: boolean;           // 是否激活
  usage_count?: number;          // 使用次数
  rating?: number;               // 评分
  tags?: string[];               // 标签
  author?: string;               // 作者
  version?: string;              // 版本
}

// 自定义因子定义
export interface CustomFactorDefinition {
  name: string;
  description: string;
  category: FactorCategory;
  data_type: FactorDataType;
  formula: string;
  parameters: FactorParameter[];
  tags?: string[];
}

// 因子值接口
export interface FactorValue {
  factor_id: string;             // 因子ID
  symbol: string;                // 股票代码
  date: string;                  // 日期
  value: number | string | boolean; // 因子值
  percentile?: number;           // 百分位数
  z_score?: number;              // Z分数
}

// 因子测试请求
export interface FactorTestRequest {
  factor_id: string;
  symbols: string[];             // 股票代码列表
  start_date: string;
  end_date: string;
  parameters?: Record<string, any>; // 因子参数
}

// 因子测试结果
export interface FactorTestResult {
  factor_id: string;
  test_period: {
    start_date: string;
    end_date: string;
  };
  statistics: {
    mean: number;
    std: number;
    min: number;
    max: number;
    skewness: number;
    kurtosis: number;
    sharpe_ratio?: number;
    information_ratio?: number;
  };
  correlation_with_returns?: number;
  ic_analysis?: {
    ic_mean: number;
    ic_std: number;
    ic_ir: number;
    ic_win_rate: number;
  };
  factor_values: FactorValue[];
  performance_metrics?: {
    annual_return: number;
    volatility: number;
    max_drawdown: number;
  };
}

// 因子查询参数
export interface FactorQueryParams {
  category?: FactorCategory;
  data_type?: FactorDataType;
  is_builtin?: boolean;
  is_active?: boolean;
  search?: string;               // 搜索关键词
  tags?: string[];               // 标签过滤
  page?: number;
  page_size?: number;
  sort_by?: 'name' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

// 因子计算请求
export interface FactorCalculationRequest {
  factor_id: string;
  symbols: string[];
  start_date: string;
  end_date: string;
  parameters?: Record<string, any>;
  normalize?: boolean;           // 是否标准化
  fill_method?: 'forward' | 'backward' | 'interpolate' | 'drop';
}

// API响应类型
export interface FactorResponse {
  success: boolean;
  data: Factor;
  message?: string;
}

export interface FactorsResponse {
  success: boolean;
  data: Factor[];
  total: number;
  page: number;
  page_size: number;
  message?: string;
}

export interface FactorTestResponse {
  success: boolean;
  data: FactorTestResult;
  message?: string;
}

export interface FactorValuesResponse {
  success: boolean;
  data: FactorValue[];
  message?: string;
}

export interface FactorCreationResponse {
  success: boolean;
  data: Factor;
  message?: string;
}

// 因子验证错误
export interface FactorValidationError {
  field: string;
  message: string;
  code: string;
}

export interface FactorValidationResponse {
  success: boolean;
  errors?: FactorValidationError[];
  warnings?: string[];
  message?: string;
}

// 因子实例接口（保持向后兼容性）
export interface FactorInstance {
  id: string;
  name: string;
  class_name: string;
  factor_type: 'buy' | 'sell';
  description?: string;
  parameters: Record<string, any>;
  exclusive_to_buy_factor?: string;  // 专属于某个买入因子的标记
}

export interface BuyFactorInstance extends FactorInstance {
  factor_type: 'buy';
  sell_factors?: SellFactorInstance[];  // 专属卖出规则
}

export interface SellFactorInstance extends FactorInstance {
  factor_type: 'sell';
}