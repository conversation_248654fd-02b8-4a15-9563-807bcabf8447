import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as backtestApi from '../../src/api/modules/backtest'
import { apiClient } from '../../src/api/client'
import type { BacktestConfig, BacktestResult, BacktestTask } from '../../src/api/types/backtest'
import { SimpleBacktestDataFactory } from '../factories/SimpleBacktestDataFactory'

// Mock API client
vi.mock('../../src/api/client', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
    delete: vi.fn()
  }
}))

const mockApiClient = vi.mocked(apiClient)

describe('Backtest API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('startBacktest', () => {
    it('应该能够启动回测任务', async () => {
      // Arrange
      const config: BacktestConfig = SimpleBacktestDataFactory.createSimpleConfig()
      const expectedTask: BacktestTask = SimpleBacktestDataFactory.createSimpleTask(config)
      
      mockApiClient.post.mockResolvedValue({ data: expectedTask })

      // Act
      const result = await backtestApi.startBacktest(config)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith(`/api/v1/strategy/${config.strategy_id}/execute`, {
        choice_symbols: [config.symbol],
        start_date: config.start_date,
        end_date: config.end_date,
        initial_capital: config.capital,
        benchmark_symbol: config.benchmark || 'sh000300',
        data_source: 'tushare'
      })
      expect(result).toEqual({ data: expectedTask })
    })

    it('应该处理API错误', async () => {
      // Arrange
      const config: BacktestConfig = SimpleBacktestDataFactory.createSimpleConfig()
      const error = new Error('网络错误')
      
      mockApiClient.post.mockRejectedValue(error)

      // Act & Assert
      await expect(backtestApi.startBacktest(config)).rejects.toThrow('网络错误')
    })
  })

  describe('getBacktestResult', () => {
    it('应该能够获取回测结果', async () => {
      // Arrange
      const taskId = 'test-task-id'
      const expectedResult: BacktestResult = SimpleBacktestDataFactory.createSimpleResult('test-task-id')
      
      mockApiClient.get.mockResolvedValue({ data: expectedResult })

      // Act
      const result = await backtestApi.getBacktestResults(taskId)

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(`/api/v1/backtest/results/${taskId}`)
      expect(result).toEqual({ data: expectedResult })
    })

    it('应该处理任务不存在的情况', async () => {
      // Arrange
      const taskId = 'non-existent-task'
      const error = new Error('任务不存在')
      
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(backtestApi.getBacktestResults(taskId)).rejects.toThrow('任务不存在')
    })
  })

  describe('getBacktestHistory', () => {
    it('应该能够获取回测历史', async () => {
      // Arrange
      const strategyId = 'test-strategy-id'
      const expectedHistory: BacktestResult[] = [
        SimpleBacktestDataFactory.createSimpleResult('task-1'),
        SimpleBacktestDataFactory.createSimpleResult('task-2')
      ]
      
      mockApiClient.get.mockResolvedValue({ data: expectedHistory })

      // Act
      const result = await backtestApi.getBacktestHistory({ strategy_id: strategyId })

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/backtest/history', { params: { strategy_id: strategyId } })
      expect(result).toEqual({ data: expectedHistory })
    })
  })

  describe('stopBacktest', () => {
    it('应该能够停止回测任务', async () => {
      // Arrange
      const taskId = 'test-task-id'
      const expectedResponse = { success: true }
      
      mockApiClient.post.mockResolvedValue({ data: expectedResponse })

      // Act
      const result = await backtestApi.stopBacktest(taskId)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith(`/api/v1/backtest/stop/${taskId}`)
      expect(result).toEqual({ data: expectedResponse })
    })
  })

  describe('getBacktestProgress', () => {
    it('应该能够获取回测进度', async () => {
      // Arrange
      const taskId = 'test-task-id'
      const expectedProgress = { progress: 50, status: 'running' }
      
      mockApiClient.get.mockResolvedValue({ data: expectedProgress })

      // Act
      const result = await backtestApi.getBacktestProgress(taskId)

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(`/api/v1/backtest/progress/${taskId}`)
      expect(result).toEqual({ data: expectedProgress })
    })
  })

  describe('deleteBacktest', () => {
    it('应该能够删除回测任务', async () => {
      // Arrange
      const taskId = 'test-task-id'
      mockApiClient.delete.mockResolvedValue(undefined)

      // Act
      await backtestApi.deleteBacktestTask(taskId)

      // Assert
      expect(mockApiClient.delete).toHaveBeenCalledWith(`/api/v1/backtest/tasks/${taskId}`)
    })
  })
})