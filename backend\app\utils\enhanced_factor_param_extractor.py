#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版abupy因子参数提取器
基于深度勘探结果实现，支持所有5种参数模式
"""

import re
import inspect
import ast
import os
import logging
from typing import Dict, Any, Optional, List

class EnhancedFactorParamExtractor:
    """增强版abupy因子参数提取器"""
    
    # 系统参数，需要过滤
    SYSTEM_PARAMS = {
        'capital', 'kl_pd', 'combine_kl_pd', 'benchmark', 
        'self', 'kwargs', 'args', 'slippage', 'position',
        'stock_pickers', 'sell_factors', 'win_rate', 
        'gains_mean', 'losses_mean'
    }
    
    def extract_factor_params(self, factor_class) -> Dict[str, Any]:
        """提取因子的用户可配置参数"""
        
        # 主要方法：从_init_self方法提取
        if hasattr(factor_class, '_init_self'):
            return self._extract_from_init_self_method(factor_class)
        
        # 备用方法：从__init__签名提取
        return self._extract_from_init_signature(factor_class)
    
    def _extract_from_init_self_method(self, factor_class) -> Dict[str, Any]:
        """从因子类的_init_self方法提取参数"""
        try:
            # 获取因子类的源码
            import inspect
            source = inspect.getsource(factor_class)
            
            # 从源码中提取参数
            return self._extract_from_source_content(source, factor_class.__name__)
            
        except Exception as e:
            logging.warning(f"从因子类 {factor_class.__name__} 的_init_self方法提取参数时出错: {e}")
            return {}
    
    def extract_factor_params_from_file(self, file_path: str, class_name: str) -> Dict[str, Any]:
        """从文件直接提取因子参数（用于无法导入模块的情况）"""
        
        if not os.path.exists(file_path):
            return {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self._extract_from_source_content(content, class_name)
        except Exception as e:
            logging.warning(f"从文件 {file_path} 提取参数时出错: {e}")
            return {}
    
    def _extract_from_source_content(self, content: str, class_name: str) -> Dict[str, Any]:
        """从源码内容提取参数"""
        
        # 提取_init_self方法
        init_self_pattern = rf'class\s+{class_name}.*?def\s+_init_self\s*\([^)]*\):(.*?)(?=def\s+|\Z)'
        init_self_match = re.search(init_self_pattern, content, re.DOTALL)
        
        if not init_self_match:
            return {}
        
        init_self_body = init_self_match.group(1)
        
        # 解析全局变量
        global_vars = self._extract_global_variables(content)
        
        # 解析参数
        return self._parse_init_self_body_complete(init_self_body, global_vars)
    
    def _extract_global_variables(self, content: str) -> Dict[str, Any]:
        """提取文件中的全局变量"""
        global_vars = {}
        
        # 查找 g_default_* = value 模式
        global_pattern = r'^(g_default_\w+)\s*=\s*([^#\n]+)'
        
        for match in re.finditer(global_pattern, content, re.MULTILINE):
            var_name, var_value = match.groups()
            try:
                global_vars[var_name] = self._parse_default_value(var_value.strip())
            except:
                # 如果解析失败，保持原始值
                global_vars[var_name] = var_value.strip()
        
        return global_vars
    
    def _parse_init_self_body_complete(self, init_self_body: str, global_vars: Dict[str, Any]) -> Dict[str, Any]:
        """完整解析_init_self方法体"""
        
        params = {}
        lines = init_self_body.split('\n')
        
        # 第一遍：收集所有赋值和条件
        assignments = {}  # attr_name -> value
        conditionals = set()  # param_name
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            if not line or line.startswith('#') or line.startswith('"""'):
                continue
            
            # 收集所有self.attr = value的赋值
            assign_match = re.search(r"self\.(\w+)\s*=\s*([^#\n]+)", line)
            if assign_match:
                attr_name, value = assign_match.groups()
                assignments[attr_name] = value.strip()
            
            # 收集条件检查
            conditional_match = re.search(r"if\s+['\"](.*?)['\"]\s+in\s+kwargs:", line)
            if conditional_match:
                param_name = conditional_match.group(1)
                conditionals.add(param_name)
        
        # 第二遍：分析参数模式
        for i, line in enumerate(lines):
            line = line.strip()
            
            if not line or line.startswith('#') or line.startswith('"""'):
                continue
            
            # 模式1: kwargs['param'] - 必需参数
            required_match = re.search(r"self\.(\w+)\s*=\s*kwargs\[['\"](.*?)['\"]\]", line)
            if required_match:
                attr_name, param_name = required_match.groups()
                if param_name not in conditionals:  # 不在条件块中才是必需的
                    params[param_name] = {
                        'type': self._infer_type_from_name(param_name),
                        'required': True,
                        'default': None,
                        'description': self._extract_description_from_lines(lines, i)
                    }
                continue
            
            # 模式2: kwargs.pop('param', default) - 可选参数
            pop_match = re.search(r"self\.(\w+)\s*=\s*kwargs\.pop\(['\"](.*?)['\"]\s*,\s*(.*?)\)", line)
            if pop_match:
                attr_name, param_name, default_value = pop_match.groups()
                params[param_name] = {
                    'type': self._infer_type_from_default(default_value),
                    'required': False,
                    'default': self._parse_default_value(default_value),
                    'description': self._extract_description_from_lines(lines, i)
                }
                continue
            
            # 模式3: kwargs.pop('param') - 必需参数（pop形式）
            required_pop_match = re.search(r"self\.(\w+)\s*=\s*kwargs\.pop\(['\"](.*?)['\"]\)", line)
            if required_pop_match:
                attr_name, param_name = required_pop_match.groups()
                params[param_name] = {
                    'type': self._infer_type_from_name(param_name),
                    'required': True,
                    'default': None,
                    'description': self._extract_description_from_lines(lines, i)
                }
                continue
        
        # 第三遍：处理条件参数
        for param_name in conditionals:
            if param_name not in params:
                # 查找该参数的默认值
                default_value = None
                
                # 在赋值中查找
                if param_name in assignments:
                    default_expr = assignments[param_name]
                    
                    # 如果是全局变量
                    if default_expr in global_vars:
                        default_value = global_vars[default_expr]
                    else:
                        default_value = self._parse_default_value(default_expr)
                
                params[param_name] = {
                    'type': self._infer_type_from_name(param_name),
                    'required': False,  # 条件参数都是可选的
                    'default': default_value,
                    'description': self._extract_description_from_lines(lines, 0)
                }
        
        return self._filter_user_params(params)
    
    def _extract_description_from_lines(self, lines: List[str], line_idx: int) -> Optional[str]:
        """从代码行中提取参数描述"""
        # 查找前面的注释或文档字符串
        for i in range(max(0, line_idx - 5), line_idx + 1):
            if i < len(lines):
                line = lines[i].strip()
                if '"""' in line or line.startswith('#'):
                    # 提取描述文本
                    desc = re.sub(r'[#"""]+', '', line).strip()
                    if desc and any(keyword in desc for keyword in ['参数', 'param', '可选', '必须']):
                        return desc
        return None
    
    def _infer_type_from_name(self, param_name: str) -> str:
        """根据参数名推断类型"""
        param_name = param_name.lower()
        
        if param_name == 'xd' or 'period' in param_name or param_name in ['fast', 'slow']:
            return 'int'
        elif param_name.endswith('_n') or 'threshold' in param_name or 'rate' in param_name or param_name.startswith('buy_'):
            return 'float'
        elif 'is_' in param_name or param_name.startswith('is_') or 'dynamic' in param_name:
            return 'bool'
        else:
            return 'str'
    
    def _infer_type_from_default(self, default_value: str) -> str:
        """根据默认值推断类型"""
        default_value = default_value.strip()
        
        try:
            parsed = ast.literal_eval(default_value)
            return type(parsed).__name__
        except:
            if default_value.isdigit() or (default_value.startswith('-') and default_value[1:].isdigit()):
                return 'int'
            elif '.' in default_value and default_value.replace('.', '').replace('-', '').isdigit():
                return 'float'
            elif default_value.lower() in ['true', 'false']:
                return 'bool'
            else:
                return 'str'
    
    def _parse_default_value(self, default_value: str) -> Any:
        """解析默认值"""
        if not default_value:
            return None
            
        default_value = default_value.strip()
        
        try:
            return ast.literal_eval(default_value)
        except:
            if default_value.lower() == 'true':
                return True
            elif default_value.lower() == 'false':
                return False
            elif default_value.lower() == 'none':
                return None
            else:
                # 保持原始字符串
                return default_value
    
    def _filter_user_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """过滤出用户可配置参数"""
        return {k: v for k, v in params.items() if k not in self.SYSTEM_PARAMS}
    
    def _extract_from_init_signature(self, factor_class) -> Dict[str, Any]:
        """从__init__方法签名提取参数（备用方法）"""
        try:
            sig = inspect.signature(factor_class.__init__)
            params = {}
            
            for param_name, param in sig.parameters.items():
                if param_name in self.SYSTEM_PARAMS:
                    continue
                
                if param.default != inspect.Parameter.empty:
                    params[param_name] = {
                        'type': type(param.default).__name__ if param.default is not None else 'str',
                        'required': False,
                        'default': param.default,
                        'description': None
                    }
                elif param.kind != inspect.Parameter.VAR_KEYWORD:
                    params[param_name] = {
                        'type': 'str',
                        'required': True,
                        'default': None,
                        'description': None
                    }
            
            return params
        except Exception as e:
            logging.warning(f"从__init__签名提取参数时出错: {e}")
            return {}

# 测试函数
def test_enhanced_extractor():
    """测试增强版参数提取器"""
    
    extractor = EnhancedFactorParamExtractor()
    
    # 测试用例：模拟的因子类源码
    test_source = '''
class TestFactor:
    g_default_stop_n = 3
    
    def _init_self(self, **kwargs):
        # 必需参数
        self.xd = kwargs['xd']
        
        # 可选参数
        self.fast_ma = kwargs.pop('fast_ma', 5)
        self.slow_ma = kwargs.pop('slow_ma', 20)
        
        # 条件参数
        if 'stop_loss_n' in kwargs:
            self.stop_loss_n = kwargs['stop_loss_n']
        if 'stop_win_n' in kwargs:
            self.stop_win_n = kwargs['stop_win_n']
        
        # 全局默认值参数
        self.close_atr_n = g_default_stop_n
        if 'close_atr_n' in kwargs:
            self.close_atr_n = kwargs['close_atr_n']
    '''
    
    params = extractor._extract_from_source_content(test_source, 'TestFactor')
    
    print("=== 增强版参数提取器测试 ===\n")
    print(f"提取到 {len(params)} 个参数:")
    for param_name, param_info in params.items():
        required_str = "必需" if param_info['required'] else "可选"
        default_str = f", 默认值={param_info['default']}" if param_info['default'] is not None else ""
        print(f"  - {param_name} ({param_info['type']}): {required_str}{default_str}")
    
    return params

if __name__ == '__main__':
    test_enhanced_extractor()
