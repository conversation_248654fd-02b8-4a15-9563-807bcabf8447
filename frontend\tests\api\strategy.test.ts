import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as strategyApi from '../../src/api/modules/strategy'
import { apiClient } from '../../src/api/client'
import type { Strategy, CreateStrategyRequest, UpdateStrategyRequest } from '../../src/types'
import { SimpleStrategyDataFactory } from '../factories/SimpleStrategyDataFactory'

// Mock API client
vi.mock('../../src/api/client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}))

const mockApiClient = vi.mocked(apiClient)

describe('Strategy API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getStrategies', () => {
    it('应该能够获取所有策略列表', async () => {
      // Arrange
      const expectedStrategies: Strategy[] = [
        SimpleStrategyDataFactory.createStrategy(),
        SimpleStrategyDataFactory.createStrategy()
      ]
      
      mockApiClient.get.mockResolvedValue({ data: expectedStrategies })

      // Act
      const result = await strategyApi.getStrategies()

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/strategy?limit=100')
      expect(result).toEqual(expectedStrategies)
    })

    it('应该处理获取策略列表失败的情况', async () => {
      // Arrange
      const error = new Error('获取策略列表失败')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(strategyApi.getStrategies()).rejects.toThrow('获取策略列表失败')
    })
  })

  describe('getStrategy', () => {
    it('应该能够根据ID获取特定策略', async () => {
      // Arrange
      const strategyId = 'test-strategy-id'
      const expectedStrategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      
      mockApiClient.get.mockResolvedValue({ data: expectedStrategy })

      // Act
      const result = await strategyApi.getStrategy(strategyId)

      // Assert
      expect(mockApiClient.get).toHaveBeenCalledWith(`/api/v1/strategy/${strategyId}`)
      expect(result).toEqual(expectedStrategy)
    })

    it('应该处理策略不存在的情况', async () => {
      // Arrange
      const strategyId = 'non-existent-strategy'
      const error = new Error('策略不存在')
      mockApiClient.get.mockRejectedValue(error)

      // Act & Assert
      await expect(strategyApi.getStrategy(strategyId)).rejects.toThrow('策略不存在')
    })
  })

  describe('createStrategy', () => {
    it('应该能够创建新策略', async () => {
      // Arrange
      const createRequest: CreateStrategyRequest = {
        name: '测试策略',
        description: '这是一个测试策略',
        buy_factors: [],
        sell_factors: []
      }
      const expectedStrategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      
      mockApiClient.post.mockResolvedValue({ data: expectedStrategy })

      // Act
      const result = await strategyApi.createStrategy(createRequest)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith('/api/v1/strategy', createRequest)
      expect(result).toEqual(expectedStrategy)
    })

    it('应该处理创建策略失败的情况', async () => {
      // Arrange
      const createRequest: CreateStrategyRequest = {
        name: '',
        description: '',
        buy_factors: [],
        sell_factors: []
      }
      const error = new Error('策略名称不能为空')
      mockApiClient.post.mockRejectedValue(error)

      // Act & Assert
      await expect(strategyApi.createStrategy(createRequest)).rejects.toThrow('策略名称不能为空')
    })
  })

  describe('updateStrategy', () => {
    it('应该能够更新现有策略', async () => {
      // Arrange
      const strategyId = 'test-strategy-id'
      const updateRequest: UpdateStrategyRequest = {
        name: '更新后的策略名称',
        description: '更新后的描述'
      }
      const expectedStrategy: Strategy = SimpleStrategyDataFactory.createStrategy()
      
      mockApiClient.put.mockResolvedValue({ data: expectedStrategy })

      // Act
      const result = await strategyApi.updateStrategy(strategyId, updateRequest)

      // Assert
      expect(mockApiClient.put).toHaveBeenCalledWith(`/api/v1/strategy/${strategyId}`, updateRequest)
      expect(result).toEqual(expectedStrategy)
    })

    it('应该处理更新不存在策略的情况', async () => {
      // Arrange
      const strategyId = 'non-existent-strategy'
      const updateRequest: UpdateStrategyRequest = {
        name: '更新后的策略名称'
      }
      const error = new Error('策略不存在')
      mockApiClient.put.mockRejectedValue(error)

      // Act & Assert
      await expect(strategyApi.updateStrategy(strategyId, updateRequest)).rejects.toThrow('策略不存在')
    })
  })

  describe('deleteStrategy', () => {
    it('应该能够删除策略', async () => {
      // Arrange
      const strategyId = 'test-strategy-id'
      mockApiClient.delete.mockResolvedValue(undefined)

      // Act
      await strategyApi.deleteStrategy(strategyId)

      // Assert
      expect(mockApiClient.delete).toHaveBeenCalledWith(`/api/v1/strategy/${strategyId}`)
    })

    it('应该处理删除不存在策略的情况', async () => {
      // Arrange
      const strategyId = 'non-existent-strategy'
      const error = new Error('策略不存在')
      mockApiClient.delete.mockRejectedValue(error)

      // Act & Assert
      await expect(strategyApi.deleteStrategy(strategyId)).rejects.toThrow('策略不存在')
    })
  })

  describe('executeStrategy', () => {
    it('应该能够执行策略', async () => {
      // Arrange
      const strategyId = 'test-strategy-id'
      const config = {
        symbol: '000001.SZ',
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      }
      const expectedResponse = { task_id: 'execution-task-id' }
      
      mockApiClient.post.mockResolvedValue({ data: expectedResponse })

      // Act
      const result = await strategyApi.executeStrategy(strategyId, config)

      // Assert
      expect(mockApiClient.post).toHaveBeenCalledWith(`/api/v1/strategy/${strategyId}/execute`, config)
      expect(result).toEqual({ data: expectedResponse })
    })

    it('应该处理执行策略失败的情况', async () => {
      // Arrange
      const strategyId = 'test-strategy-id'
      const config = {}
      const error = new Error('执行参数不完整')
      mockApiClient.post.mockRejectedValue(error)

      // Act & Assert
      await expect(strategyApi.executeStrategy(strategyId, config)).rejects.toThrow('执行参数不完整')
    })
  })
})