# -*- coding: utf-8 -*-
print("ABUPY_ADAPTER_INIT_DIAG: Loading abupy_adapter/__init__.py")

import collections
import collections.abc
import sys

# Compatibility patch for Python 3.10+ where collections.Iterable was removed.
# This must be executed before any abupy modules are imported.
# When abupy.CoreBu.ABuPdHelper tries `from collections import Iterable`,
# this patch ensures it finds collections.abc.Iterable via collections.Iterable.
if sys.version_info >= (3, 10):
    if not hasattr(collections, 'Iterable'):
        setattr(collections, 'Iterable', collections.abc.Iterable)
    if not hasattr(collections, 'Callable'): # collections.Callable is removed in 3.10, deprecated in 3.3
        setattr(collections, 'Callable', collections.abc.Callable)
    if not hasattr(collections, 'Mapping'): # collections.Mapping is removed in 3.10
        setattr(collections, 'Mapping', collections.abc.Mapping)
    # Add more patches if other similar ImportErrors from 'collections' arise from abupy
    # For example, for other common ones that were moved to collections.abc:
    # if not hasattr(collections, 'Mapping'):


# Monkeypatch for IPython.display.clear_output if IPython is not installed.
# This is to prevent ModuleNotFoundError in abupy.UtilBu.ABuProgress
import types
try:
    from IPython.display import clear_output
except ModuleNotFoundError:
    # Create a mock IPython module
    ipython_mock = types.ModuleType('IPython')
    sys.modules['IPython'] = ipython_mock

    # Create a mock IPython.display submodule
    ipython_display_mock = types.ModuleType('IPython.display')
    sys.modules['IPython.display'] = ipython_display_mock

    # Define a dummy clear_output function
    def _dummy_clear_output(wait=False):
        pass  # In a non-interactive environment, this can be a no-op

    def _dummy_display(*args, **kwargs):
        pass # In a non-interactive environment, this can be a no-op

    ipython_display_mock.clear_output = _dummy_clear_output
    ipython_display_mock.display = _dummy_display


# Monkeypatch for ipywidgets if it's not installed.
# This is to prevent ModuleNotFoundError in abupy.UtilBu.ABuProgress
try:
    from ipywidgets import FloatProgress, Text, Box
except ModuleNotFoundError:
    # Create a mock ipywidgets module
    ipywidgets_mock = types.ModuleType('ipywidgets')
    sys.modules['ipywidgets'] = ipywidgets_mock

    # Define dummy classes for the widgets
    class DummyWidget:
        def __init__(self, *args, **kwargs):
            pass
        def layout_display(self, *args, **kwargs):
            pass # some widgets might have this called
        def add_class(self, *args, **kwargs):
            pass # some widgets might have this called
        def remove_class(self, *args, **kwargs):
            pass # some widgets might have this called
        # Add other methods that might be called by ABuProgress if necessary

    ipywidgets_mock.FloatProgress = DummyWidget
    ipywidgets_mock.Text = DummyWidget
    ipywidgets_mock.Box = DummyWidget


# Monkeypatch for scipy.interp
# abupy.MLBu.ABuMLExecute.py tries `from scipy import interp`
# `scipy.interp` was removed/moved in newer SciPy versions; numpy.interp is the replacement.
try:
    import scipy
    import numpy
    if not hasattr(scipy, 'interp'):
        setattr(scipy, 'interp', numpy.interp)
except ImportError:
    # If scipy or numpy is not installed, this patch might fail.
    # Given the error, scipy is installed. Assume numpy is also available.
    pass
except Exception:
    # Catch any other unexpected error during patching.
    pass

"""
abu框架适配器包，用于连接原有abu框架与现代化的FastAPI架构

本模块重构为多个小文件以提高可维护性：
- strategy_adapter.py - 策略适配器主类
- factors_converter.py - 因子转换逻辑
- strategy_executor.py - 策略执行逻辑
- benchmark.py - 基准对象相关逻辑
- exceptions.py - 异常定义
"""

# 导出主要的类和函数，使用户可以直接从app.abupy_adapter导入
print("ABUPY_ADAPTER_INIT_DIAG: About to import .strategy_adapter")
from .strategy_adapter import StrategyAdapter
print("ABUPY_ADAPTER_INIT_DIAG: Imported .strategy_adapter")
print("ABUPY_ADAPTER_INIT_DIAG: About to import .benchmark")
from .benchmark import SimpleBenchmark, create_benchmark
print("ABUPY_ADAPTER_INIT_DIAG: Imported .benchmark")
print("ABUPY_ADAPTER_INIT_DIAG: About to import .factors_converter")
from .factors_converter import FactorsConverter
print("ABUPY_ADAPTER_INIT_DIAG: Imported .factors_converter")
print("ABUPY_ADAPTER_INIT_DIAG: About to import .strategy_executor")
from .strategy_executor import StrategyExecutor
print("ABUPY_ADAPTER_INIT_DIAG: Imported .strategy_executor")
print("ABUPY_ADAPTER_INIT_DIAG: About to import .exceptions")
from .exceptions import AdapterError, FactorError, ParameterError
print("ABUPY_ADAPTER_INIT_DIAG: Imported .exceptions")

__all__ = [
    'StrategyAdapter',
    'SimpleBenchmark',
    'create_benchmark',
    'FactorsConverter',
    'StrategyExecutor',
    'AdapterError',
    'FactorError',
    'ParameterError'
]
