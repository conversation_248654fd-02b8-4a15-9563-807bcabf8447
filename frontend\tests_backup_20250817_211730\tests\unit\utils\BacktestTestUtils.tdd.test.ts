import { describe, it, expect, beforeEach, afterEach, afterAll, vi } from 'vitest';
import { BacktestTestUtils } from '../../utils/BacktestTestUtils';
import type { BacktestMetrics, EquityPoint, Trade } from '../../../src/api/types/backtest';

describe('BacktestTestUtils - TDD Core', () => {
  describe('基础数据处理', () => {
    it('应该能验证数值精度 - TDD', () => {
      // 测试有效数值
      const validResult = BacktestTestUtils.validateNumericalPrecision(123.45, '测试数值');
      expect(validResult.isValid).toBe(true);
      expect(validResult.issues).toHaveLength(0);
      
      // 测试NaN
      const nanResult = BacktestTestUtils.validateNumericalPrecision(NaN, 'NaN测试');
      expect(nanResult.isValid).toBe(false);
      expect(nanResult.issues[0]).toContain('为NaN');
    });

    it('应该能验证百分比数值精度 - TDD', () => {
      // 测试相等的百分比
      expect(() => {
        BacktestTestUtils.expectToBeCloseToPercentage(0.1234, 0.1234, 2);
      }).not.toThrow();
      
      // 测试不相等的百分比
      expect(() => {
        BacktestTestUtils.expectToBeCloseToPercentage(0.1, 0.2, 2);
      }).toThrow('百分比数值不匹配');
    });

    it('应该能验证基础金融指标逻辑 - TDD', () => {
      const validMetrics: BacktestMetrics = {
        total_trades: 10,
        winning_trades: 6,
        losing_trades: 4,
        win_rate: 0.6,
        total_return: 0.15,
        annual_return: 0.15,
        max_drawdown: -0.05,
        sharpe_ratio: 1.2,
        profit_loss_ratio: 1.5,
        volatility: 0.2,
        avg_holding_period: 5
      };
      
      expect(() => {
        BacktestTestUtils.validateFinancialMetricsLogic(validMetrics);
      }).not.toThrow();
    });

    it('应该能检测无效的金融指标 - TDD', () => {
      const invalidMetrics: BacktestMetrics = {
        total_trades: 10,
        winning_trades: 6,
        losing_trades: 3, // 错误：6 + 3 ≠ 10
        win_rate: 0.6,
        total_return: 0.15,
        annual_return: 0.15,
        max_drawdown: -0.05,
        sharpe_ratio: 1.2,
        profit_loss_ratio: 1.5,
        volatility: 0.2,
        avg_holding_period: 5
      };
      
      expect(() => {
        BacktestTestUtils.validateFinancialMetricsLogic(invalidMetrics);
      }).toThrow('盈利交易数 + 亏损交易数 必须等于总交易数');
    });
  });

  describe('基础状态管理', () => {
    it('应该能等待DOM更新完成 - TDD', async () => {
      const startTime = performance.now();
      await BacktestTestUtils.waitForDOMUpdate();
      const endTime = performance.now();
      
      // 验证等待确实发生了
      expect(endTime - startTime).toBeGreaterThan(0);
    });

    it('应该能等待Promise队列清空 - TDD', async () => {
      let resolved = false;
      
      // 创建一个异步操作
      Promise.resolve().then(() => {
        resolved = true;
      });
      
      // 等待Promise队列清空
      await BacktestTestUtils.flushPromises();
      
      expect(resolved).toBe(true);
    });

    it('应该能测量操作响应时间 - TDD', async () => {
      const testOperation = async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'test result';
      };
      
      const { result, duration } = await BacktestTestUtils.measureResponseTime(testOperation);
      
      expect(result).toBe('test result');
      expect(duration).toBeGreaterThan(5); // 至少5ms
      expect(duration).toBeLessThan(50); // 不超过50ms
    });

    it('应该能处理操作失败的响应时间测量 - TDD', async () => {
      const failingOperation = async () => {
        await new Promise(resolve => setTimeout(resolve, 5));
        throw new Error('测试错误');
      };
      
      await expect(BacktestTestUtils.measureResponseTime(failingOperation))
        .rejects.toThrow('测试错误');
    });
  });

  describe('基础Mock工具', () => {
    it('应该能创建网络错误场景 - TDD', () => {
      const scenarios = BacktestTestUtils.createNetworkErrorScenarios();
      
      expect(scenarios).toHaveLength(6);
      expect(scenarios[0].name).toBe('网络连接超时');
      expect(scenarios[1].name).toBe('服务器内部错误 (500)');
      
      // 验证每个场景都有必要的方法
      scenarios.forEach(scenario => {
        expect(scenario.setup).toBeTypeOf('function');
        expect(scenario.cleanup).toBeTypeOf('function');
      });
    });

    it('应该能创建数据源错误场景 - TDD', () => {
      const scenario = BacktestTestUtils.createDataSourceErrorScenario('tushare_quota_exceeded');
      
      expect(scenario.setup).toBeTypeOf('function');
      expect(scenario.cleanup).toBeTypeOf('function');
      expect(scenario.getExpectedError()).toBeInstanceOf(Error);
      expect(scenario.getStatusCode()).toBe(429);
      expect(scenario.getRetryAfter()).toBe(3600);
    });

    it('应该能创建数据源降级Mock - TDD', () => {
      const fallbackMock = BacktestTestUtils.createDataSourceFallbackMock(
        'tushare',
        'abu_legacy',
        'quota_exceeded'
      );
      
      expect(fallbackMock.success).toBe(true);
      expect(fallbackMock.data.requested_data_source).toBe('tushare');
      expect(fallbackMock.data.actual_data_source).toBe('abu_legacy');
      expect(fallbackMock.data.fallback_occurred).toBe(true);
      expect(fallbackMock.data.fallback_reason).toBe('quota_exceeded');
    });

    it('应该能创建Abu策略验证Mock - TDD', () => {
      const validMock = BacktestTestUtils.createAbuStrategyValidationMock('trend_follow', true);
      
      expect(validMock.success).toBe(true);
      expect(validMock.data.strategy_type).toBe('trend_follow');
      expect(validMock.data.abu_params).toBeDefined();
      expect(validMock.data.abu_params.period).toBe(20);
      
      const invalidMock = BacktestTestUtils.createAbuStrategyValidationMock('trend_follow', false);
      expect(invalidMock.error).toBeInstanceOf(Error);
    });
  });

  describe('基础验证工具', () => {
    it('应该能验证权益曲线基础数据 - TDD', () => {
      const validCurve: EquityPoint[] = [
        { date: '2024-01-01', equity: 100000, drawdown: 0 },
        { date: '2024-01-02', equity: 101000, drawdown: 0 },
        { date: '2024-01-03', equity: 99000, drawdown: -1.98 }
      ];
      
      expect(() => {
        BacktestTestUtils.validateEquityCurveContinuity(validCurve);
      }).not.toThrow();
    });

    it('应该能检测权益曲线日期顺序错误 - TDD', () => {
      const invalidCurve: EquityPoint[] = [
        { date: '2024-01-02', equity: 100000, drawdown: 0 },
        { date: '2024-01-01', equity: 101000, drawdown: 0 } // 日期倒序
      ];
      
      expect(() => {
        BacktestTestUtils.validateEquityCurveContinuity(invalidCurve);
      }).toThrow('日期顺序错误');
    });

    it('应该能验证Abu策略配置 - TDD', () => {
      const validConfig = {
        strategy_type: 'trend_follow',
        abu_params: {
          period: 20,
          threshold: 0.02
        },
        data_source: 'tushare'
      };
      
      const result = BacktestTestUtils.validateAbuStrategyConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该能检测无效的Abu策略配置 - TDD', () => {
      const invalidConfig = {
        strategy_type: 'invalid_strategy',
        abu_params: null,
        data_source: 'unknown_source'
      };
      
      const result = BacktestTestUtils.validateAbuStrategyConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('无效的abu策略类型');
    });
  });

  describe('基础API验证', () => {
    it('应该能验证数据源降级逻辑 - TDD', () => {
      const validResponse = {
        data: {
          fallback_occurred: true,
          requested_data_source: 'tushare',
          actual_data_source: 'abu_legacy',
          fallback_reason: 'quota_exceeded',
          data_quality_score: 0.85
        }
      };
      
      const result = BacktestTestUtils.validateDataSourceFallback(validResponse);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该能检测数据源降级配置错误 - TDD', () => {
      const invalidResponse = {
        data: {
          fallback_occurred: true,
          requested_data_source: 'tushare',
          actual_data_source: 'tushare', // 错误：降级后数据源相同
          fallback_reason: 'quota_exceeded'
        }
      };
      
      const result = BacktestTestUtils.validateDataSourceFallback(invalidResponse);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('降级发生时请求的数据源不应等于实际数据源');
    });
  });
});