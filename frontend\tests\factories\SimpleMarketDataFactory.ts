// 简化数据工厂 - Market
// 用于TDD快速测试数据生成

import type { Symbol, KlineData, SymbolsResponse, KlineDataResponse, ApiResponse } from '../../src/api/types';

/**
 * Market数据工厂类
 * 提供类型安全的市场数据mock生成方法
 */
export class SimpleMarketDataFactory {
  /**
   * 创建基础股票代码数据
   */
  static createSymbol(overrides: Partial<Symbol> = {}): Symbol {
    return {
      symbol: '000001.SZ',
      name: '平安银行',
      market: 'SZ',
      industry: '银行',
      list_date: '1991-04-03',
      ...overrides
    };
  }

  /**
   * 创建K线数据
   */
  static createKlineData(overrides: Partial<KlineData> = {}): KlineData {
    return {
      date: '2024-01-01',
      open: 10.50,
      high: 11.20,
      low: 10.30,
      close: 11.00,
      volume: 1000000,
      amount: 10800000,
      turnover_rate: 0.85,
      change_rate: 0.048,
      ...overrides
    };
  }

  /**
   * 创建多个股票代码数据
   */
  static createSymbols(count: number = 5): Symbol[] {
    const symbols = [
      { symbol: '000001.SZ', name: '平安银行', market: 'SZ', industry: '银行' },
      { symbol: '000002.SZ', name: '万科A', market: 'SZ', industry: '房地产' },
      { symbol: '600000.SH', name: '浦发银行', market: 'SH', industry: '银行' },
      { symbol: '600036.SH', name: '招商银行', market: 'SH', industry: '银行' },
      { symbol: '000858.SZ', name: '五粮液', market: 'SZ', industry: '食品饮料' }
    ];

    return Array.from({ length: count }, (_, index) => 
      this.createSymbol({
        ...symbols[index % symbols.length],
        list_date: `199${1 + index}-04-03`
      })
    );
  }

  /**
   * 创建K线数据序列
   */
  static createKlineDataSeries(days: number = 30): KlineData[] {
    const baseDate = new Date('2024-01-01');
    const basePrice = 10.0;
    
    return Array.from({ length: days }, (_, index) => {
      const date = new Date(baseDate);
      date.setDate(baseDate.getDate() + index);
      
      // 模拟价格波动
      const randomFactor = 0.95 + Math.random() * 0.1; // 0.95-1.05的随机因子
      const price = basePrice * Math.pow(randomFactor, index / 10);
      const volatility = 0.02; // 2%的日内波动
      
      const open = price * (0.98 + Math.random() * 0.04);
      const close = price * (0.98 + Math.random() * 0.04);
      const high = Math.max(open, close) * (1 + Math.random() * volatility);
      const low = Math.min(open, close) * (1 - Math.random() * volatility);
      
      return this.createKlineData({
        date: date.toISOString().split('T')[0],
        open: Number(open.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        close: Number(close.toFixed(2)),
        volume: Math.floor(500000 + Math.random() * 1000000),
        amount: Math.floor(open * (500000 + Math.random() * 1000000)),
        turnover_rate: Number((0.5 + Math.random() * 2).toFixed(2)),
        change_rate: index > 0 ? Number(((close - basePrice) / basePrice).toFixed(4)) : 0
      });
    });
  }

  /**
   * 创建股票代码响应数据
   */
  static createSymbolsResponse(symbols?: Symbol[]): SymbolsResponse {
    const data = symbols || this.createSymbols();
    return {
      data,
      success: true,
      message: 'Success',
      total: data.length
    };
  }

  /**
   * 创建K线数据响应
   */
  static createKlineDataResponse(symbol: string = '000001.SZ', period: string = '1d', klineData?: KlineData[]): KlineDataResponse {
    const data = klineData || this.createKlineDataSeries();
    const symbolInfo = this.createSymbol({ symbol });
    
    return {
      data: {
        symbol,
        name: symbolInfo.name,
        market: symbolInfo.market,
        period,
        data,
        latest_date: data[data.length - 1]?.date || '2024-01-30',
        indicators: {
          ma5: data.slice(-5).reduce((sum, item) => sum + item.close, 0) / Math.min(5, data.length),
          ma20: data.slice(-20).reduce((sum, item) => sum + item.close, 0) / Math.min(20, data.length),
          volume_ma: data.slice(-5).reduce((sum, item) => sum + item.volume, 0) / Math.min(5, data.length)
        }
      },
      success: true,
      message: 'Success'
    };
  }

  /**
   * 创建边界情况的数据
   */
  static createBoundarySymbol(): Symbol {
    return this.createSymbol({
      symbol: '999999.SZ',
      name: 'A'.repeat(50), // 长名称
      market: 'SZ',
      industry: 'B'.repeat(20), // 长行业名
      list_date: '1990-01-01'
    });
  }

  /**
   * 创建异常K线数据（用于错误测试）
   */
  static createInvalidKlineData(): Partial<KlineData> {
    return {
      date: 'invalid-date',
      open: -1, // 负价格
      high: 0,
      low: 100, // low > high
      close: NaN,
      volume: -1000 // 负成交量
    };
  }

  /**
   * 创建复杂市场数据（包含多种周期）
   */
  static createComplexMarketData() {
    const symbols = this.createSymbols(10);
    const periods = ['1m', '5m', '15m', '30m', '1h', '1d', '1w', '1M'];
    
    return {
      symbols,
      klineData: periods.reduce((acc, period) => {
        acc[period] = symbols.reduce((symbolAcc, symbol) => {
          symbolAcc[symbol.symbol] = this.createKlineDataSeries(
            period.includes('m') ? 100 : period.includes('h') ? 50 : 30
          );
          return symbolAcc;
        }, {} as Record<string, KlineData[]>);
        return acc;
      }, {} as Record<string, Record<string, KlineData[]>>)
    };
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(message: string = 'Market data not found'): ApiResponse<null> {
    return {
      data: null,
      success: false,
      message
    };
  }

  /**
   * 验证市场数据的API契约
   */
  static validateSymbolContract(symbol: Symbol): boolean {
    const requiredFields = ['symbol', 'name', 'market', 'industry', 'list_date'];
    
    for (const field of requiredFields) {
      if (!(field in symbol)) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // 验证股票代码格式
    if (!/^\d{6}\.(SH|SZ)$/.test(symbol.symbol)) {
      console.error('Invalid symbol format');
      return false;
    }

    return true;
  }

  /**
   * 验证K线数据契约
   */
  static validateKlineDataContract(klineData: KlineData): boolean {
    const requiredFields = ['date', 'open', 'high', 'low', 'close', 'volume'];
    
    for (const field of requiredFields) {
      if (!(field in klineData)) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    // 验证价格逻辑
    if (klineData.high < klineData.low) {
      console.error('High price cannot be lower than low price');
      return false;
    }

    if (klineData.open < 0 || klineData.close < 0 || klineData.high < 0 || klineData.low < 0) {
      console.error('Prices cannot be negative');
      return false;
    }

    if (klineData.volume < 0) {
      console.error('Volume cannot be negative');
      return false;
    }

    return true;
  }

  /**
   * 创建实时行情数据
   */
  static createRealtimeQuote(symbol: string = '000001.SZ') {
    const baseData = this.createKlineData();
    return {
      symbol,
      name: this.createSymbol({ symbol }).name,
      current_price: baseData.close,
      change: Number((Math.random() * 2 - 1).toFixed(2)),
      change_percent: Number((Math.random() * 0.1 - 0.05).toFixed(4)),
      volume: baseData.volume,
      amount: baseData.amount,
      high: baseData.high,
      low: baseData.low,
      open: baseData.open,
      prev_close: Number((baseData.close * (0.95 + Math.random() * 0.1)).toFixed(2)),
      timestamp: new Date().toISOString()
    };
  }
}

// 导出便捷方法
export const {
  createSymbol,
  createKlineData,
  createSymbols,
  createKlineDataSeries,
  createSymbolsResponse,
  createKlineDataResponse,
  createBoundarySymbol,
  createInvalidKlineData,
  createComplexMarketData,
  createErrorResponse,
  validateSymbolContract,
  validateKlineDataContract,
  createRealtimeQuote
} = SimpleMarketDataFactory;